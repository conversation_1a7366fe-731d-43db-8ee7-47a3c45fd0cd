import { test, expect } from '@playwright/test';

test.describe('Enhanced Brainstorming System', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // Wait for app to load
    await page.waitForLoadState('networkidle');
  });

  test('should create a new brainstorming session', async ({ page }) => {
    // Navigate to brainstorming
    await page.click('text=Brainstorming');
    
    // Create new session
    await page.click('button:has-text("New Session")');
    
    // Fill session details
    await page.fill('input[name="title"]', 'Product Launch Ideas');
    await page.fill('textarea[name="description"]', 'Brainstorming session for Q2 product launch');
    
    // Select template
    await page.click('text=Select Template');
    await page.click('text=Product Features');
    
    // Start session
    await page.click('button:has-text("Start Session")');
    
    // Verify session created
    await expect(page.locator('h1')).toContainText('Product Launch Ideas');
    await expect(page.locator('.session-status')).toContainText('Active');
  });

  test('should extract ideas from chat messages', async ({ page }) => {
    // Assume we're in an active session
    await page.goto('/brainstorming/session-123');
    
    // Type a message with ideas
    const chatInput = page.locator('textarea[placeholder*="Type your ideas"]');
    await chatInput.fill('We should implement user authentication with OAuth support and add dark mode to improve UX');
    await page.keyboard.press('Enter');
    
    // Wait for AI response
    await page.waitForSelector('.assistant-message', { timeout: 30000 });
    
    // Check if ideas were extracted
    await page.click('button:has-text("Ideas")');
    await expect(page.locator('.idea-card')).toHaveCount(2);
    await expect(page.locator('.idea-card').first()).toContainText('user authentication with OAuth');
    await expect(page.locator('.idea-card').nth(1)).toContainText('dark mode');
  });

  test('should organize ideas in kanban board', async ({ page }) => {
    await page.goto('/brainstorming/session-123');
    
    // Switch to Kanban view
    await page.click('button:has-text("Kanban")');
    
    // Verify columns exist
    await expect(page.locator('.kanban-column')).toHaveCount(4);
    await expect(page.locator('.kanban-column').first()).toContainText('To Explore');
    
    // Drag idea to different column
    const idea = page.locator('.idea-card').first();
    const inProgressColumn = page.locator('.kanban-column:has-text("In Progress")');
    
    await idea.dragTo(inProgressColumn);
    
    // Verify idea moved
    await expect(inProgressColumn.locator('.idea-card')).toHaveCount(1);
  });

  test('should create mind map visualization', async ({ page }) => {
    await page.goto('/brainstorming/session-123');
    
    // Switch to Mind Map view
    await page.click('button:has-text("Mind Map")');
    
    // Wait for visualization to load
    await page.waitForSelector('.react-flow__renderer', { timeout: 10000 });
    
    // Verify nodes exist
    const nodes = page.locator('.react-flow__node');
    await expect(nodes).toHaveCount(3); // Central node + 2 ideas
    
    // Click on a node
    await nodes.first().click();
    
    // Verify node details appear
    await expect(page.locator('.node-details')).toBeVisible();
  });

  test('should export session data', async ({ page }) => {
    await page.goto('/brainstorming/session-123');
    
    // Open export dialog
    await page.click('button:has-text("Export")');
    
    // Select export format
    await page.click('label:has-text("Markdown")');
    
    // Configure export options
    await page.check('input[name="includeIdeas"]');
    await page.check('input[name="includeClusters"]');
    
    // Start download
    const downloadPromise = page.waitForEvent('download');
    await page.click('button:has-text("Download")');
    const download = await downloadPromise;
    
    // Verify download
    expect(download.suggestedFilename()).toContain('brainstorm-export');
    expect(download.suggestedFilename()).toContain('.md');
  });

  test('should switch AI personas', async ({ page }) => {
    await page.goto('/brainstorming/session-123');
    
    // Open persona selector
    await page.click('button:has-text("AI Persona")');
    
    // Select Devil's Advocate
    await page.click('text=Devil\'s Advocate');
    
    // Send a message
    const chatInput = page.locator('textarea[placeholder*="Type your ideas"]');
    await chatInput.fill('We should expand internationally');
    await page.keyboard.press('Enter');
    
    // Wait for response
    await page.waitForSelector('.assistant-message:last-child', { timeout: 30000 });
    
    // Verify persona indicator
    await expect(page.locator('.persona-indicator')).toContainText('Devil\'s Advocate');
    
    // Response should be challenging
    const response = page.locator('.assistant-message:last-child');
    await expect(response).toContainText(/challenge|risk|consider|however/i);
  });

  test('should perform real-time search', async ({ page }) => {
    await page.goto('/brainstorming/session-123');
    
    // Open search interface
    await page.click('button:has-text("Search")');
    
    // Type search query
    const searchInput = page.locator('input[placeholder*="Search"]');
    await searchInput.fill('authentication');
    
    // Wait for results
    await page.waitForSelector('.search-results', { timeout: 5000 });
    
    // Verify results
    const results = page.locator('.search-result');
    await expect(results).toHaveCountGreaterThan(0);
    
    // Click on a result
    await results.first().click();
    
    // Verify navigation
    await expect(page.locator('.highlighted')).toBeVisible();
  });

  test('should handle multi-modal input', async ({ page }) => {
    await page.goto('/brainstorming/session-123');
    
    // Open multi-modal input
    await page.click('button:has-text("Add Media")');
    
    // Upload an image
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles('./test-assets/mockup.png');
    
    // Add description
    await page.fill('textarea[name="mediaDescription"]', 'Mobile app mockup for authentication flow');
    
    // Submit
    await page.click('button:has-text("Attach")');
    
    // Verify media attached
    await expect(page.locator('.attached-media')).toHaveCount(1);
    await expect(page.locator('.media-thumbnail')).toBeVisible();
  });

  test('should generate tasks from ideas', async ({ page }) => {
    await page.goto('/brainstorming/session-123');
    
    // Open task generator
    await page.click('button:has-text("Generate Tasks")');
    
    // Select ideas to convert
    await page.check('input[data-idea-id="idea-1"]');
    await page.check('input[data-idea-id="idea-2"]');
    
    // Configure task generation
    await page.selectOption('select[name="priority"]', 'high');
    await page.fill('input[name="estimatedHours"]', '8');
    
    // Generate tasks
    await page.click('button:has-text("Generate")');
    
    // Wait for tasks
    await page.waitForSelector('.generated-tasks', { timeout: 10000 });
    
    // Verify tasks created
    const tasks = page.locator('.task-item');
    await expect(tasks).toHaveCount(2);
    await expect(tasks.first()).toContainText('high priority');
  });

  test('should cluster ideas automatically', async ({ page }) => {
    await page.goto('/brainstorming/session-123');
    
    // Ensure we have multiple ideas
    await page.click('button:has-text("Ideas")');
    
    // Trigger clustering
    await page.click('button:has-text("Auto-Cluster")');
    
    // Wait for clustering to complete
    await page.waitForSelector('.cluster-complete-notification', { timeout: 15000 });
    
    // View clusters
    await page.click('button:has-text("Clusters")');
    
    // Verify clusters created
    const clusters = page.locator('.cluster-card');
    await expect(clusters).toHaveCountGreaterThan(0);
    
    // Check cluster details
    await clusters.first().click();
    await expect(page.locator('.cluster-details')).toContainText('ideas');
    await expect(page.locator('.cluster-theme')).toBeVisible();
  });
});