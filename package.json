{"name": "claudia", "private": true, "version": "0.1.0", "license": "AGPL-3.0", "type": "module", "scripts": {"predev": "bun run build:executables:current", "dev": "vite", "build": "tsc && vite build", "prebuild": "bun run build:executables:current", "build:executables": "bun run scripts/fetch-and-build.js --version=1.0.41", "build:executables:current": "bun run scripts/fetch-and-build.js current --version=1.0.41", "build:executables:linux": "bun run scripts/fetch-and-build.js linux --version=1.0.41", "build:executables:macos": "bun run scripts/fetch-and-build.js macos --version=1.0.41", "build:executables:windows": "bun run scripts/fetch-and-build.js windows --version=1.0.41", "preview": "vite preview", "tauri": "tauri", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.1", "@modelcontextprotocol/sdk": "^1.16.0", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.1.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.3", "@radix-ui/react-tooltip": "^1.1.5", "@tailwindcss/cli": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-virtual": "^3.13.10", "@tauri-apps/api": "^2.7.0", "@tauri-apps/plugin-dialog": "^2.0.2", "@tauri-apps/plugin-fs": "^2.4.1", "@tauri-apps/plugin-global-shortcut": "^2.0.0", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2.0.1", "@types/diff": "^8.0.0", "@types/dompurify": "^3.0.5", "@types/react-grid-layout": "^1.3.5", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "@uiw/react-md-editor": "^4.0.7", "ansi-to-html": "^0.7.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "diff": "^8.0.2", "framer-motion": "^12.0.0-alpha.1", "frappe-gantt-react": "^0.0.8", "html2canvas": "^1.4.1", "immer": "^10.1.1", "isomorphic-dompurify": "^2.26.0", "jspdf": "^3.0.1", "lucide-react": "^0.468.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-grid-layout": "^1.5.2", "react-hook-form": "^7.54.2", "react-hotkeys-hook": "^5.1.0", "react-markdown": "^9.0.3", "react-router-dom": "^7.7.0", "react-syntax-highlighter": "^5.8.0", "react-window": "^1.8.11", "reactflow": "^11.11.4", "recharts": "^2.14.1", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.1.8", "zod": "^3.24.1", "zustand": "^5.0.6"}, "devDependencies": {"@playwright/test": "^1.54.1", "@tauri-apps/cli": "^2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.15.30", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/sharp": "^0.32.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/ui": "^3.2.4", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "sharp": "^0.34.2", "typescript": "^5.9.0-beta", "vite": "^6.0.3", "vitest": "^3.2.4"}, "trustedDependencies": ["@parcel/watcher", "@tailwindcss/oxide"]}