import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

// Types for marketplace functionality
export interface MarketplaceAgent {
  id: string;
  name: string;
  description: string;
  longDescription?: string;
  version: string;
  author: string;
  category: string;
  tags: string[];
  icon: string;
  downloadCount: number;
  rating: number;
  reviewCount: number;
  lastUpdated: string;
  size: number;
  dependencies: string[];
  screenshots?: string[];
  documentation?: string;
  sourceUrl?: string;
  licenseType: string;
  isVerified: boolean;
  isFeatured: boolean;
  installUrl: string;
  // Installation status
  isInstalled?: boolean;
  installedVersion?: string;
}

export interface MarketplaceReview {
  id: string;
  agentId: string;
  userId: string;
  userName: string;
  rating: number;
  review: string;
  createdAt: string;
  isVerified: boolean;
}

export interface MarketplaceCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  agentCount: number;
}

export interface MarketplaceCollection {
  id: string;
  name: string;
  description: string;
  curator: string;
  agents: string[]; // agent IDs
  isOfficial: boolean;
  createdAt: string;
}

export type SortBy = 'popularity' | 'rating' | 'recent' | 'downloads' | 'name';
export type FilterBy = 'all' | 'free' | 'verified' | 'featured';

interface MarketplaceState {
  // Data
  agents: MarketplaceAgent[];
  categories: MarketplaceCategory[];
  collections: MarketplaceCollection[];
  reviews: Record<string, MarketplaceReview[]>; // agentId -> reviews
  installedAgents: Set<string>;
  
  // UI state
  isLoading: boolean;
  isInstalling: boolean;
  error: string | null;
  searchQuery: string;
  selectedCategory: string | null;
  selectedCollection: string | null;
  sortBy: SortBy;
  filterBy: FilterBy;
  currentPage: number;
  itemsPerPage: number;
  
  // Modals and dialogs
  selectedAgent: MarketplaceAgent | null;
  showDetailsModal: boolean;
  showInstallDialog: boolean;
  
  // Cache management
  lastFetchTime: number;
  cacheTimeout: number;
  
  // Actions
  fetchAgents: (forceRefresh?: boolean) => Promise<void>;
  fetchCategories: () => Promise<void>;
  fetchCollections: () => Promise<void>;
  fetchReviews: (agentId: string) => Promise<void>;
  
  // Search and filtering
  setSearchQuery: (query: string) => void;
  setSelectedCategory: (categoryId: string | null) => void;
  setSelectedCollection: (collectionId: string | null) => void;
  setSortBy: (sortBy: SortBy) => void;
  setFilterBy: (filterBy: FilterBy) => void;
  setCurrentPage: (page: number) => void;
  clearFilters: () => void;
  
  // Modal management
  openDetailsModal: (agent: MarketplaceAgent) => void;
  closeDetailsModal: () => void;
  openInstallDialog: (agent: MarketplaceAgent) => void;
  closeInstallDialog: () => void;
  
  // Agent operations
  installAgent: (agentId: string) => Promise<void>;
  uninstallAgent: (agentId: string) => Promise<void>;
  updateAgent: (agentId: string) => Promise<void>;
  rateAgent: (agentId: string, rating: 1 | 2 | 3 | 4 | 5, review?: string) => Promise<void>;
  
  // Utilities
  getFilteredAgents: () => MarketplaceAgent[];
  clearError: () => void;
  refreshData: () => Promise<void>;
}

export const useMarketplaceStore = create<MarketplaceState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    agents: [],
    categories: [],
    collections: [],
    reviews: {},
    installedAgents: new Set(),
    
    // UI state
    isLoading: false,
    isInstalling: false,
    error: null,
    searchQuery: '',
    selectedCategory: null,
    selectedCollection: null,
    sortBy: 'popularity',
    filterBy: 'all',
    currentPage: 1,
    itemsPerPage: 12,
    
    // Modal state
    selectedAgent: null,
    showDetailsModal: false,
    showInstallDialog: false,
    
    // Cache
    lastFetchTime: 0,
    cacheTimeout: 5 * 60 * 1000, // 5 minutes
    
    // Fetch agents from marketplace API
    fetchAgents: async (forceRefresh = false) => {
      const now = Date.now();
      const { lastFetchTime, cacheTimeout } = get();
      
      // Use cache unless forced refresh
      if (!forceRefresh && now - lastFetchTime < cacheTimeout && get().agents.length > 0) {
        return;
      }
      
      set({ isLoading: true, error: null });
      
      try {
        // Use real marketplace API - import invoke properly
        const { invoke } = await import('@tauri-apps/api/core');
        const result = await invoke('marketplace_search_agents', {
          params: {
            page: 1,
            limit: 50,
            sort_by: 'downloads',
            sort_order: 'desc'
          }
        });
        
        // Transform marketplace agents to store format
        const transformedAgents: MarketplaceAgent[] = result.agents.map(agent => ({
          id: agent.remote_id,
          name: agent.name,
          description: agent.description,
          longDescription: agent.long_description || agent.description,
          version: agent.version,
          author: agent.author,
          category: agent.category_id?.toString() || 'other',
          tags: Array.isArray(agent.tags) ? agent.tags : (agent.tags ? JSON.parse(agent.tags as string) : []),
          icon: agent.icon,
          downloadCount: agent.download_count,
          rating: agent.rating_average,
          reviewCount: agent.rating_count,
          lastUpdated: agent.updated_at,
          size: agent.file_size,
          dependencies: [], // TODO: Fetch dependencies separately
          screenshots: [],
          documentation: agent.documentation_url || undefined,
          sourceUrl: agent.source_url || undefined,
          licenseType: agent.license || 'Unknown',
          isVerified: agent.rating_count >= 5 && agent.rating_average >= 4.0,
          isFeatured: agent.rating_average >= 4.5 && agent.download_count >= 1000,
          installUrl: agent.download_url,
          isInstalled: agent.is_installed,
          installedVersion: agent.is_installed ? agent.version : undefined
        }));
        
        set({
          agents: transformedAgents,
          lastFetchTime: now,
          isLoading: false
        });
      } catch (error) {
        console.error('Failed to fetch marketplace agents:', error);
        set({
          error: error instanceof Error ? error.message : 'Failed to fetch agents',
          isLoading: false
        });
      }
    },
    
    // Fetch categories
    fetchCategories: async () => {
      try {
        // Use real marketplace API
        const { invoke } = await import('@tauri-apps/api/core');
        const apiCategories = await invoke('marketplace_get_categories', { includeInactive: false });
        
        // Transform API categories to store format
        const transformedCategories: MarketplaceCategory[] = apiCategories.map(cat => ({
          id: cat.id.toString(),
          name: cat.name,
          description: cat.description || '',
          icon: cat.icon || 'folder',
          agentCount: cat.agent_count || 0
        }));
        
        set({ categories: transformedCategories });
      } catch (error) {
        console.error('Failed to fetch categories:', error);
        set({
          error: error instanceof Error ? error.message : 'Failed to fetch categories'
        });
      }
    },
    
    // Fetch collections
    fetchCollections: async () => {
      try {
        // Use real marketplace API
        const { invoke } = await import('@tauri-apps/api/core');
        const apiCollections = await invoke('marketplace_get_collections', { page: 1, limit: 20 }) || [];
        
        // Transform API collections to store format
        const transformedCollections: MarketplaceCollection[] = apiCollections.map(collection => ({
          id: collection.id.toString(),
          name: collection.name,
          description: collection.description || '',
          curator: collection.curator || 'Unknown',
          agents: [], // TODO: Fetch collection agents separately
          isOfficial: collection.is_featured,
          createdAt: collection.created_at
        }));
        
        set({ collections: transformedCollections });
      } catch (error) {
        console.error('Failed to fetch collections:', error);
        set({
          error: error instanceof Error ? error.message : 'Failed to fetch collections'
        });
      }
    },
    
    // Fetch reviews for an agent
    fetchReviews: async (agentId: string) => {
      try {
        // Use real marketplace API
        const { getAgentRatings } = await import('@/lib/api-marketplace');
        const ratingsData = await getAgentRatings(agentId, 1, 20);
        
        // Transform API ratings to store format
        const transformedReviews: MarketplaceReview[] = ratingsData.ratings.map(rating => ({
          id: rating.id.toString(),
          agentId,
          userId: rating.user_identifier || 'anonymous',
          userName: 'User', // API doesn't provide usernames for privacy
          rating: rating.rating,
          review: rating.review_content || '',
          createdAt: rating.created_at,
          isVerified: rating.is_verified
        }));
        
        set(state => ({
          reviews: {
            ...state.reviews,
            [agentId]: transformedReviews
          }
        }));
      } catch (error) {
        console.error('Failed to fetch reviews:', error);
        set({
          error: error instanceof Error ? error.message : 'Failed to fetch reviews'
        });
      }
    },
    
    // Search and filtering
    setSearchQuery: (query: string) => {
      set({ searchQuery: query, currentPage: 1 });
    },
    
    setSelectedCategory: (categoryId: string | null) => {
      set({ selectedCategory: categoryId, currentPage: 1 });
    },
    
    setSelectedCollection: (collectionId: string | null) => {
      set({ selectedCollection: collectionId, currentPage: 1 });
    },
    
    setSortBy: (sortBy: SortBy) => {
      set({ sortBy, currentPage: 1 });
    },
    
    setFilterBy: (filterBy: FilterBy) => {
      set({ filterBy, currentPage: 1 });
    },
    
    setCurrentPage: (page: number) => {
      set({ currentPage: page });
    },
    
    clearFilters: () => {
      set({
        searchQuery: '',
        selectedCategory: null,
        selectedCollection: null,
        sortBy: 'popularity',
        filterBy: 'all',
        currentPage: 1
      });
    },
    
    // Modal management
    openDetailsModal: (agent: MarketplaceAgent) => {
      set({ selectedAgent: agent, showDetailsModal: true });
    },
    
    closeDetailsModal: () => {
      set({ selectedAgent: null, showDetailsModal: false });
    },
    
    openInstallDialog: (agent: MarketplaceAgent) => {
      set({ selectedAgent: agent, showInstallDialog: true });
    },
    
    closeInstallDialog: () => {
      set({ selectedAgent: null, showInstallDialog: false });
    },
    
    // Agent operations
    installAgent: async (agentId: string) => {
      set({ isInstalling: true, error: null });
      
      try {
        // Use real marketplace API
        const { invoke } = await import('@tauri-apps/api/core');
        const result = await invoke('marketplace_install_agent', {
          remote_id: agentId,
          source: 'marketplace',
          force_reinstall: false
        });
        
        if (result.success) {
          set(state => ({
            installedAgents: new Set([...state.installedAgents, agentId]),
            agents: state.agents.map(agent =>
              agent.id === agentId
                ? { ...agent, isInstalled: true, installedVersion: agent.version }
                : agent
            ),
            isInstalling: false
          }));
        } else {
          throw new Error(result.message || 'Installation failed');
        }
      } catch (error) {
        console.error('Failed to install agent:', error);
        set({
          error: error instanceof Error ? error.message : 'Failed to install agent',
          isInstalling: false
        });
      }
    },
    
    uninstallAgent: async (agentId: string) => {
      try {
        // Use real marketplace API
        const { uninstallAgent } = await import('@/lib/api-marketplace');
        const result = await uninstallAgent(agentId, false);
        
        if (result.success) {
          set(state => ({
            installedAgents: new Set([...state.installedAgents].filter(id => id !== agentId)),
            agents: state.agents.map(agent =>
              agent.id === agentId
                ? { ...agent, isInstalled: false, installedVersion: undefined }
                : agent
            )
          }));
        } else {
          throw new Error(result.message || 'Uninstallation failed');
        }
      } catch (error) {
        console.error('Failed to uninstall agent:', error);
        set({
          error: error instanceof Error ? error.message : 'Failed to uninstall agent'
        });
      }
    },
    
    updateAgent: async (agentId: string) => {
      try {
        // Mock update - replace with actual API
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        set(state => ({
          agents: state.agents.map(agent =>
            agent.id === agentId
              ? { ...agent, installedVersion: agent.version }
              : agent
          )
        }));
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to update agent'
        });
      }
    },
    
    rateAgent: async (agentId: string, rating: 1 | 2 | 3 | 4 | 5, review?: string) => {
      try {
        // Use real marketplace API
        const { submitRating } = await import('@/lib/api-marketplace');
        await submitRating({
          marketplace_agent_id: parseInt(agentId),
          rating,
          review_content: review,
          review_title: undefined
        });
        
        // Refresh agent data to get updated ratings
        const state = get();
        await state.fetchAgents(true);
      } catch (error) {
        console.error('Failed to rate agent:', error);
        set({
          error: error instanceof Error ? error.message : 'Failed to rate agent'
        });
      }
    },
    
    // Get filtered agents based on current filters
    getFilteredAgents: () => {
      const state = get();
      let filtered = [...state.agents];
      
      // Apply search
      if (state.searchQuery) {
        const query = state.searchQuery.toLowerCase();
        filtered = filtered.filter(agent =>
          agent.name.toLowerCase().includes(query) ||
          agent.description.toLowerCase().includes(query) ||
          agent.tags.some(tag => tag.toLowerCase().includes(query)) ||
          agent.author.toLowerCase().includes(query)
        );
      }
      
      // Apply category filter
      if (state.selectedCategory) {
        filtered = filtered.filter(agent => agent.category === state.selectedCategory);
      }
      
      // Apply collection filter
      if (state.selectedCollection) {
        const collection = state.collections.find(c => c.id === state.selectedCollection);
        if (collection) {
          filtered = filtered.filter(agent => collection.agents.includes(agent.id));
        }
      }
      
      // Apply additional filters
      switch (state.filterBy) {
        case 'verified':
          filtered = filtered.filter(agent => agent.isVerified);
          break;
        case 'featured':
          filtered = filtered.filter(agent => agent.isFeatured);
          break;
        case 'free':
          filtered = filtered.filter(agent => agent.licenseType !== 'Commercial');
          break;
      }
      
      // Apply sorting
      filtered.sort((a, b) => {
        switch (state.sortBy) {
          case 'popularity':
            return b.downloadCount - a.downloadCount;
          case 'rating':
            return b.rating - a.rating;
          case 'recent':
            return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime();
          case 'downloads':
            return b.downloadCount - a.downloadCount;
          case 'name':
            return a.name.localeCompare(b.name);
          default:
            return 0;
        }
      });
      
      return filtered;
    },
    
    // Utilities
    clearError: () => set({ error: null }),
    
    refreshData: async () => {
      const state = get();
      await Promise.all([
        state.fetchAgents(true),
        state.fetchCategories(),
        state.fetchCollections()
      ]);
    }
  }))
);