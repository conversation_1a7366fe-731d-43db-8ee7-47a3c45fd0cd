import { describe, it, expect, beforeEach } from 'vitest';
import { useBrainstormStore } from './brainstormStore';
import { mockIdea, mockSession, mockCluster } from '@/test/utils';

describe('BrainstormStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useBrainstormStore.setState({
      sessions: [],
      ideas: [],
      clusters: [],
      memories: [],
      currentSessionId: null,
    });
  });

  describe('Sessions', () => {
    it('should create a new session', () => {
      const store = useBrainstormStore.getState();
      const session = mockSession();
      
      store.createSession(session);
      
      expect(store.sessions).toHaveLength(1);
      expect(store.sessions[0]).toEqual(session);
      expect(store.currentSessionId).toBe(session.id);
    });

    it('should update an existing session', () => {
      const store = useBrainstormStore.getState();
      const session = mockSession();
      store.createSession(session);

      const updatedTitle = 'Updated Session Title';
      store.updateSession(session.id, { title: updatedTitle });

      expect(store.sessions[0].title).toBe(updatedTitle);
    });

    it('should delete a session and its related data', () => {
      const store = useBrainstormStore.getState();
      const session = mockSession();
      const idea = mockIdea({ sessionId: session.id });
      const cluster = mockCluster({ sessionId: session.id });

      store.createSession(session);
      store.addIdea(idea);
      store.addCluster(cluster);

      store.deleteSession(session.id);

      expect(store.sessions).toHaveLength(0);
      expect(store.ideas).toHaveLength(0);
      expect(store.clusters).toHaveLength(0);
      expect(store.currentSessionId).toBeNull();
    });
  });

  describe('Ideas', () => {
    it('should add a new idea', () => {
      const store = useBrainstormStore.getState();
      const idea = mockIdea();

      store.addIdea(idea);

      expect(store.ideas).toHaveLength(1);
      expect(store.ideas[0]).toEqual(idea);
    });

    it('should update an existing idea', () => {
      const store = useBrainstormStore.getState();
      const idea = mockIdea();
      store.addIdea(idea);

      const updatedContent = 'Updated idea content';
      store.updateIdea(idea.id, { content: updatedContent });

      expect(store.ideas[0].content).toBe(updatedContent);
    });

    it('should delete an idea', () => {
      const store = useBrainstormStore.getState();
      const idea = mockIdea();
      store.addIdea(idea);

      store.deleteIdea(idea.id);

      expect(store.ideas).toHaveLength(0);
    });

    it('should link two ideas', () => {
      const store = useBrainstormStore.getState();
      const idea1 = mockIdea({ id: 'idea-1' });
      const idea2 = mockIdea({ id: 'idea-2' });
      
      store.addIdea(idea1);
      store.addIdea(idea2);
      store.linkIdeas('idea-1', 'idea-2');

      expect(store.ideas[0].linkedIdeas).toContain('idea-2');
      expect(store.ideas[1].linkedIdeas).toContain('idea-1');
    });

    it('should get ideas by session', () => {
      const store = useBrainstormStore.getState();
      const sessionId = 'session-1';
      const idea1 = mockIdea({ sessionId });
      const idea2 = mockIdea({ id: 'idea-2', sessionId });
      const idea3 = mockIdea({ id: 'idea-3', sessionId: 'session-2' });

      store.addIdea(idea1);
      store.addIdea(idea2);
      store.addIdea(idea3);

      const sessionIdeas = store.getIdeasBySession(sessionId);

      expect(sessionIdeas).toHaveLength(2);
      expect(sessionIdeas.map(i => i.id)).toEqual(['idea-1', 'idea-2']);
    });
  });

  describe('Clusters', () => {
    it('should add a new cluster', () => {
      const store = useBrainstormStore.getState();
      const cluster = mockCluster();

      store.addCluster(cluster);

      expect(store.clusters).toHaveLength(1);
      expect(store.clusters[0]).toEqual(cluster);
    });

    it('should update cluster ideas', () => {
      const store = useBrainstormStore.getState();
      const cluster = mockCluster({ ideas: ['idea-1'] });
      store.addCluster(cluster);

      store.updateCluster(cluster.id, { ideas: ['idea-1', 'idea-2'] });

      expect(store.clusters[0].ideas).toEqual(['idea-1', 'idea-2']);
    });

    it('should merge two clusters', () => {
      const store = useBrainstormStore.getState();
      const cluster1 = mockCluster({ id: 'cluster-1', ideas: ['idea-1'] });
      const cluster2 = mockCluster({ id: 'cluster-2', ideas: ['idea-2'] });
      
      store.addCluster(cluster1);
      store.addCluster(cluster2);
      store.mergeClusters('cluster-1', 'cluster-2');

      expect(store.clusters).toHaveLength(1);
      expect(store.clusters[0].ideas).toEqual(['idea-1', 'idea-2']);
    });
  });

  describe('Settings', () => {
    it('should update settings', () => {
      const store = useBrainstormStore.getState();
      
      store.updateSettings({ autoCluster: false, clusterThreshold: 0.8 });

      expect(store.settings.autoCluster).toBe(false);
      expect(store.settings.clusterThreshold).toBe(0.8);
    });
  });
});