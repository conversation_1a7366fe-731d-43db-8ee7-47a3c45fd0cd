/**
 * Persona Management Store
 * 
 * Zustand store for managing brainstorming personas
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  BrainstormingPersona,
  PersonaRole,
  PersonaTemplate,
  PersonaInteraction,
  PersonaAnalytics,
  PersonaManagerState,
  PersonaContext,
  PersonaResponse,
  PersonaRecommendation,
  DEFAULT_PERSONA_PROFILES,
  PERSONA_PROMPT_TEMPLATES
} from '@/types/persona';

interface PersonaStore extends PersonaManagerState {
  // Persona CRUD operations
  createPersona: (template: Partial<BrainstormingPersona>) => string;
  updatePersona: (id: string, updates: Partial<BrainstormingPersona>) => void;
  deletePersona: (id: string) => void;
  duplicatePersona: (id: string) => string;
  
  // Persona activation
  setActivePersona: (id: string | null) => void;
  getActivePersona: () => BrainstormingPersona | null;
  
  // Persona templates
  getPersonaTemplates: () => PersonaTemplate[];
  createPersonaFromTemplate: (role: PersonaRole, customizations?: Partial<BrainstormingPersona>) => string;
  
  // Persona interactions
  recordInteraction: (interaction: Omit<PersonaInteraction, 'id' | 'timestamp'>) => void;
  getPersonaInteractions: (personaId: string, sessionId?: string) => PersonaInteraction[];
  
  // Persona recommendations
  getPersonaRecommendations: (context: PersonaContext) => PersonaRecommendation[];
  
  // Analytics
  updatePersonaMetrics: (personaId: string, metrics: Partial<BrainstormingPersona['metrics']>) => void;
  getPersonaAnalytics: (personaId: string, timeframe?: 'day' | 'week' | 'month' | 'all') => PersonaAnalytics | null;
  
  // Persona AI responses
  generatePersonaResponse: (personaId: string, prompt: string, context: PersonaContext) => Promise<PersonaResponse>;
  
  // Settings
  updateSettings: (settings: Partial<PersonaManagerState['settings']>) => void;
  
  // Utility functions
  searchPersonas: (query: string) => BrainstormingPersona[];
  getPersonasByRole: (role: PersonaRole) => BrainstormingPersona[];
  getTopPerformingPersonas: (limit?: number) => BrainstormingPersona[];
}

// Default persona templates
const createDefaultPersonaTemplates = (): PersonaTemplate[] => [
  {
    role: PersonaRole.CREATIVE_THINKER,
    name: 'Creative Thinker',
    description: 'Generates innovative and out-of-the-box ideas',
    characteristics: DEFAULT_PERSONA_PROFILES[PersonaRole.CREATIVE_THINKER],
    promptStyle: {
      questionTypes: ['What if...?', 'How might we...?', 'Imagine if...'],
      responsePatterns: ['Building on that idea...', 'What if we took this further...', 'That sparks another thought...'],
      encouragementPhrases: ['Brilliant!', 'Love the creativity!', 'That\'s innovative!'],
      challengingPhrases: ['Let\'s push this even further', 'What\'s the wildest version of this?'],
      transitionWords: ['Additionally', 'Building on that', 'Expanding this idea']
    },
    color: '#8B5CF6',
    examples: [
      'What if we completely reimagined how users interact with this feature?',
      'Let\'s combine elements from completely different industries',
      'How might we make this experience magical and delightful?'
    ],
    tags: ['creative', 'innovative', 'ideation', 'inspiration']
  },
  {
    role: PersonaRole.DEVILS_ADVOCATE,
    name: 'Devil\'s Advocate',
    description: 'Challenges ideas and identifies potential problems',
    characteristics: DEFAULT_PERSONA_PROFILES[PersonaRole.DEVILS_ADVOCATE],
    promptStyle: {
      questionTypes: ['What could go wrong?', 'Have we considered...?', 'What if this fails?'],
      responsePatterns: ['However, we should consider...', 'The risk here is...', 'A potential issue might be...'],
      encouragementPhrases: ['Good point, but...', 'That\'s valid, however...'],
      challengingPhrases: ['What\'s the worst case scenario?', 'How would critics respond?'],
      transitionWords: ['However', 'On the other hand', 'Conversely']
    },
    color: '#EF4444',
    examples: [
      'What assumptions are we making that might not hold true?',
      'How would our competitors exploit this weakness?',
      'What regulatory or compliance issues might arise?'
    ],
    tags: ['critical', 'analytical', 'risk-assessment', 'validation']
  },
  {
    role: PersonaRole.PRACTICAL_ANALYST,
    name: 'Practical Analyst',
    description: 'Focuses on feasibility and implementation details',
    characteristics: DEFAULT_PERSONA_PROFILES[PersonaRole.PRACTICAL_ANALYST],
    promptStyle: {
      questionTypes: ['How would we implement...?', 'What resources do we need?', 'What\'s the timeline?'],
      responsePatterns: ['From an implementation standpoint...', 'The practical steps would be...', 'Resource-wise, we\'d need...'],
      encouragementPhrases: ['That\'s achievable', 'We can make this work', 'This is feasible'],
      challengingPhrases: ['How realistic is this timeline?', 'Do we have the resources?'],
      transitionWords: ['Practically speaking', 'In terms of implementation', 'From a resource perspective']
    },
    color: '#10B981',
    examples: [
      'Let\'s break this down into actionable milestones',
      'What\'s our MVP version of this idea?',
      'How does this fit within our current budget constraints?'
    ],
    tags: ['practical', 'implementation', 'feasibility', 'planning']
  },
  {
    role: PersonaRole.VISIONARY,
    name: 'Visionary',
    description: 'Thinks big picture and long-term impact',
    characteristics: DEFAULT_PERSONA_PROFILES[PersonaRole.VISIONARY],
    promptStyle: {
      questionTypes: ['Where could this lead in 5 years?', 'What\'s the bigger vision?', 'How does this transform...?'],
      responsePatterns: ['Imagine the future where...', 'This could revolutionize...', 'The long-term impact...'],
      encouragementPhrases: ['This has transformative potential', 'I see the bigger picture'],
      challengingPhrases: ['Think bigger', 'What\'s the moonshot version?'],
      transitionWords: ['Envisioning', 'Looking ahead', 'In the future']
    },
    color: '#F59E0B',
    examples: [
      'How might this reshape the entire industry?',
      'What would success look like in 10 years?',
      'How does this align with future trends?'
    ],
    tags: ['visionary', 'strategic', 'future-thinking', 'transformation']
  },
  {
    role: PersonaRole.CUSTOMER_ADVOCATE,
    name: 'Customer Advocate',
    description: 'Represents user needs and perspectives',
    characteristics: DEFAULT_PERSONA_PROFILES[PersonaRole.CUSTOMER_ADVOCATE],
    promptStyle: {
      questionTypes: ['How would users feel about...?', 'What do customers really need?', 'Is this user-friendly?'],
      responsePatterns: ['From the user\'s perspective...', 'Customers would expect...', 'This impacts users by...'],
      encouragementPhrases: ['Users will love this', 'This solves a real problem'],
      challengingPhrases: ['Is this what users actually want?', 'How does this improve their experience?'],
      transitionWords: ['From a user standpoint', 'Considering the customer', 'User-wise']
    },
    color: '#06B6D4',
    examples: [
      'How does this solve the customer\'s core pain point?',
      'What would make users choose this over alternatives?',
      'How can we make this more intuitive for users?'
    ],
    tags: ['user-focused', 'empathy', 'customer-centric', 'usability']
  }
];

// Create default personas from templates
const createDefaultPersonas = (): Record<string, BrainstormingPersona> => {
  const templates = createDefaultPersonaTemplates();
  const personas: Record<string, BrainstormingPersona> = {};
  
  templates.forEach((template, index) => {
    const id = `default_${template.role}_${Date.now()}_${index}`;
    personas[id] = {
      id,
      name: template.name,
      description: template.description,
      role: template.role,
      characteristics: template.characteristics,
      promptStyle: template.promptStyle,
      color: template.color,
      isActive: true,
      isCustom: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metrics: {
        totalContributions: 0,
        ideasGenerated: 0,
        questionsAsked: 0,
        challengesMade: 0,
        collaborations: 0,
        sessionParticipation: 0,
        averageRating: 0,
        lastUsed: new Date().toISOString()
      },
      tags: template.tags,
      examples: template.examples
    };
  });
  
  return personas;
};

export const usePersonaStore = create<PersonaStore>()(
  persist(
    (set, get) => ({
      // Initial state
      personas: createDefaultPersonas(),
      activePersonaId: null,
      templates: createDefaultPersonaTemplates(),
      interactions: [],
      analytics: {},
      settings: {
        autoSuggestPersonas: true,
        rotatePersonas: false,
        trackAnalytics: true,
        showPersonaInsights: true
      },

      // Persona CRUD operations
      createPersona: (template) => {
        const id = `persona_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const persona: BrainstormingPersona = {
          id,
          name: template.name || 'New Persona',
          description: template.description || '',
          role: template.role || PersonaRole.CREATIVE_THINKER,
          characteristics: template.characteristics || DEFAULT_PERSONA_PROFILES[PersonaRole.CREATIVE_THINKER],
          promptStyle: template.promptStyle || {
            questionTypes: [],
            responsePatterns: [],
            encouragementPhrases: [],
            challengingPhrases: [],
            transitionWords: []
          },
          color: template.color || '#8B5CF6',
          isActive: true,
          isCustom: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          metrics: {
            totalContributions: 0,
            ideasGenerated: 0,
            questionsAsked: 0,
            challengesMade: 0,
            collaborations: 0,
            sessionParticipation: 0,
            averageRating: 0,
            lastUsed: new Date().toISOString()
          },
          tags: template.tags || [],
          examples: template.examples || [],
          ...template
        };

        set(state => ({
          personas: { ...state.personas, [id]: persona }
        }));

        return id;
      },

      updatePersona: (id, updates) => {
        set(state => ({
          personas: {
            ...state.personas,
            [id]: {
              ...state.personas[id],
              ...updates,
              updatedAt: new Date().toISOString()
            }
          }
        }));
      },

      deletePersona: (id) => {
        set(state => {
          const { [id]: deleted, ...remaining } = state.personas;
          return {
            personas: remaining,
            activePersonaId: state.activePersonaId === id ? null : state.activePersonaId
          };
        });
      },

      duplicatePersona: (id) => {
        const original = get().personas[id];
        if (!original) return '';

        const newId = get().createPersona({
          ...original,
          name: `${original.name} (Copy)`,
          isCustom: true
        });

        return newId;
      },

      // Persona activation
      setActivePersona: (id) => {
        set({ activePersonaId: id });
        
        if (id) {
          // Update last used timestamp
          get().updatePersona(id, {
            metrics: {
              ...get().personas[id].metrics,
              lastUsed: new Date().toISOString()
            }
          });
        }
      },

      getActivePersona: () => {
        const { activePersonaId, personas } = get();
        return activePersonaId ? personas[activePersonaId] || null : null;
      },

      // Persona templates
      getPersonaTemplates: () => get().templates,

      createPersonaFromTemplate: (role, customizations = {}) => {
        const template = get().templates.find(t => t.role === role);
        if (!template) return '';

        return get().createPersona({
          ...template,
          ...customizations,
          isCustom: Object.keys(customizations).length > 0
        });
      },

      // Persona interactions
      recordInteraction: (interaction) => {
        const id = `interaction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const newInteraction: PersonaInteraction = {
          ...interaction,
          id,
          timestamp: new Date().toISOString()
        };

        set(state => ({
          interactions: [...state.interactions, newInteraction]
        }));

        // Update persona metrics
        if (interaction.personaId) {
          const persona = get().personas[interaction.personaId];
          if (persona) {
            get().updatePersona(interaction.personaId, {
              metrics: {
                ...persona.metrics,
                totalContributions: persona.metrics.totalContributions + 1,
                lastUsed: new Date().toISOString()
              }
            });
          }
        }
      },

      getPersonaInteractions: (personaId, sessionId) => {
        const { interactions } = get();
        return interactions.filter(interaction => 
          interaction.personaId === personaId &&
          (!sessionId || interaction.sessionId === sessionId)
        );
      },

      // Persona recommendations
      getPersonaRecommendations: (context) => {
        const { personas, settings } = get();
        if (!settings.autoSuggestPersonas) return [];

        const recommendations: PersonaRecommendation[] = [];
        
        // Simple recommendation logic based on session phase
        Object.values(personas).forEach(persona => {
          if (!persona.isActive) return;

          let confidence = 0;
          let reason = '';

          switch (context.sessionPhase) {
            case 'ideation':
              if (persona.role === PersonaRole.CREATIVE_THINKER || persona.role === PersonaRole.INNOVATOR) {
                confidence = 0.9;
                reason = 'Perfect for generating creative ideas';
              }
              break;
            case 'evaluation':
              if (persona.role === PersonaRole.DEVILS_ADVOCATE || persona.role === PersonaRole.PRACTICAL_ANALYST) {
                confidence = 0.8;
                reason = 'Great for evaluating and refining ideas';
              }
              break;
            case 'refinement':
              if (persona.role === PersonaRole.IMPLEMENTER || persona.role === PersonaRole.SYNTHESIZER) {
                confidence = 0.85;
                reason = 'Ideal for refining and organizing ideas';
              }
              break;
          }

          if (confidence > 0.5) {
            recommendations.push({
              personaId: persona.id,
              reason,
              confidence,
              context: `Session phase: ${context.sessionPhase}`,
              expectedBenefit: `This persona excels in ${context.sessionPhase} activities`
            });
          }
        });

        return recommendations.sort((a, b) => b.confidence - a.confidence);
      },

      // Analytics
      updatePersonaMetrics: (personaId, metrics) => {
        const persona = get().personas[personaId];
        if (!persona) return;

        get().updatePersona(personaId, {
          metrics: { ...persona.metrics, ...metrics }
        });
      },

      getPersonaAnalytics: (personaId, timeframe = 'all') => {
        // This would typically fetch from a backend
        // For now, return mock analytics
        return null;
      },

      // Persona AI responses (mock implementation)
      generatePersonaResponse: async (personaId, prompt, context) => {
        const persona = get().personas[personaId];
        if (!persona) {
          throw new Error('Persona not found');
        }

        // Mock response generation based on persona characteristics
        const response: PersonaResponse = {
          content: `As a ${persona.name}, I think we should consider...`,
          type: 'idea',
          confidence: 0.8,
          reasoning: `Based on my ${persona.role} perspective`,
          suggestedFollowUps: ['What do you think about this approach?'],
          relatedIdeas: []
        };

        return response;
      },

      // Settings
      updateSettings: (newSettings) => {
        set(state => ({
          settings: { ...state.settings, ...newSettings }
        }));
      },

      // Utility functions
      searchPersonas: (query) => {
        const { personas } = get();
        const lowercaseQuery = query.toLowerCase();
        
        return Object.values(personas).filter(persona =>
          persona.name.toLowerCase().includes(lowercaseQuery) ||
          persona.description.toLowerCase().includes(lowercaseQuery) ||
          persona.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
        );
      },

      getPersonasByRole: (role) => {
        const { personas } = get();
        return Object.values(personas).filter(persona => persona.role === role);
      },

      getTopPerformingPersonas: (limit = 5) => {
        const { personas } = get();
        return Object.values(personas)
          .sort((a, b) => b.metrics.averageRating - a.metrics.averageRating)
          .slice(0, limit);
      }
    }),
    {
      name: 'persona-store',
      partialize: (state) => ({
        personas: state.personas,
        activePersonaId: state.activePersonaId,
        settings: state.settings
      })
    }
  )
);
