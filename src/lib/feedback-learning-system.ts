import { invoke } from '@tauri-apps/api/core';
import { Idea, IdeaCluster, BrainstormSession } from '@/types/brainstorm';

export interface UserInteraction {
  id: string;
  sessionId: string;
  type: 'idea_created' | 'idea_edited' | 'idea_deleted' | 'idea_clustered' | 'idea_tagged' | 'idea_prioritized' | 'session_exported' | 'view_switched' | 'search_performed';
  timestamp: string;
  userId?: string;
  context: {
    ideaId?: string;
    clusterId?: string;
    previousValue?: any;
    newValue?: any;
    metadata?: Record<string, any>;
  };
  outcome?: 'positive' | 'negative' | 'neutral';
  feedback?: UserFeedback;
}

export interface UserFeedback {
  rating: number; // 1-5 scale
  comment?: string;
  category: 'idea_quality' | 'clustering' | 'ui_experience' | 'ai_suggestions' | 'export_quality';
  timestamp: string;
}

export interface LearningPattern {
  id: string;
  type: 'user_preference' | 'idea_pattern' | 'clustering_pattern' | 'export_preference';
  pattern: Record<string, any>;
  confidence: number;
  frequency: number;
  lastSeen: string;
  userId?: string;
}

export interface AIRecommendation {
  id: string;
  type: 'idea_suggestion' | 'cluster_suggestion' | 'tag_suggestion' | 'priority_suggestion' | 'export_suggestion';
  content: string;
  confidence: number;
  reasoning: string;
  basedOn: string[];
  timestamp: string;
  accepted?: boolean;
}

export interface AdaptationRule {
  id: string;
  condition: string;
  action: string;
  priority: number;
  enabled: boolean;
  successRate: number;
  timesApplied: number;
}

export class FeedbackLearningSystem {
  private interactions: UserInteraction[] = [];
  private patterns: Map<string, LearningPattern> = new Map();
  private adaptationRules: AdaptationRule[] = [];
  private userId: string;

  constructor(userId: string = 'default') {
    this.userId = userId;
    this.loadLearningData();
  }

  // Track user interaction
  async trackInteraction(interaction: Omit<UserInteraction, 'id' | 'timestamp' | 'userId'>): Promise<void> {
    const fullInteraction: UserInteraction = {
      ...interaction,
      id: `interaction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      userId: this.userId,
    };

    this.interactions.push(fullInteraction);
    
    // Save to backend
    await invoke('save_user_interaction', { interaction: fullInteraction });
    
    // Analyze for patterns
    await this.analyzeInteractionPatterns();
  }

  // Provide explicit feedback
  async provideFeedback(
    interactionId: string,
    rating: number,
    comment?: string,
    category: UserFeedback['category'] = 'ui_experience'
  ): Promise<void> {
    const feedback: UserFeedback = {
      rating,
      comment,
      category,
      timestamp: new Date().toISOString(),
    };

    // Update the interaction with feedback
    const interaction = this.interactions.find(i => i.id === interactionId);
    if (interaction) {
      interaction.feedback = feedback;
      interaction.outcome = rating >= 4 ? 'positive' : rating <= 2 ? 'negative' : 'neutral';
    }

    await invoke('save_user_feedback', { interactionId, feedback });
    
    // Update learning patterns based on feedback
    await this.updatePatternsFromFeedback(feedback, interaction);
  }

  // Generate AI recommendations based on learned patterns
  async generateRecommendations(
    sessionId: string,
    context: {
      currentIdeas: Idea[];
      currentClusters: IdeaCluster[];
      recentInteractions: UserInteraction[];
    }
  ): Promise<AIRecommendation[]> {
    const recommendations: AIRecommendation[] = [];

    // Analyze current session context
    const sessionAnalysis = await this.analyzeSessionContext(sessionId, context);

    // Generate idea suggestions
    const ideaSuggestions = await this.generateIdeaSuggestions(sessionAnalysis);
    recommendations.push(...ideaSuggestions);

    // Generate clustering suggestions
    const clusterSuggestions = await this.generateClusterSuggestions(context.currentIdeas);
    recommendations.push(...clusterSuggestions);

    // Generate tag suggestions
    const tagSuggestions = await this.generateTagSuggestions(context.currentIdeas);
    recommendations.push(...tagSuggestions);

    // Generate priority suggestions
    const prioritySuggestions = await this.generatePrioritySuggestions(context.currentIdeas);
    recommendations.push(...prioritySuggestions);

    // Sort by confidence and return top recommendations
    return recommendations
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 10);
  }

  // Adapt UI/UX based on learned preferences
  async getUIAdaptations(): Promise<{
    preferredView: string;
    suggestedFeatures: string[];
    hiddenFeatures: string[];
    customizations: Record<string, any>;
  }> {
    const userPreferences = await this.getUserPreferences();
    
    return {
      preferredView: userPreferences.preferredView || 'chat',
      suggestedFeatures: userPreferences.frequentlyUsedFeatures || [],
      hiddenFeatures: userPreferences.rarelyUsedFeatures || [],
      customizations: {
        autoSaveInterval: userPreferences.autoSaveInterval || 30000,
        defaultExportFormat: userPreferences.defaultExportFormat || 'markdown',
        defaultSearchProvider: userPreferences.defaultSearchProvider || 'duckduckgo',
        enableVoiceCommands: userPreferences.enableVoiceCommands !== false,
        enableRealTimeCollaboration: userPreferences.enableRealTimeCollaboration !== false,
      },
    };
  }

  // Learn from session outcomes
  async learnFromSessionOutcome(
    sessionId: string,
    outcome: {
      ideasGenerated: number;
      ideasImplemented: number;
      clustersCreated: number;
      exportCount: number;
      sessionDuration: number;
      userSatisfaction: number; // 1-5 scale
    }
  ): Promise<void> {
    const sessionData = {
      sessionId,
      outcome,
      timestamp: new Date().toISOString(),
      userId: this.userId,
    };

    await invoke('save_session_outcome', { sessionData });
    
    // Update patterns based on successful sessions
    if (outcome.userSatisfaction >= 4) {
      await this.reinforceSuccessfulPatterns(sessionId);
    }
  }

  // Private methods

  private async loadLearningData(): Promise<void> {
    try {
      // Load interactions
      const interactions = await invoke<UserInteraction[]>('get_user_interactions', {
        userId: this.userId,
      });
      this.interactions = interactions;

      // Load patterns
      const patterns = await invoke<LearningPattern[]>('get_learning_patterns', {
        userId: this.userId,
      });
      patterns.forEach(pattern => {
        this.patterns.set(pattern.id, pattern);
      });

      // Load adaptation rules
      this.adaptationRules = await invoke<AdaptationRule[]>('get_adaptation_rules');
    } catch (error) {
      console.error('Failed to load learning data:', error);
    }
  }

  private async analyzeInteractionPatterns(): Promise<void> {
    // Analyze recent interactions for patterns
    const recentInteractions = this.interactions.slice(-100);
    
    // Find frequent action sequences
    const sequences = this.findActionSequences(recentInteractions);
    
    // Find clustering preferences
    const clusteringPatterns = this.findClusteringPatterns(recentInteractions);
    
    // Find timing patterns
    const timingPatterns = this.findTimingPatterns(recentInteractions);

    // Update patterns
    await this.updatePatterns([...sequences, ...clusteringPatterns, ...timingPatterns]);
  }

  private findActionSequences(interactions: UserInteraction[]): LearningPattern[] {
    const sequences: Map<string, { count: number; lastSeen: string }> = new Map();
    
    for (let i = 0; i < interactions.length - 1; i++) {
      const current = interactions[i];
      const next = interactions[i + 1];
      
      const sequence = `${current.type}->${next.type}`;
      const existing = sequences.get(sequence) || { count: 0, lastSeen: '' };
      sequences.set(sequence, {
        count: existing.count + 1,
        lastSeen: next.timestamp,
      });
    }

    return Array.from(sequences.entries()).map(([sequence, data]) => ({
      id: `sequence_${sequence}`,
      type: 'user_preference',
      pattern: { sequence, actionPair: sequence.split('->') },
      confidence: Math.min(data.count / 10, 1.0),
      frequency: data.count,
      lastSeen: data.lastSeen,
      userId: this.userId,
    }));
  }

  private findClusteringPatterns(interactions: UserInteraction[]): LearningPattern[] {
    const clusteringActions = interactions.filter(i => i.type === 'idea_clustered');
    
    // Analyze clustering preferences (manual vs automatic, cluster size preferences, etc.)
    const patterns: LearningPattern[] = [];
    
    if (clusteringActions.length > 5) {
      const manualClusters = clusteringActions.filter(a => 
        a.context.metadata?.method === 'manual'
      ).length;
      
      const automaticClusters = clusteringActions.length - manualClusters;
      
      patterns.push({
        id: 'clustering_preference',
        type: 'clustering_pattern',
        pattern: {
          prefersManual: manualClusters > automaticClusters,
          manualRatio: manualClusters / clusteringActions.length,
        },
        confidence: Math.min(clusteringActions.length / 20, 1.0),
        frequency: clusteringActions.length,
        lastSeen: clusteringActions[clusteringActions.length - 1].timestamp,
        userId: this.userId,
      });
    }

    return patterns;
  }

  private findTimingPatterns(interactions: UserInteraction[]): LearningPattern[] {
    // Find peak activity hours, session duration preferences, etc.
    const hourCounts = new Array(24).fill(0);
    
    interactions.forEach(interaction => {
      const hour = new Date(interaction.timestamp).getHours();
      hourCounts[hour]++;
    });

    const peakHour = hourCounts.indexOf(Math.max(...hourCounts));
    
    return [{
      id: 'timing_pattern',
      type: 'user_preference',
      pattern: {
        peakHour,
        hourDistribution: hourCounts,
      },
      confidence: 0.7,
      frequency: interactions.length,
      lastSeen: interactions[interactions.length - 1]?.timestamp || new Date().toISOString(),
      userId: this.userId,
    }];
  }

  private async updatePatterns(newPatterns: LearningPattern[]): Promise<void> {
    for (const pattern of newPatterns) {
      this.patterns.set(pattern.id, pattern);
      await invoke('save_learning_pattern', { pattern });
    }
  }

  private async updatePatternsFromFeedback(
    feedback: UserFeedback,
    interaction?: UserInteraction
  ): Promise<void> {
    if (!interaction) return;

    // Create or update patterns based on feedback
    const patternId = `feedback_${feedback.category}_${interaction.type}`;
    const existing = this.patterns.get(patternId);

    const pattern: LearningPattern = {
      id: patternId,
      type: 'user_preference',
      pattern: {
        interactionType: interaction.type,
        feedbackCategory: feedback.category,
        averageRating: existing 
          ? (existing.pattern.averageRating * existing.frequency + feedback.rating) / (existing.frequency + 1)
          : feedback.rating,
        comments: [...(existing?.pattern.comments || []), feedback.comment].filter(Boolean),
      },
      confidence: Math.min((existing?.frequency || 0) + 1 / 10, 1.0),
      frequency: (existing?.frequency || 0) + 1,
      lastSeen: feedback.timestamp,
      userId: this.userId,
    };

    this.patterns.set(patternId, pattern);
    await invoke('save_learning_pattern', { pattern });
  }

  private async analyzeSessionContext(
    sessionId: string,
    context: any
  ): Promise<any> {
    // Analyze current session for patterns and opportunities
    return {
      ideaCount: context.currentIdeas.length,
      clusterCount: context.currentClusters.length,
      dominantTopics: this.extractTopics(context.currentIdeas),
      recentActivity: context.recentInteractions.slice(-10),
    };
  }

  private async generateIdeaSuggestions(analysis: any): Promise<AIRecommendation[]> {
    const suggestions: AIRecommendation[] = [];
    
    // Based on learned patterns, suggest new ideas
    if (analysis.ideaCount < 5) {
      suggestions.push({
        id: `suggestion_${Date.now()}`,
        type: 'idea_suggestion',
        content: 'Consider exploring alternative approaches to your main concept',
        confidence: 0.7,
        reasoning: 'Sessions with more diverse ideas tend to have better outcomes',
        basedOn: ['session_outcome_patterns', 'idea_diversity_analysis'],
        timestamp: new Date().toISOString(),
      });
    }

    return suggestions;
  }

  private async generateClusterSuggestions(ideas: Idea[]): Promise<AIRecommendation[]> {
    // Suggest clustering based on learned preferences
    if (ideas.length >= 8 && ideas.filter(i => i.cluster).length < ideas.length * 0.5) {
      return [{
        id: `cluster_suggestion_${Date.now()}`,
        type: 'cluster_suggestion',
        content: 'Group similar ideas together to better organize your thoughts',
        confidence: 0.8,
        reasoning: 'You typically cluster ideas when you have more than 7 ideas',
        basedOn: ['clustering_patterns'],
        timestamp: new Date().toISOString(),
      }];
    }
    return [];
  }

  private async generateTagSuggestions(ideas: Idea[]): Promise<AIRecommendation[]> {
    // Suggest tags based on content analysis and user patterns
    const commonWords = this.extractCommonWords(ideas);
    
    return commonWords.slice(0, 3).map(word => ({
      id: `tag_suggestion_${word}_${Date.now()}`,
      type: 'tag_suggestion',
      content: `Consider adding "${word}" as a tag`,
      confidence: 0.6,
      reasoning: `"${word}" appears frequently in your ideas`,
      basedOn: ['content_analysis'],
      timestamp: new Date().toISOString(),
    }));
  }

  private async generatePrioritySuggestions(ideas: Idea[]): Promise<AIRecommendation[]> {
    // Suggest priorities based on content and user patterns
    const unprioritizedIdeas = ideas.filter(i => !i.priority);
    
    if (unprioritizedIdeas.length > 3) {
      return [{
        id: `priority_suggestion_${Date.now()}`,
        type: 'priority_suggestion',
        content: 'Consider setting priorities for your ideas to focus on the most important ones',
        confidence: 0.7,
        reasoning: 'Prioritized sessions typically have better implementation rates',
        basedOn: ['session_outcome_patterns'],
        timestamp: new Date().toISOString(),
      }];
    }
    return [];
  }

  private async getUserPreferences(): Promise<Record<string, any>> {
    // Extract user preferences from patterns
    const preferences: Record<string, any> = {};
    
    this.patterns.forEach(pattern => {
      if (pattern.type === 'user_preference') {
        if (pattern.pattern.sequence) {
          // Action sequence preferences
          if (!preferences.commonSequences) preferences.commonSequences = [];
          preferences.commonSequences.push(pattern.pattern.sequence);
        }
        
        if (pattern.pattern.peakHour !== undefined) {
          preferences.peakActivityHour = pattern.pattern.peakHour;
        }
      }
    });

    return preferences;
  }

  private async reinforceSuccessfulPatterns(sessionId: string): Promise<void> {
    // Increase confidence of patterns used in successful sessions
    const sessionInteractions = this.interactions.filter(i => i.sessionId === sessionId);
    
    // Identify patterns that contributed to success
    sessionInteractions.forEach(interaction => {
      const relatedPatterns = Array.from(this.patterns.values()).filter(p => 
        p.pattern.interactionType === interaction.type
      );
      
      relatedPatterns.forEach(pattern => {
        pattern.confidence = Math.min(pattern.confidence * 1.1, 1.0);
        invoke('save_learning_pattern', { pattern });
      });
    });
  }

  private extractTopics(ideas: Idea[]): string[] {
    // Simple topic extraction based on common words
    const allText = ideas.map(i => i.content).join(' ').toLowerCase();
    const words = allText.split(/\W+/).filter(w => w.length > 3);
    const wordCounts = words.reduce((acc, word) => {
      acc[word] = (acc[word] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return Object.entries(wordCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
  }

  private extractCommonWords(ideas: Idea[]): string[] {
    const allText = ideas.map(i => i.content).join(' ').toLowerCase();
    const words = allText.split(/\W+/).filter(w => w.length > 4);
    const wordCounts = words.reduce((acc, word) => {
      acc[word] = (acc[word] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return Object.entries(wordCounts)
      .filter(([,count]) => count > 1)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }
}

// Export singleton instance
export const feedbackLearningSystem = new FeedbackLearningSystem();

// Helper functions for easy integration
export async function trackIdeaCreation(sessionId: string, ideaId: string, content: string): Promise<void> {
  await feedbackLearningSystem.trackInteraction({
    sessionId,
    type: 'idea_created',
    context: { ideaId, newValue: content },
  });
}

export async function trackIdeaClustering(sessionId: string, ideaId: string, clusterId: string, method: 'manual' | 'automatic'): Promise<void> {
  await feedbackLearningSystem.trackInteraction({
    sessionId,
    type: 'idea_clustered',
    context: { ideaId, clusterId, metadata: { method } },
  });
}

export async function trackViewSwitch(sessionId: string, fromView: string, toView: string): Promise<void> {
  await feedbackLearningSystem.trackInteraction({
    sessionId,
    type: 'view_switched',
    context: { previousValue: fromView, newValue: toView },
  });
}

export async function getSmartRecommendations(sessionId: string, ideas: Idea[], clusters: IdeaCluster[]): Promise<AIRecommendation[]> {
  const recentInteractions = await invoke<UserInteraction[]>('get_recent_interactions', {
    sessionId,
    limit: 20,
  });
  
  return feedbackLearningSystem.generateRecommendations(sessionId, {
    currentIdeas: ideas,
    currentClusters: clusters,
    recentInteractions,
  });
}