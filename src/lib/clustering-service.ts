/**
 * Enhanced Clustering Service with Web Worker Support
 * 
 * Provides high-performance clustering using web workers
 */

import { Idea, IdeaCluster } from '@/types/brainstorm';
import { workerManager } from './worker-manager';
import { api } from '@/lib/api';

interface ClusteringOptions {
  minClusterSize?: number;
  maxClusters?: number;
  similarityThreshold?: number;
  algorithm?: 'kmeans' | 'hierarchical' | 'density' | 'ai';
  useWebWorker?: boolean;
  preserveExisting?: boolean;
}

export interface ClusteringSuggestion {
  clusterId: string;
  name: string;
  theme: string;
  ideaIds: string[];
  confidence: number;
  rationale: string;
}

interface ClusterSuggestion extends Omit<IdeaCluster, 'id' | 'createdAt' | 'updatedAt'> {
  name: string;
  description: string;
  ideaIds: string[];
  confidence: number;
  tags: string[];
  color: string;
}

class ClusteringService {
  private useWebWorker = true;
  private colorPalette = [
    '#8B5CF6', // Violet
    '#3B82F6', // Blue
    '#10B981', // Emerald
    '#F59E0B', // Amber
    '#EF4444', // Red
    '#EC4899', // Pink
    '#14B8A6', // Teal
    '#6366F1', // Indigo
    '#84CC16', // Lime
    '#F97316', // Orange
  ];

  /**
   * Enable or disable web worker usage
   */
  setWebWorkerEnabled(enabled: boolean): void {
    this.useWebWorker = enabled;
  }

  /**
   * Suggest clusters based on idea similarity
   */
  async suggestClusters(
    ideas: Idea[],
    options: ClusteringOptions = {}
  ): Promise<ClusterSuggestion[]> {
    const {
      minClusterSize = 2,
      maxClusters = 5,
      similarityThreshold = 0.6,
      algorithm = 'kmeans',
      useWebWorker = this.useWebWorker
    } = options;

    if (ideas.length < minClusterSize) {
      return [];
    }

    // Use web worker if available and enabled
    if (useWebWorker && typeof Worker !== 'undefined') {
      try {
        const result = await workerManager.clusterIdeas(ideas, {
          algorithm,
          maxClusters,
          minClusterSize,
          similarityThreshold
        });

        // Convert worker results to ClusterSuggestion format
        return result.clusters.map((cluster, index) => ({
          name: cluster.name,
          description: `Cluster with ${cluster.ideaIds.length} related ideas`,
          sessionId: ideas[0].sessionId,
          ideaIds: cluster.ideaIds,
          color: this.colorPalette[index % this.colorPalette.length],
          tags: this.extractClusterTags(ideas.filter(i => cluster.ideaIds.includes(i.id))),
          confidence: cluster.quality
        }));
      } catch (error) {
        console.warn('Web worker clustering failed, falling back to main thread:', error);
        // Fall through to main thread implementation
      }
    }

    // Fallback to main thread clustering
    return this.clusterOnMainThread(ideas, {
      minClusterSize,
      maxClusters,
      similarityThreshold
    });
  }

  /**
   * Re-cluster with different parameters
   */
  async recluster(
    ideas: Idea[],
    currentClusters: number,
    options?: ClusteringOptions
  ): Promise<ClusterSuggestion[]> {
    // Try with more clusters than current
    const newMaxClusters = Math.min(currentClusters + 2, ideas.length);
    
    return this.suggestClusters(ideas, {
      ...options,
      maxClusters: newMaxClusters
    });
  }

  /**
   * Optimize cluster count automatically
   */
  async optimizeClusters(ideas: Idea[]): Promise<{
    clusters: ClusterSuggestion[];
    optimalK: number;
    analysis: Array<{ k: number; quality: number }>;
  }> {
    if (ideas.length < 2) {
      return {
        clusters: [],
        optimalK: 0,
        analysis: []
      };
    }

    if (this.useWebWorker && typeof Worker !== 'undefined') {
      try {
        const result = await workerManager.optimizeClusters(ideas);
        
        const clusters = result.clusters.map((cluster, index) => ({
          name: cluster.name,
          description: `Optimized cluster with ${cluster.ideaIds.length} ideas`,
          sessionId: ideas[0].sessionId,
          ideaIds: cluster.ideaIds,
          color: this.colorPalette[index % this.colorPalette.length],
          tags: this.extractClusterTags(ideas.filter(i => cluster.ideaIds.includes(i.id))),
          confidence: cluster.quality
        }));

        return {
          clusters,
          optimalK: result.optimal.k,
          analysis: result.optimal.results
        };
      } catch (error) {
        console.warn('Web worker optimization failed:', error);
      }
    }

    // Fallback: use different k values and pick best
    const results: Array<{ k: number; quality: number }> = [];
    let bestClusters: ClusterSuggestion[] = [];
    let bestQuality = -Infinity;
    let optimalK = 3;

    for (let k = 2; k <= Math.min(10, ideas.length); k++) {
      const clusters = await this.suggestClusters(ideas, {
        maxClusters: k,
        useWebWorker: false
      });

      const avgQuality = clusters.reduce((sum, c) => sum + c.confidence, 0) / clusters.length;
      results.push({ k, quality: avgQuality });

      if (avgQuality > bestQuality) {
        bestQuality = avgQuality;
        bestClusters = clusters;
        optimalK = k;
      }
    }

    return {
      clusters: bestClusters,
      optimalK,
      analysis: results
    };
  }

  /**
   * Main thread clustering implementation (fallback)
   */
  private async clusterOnMainThread(
    ideas: Idea[],
    options: {
      minClusterSize: number;
      maxClusters: number;
      similarityThreshold: number;
    }
  ): Promise<ClusterSuggestion[]> {
    const { minClusterSize, maxClusters } = options;

    // Simple clustering based on tags and content similarity
    const clusters: ClusterSuggestion[] = [];
    const processedIdeas = new Set<string>();

    // Group by common tags first
    const tagGroups = this.groupByTags(ideas);
    
    for (const [tag, tagIdeas] of tagGroups.entries()) {
      if (tagIdeas.length >= minClusterSize) {
        const unprocessed = tagIdeas.filter(idea => !processedIdeas.has(idea.id));
        
        if (unprocessed.length >= minClusterSize) {
          clusters.push({
            name: this.formatClusterName(tag),
            description: `Ideas related to ${tag}`,
            sessionId: unprocessed[0].sessionId,
            ideaIds: unprocessed.map(i => i.id),
            color: this.colorPalette[clusters.length % this.colorPalette.length],
            tags: [tag],
            confidence: 0.8
          });

          unprocessed.forEach(idea => processedIdeas.add(idea.id));
        }
      }
    }

    // Group remaining ideas by priority
    const remainingIdeas = ideas.filter(idea => !processedIdeas.has(idea.id));
    const priorityGroups = this.groupByPriority(remainingIdeas);

    for (const [priority, priorityIdeas] of priorityGroups.entries()) {
      if (priorityIdeas.length >= minClusterSize && clusters.length < maxClusters) {
        clusters.push({
          name: `${this.formatPriority(priority)} Priority Ideas`,
          description: `All ${priority} priority ideas`,
          sessionId: priorityIdeas[0].sessionId,
          ideaIds: priorityIdeas.map(i => i.id),
          color: this.getPriorityColor(priority),
          tags: [],
          confidence: 0.6
        });

        priorityIdeas.forEach(idea => processedIdeas.add(idea.id));
      }
    }

    return clusters.slice(0, maxClusters);
  }

  /**
   * Extract common tags from a group of ideas
   */
  private extractClusterTags(ideas: Idea[]): string[] {
    const tagFrequency = new Map<string, number>();
    
    for (const idea of ideas) {
      for (const tag of idea.tags) {
        tagFrequency.set(tag, (tagFrequency.get(tag) || 0) + 1);
      }
    }
    
    // Return tags that appear in at least 30% of ideas
    const threshold = ideas.length * 0.3;
    return Array.from(tagFrequency.entries())
      .filter(([_, count]) => count >= threshold)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([tag]) => tag);
  }

  /**
   * Group ideas by common tags
   */
  private groupByTags(ideas: Idea[]): Map<string, Idea[]> {
    const groups = new Map<string, Idea[]>();
    
    for (const idea of ideas) {
      for (const tag of idea.tags) {
        const group = groups.get(tag) || [];
        group.push(idea);
        groups.set(tag, group);
      }
    }
    
    return groups;
  }

  /**
   * Group ideas by priority
   */
  private groupByPriority(ideas: Idea[]): Map<string, Idea[]> {
    const groups = new Map<string, Idea[]>();
    
    for (const idea of ideas) {
      const priority = idea.priority || 'medium';
      const group = groups.get(priority) || [];
      group.push(idea);
      groups.set(priority, group);
    }
    
    return groups;
  }

  /**
   * Format tag name for cluster name
   */
  private formatClusterName(tag: string): string {
    return tag
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Format priority name
   */
  private formatPriority(priority: string): string {
    return priority.charAt(0).toUpperCase() + priority.slice(1);
  }

  /**
   * Get color for priority-based clusters
   */
  private getPriorityColor(priority: string): string {
    const colors = {
      critical: '#DC2626', // Red
      high: '#F59E0B',     // Amber
      medium: '#3B82F6',   // Blue
      low: '#10B981'       // Green
    };
    return colors[priority as keyof typeof colors] || '#6B7280';
  }

  /**
   * Calculate similarity between two ideas
   */
  calculateSimilarity(idea1: Idea, idea2: Idea): number {
    // Tag similarity
    const tags1 = new Set(idea1.tags);
    const tags2 = new Set(idea2.tags);
    const sharedTags = [...tags1].filter(tag => tags2.has(tag)).length;
    const totalTags = tags1.size + tags2.size - sharedTags;
    const tagSimilarity = totalTags > 0 ? sharedTags / totalTags : 0;
    
    // Priority similarity
    const prioritySimilarity = idea1.priority === idea2.priority ? 0.5 : 0;
    
    // Status similarity
    const statusSimilarity = idea1.status === idea2.status ? 0.3 : 0;
    
    // Combined similarity
    return tagSimilarity * 0.5 + prioritySimilarity * 0.3 + statusSimilarity * 0.2;
  }

  /**
   * Get clustering status
   */
  getStatus(): {
    webWorkerEnabled: boolean;
    workerStatus: ReturnType<typeof workerManager.getStatus>;
  } {
    return {
      webWorkerEnabled: this.useWebWorker,
      workerStatus: workerManager.getStatus()
    };
  }

  /**
   * Suggest clusters using AI analysis
   */
  async suggestClustersWithAI(
    ideas: Idea[],
    options: Partial<ClusteringOptions> = {}
  ): Promise<ClusteringSuggestion[]> {
    const opts = {
      minClusterSize: 2,
      similarityThreshold: 0.7,
      maxClusters: 10,
      ...options
    };
    
    if (ideas.length < opts.minClusterSize) {
      return [];
    }

    try {
      // Prepare ideas for analysis
      const ideaData = ideas.map(idea => ({
        id: idea.id,
        content: idea.content,
        tags: idea.tags,
        status: idea.status,
        priority: idea.priority,
      }));

      // Call AI to analyze and cluster
      const prompt = `Analyze these brainstorming ideas and suggest logical groupings/clusters:

Ideas:
${ideaData.map((idea, i) => `${i + 1}. [${idea.id}] ${idea.content} (tags: ${idea.tags.join(', ')})`).join('\n')}

Requirements:
- Minimum cluster size: ${opts.minClusterSize} ideas
- Maximum clusters: ${opts.maxClusters || 'unlimited'}
- Group by semantic similarity, theme, or logical relationship
- Each idea can only belong to one cluster
- Provide a clear theme/name for each cluster

Return clusters as JSON array with format:
{
  "clusters": [
    {
      "name": "cluster name",
      "theme": "detailed theme description",
      "ideaIds": ["id1", "id2"],
      "confidence": 0.85,
      "rationale": "why these ideas belong together"
    }
  ]
}`;

      const response = await api.sendMessage('claude-code', prompt);
      
      // Parse AI response
      const result = this.parseClusteringResponse(response);
      
      // Validate and filter clusters
      return result.clusters
        .filter(cluster => 
          cluster.ideaIds.length >= opts.minClusterSize &&
          cluster.confidence >= opts.similarityThreshold
        )
        .map(cluster => ({
          clusterId: this.generateClusterId(),
          ...cluster,
        }));
      
    } catch (error) {
      console.error('Failed to suggest clusters with AI:', error);
      // Fallback to algorithmic clustering
      return this.suggestClusters(ideas, { ...options, algorithm: 'kmeans' }) as any;
    }
  }

  /**
   * Parse AI clustering response
   */
  private parseClusteringResponse(response: string): { clusters: Omit<ClusteringSuggestion, 'clusterId'>[] } {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const parsed = JSON.parse(jsonMatch[0]);
      return parsed;
    } catch (error) {
      console.error('Failed to parse clustering response:', error);
      return { clusters: [] };
    }
  }

  /**
   * Generate cluster ID
   */
  private generateClusterId(): string {
    return `cluster_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Re-cluster ideas with AI or algorithmic approach
   */
  async reclusterIdeas(
    ideas: Idea[],
    existingClusters: IdeaCluster[],
    options: Partial<ClusteringOptions> = {}
  ): Promise<IdeaCluster[]> {
    const opts = { preserveExisting: true, ...options };
    
    if (opts.preserveExisting) {
      // Only cluster unassigned ideas
      const assignedIds = new Set(
        existingClusters.flatMap(c => c.ideaIds)
      );
      const unassignedIdeas = ideas.filter(i => !assignedIds.has(i.id));
      
      if (unassignedIdeas.length >= (opts.minClusterSize || 2)) {
        const newClusters = opts.algorithm === 'ai' 
          ? await this.suggestClustersWithAI(unassignedIdeas, opts)
          : await this.suggestClusters(unassignedIdeas, opts);
          
        return [...existingClusters, ...newClusters.map((suggestion: any, i) => ({
          id: suggestion.clusterId || suggestion.id || this.generateClusterId(),
          name: suggestion.name,
          description: suggestion.theme || suggestion.description || '',
          sessionId: ideas[0]?.sessionId || '',
          ideaIds: suggestion.ideaIds,
          color: this.colorPalette[existingClusters.length + i % this.colorPalette.length],
          tags: suggestion.tags || [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }))];
      }
      
      return existingClusters;
    } else {
      // Complete re-clustering
      const suggestions = opts.algorithm === 'ai'
        ? await this.suggestClustersWithAI(ideas, opts)
        : await this.suggestClusters(ideas, opts);
        
      return suggestions.map((suggestion: any, i) => ({
        id: suggestion.clusterId || suggestion.id || this.generateClusterId(),
        name: suggestion.name,
        description: suggestion.theme || suggestion.description || '',
        sessionId: ideas[0]?.sessionId || '',
        ideaIds: suggestion.ideaIds,
        color: this.colorPalette[i % this.colorPalette.length],
        tags: suggestion.tags || [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }));
    }
  }
}

// Export singleton instance
export const clusteringService = new ClusteringService();

// Also export class for testing
export default ClusteringService;