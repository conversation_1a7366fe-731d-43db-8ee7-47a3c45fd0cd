// Secure toast notification utility
export function showSecureToast(
  message: string,
  type: 'success' | 'error' | 'warning' | 'info' = 'info',
  duration: number = 5000
): void {
  const toast = document.createElement('div');
  
  // Build classes based on type
  const typeClasses = {
    success: 'bg-success text-success-foreground',
    error: 'bg-destructive text-destructive-foreground',
    warning: 'bg-warning text-warning-foreground',
    info: 'bg-info text-info-foreground'
  };
  
  toast.className = `fixed bottom-4 right-4 ${typeClasses[type]} px-4 py-2 rounded-lg shadow-lg z-50 animate-in slide-in-from-bottom-2`;
  
  // Create content elements safely without innerHTML
  const contentDiv = document.createElement('div');
  contentDiv.className = 'flex items-center gap-2';
  
  // Create icon
  const icon = document.createElement('svg');
  icon.className = 'h-4 w-4';
  icon.setAttribute('fill', 'none');
  icon.setAttribute('stroke', 'currentColor');
  icon.setAttribute('viewBox', '0 0 24 24');
  
  // Set icon path based on type
  const iconPath = document.createElement('path');
  iconPath.setAttribute('stroke-linecap', 'round');
  iconPath.setAttribute('stroke-linejoin', 'round');
  iconPath.setAttribute('stroke-width', '2');
  
  switch (type) {
    case 'success':
      iconPath.setAttribute('d', 'M5 13l4 4L19 7');
      break;
    case 'error':
      iconPath.setAttribute('d', 'M6 18L18 6M6 6l12 12');
      break;
    case 'warning':
      iconPath.setAttribute('d', 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z');
      break;
    case 'info':
      iconPath.setAttribute('d', 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z');
      break;
  }
  
  icon.appendChild(iconPath);
  
  // Create text content safely
  const textSpan = document.createElement('span');
  textSpan.textContent = message; // textContent is safe from XSS
  
  // Assemble the toast
  contentDiv.appendChild(icon);
  contentDiv.appendChild(textSpan);
  toast.appendChild(contentDiv);
  
  // Add to document
  document.body.appendChild(toast);
  
  // Remove after duration
  setTimeout(() => {
    toast.classList.add('animate-out', 'slide-out-to-bottom-2');
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 300);
  }, duration);
}

// Export a function to escape HTML for safe display
export function escapeHtml(unsafe: string): string {
  return unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}