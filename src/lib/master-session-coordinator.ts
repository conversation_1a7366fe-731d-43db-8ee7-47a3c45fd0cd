/**
 * Master Session Coordinator
 * 
 * Unified session management system that coordinates all session types:
 * - Claude <PERSON> sessions
 * - Brainstorming sessions  
 * - Agent Orchestra sessions
 * 
 * Provides session lifecycle management, relationship tracking, 
 * and cross-session communication.
 */

import { sessionManager } from './session-manager';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { universalPersistence } from './universal-persistence';
import type { SessionData } from '@/types/session';
import type { BrainstormSession } from '@/types/brainstorm';

// Session Types
export type SessionType = 'claude' | 'brainstorm' | 'orchestra';

export interface BaseSessionInfo {
  id: string;
  type: SessionType;
  title: string;
  projectPath?: string;
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'idle' | 'paused' | 'terminated';
  parentSessionId?: string; // For session relationships
  childSessionIds: string[]; // Sessions spawned from this one
  metadata: Record<string, any>;
}

export interface ClaudeSessionInfo extends BaseSessionInfo {
  type: 'claude';
  sessionData: SessionData;
  projectPath: string;
}

export interface BrainstormSessionInfo extends BaseSessionInfo {
  type: 'brainstorm';
  sessionData: BrainstormSession;
}

export interface OrchestraSessionInfo extends BaseSessionInfo {
  type: 'orchestra';
  projectPath: string;
  agentIds: string[];
  taskIds: string[];
}

export type UnifiedSessionInfo = ClaudeSessionInfo | BrainstormSessionInfo | OrchestraSessionInfo;

// Session Events
export type SessionLifecycleEvent = 
  | { type: 'session_created'; sessionInfo: UnifiedSessionInfo }
  | { type: 'session_activated'; sessionId: string; sessionType: SessionType }
  | { type: 'session_paused'; sessionId: string; sessionType: SessionType }
  | { type: 'session_resumed'; sessionId: string; sessionType: SessionType }
  | { type: 'session_terminated'; sessionId: string; sessionType: SessionType }
  | { type: 'session_relationship_created'; parentId: string; childId: string; relationshipType: string }
  | { type: 'cross_session_data_shared'; sourceId: string; targetId: string; dataType: string; data: any };

export type SessionEventCallback = (event: SessionLifecycleEvent) => void;

// Session Relationship Types
export type SessionRelationshipType = 'spawned' | 'forked' | 'related' | 'merged';

export interface SessionRelationship {
  id: string;
  parentSessionId: string;
  childSessionId: string;
  relationshipType: SessionRelationshipType;
  createdAt: string;
  metadata: Record<string, any>;
}

// Cross-Session Data Sharing
export interface CrossSessionData {
  sourceSessionId: string;
  targetSessionId: string;
  dataType: 'ideas' | 'code' | 'tasks' | 'insights' | 'context';
  data: any;
  timestamp: string;
}

/**
 * Master Session Coordinator Class
 */
export class MasterSessionCoordinator {
  private activeSessions = new Map<string, UnifiedSessionInfo>();
  private sessionRelationships = new Map<string, SessionRelationship>();
  private eventListeners: SessionEventCallback[] = [];
  private activeSessionId: string | null = null;
  private sessionHistory: string[] = []; // For navigation

  constructor() {
    this.initializeFromExistingSessions();
  }

  /**
   * Initialize coordinator from existing sessions
   */
  private async initializeFromExistingSessions(): Promise<void> {
    try {
      // Initialize persistence service first
      await universalPersistence.initialize();

      // Load persisted sessions
      const persistedSessions = await universalPersistence.loadAllSessions();
      
      // Add persisted sessions to active sessions
      for (const session of persistedSessions) {
        this.activeSessions.set(session.id, session);
      }

      // Load persisted relationships
      const persistedRelationships = await universalPersistence.loadAllRelationships();
      for (const relationship of persistedRelationships) {
        this.sessionRelationships.set(relationship.id, relationship);
      }

      // Initialize from existing Claude session if any
      const currentClaudeSession = sessionManager.getCurrentSession();
      if (currentClaudeSession) {
        const existingSession = persistedSessions.find(s => s.id === currentClaudeSession.id);
        
        if (!existingSession) {
          const sessionInfo: ClaudeSessionInfo = {
            id: currentClaudeSession.id,
            type: 'claude',
            title: `Claude Session - ${currentClaudeSession.projectPath.split('/').pop()}`,
            projectPath: currentClaudeSession.projectPath,
            createdAt: currentClaudeSession.createdAt,
            updatedAt: currentClaudeSession.updatedAt,
            status: this.mapClaudeStatus(currentClaudeSession.status),
            childSessionIds: [],
            metadata: { originalMetadata: currentClaudeSession.metadata },
            sessionData: currentClaudeSession
          };
          this.activeSessions.set(sessionInfo.id, sessionInfo);
          
          // Persist this new session
          await universalPersistence.saveSession(sessionInfo);
        }
      }

      // Initialize from existing brainstorm sessions
      const brainstormStore = useBrainstormStore.getState();
      for (const session of Object.values(brainstormStore.sessions)) {
        const existingSession = persistedSessions.find(s => s.id === session.id);
        
        if (!existingSession) {
          const sessionInfo: BrainstormSessionInfo = {
            id: session.id,
            type: 'brainstorm',
            title: session.title,
            createdAt: session.createdAt,
            updatedAt: session.updatedAt,
            status: 'idle', // Brainstorm sessions don't have active status
            childSessionIds: [],
            metadata: { originalMetadata: session.metadata },
            sessionData: session
          };
          this.activeSessions.set(sessionInfo.id, sessionInfo);
          
          // Persist this new session
          await universalPersistence.saveSession(sessionInfo);
        }
      }

      console.log(`Master Session Coordinator initialized with ${this.activeSessions.size} sessions`);
    } catch (error) {
      console.error('Failed to initialize from existing sessions:', error);
      // Continue with in-memory initialization even if persistence fails
      await this.fallbackInitialization();
    }
  }

  /**
   * Fallback initialization if persistence fails
   */
  private async fallbackInitialization(): Promise<void> {
    console.log('Using fallback initialization without persistence');
    
    // Initialize from existing Claude session
    const currentClaudeSession = sessionManager.getCurrentSession();
    if (currentClaudeSession) {
      const sessionInfo: ClaudeSessionInfo = {
        id: currentClaudeSession.id,
        type: 'claude',
        title: `Claude Session - ${currentClaudeSession.projectPath.split('/').pop()}`,
        projectPath: currentClaudeSession.projectPath,
        createdAt: currentClaudeSession.createdAt,
        updatedAt: currentClaudeSession.updatedAt,
        status: this.mapClaudeStatus(currentClaudeSession.status),
        childSessionIds: [],
        metadata: { originalMetadata: currentClaudeSession.metadata },
        sessionData: currentClaudeSession
      };
      this.activeSessions.set(sessionInfo.id, sessionInfo);
    }

    // Initialize from existing brainstorm sessions
    const brainstormStore = useBrainstormStore.getState();
    Object.values(brainstormStore.sessions).forEach(session => {
      const sessionInfo: BrainstormSessionInfo = {
        id: session.id,
        type: 'brainstorm',
        title: session.title,
        createdAt: session.createdAt,
        updatedAt: session.updatedAt,
        status: 'idle', // Brainstorm sessions don't have active status
        childSessionIds: [],
        metadata: { originalMetadata: session.metadata },
        sessionData: session
      };
      this.activeSessions.set(sessionInfo.id, sessionInfo);
    });
  }

  /**
   * Create a new session of specified type
   */
  async createSession(
    type: SessionType, 
    options: {
      title?: string;
      projectPath?: string;
      parentSessionId?: string;
      initialData?: any;
    } = {}
  ): Promise<UnifiedSessionInfo> {
    const sessionId = this.generateSessionId();
    let sessionInfo: UnifiedSessionInfo;

    switch (type) {
      case 'claude':
        if (!options.projectPath) {
          throw new Error('Project path required for Claude sessions');
        }
        const claudeSession = await sessionManager.createSession({
          projectPath: options.projectPath,
          initialPrompt: options.initialData?.prompt
        });
        
        sessionInfo = {
          id: sessionId,
          type: 'claude',
          title: options.title || `Claude Session - ${options.projectPath.split('/').pop()}`,
          projectPath: options.projectPath,
          createdAt: claudeSession.createdAt,
          updatedAt: claudeSession.updatedAt,
          status: this.mapClaudeStatus(claudeSession.status),
          parentSessionId: options.parentSessionId,
          childSessionIds: [],
          metadata: { originalMetadata: claudeSession.metadata },
          sessionData: claudeSession
        } as ClaudeSessionInfo;
        break;

      case 'brainstorm':
        const brainstormStore = useBrainstormStore.getState();
        const brainstormSessionId = brainstormStore.createSession(
          options.title || 'New Brainstorming Session',
          options.initialData?.template
        );
        const brainstormSession = brainstormStore.sessions[brainstormSessionId];
        
        sessionInfo = {
          id: sessionId,
          type: 'brainstorm',
          title: brainstormSession.title,
          createdAt: brainstormSession.createdAt,
          updatedAt: brainstormSession.updatedAt,
          status: 'idle',
          parentSessionId: options.parentSessionId,
          childSessionIds: [],
          metadata: { originalMetadata: brainstormSession.metadata, originalId: brainstormSessionId },
          sessionData: brainstormSession
        } as BrainstormSessionInfo;
        break;

      case 'orchestra':
        if (!options.projectPath) {
          throw new Error('Project path required for Orchestra sessions');
        }
        
        sessionInfo = {
          id: sessionId,
          type: 'orchestra',
          title: options.title || `Orchestra Session - ${options.projectPath.split('/').pop()}`,
          projectPath: options.projectPath,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          status: 'idle',
          parentSessionId: options.parentSessionId,
          childSessionIds: [],
          metadata: {},
          agentIds: [],
          taskIds: []
        } as OrchestraSessionInfo;
        break;

      default:
        throw new Error(`Unknown session type: ${type}`);
    }

    // Register session
    this.activeSessions.set(sessionInfo.id, sessionInfo);

    try {
      // Persist session
      await universalPersistence.saveSession(sessionInfo);
    } catch (error) {
      console.error('Failed to persist session:', error);
      // Continue without persistence
    }

    // Create relationship if parent exists
    if (options.parentSessionId && this.activeSessions.has(options.parentSessionId)) {
      this.createSessionRelationship(options.parentSessionId, sessionInfo.id, 'spawned');
    }

    // Emit event
    this.emitEvent({ type: 'session_created', sessionInfo });

    return sessionInfo;
  }

  /**
   * Activate a session (switch to it)
   */
  async activateSession(sessionId: string): Promise<void> {
    const sessionInfo = this.activeSessions.get(sessionId);
    if (!sessionInfo) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    // Pause current session if different
    if (this.activeSessionId && this.activeSessionId !== sessionId) {
      await this.pauseSession(this.activeSessionId);
    }

    // Activate the session based on type
    switch (sessionInfo.type) {
      case 'claude':
        // The sessionManager handles activation internally
        break;
      case 'brainstorm':
        useBrainstormStore.getState().setCurrentSession(sessionInfo.metadata.originalId);
        break;
      case 'orchestra':
        // Orchestra sessions are activated by opening the panel
        break;
    }

    // Update coordinator state
    this.activeSessionId = sessionId;
    this.sessionHistory.unshift(sessionId);
    if (this.sessionHistory.length > 50) {
      this.sessionHistory = this.sessionHistory.slice(0, 50);
    }

    sessionInfo.status = 'active';
    sessionInfo.updatedAt = new Date().toISOString();

    this.emitEvent({ type: 'session_activated', sessionId, sessionType: sessionInfo.type });
  }

  /**
   * Pause a session
   */
  async pauseSession(sessionId: string): Promise<void> {
    const sessionInfo = this.activeSessions.get(sessionId);
    if (!sessionInfo) return;

    sessionInfo.status = 'paused';
    sessionInfo.updatedAt = new Date().toISOString();

    this.emitEvent({ type: 'session_paused', sessionId, sessionType: sessionInfo.type });
  }

  /**
   * Resume a paused session
   */
  async resumeSession(sessionId: string): Promise<void> {
    const sessionInfo = this.activeSessions.get(sessionId);
    if (!sessionInfo) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    await this.activateSession(sessionId);
    this.emitEvent({ type: 'session_resumed', sessionId, sessionType: sessionInfo.type });
  }

  /**
   * Terminate a session
   */
  async terminateSession(sessionId: string): Promise<void> {
    const sessionInfo = this.activeSessions.get(sessionId);
    if (!sessionInfo) return;

    // Terminate based on session type
    switch (sessionInfo.type) {
      case 'claude':
        if (sessionManager.getCurrentSession()?.id === sessionId) {
          await sessionManager.terminateSession();
        }
        break;
      case 'brainstorm':
        // Brainstorm sessions don't need special termination
        break;
      case 'orchestra':
        // Orchestra sessions are terminated by closing the panel
        break;
    }

    sessionInfo.status = 'terminated';
    sessionInfo.updatedAt = new Date().toISOString();

    // Remove from active sessions
    this.activeSessions.delete(sessionId);

    try {
      // Remove from persistent storage
      await universalPersistence.deleteSession(sessionId);
    } catch (error) {
      console.error('Failed to delete session from persistence:', error);
      // Continue with termination even if persistence fails
    }

    // Update active session if this was active
    if (this.activeSessionId === sessionId) {
      this.activeSessionId = null;
    }

    this.emitEvent({ type: 'session_terminated', sessionId, sessionType: sessionInfo.type });
  }

  /**
   * Create relationship between sessions
   */
  async createSessionRelationship(
    parentId: string, 
    childId: string, 
    relationshipType: SessionRelationshipType,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    const relationshipId = this.generateSessionId();
    const relationship: SessionRelationship = {
      id: relationshipId,
      parentSessionId: parentId,
      childSessionId: childId,
      relationshipType,
      createdAt: new Date().toISOString(),
      metadata
    };

    this.sessionRelationships.set(relationshipId, relationship);

    try {
      // Persist relationship
      await universalPersistence.saveRelationship(relationship);
    } catch (error) {
      console.error('Failed to persist relationship:', error);
      // Continue without persistence
    }

    // Update parent session's child list
    const parentSession = this.activeSessions.get(parentId);
    if (parentSession) {
      parentSession.childSessionIds.push(childId);
      
      try {
        // Update persisted parent session
        await universalPersistence.saveSession(parentSession);
      } catch (error) {
        console.error('Failed to update parent session in persistence:', error);
      }
    }

    // Update child session's parent
    const childSession = this.activeSessions.get(childId);
    if (childSession) {
      childSession.parentSessionId = parentId;
      
      try {
        // Update persisted child session
        await universalPersistence.saveSession(childSession);
      } catch (error) {
        console.error('Failed to update child session in persistence:', error);
      }
    }

    this.emitEvent({ 
      type: 'session_relationship_created', 
      parentId, 
      childId, 
      relationshipType 
    });
  }

  /**
   * Share data between sessions
   */
  shareCrossSessionData(
    sourceId: string, 
    targetId: string, 
    dataType: CrossSessionData['dataType'], 
    data: any
  ): void {
    const crossSessionData: CrossSessionData = {
      sourceSessionId: sourceId,
      targetSessionId: targetId,
      dataType,
      data,
      timestamp: new Date().toISOString()
    };

    // Process the data sharing based on types
    this.processCrossSessionDataSharing(crossSessionData);

    this.emitEvent({
      type: 'cross_session_data_shared',
      sourceId,
      targetId,
      dataType,
      data
    });
  }

  /**
   * Get all active sessions
   */
  getAllSessions(): UnifiedSessionInfo[] {
    return Array.from(this.activeSessions.values());
  }

  /**
   * Get sessions by type
   */
  getSessionsByType(type: SessionType): UnifiedSessionInfo[] {
    return this.getAllSessions().filter(session => session.type === type);
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): UnifiedSessionInfo | undefined {
    return this.activeSessions.get(sessionId);
  }

  /**
   * Get active session
   */
  getActiveSession(): UnifiedSessionInfo | null {
    return this.activeSessionId ? this.activeSessions.get(this.activeSessionId) || null : null;
  }

  /**
   * Get session relationships
   */
  getSessionRelationships(sessionId: string): SessionRelationship[] {
    return Array.from(this.sessionRelationships.values()).filter(
      rel => rel.parentSessionId === sessionId || rel.childSessionId === sessionId
    );
  }

  /**
   * Subscribe to session events
   */
  subscribe(callback: SessionEventCallback): () => void {
    this.eventListeners.push(callback);
    return () => {
      const index = this.eventListeners.indexOf(callback);
      if (index > -1) {
        this.eventListeners.splice(index, 1);
      }
    };
  }

  /**
   * Get session navigation history
   */
  getSessionHistory(): string[] {
    return [...this.sessionHistory];
  }

  // Private helper methods

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private mapClaudeStatus(claudeStatus: string): BaseSessionInfo['status'] {
    switch (claudeStatus) {
      case 'streaming': return 'active';
      case 'idle': return 'idle';
      case 'error': return 'paused';
      case 'terminated': return 'terminated';
      default: return 'idle';
    }
  }

  private emitEvent(event: SessionLifecycleEvent): void {
    this.eventListeners.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error('Error in session event callback:', error);
      }
    });
  }

  private processCrossSessionDataSharing(crossSessionData: CrossSessionData): void {
    const { sourceSessionId, targetSessionId, dataType, data } = crossSessionData;
    
    const sourceSession = this.activeSessions.get(sourceSessionId);
    const targetSession = this.activeSessions.get(targetSessionId);
    
    if (!sourceSession || !targetSession) return;

    // Implement data sharing logic based on session types and data type
    switch (dataType) {
      case 'ideas':
        if (sourceSession.type === 'brainstorm' && targetSession.type === 'claude') {
          // Share brainstormed ideas as context for Claude session
          // This would be implemented with specific logic
        }
        break;
      case 'code':
        if (sourceSession.type === 'claude' && targetSession.type === 'orchestra') {
          // Share code context for agent orchestra tasks
        }
        break;
      case 'tasks':
        if (sourceSession.type === 'orchestra' && targetSession.type === 'claude') {
          // Share orchestrated tasks as prompts for Claude
        }
        break;
    }
  }
}

// Export singleton instance
export const masterSessionCoordinator = new MasterSessionCoordinator();