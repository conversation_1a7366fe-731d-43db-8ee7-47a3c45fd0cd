/**
 * Brainstorm Storage Service
 * 
 * Handles data persistence for the enhanced brainstorming system using <PERSON><PERSON>'s
 * storage APIs for cross-platform file system access and data management.
 */

import { invoke } from '@tauri-apps/api/core';
import { appDataDir, join } from '@tauri-apps/api/path';
import { readTextFile, writeTextFile, exists, mkdir, readDir, remove } from '@tauri-apps/plugin-fs';
import { 
  BrainstormSession, 
  Idea, 
  IdeaCluster, 
  PersistentMemory, 
  BrainstormTemplate,
  AIPersona,
  BrainstormSettings,
  IntegrationConfig
} from '@/types/brainstorm';

// Storage paths
const STORAGE_DIR = 'brainstorm';
const SESSIONS_DIR = `${STORAGE_DIR}/sessions`;
const IDEAS_DIR = `${STORAGE_DIR}/ideas`;
const CLUSTERS_DIR = `${STORAGE_DIR}/clusters`;
const MEMORIES_DIR = `${STORAGE_DIR}/memories`;
const TEMPLATES_DIR = `${STORAGE_DIR}/templates`;
const SETTINGS_FILE = `${STORAGE_DIR}/settings.json`;
const PERSONAS_FILE = `${STORAGE_DIR}/personas.json`;
const INTEGRATIONS_FILE = `${STORAGE_DIR}/integrations.json`;

export class BrainstormStorage {
  private static instance: BrainstormStorage;
  
  private constructor() {}
  
  static getInstance(): BrainstormStorage {
    if (!BrainstormStorage.instance) {
      BrainstormStorage.instance = new BrainstormStorage();
    }
    return BrainstormStorage.instance;
  }
  
  /**
   * Initialize storage directories
   */
  async initialize(): Promise<void> {
    try {
      const directories = [
        STORAGE_DIR,
        SESSIONS_DIR,
        IDEAS_DIR,
        CLUSTERS_DIR,
        MEMORIES_DIR,
        TEMPLATES_DIR
      ];
      
      const appDataPath = await appDataDir();
      
      for (const dir of directories) {
        const fullPath = await join(appDataPath, dir);
        if (!(await exists(fullPath))) {
          await mkdir(fullPath, { recursive: true });
        }
      }
    } catch (error) {
      console.error('Failed to initialize brainstorm storage:', error);
      throw new Error('Storage initialization failed');
    }
  }
  
  // Session storage
  async saveSession(session: BrainstormSession): Promise<void> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, SESSIONS_DIR, `${session.id}.json`);
      await writeTextFile(filePath, JSON.stringify(session, null, 2));
    } catch (error) {
      console.error('Failed to save session:', error);
      throw new Error(`Failed to save session ${session.id}`);
    }
  }
  
  async loadSession(sessionId: string): Promise<BrainstormSession | null> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, SESSIONS_DIR, `${sessionId}.json`);
      if (!(await exists(filePath))) {
        return null;
      }
      
      const content = await readTextFile(filePath);
      return JSON.parse(content) as BrainstormSession;
    } catch (error) {
      console.error('Failed to load session:', error);
      return null;
    }
  }
  
  async loadAllSessions(): Promise<Record<string, BrainstormSession>> {
    try {
      const sessions: Record<string, BrainstormSession> = {};
      const appDataPath = await appDataDir();
      const sessionsPath = await join(appDataPath, SESSIONS_DIR);
      
      // Ensure directory exists
      if (!(await exists(sessionsPath))) {
        return sessions;
      }
      
      // Get all session files
      const entries = await readDir(sessionsPath);
      
      for (const entry of entries) {
        if (entry.name.endsWith('.json')) {
          const sessionId = entry.name.replace('.json', '');
          const session = await this.loadSession(sessionId);
          if (session) {
            sessions[sessionId] = session;
          }
        }
      }
      
      return sessions;
    } catch (error) {
      console.error('Failed to load sessions:', error);
      return {};
    }
  }
  
  async deleteSession(sessionId: string): Promise<void> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, SESSIONS_DIR, `${sessionId}.json`);
      if (await exists(filePath)) {
        await remove(filePath);
      }
    } catch (error) {
      console.error('Failed to delete session:', error);
      throw new Error(`Failed to delete session ${sessionId}`);
    }
  }
  
  // Idea storage
  async saveIdea(idea: Idea): Promise<void> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, IDEAS_DIR, `${idea.id}.json`);
      await writeTextFile(filePath, JSON.stringify(idea, null, 2));
    } catch (error) {
      console.error('Failed to save idea:', error);
      throw new Error(`Failed to save idea ${idea.id}`);
    }
  }
  
  async loadIdea(ideaId: string): Promise<Idea | null> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, IDEAS_DIR, `${ideaId}.json`);
      if (!(await exists(filePath))) {
        return null;
      }
      
      const content = await readTextFile(filePath);
      return JSON.parse(content) as Idea;
    } catch (error) {
      console.error('Failed to load idea:', error);
      return null;
    }
  }
  
  async loadAllIdeas(): Promise<Record<string, Idea>> {
    try {
      const ideas: Record<string, Idea> = {};
      const appDataPath = await appDataDir();
      const ideasPath = await join(appDataPath, IDEAS_DIR);
      
      // Ensure directory exists
      if (!(await exists(ideasPath))) {
        return ideas;
      }
      
      const entries = await readDir(ideasPath);
      
      for (const entry of entries) {
        if (entry.name.endsWith('.json')) {
          const ideaId = entry.name.replace('.json', '');
          const idea = await this.loadIdea(ideaId);
          if (idea) {
            ideas[ideaId] = idea;
          }
        }
      }
      
      return ideas;
    } catch (error) {
      console.error('Failed to load ideas:', error);
      return {};
    }
  }
  
  async deleteIdea(ideaId: string): Promise<void> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, IDEAS_DIR, `${ideaId}.json`);
      if (await exists(filePath)) {
        await remove(filePath);
      }
    } catch (error) {
      console.error('Failed to delete idea:', error);
      throw new Error(`Failed to delete idea ${ideaId}`);
    }
  }
  
  // Cluster storage
  async saveCluster(cluster: IdeaCluster): Promise<void> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, CLUSTERS_DIR, `${cluster.id}.json`);
      await writeTextFile(filePath, JSON.stringify(cluster, null, 2));
    } catch (error) {
      console.error('Failed to save cluster:', error);
      throw new Error(`Failed to save cluster ${cluster.id}`);
    }
  }
  
  async loadAllClusters(): Promise<Record<string, IdeaCluster>> {
    try {
      const clusters: Record<string, IdeaCluster> = {};
      const appDataPath = await appDataDir();
      const clustersPath = await join(appDataPath, CLUSTERS_DIR);
      
      // Ensure directory exists
      if (!(await exists(clustersPath))) {
        return clusters;
      }
      
      const entries = await readDir(clustersPath);
      
      for (const entry of entries) {
        if (entry.name.endsWith('.json')) {
          const clusterId = entry.name.replace('.json', '');
          const filePath = await join(clustersPath, entry.name);
          
          if (await exists(filePath)) {
            const content = await readTextFile(filePath);
            const cluster = JSON.parse(content) as IdeaCluster;
            clusters[clusterId] = cluster;
          }
        }
      }
      
      return clusters;
    } catch (error) {
      console.error('Failed to load clusters:', error);
      return {};
    }
  }
  
  async deleteCluster(clusterId: string): Promise<void> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, CLUSTERS_DIR, `${clusterId}.json`);
      if (await exists(filePath)) {
        await remove(filePath);
      }
    } catch (error) {
      console.error('Failed to delete cluster:', error);
      throw new Error(`Failed to delete cluster ${clusterId}`);
    }
  }
  
  // Memory storage
  async saveMemory(memory: PersistentMemory): Promise<void> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, MEMORIES_DIR, `${memory.id}.json`);
      await writeTextFile(filePath, JSON.stringify(memory, null, 2));
    } catch (error) {
      console.error('Failed to save memory:', error);
      throw new Error(`Failed to save memory ${memory.id}`);
    }
  }
  
  async loadAllMemories(): Promise<Record<string, PersistentMemory>> {
    try {
      const memories: Record<string, PersistentMemory> = {};
      const appDataPath = await appDataDir();
      const memoriesPath = await join(appDataPath, MEMORIES_DIR);
      
      // Ensure directory exists
      if (!(await exists(memoriesPath))) {
        return memories;
      }
      
      const entries = await readDir(memoriesPath);
      
      for (const entry of entries) {
        if (entry.name.endsWith('.json')) {
          const memoryId = entry.name.replace('.json', '');
          const filePath = await join(memoriesPath, entry.name);
          
          if (await exists(filePath)) {
            const content = await readTextFile(filePath);
            const memory = JSON.parse(content) as PersistentMemory;
            memories[memoryId] = memory;
          }
        }
      }
      
      return memories;
    } catch (error) {
      console.error('Failed to load memories:', error);
      return {};
    }
  }
  
  async deleteMemory(memoryId: string): Promise<void> {
    try {
      const filePath = `${MEMORIES_DIR}/${memoryId}.json`;
      if (await exists(filePath, { baseDir: BaseDirectory.AppData })) {
        await invoke('delete_file', {
          path: filePath,
          baseDir: BaseDirectory.AppData
        });
      }
    } catch (error) {
      console.error('Failed to delete memory:', error);
      throw new Error(`Failed to delete memory ${memoryId}`);
    }
  }
  
  // Template storage
  async saveTemplate(template: BrainstormTemplate): Promise<void> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, TEMPLATES_DIR, `${template.id}.json`);
      await writeTextFile(filePath, JSON.stringify(template, null, 2));
    } catch (error) {
      console.error('Failed to save template:', error);
      throw new Error(`Failed to save template ${template.id}`);
    }
  }
  
  async loadAllTemplates(): Promise<Record<string, BrainstormTemplate>> {
    try {
      const templates: Record<string, BrainstormTemplate> = {};
      const appDataPath = await appDataDir();
      const templatesPath = await join(appDataPath, TEMPLATES_DIR);
      
      // Ensure directory exists
      if (!(await exists(templatesPath))) {
        return templates;
      }
      
      const entries = await readDir(templatesPath);
      
      for (const entry of entries) {
        if (entry.name.endsWith('.json')) {
          const templateId = entry.name.replace('.json', '');
          const filePath = await join(templatesPath, entry.name);
          
          if (await exists(filePath)) {
            const content = await readTextFile(filePath);
            const template = JSON.parse(content) as BrainstormTemplate;
            templates[templateId] = template;
          }
        }
      }
      
      return templates;
    } catch (error) {
      console.error('Failed to load templates:', error);
      return {};
    }
  }
  
  async deleteTemplate(templateId: string): Promise<void> {
    try {
      const filePath = `${TEMPLATES_DIR}/${templateId}.json`;
      if (await exists(filePath, { baseDir: BaseDirectory.AppData })) {
        await invoke('delete_file', {
          path: filePath,
          baseDir: BaseDirectory.AppData
        });
      }
    } catch (error) {
      console.error('Failed to delete template:', error);
      throw new Error(`Failed to delete template ${templateId}`);
    }
  }
  
  // Settings storage
  async saveSettings(settings: BrainstormSettings): Promise<void> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, SETTINGS_FILE);
      await writeTextFile(filePath, JSON.stringify(settings, null, 2));
    } catch (error) {
      console.error('Failed to save settings:', error);
      throw new Error('Failed to save settings');
    }
  }
  
  async loadSettings(): Promise<BrainstormSettings | null> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, SETTINGS_FILE);
      if (!(await exists(filePath))) {
        return null;
      }
      
      const content = await readTextFile(filePath);
      return JSON.parse(content) as BrainstormSettings;
    } catch (error) {
      console.error('Failed to load settings:', error);
      return null;
    }
  }
  
  // Personas storage
  async savePersonas(personas: AIPersona[]): Promise<void> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, PERSONAS_FILE);
      await writeTextFile(filePath, JSON.stringify(personas, null, 2));
    } catch (error) {
      console.error('Failed to save personas:', error);
      throw new Error('Failed to save personas');
    }
  }
  
  async loadPersonas(): Promise<AIPersona[] | null> {
    try {
      const appDataPath = await appDataDir();
      const filePath = await join(appDataPath, PERSONAS_FILE);
      if (!(await exists(filePath))) {
        return null;
      }
      
      const content = await readTextFile(filePath);
      return JSON.parse(content) as AIPersona[];
    } catch (error) {
      console.error('Failed to load personas:', error);
      return null;
    }
  }
  
  // Integration storage
  async saveIntegrations(integrations: Record<string, IntegrationConfig>): Promise<void> {
    try {
      await writeTextFile({ path: INTEGRATIONS_FILE, contents: JSON.stringify(integrations, null, 2) }, {
        baseDir: BaseDirectory.AppData
      });
    } catch (error) {
      console.error('Failed to save integrations:', error);
      throw new Error('Failed to save integrations');
    }
  }
  
  async loadIntegrations(): Promise<Record<string, IntegrationConfig> | null> {
    try {
      if (!(await exists(INTEGRATIONS_FILE, { baseDir: BaseDirectory.AppData }))) {
        return null;
      }
      
      const content = await readTextFile(INTEGRATIONS_FILE, { baseDir: BaseDirectory.AppData });
      return JSON.parse(content) as Record<string, IntegrationConfig>;
    } catch (error) {
      console.error('Failed to load integrations:', error);
      return null;
    }
  }
  
  // Backup and restore
  async createBackup(): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const appDataPath = await appDataDir();
      const backupPath = await join(appDataPath, STORAGE_DIR, `backup-${timestamp}.json`);
      
      const backup = {
        timestamp,
        sessions: await this.loadAllSessions(),
        ideas: await this.loadAllIdeas(),
        clusters: await this.loadAllClusters(),
        memories: await this.loadAllMemories(),
        templates: await this.loadAllTemplates(),
        settings: await this.loadSettings(),
        personas: await this.loadPersonas(),
        integrations: await this.loadIntegrations()
      };
      
      await writeTextFile(backupPath, JSON.stringify(backup, null, 2));
      
      return backupPath;
    } catch (error) {
      console.error('Failed to create backup:', error);
      throw new Error('Backup creation failed');
    }
  }
  
  async restoreFromBackup(backupPath: string): Promise<void> {
    try {
      if (!(await exists(backupPath, { baseDir: BaseDirectory.AppData }))) {
        throw new Error('Backup file not found');
      }
      
      const content = await readTextFile(backupPath, { baseDir: BaseDirectory.AppData });
      const backup = JSON.parse(content);
      
      // Restore all data
      if (backup.sessions) {
        for (const session of Object.values(backup.sessions) as BrainstormSession[]) {
          await this.saveSession(session);
        }
      }
      
      if (backup.ideas) {
        for (const idea of Object.values(backup.ideas) as Idea[]) {
          await this.saveIdea(idea);
        }
      }
      
      if (backup.clusters) {
        for (const cluster of Object.values(backup.clusters) as IdeaCluster[]) {
          await this.saveCluster(cluster);
        }
      }
      
      if (backup.memories) {
        for (const memory of Object.values(backup.memories) as PersistentMemory[]) {
          await this.saveMemory(memory);
        }
      }
      
      if (backup.templates) {
        for (const template of Object.values(backup.templates) as BrainstormTemplate[]) {
          await this.saveTemplate(template);
        }
      }
      
      if (backup.settings) {
        await this.saveSettings(backup.settings);
      }
      
      if (backup.personas) {
        await this.savePersonas(backup.personas);
      }
      
      if (backup.integrations) {
        await this.saveIntegrations(backup.integrations);
      }
      
    } catch (error) {
      console.error('Failed to restore backup:', error);
      throw new Error('Backup restoration failed');
    }
  }
  
  // Cleanup old data
  async cleanup(olderThanDays: number = 30): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
      
      // Clean up old sessions
      const sessions = await this.loadAllSessions();
      for (const session of Object.values(sessions)) {
        if (new Date(session.updatedAt) < cutoffDate) {
          await this.deleteSession(session.id);
        }
      }
      
      // Clean up orphaned ideas
      const ideas = await this.loadAllIdeas();
      
      for (const idea of Object.values(ideas)) {
        // Check if idea belongs to a valid session
        const belongsToValidSession = Object.values(sessions).some(session =>
          session.messages.some(message => message.id === idea.messageId)
        );
        
        if (!belongsToValidSession) {
          await this.deleteIdea(idea.id);
        }
      }
      
    } catch (error) {
      console.error('Failed to cleanup storage:', error);
      throw new Error('Storage cleanup failed');
    }
  }
}

// Export singleton instance
export const brainstormStorage = BrainstormStorage.getInstance();