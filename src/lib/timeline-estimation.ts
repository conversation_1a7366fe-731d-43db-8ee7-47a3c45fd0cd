/**
 * Timeline and Resource Estimation Service
 * 
 * Generates project timelines and resource estimates from brainstorming ideas and tasks
 */

import { GeneratedTask, Idea } from '@/types/brainstorm';

export interface TimelineTask extends GeneratedTask {
  startDate: string;
  endDate: string;
  duration: number; // in days
  criticalPath: boolean;
  predecessors: string[];
  successors: string[];
  resourceAllocations: ResourceAllocation[];
}

export interface ResourceAllocation {
  id: string;
  taskId: string;
  resourceId: string;
  allocation: number; // percentage 0-100
  startDate: string;
  endDate: string;
}

export interface Resource {
  id: string;
  name: string;
  type: 'person' | 'tool' | 'budget';
  skills: string[];
  availability: number; // percentage 0-100
  hourlyRate?: number;
  maxAllocation: number; // max percentage that can be allocated
}

export interface ProjectTimeline {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  duration: number;
  tasks: TimelineTask[];
  resources: Resource[];
  milestones: Milestone[];
  criticalPath: string[];
  totalCost: number;
  riskFactors: RiskFactor[];
}

export interface Milestone {
  id: string;
  name: string;
  date: string;
  description: string;
  dependencies: string[];
  completed: boolean;
  type: 'major' | 'minor' | 'checkpoint';
}

export interface RiskFactor {
  id: string;
  description: string;
  probability: number; // 0-1
  impact: number; // 0-1
  mitigation: string;
  category: 'technical' | 'resource' | 'timeline' | 'external';
}

export interface EstimationOptions {
  workingDaysPerWeek: number;
  hoursPerDay: number;
  bufferPercentage: number;
  riskTolerance: 'low' | 'medium' | 'high';
  teamExperience: 'junior' | 'mixed' | 'senior';
}

const DEFAULT_OPTIONS: EstimationOptions = {
  workingDaysPerWeek: 5,
  hoursPerDay: 8,
  bufferPercentage: 20,
  riskTolerance: 'medium',
  teamExperience: 'mixed',
};

class TimelineEstimationService {
  /**
   * Generate project timeline from tasks and ideas
   */
  generateTimeline(
    tasks: GeneratedTask[],
    ideas: Idea[],
    resources: Resource[],
    options: Partial<EstimationOptions> = {}
  ): ProjectTimeline {
    const opts = { ...DEFAULT_OPTIONS, ...options };
    
    // Analyze task dependencies
    const dependencyGraph = this.buildDependencyGraph(tasks, ideas);
    
    // Estimate task durations
    const estimatedTasks = this.estimateTaskDurations(tasks, opts);
    
    // Schedule tasks
    const scheduledTasks = this.scheduleTasks(estimatedTasks, dependencyGraph, opts);
    
    // Allocate resources
    const tasksWithResources = this.allocateResources(scheduledTasks, resources, opts);
    
    // Calculate critical path
    const criticalPath = this.calculateCriticalPath(tasksWithResources);
    
    // Generate milestones
    const milestones = this.generateMilestones(tasksWithResources);
    
    // Calculate project metrics
    const projectStart = new Date(Math.min(...tasksWithResources.map(t => new Date(t.startDate).getTime())));
    const projectEnd = new Date(Math.max(...tasksWithResources.map(t => new Date(t.endDate).getTime())));
    const duration = Math.ceil((projectEnd.getTime() - projectStart.getTime()) / (1000 * 60 * 60 * 24));
    
    // Assess risks
    const riskFactors = this.assessRisks(tasksWithResources, opts);
    
    // Calculate total cost
    const totalCost = this.calculateTotalCost(tasksWithResources, resources);
    
    return {
      id: this.generateId(),
      name: 'Generated Project Timeline',
      startDate: projectStart.toISOString(),
      endDate: projectEnd.toISOString(),
      duration,
      tasks: tasksWithResources,
      resources,
      milestones,
      criticalPath,
      totalCost,
      riskFactors,
    };
  }

  /**
   * Build dependency graph from tasks and ideas
   */
  private buildDependencyGraph(tasks: GeneratedTask[], ideas: Idea[]): Map<string, string[]> {
    const graph = new Map<string, string[]>();
    
    tasks.forEach(task => {
      const dependencies: string[] = [];
      
      // Explicit dependencies
      if (task.dependencies) {
        dependencies.push(...task.dependencies);
      }
      
      // Infer dependencies from idea relationships
      const relatedIdea = ideas.find(idea => idea.id === task.ideaId);
      if (relatedIdea && relatedIdea.connections) {
        relatedIdea.connections.forEach(connectedIdeaId => {
          const connectedTask = tasks.find(t => t.ideaId === connectedIdeaId);
          if (connectedTask && connectedTask.id !== task.id) {
            dependencies.push(connectedTask.id);
          }
        });
      }
      
      // Infer dependencies from tags and content similarity
      const taskKeywords = this.extractKeywords(task.title + ' ' + task.description);
      tasks.forEach(otherTask => {
        if (otherTask.id !== task.id) {
          const otherKeywords = this.extractKeywords(otherTask.title + ' ' + otherTask.description);
          const similarity = this.calculateSimilarity(taskKeywords, otherKeywords);
          
          // If tasks are similar and other task has higher priority, create dependency
          if (similarity > 0.3 && this.getPriorityWeight(otherTask.priority) > this.getPriorityWeight(task.priority)) {
            dependencies.push(otherTask.id);
          }
        }
      });
      
      graph.set(task.id, [...new Set(dependencies)]);
    });
    
    return graph;
  }

  /**
   * Estimate task durations based on complexity and team experience
   */
  private estimateTaskDurations(tasks: GeneratedTask[], options: EstimationOptions): GeneratedTask[] {
    return tasks.map(task => {
      let baseDuration = task.estimatedEffort || 8; // hours
      
      // Adjust for team experience
      const experienceMultiplier = {
        junior: 1.5,
        mixed: 1.2,
        senior: 0.9,
      }[options.teamExperience];
      
      // Adjust for task complexity (inferred from description length and keywords)
      const complexityMultiplier = this.assessTaskComplexity(task);
      
      // Apply multipliers and buffer
      const adjustedDuration = baseDuration * experienceMultiplier * complexityMultiplier;
      const bufferedDuration = adjustedDuration * (1 + options.bufferPercentage / 100);
      
      return {
        ...task,
        estimatedEffort: Math.ceil(bufferedDuration),
      };
    });
  }

  /**
   * Schedule tasks based on dependencies and resource availability
   */
  private scheduleTasks(
    tasks: GeneratedTask[],
    dependencyGraph: Map<string, string[]>,
    options: EstimationOptions
  ): TimelineTask[] {
    const scheduled: TimelineTask[] = [];
    const taskMap = new Map(tasks.map(t => [t.id, t]));
    const completedTasks = new Set<string>();
    
    // Start date for the project
    const projectStart = new Date();
    projectStart.setHours(0, 0, 0, 0);
    
    // Topological sort to determine task order
    const sortedTaskIds = this.topologicalSort(dependencyGraph);
    
    sortedTaskIds.forEach(taskId => {
      const task = taskMap.get(taskId);
      if (!task) return;
      
      const dependencies = dependencyGraph.get(taskId) || [];
      const validDependencies = dependencies.filter(depId => completedTasks.has(depId));
      
      // Calculate earliest start date based on dependencies
      let earliestStart = new Date(projectStart);
      
      if (validDependencies.length > 0) {
        const dependencyEndDates = validDependencies
          .map(depId => scheduled.find(t => t.id === depId))
          .filter(Boolean)
          .map(t => new Date(t!.endDate));
        
        if (dependencyEndDates.length > 0) {
          earliestStart = new Date(Math.max(...dependencyEndDates.map(d => d.getTime())));
        }
      }
      
      // Calculate duration in days
      const durationDays = Math.ceil(task.estimatedEffort / options.hoursPerDay);
      
      // Calculate end date (accounting for weekends)
      const endDate = this.addWorkingDays(earliestStart, durationDays, options.workingDaysPerWeek);
      
      const timelineTask: TimelineTask = {
        ...task,
        startDate: earliestStart.toISOString(),
        endDate: endDate.toISOString(),
        duration: durationDays,
        criticalPath: false, // Will be calculated later
        predecessors: validDependencies,
        successors: [], // Will be calculated later
        resourceAllocations: [],
      };
      
      scheduled.push(timelineTask);
      completedTasks.add(taskId);
    });
    
    // Calculate successors
    scheduled.forEach(task => {
      task.successors = scheduled
        .filter(t => t.predecessors.includes(task.id))
        .map(t => t.id);
    });
    
    return scheduled;
  }

  /**
   * Allocate resources to tasks
   */
  private allocateResources(
    tasks: TimelineTask[],
    resources: Resource[],
    options: EstimationOptions
  ): TimelineTask[] {
    const resourceSchedule = new Map<string, { taskId: string; start: Date; end: Date; allocation: number }[]>();
    
    // Initialize resource schedules
    resources.forEach(resource => {
      resourceSchedule.set(resource.id, []);
    });
    
    // Sort tasks by start date and priority
    const sortedTasks = [...tasks].sort((a, b) => {
      const dateCompare = new Date(a.startDate).getTime() - new Date(b.startDate).getTime();
      if (dateCompare !== 0) return dateCompare;
      
      return this.getPriorityWeight(b.priority) - this.getPriorityWeight(a.priority);
    });
    
    return sortedTasks.map(task => {
      const allocations: ResourceAllocation[] = [];
      
      // Find suitable resources for this task
      const suitableResources = this.findSuitableResources(task, resources);
      
      suitableResources.forEach(resource => {
        const schedule = resourceSchedule.get(resource.id) || [];
        const taskStart = new Date(task.startDate);
        const taskEnd = new Date(task.endDate);
        
        // Check resource availability during task period
        const availability = this.calculateResourceAvailability(
          resource,
          schedule,
          taskStart,
          taskEnd
        );
        
        if (availability > 0) {
          const allocation: ResourceAllocation = {
            id: this.generateId(),
            taskId: task.id,
            resourceId: resource.id,
            allocation: Math.min(availability, resource.maxAllocation),
            startDate: task.startDate,
            endDate: task.endDate,
          };
          
          allocations.push(allocation);
          
          // Update resource schedule
          schedule.push({
            taskId: task.id,
            start: taskStart,
            end: taskEnd,
            allocation: allocation.allocation,
          });
        }
      });
      
      return {
        ...task,
        resourceAllocations: allocations,
      };
    });
  }

  /**
   * Calculate critical path using CPM algorithm
   */
  private calculateCriticalPath(tasks: TimelineTask[]): string[] {
    const taskMap = new Map(tasks.map(t => [t.id, t]));
    const criticalPath: string[] = [];
    
    // Forward pass - calculate earliest start/finish times
    const earliestStart = new Map<string, number>();
    const earliestFinish = new Map<string, number>();
    
    tasks.forEach(task => {
      const taskStart = new Date(task.startDate).getTime();
      const taskDuration = task.duration * 24 * 60 * 60 * 1000; // Convert to milliseconds
      
      let maxPredecessorFinish = 0;
      task.predecessors.forEach(predId => {
        const predFinish = earliestFinish.get(predId) || 0;
        maxPredecessorFinish = Math.max(maxPredecessorFinish, predFinish);
      });
      
      const es = Math.max(taskStart, maxPredecessorFinish);
      const ef = es + taskDuration;
      
      earliestStart.set(task.id, es);
      earliestFinish.set(task.id, ef);
    });
    
    // Backward pass - calculate latest start/finish times
    const latestStart = new Map<string, number>();
    const latestFinish = new Map<string, number>();
    
    // Start from tasks with no successors
    const projectEnd = Math.max(...Array.from(earliestFinish.values()));
    
    [...tasks].reverse().forEach(task => {
      const taskDuration = task.duration * 24 * 60 * 60 * 1000;
      
      let minSuccessorStart = projectEnd;
      if (task.successors.length > 0) {
        task.successors.forEach(succId => {
          const succStart = latestStart.get(succId) || projectEnd;
          minSuccessorStart = Math.min(minSuccessorStart, succStart);
        });
      }
      
      const lf = minSuccessorStart;
      const ls = lf - taskDuration;
      
      latestStart.set(task.id, ls);
      latestFinish.set(task.id, lf);
    });
    
    // Identify critical path tasks (where ES = LS and EF = LF)
    tasks.forEach(task => {
      const es = earliestStart.get(task.id) || 0;
      const ls = latestStart.get(task.id) || 0;
      const ef = earliestFinish.get(task.id) || 0;
      const lf = latestFinish.get(task.id) || 0;
      
      if (Math.abs(es - ls) < 1000 && Math.abs(ef - lf) < 1000) { // 1 second tolerance
        task.criticalPath = true;
        criticalPath.push(task.id);
      }
    });
    
    return criticalPath;
  }

  /**
   * Generate project milestones
   */
  private generateMilestones(tasks: TimelineTask[]): Milestone[] {
    const milestones: Milestone[] = [];
    
    // Major milestones at 25%, 50%, 75%, and 100% completion
    const projectStart = new Date(Math.min(...tasks.map(t => new Date(t.startDate).getTime())));
    const projectEnd = new Date(Math.max(...tasks.map(t => new Date(t.endDate).getTime())));
    const projectDuration = projectEnd.getTime() - projectStart.getTime();
    
    [0.25, 0.5, 0.75, 1.0].forEach((percentage, index) => {
      const milestoneDate = new Date(projectStart.getTime() + projectDuration * percentage);
      const tasksCompleted = tasks.filter(t => new Date(t.endDate) <= milestoneDate);
      
      milestones.push({
        id: this.generateId(),
        name: `${Math.round(percentage * 100)}% Completion`,
        date: milestoneDate.toISOString(),
        description: `${tasksCompleted.length} of ${tasks.length} tasks completed`,
        dependencies: tasksCompleted.map(t => t.id),
        completed: false,
        type: percentage === 1.0 ? 'major' : 'checkpoint',
      });
    });
    
    return milestones;
  }

  /**
   * Assess project risks
   */
  private assessRisks(tasks: TimelineTask[], options: EstimationOptions): RiskFactor[] {
    const risks: RiskFactor[] = [];
    
    // Resource overallocation risk
    const resourceUtilization = this.calculateResourceUtilization(tasks);
    if (resourceUtilization > 0.9) {
      risks.push({
        id: this.generateId(),
        description: 'High resource utilization may lead to bottlenecks',
        probability: 0.7,
        impact: 0.8,
        mitigation: 'Consider adding additional resources or extending timeline',
        category: 'resource',
      });
    }
    
    // Timeline compression risk
    const averageBufferUsed = options.bufferPercentage / 100;
    if (averageBufferUsed < 0.15) {
      risks.push({
        id: this.generateId(),
        description: 'Aggressive timeline with minimal buffer',
        probability: 0.6,
        impact: 0.9,
        mitigation: 'Add buffer time to critical path tasks',
        category: 'timeline',
      });
    }
    
    // Technical complexity risk
    const complexTasks = tasks.filter(t => this.assessTaskComplexity(t) > 1.5);
    if (complexTasks.length > tasks.length * 0.3) {
      risks.push({
        id: this.generateId(),
        description: 'High proportion of complex tasks',
        probability: 0.5,
        impact: 0.7,
        mitigation: 'Break down complex tasks and add technical reviews',
        category: 'technical',
      });
    }
    
    return risks;
  }

  /**
   * Calculate total project cost
   */
  private calculateTotalCost(tasks: TimelineTask[], resources: Resource[]): number {
    let totalCost = 0;
    
    tasks.forEach(task => {
      task.resourceAllocations.forEach(allocation => {
        const resource = resources.find(r => r.id === allocation.resourceId);
        if (resource && resource.hourlyRate) {
          const hours = task.estimatedEffort * (allocation.allocation / 100);
          totalCost += hours * resource.hourlyRate;
        }
      });
    });
    
    return totalCost;
  }

  // Helper methods
  private extractKeywords(text: string): string[] {
    return text.toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 3)
      .slice(0, 10);
  }

  private calculateSimilarity(keywords1: string[], keywords2: string[]): number {
    const set1 = new Set(keywords1);
    const set2 = new Set(keywords2);
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    return intersection.size / union.size;
  }

  private getPriorityWeight(priority: string): number {
    const weights = { low: 1, medium: 2, high: 3, critical: 4 };
    return weights[priority as keyof typeof weights] || 2;
  }

  private assessTaskComplexity(task: GeneratedTask): number {
    let complexity = 1.0;
    
    // Increase complexity based on description length
    if (task.description.length > 200) complexity += 0.3;
    
    // Increase complexity based on keywords
    const complexityKeywords = ['integration', 'algorithm', 'optimization', 'security', 'performance'];
    const hasComplexKeywords = complexityKeywords.some(keyword => 
      task.description.toLowerCase().includes(keyword) || 
      task.title.toLowerCase().includes(keyword)
    );
    
    if (hasComplexKeywords) complexity += 0.5;
    
    // Increase complexity based on dependencies
    if (task.dependencies && task.dependencies.length > 2) complexity += 0.2;
    
    return Math.min(complexity, 2.0);
  }

  private topologicalSort(dependencyGraph: Map<string, string[]>): string[] {
    const visited = new Set<string>();
    const visiting = new Set<string>();
    const result: string[] = [];
    
    const visit = (taskId: string) => {
      if (visiting.has(taskId)) {
        throw new Error('Circular dependency detected');
      }
      
      if (!visited.has(taskId)) {
        visiting.add(taskId);
        
        const dependencies = dependencyGraph.get(taskId) || [];
        dependencies.forEach(depId => visit(depId));
        
        visiting.delete(taskId);
        visited.add(taskId);
        result.push(taskId);
      }
    };
    
    Array.from(dependencyGraph.keys()).forEach(taskId => visit(taskId));
    
    return result;
  }

  private addWorkingDays(startDate: Date, days: number, workingDaysPerWeek: number): Date {
    const result = new Date(startDate);
    let remainingDays = days;
    
    while (remainingDays > 0) {
      result.setDate(result.getDate() + 1);
      
      // Skip weekends if working 5 days per week
      if (workingDaysPerWeek === 5 && (result.getDay() === 0 || result.getDay() === 6)) {
        continue;
      }
      
      remainingDays--;
    }
    
    return result;
  }

  private findSuitableResources(task: GeneratedTask, resources: Resource[]): Resource[] {
    return resources.filter(resource => {
      // Match skills if available
      if (resource.skills.length > 0 && task.tags.length > 0) {
        const skillMatch = resource.skills.some(skill => 
          task.tags.some(tag => tag.toLowerCase().includes(skill.toLowerCase()))
        );
        return skillMatch;
      }
      
      // Default to person resources for tasks
      return resource.type === 'person';
    });
  }

  private calculateResourceAvailability(
    resource: Resource,
    schedule: { taskId: string; start: Date; end: Date; allocation: number }[],
    taskStart: Date,
    taskEnd: Date
  ): number {
    let totalAllocation = 0;
    
    schedule.forEach(entry => {
      // Check for overlap
      if (entry.start < taskEnd && entry.end > taskStart) {
        totalAllocation += entry.allocation;
      }
    });
    
    return Math.max(0, resource.availability - totalAllocation);
  }

  private calculateResourceUtilization(tasks: TimelineTask[]): number {
    const resourceAllocations = new Map<string, number>();
    
    tasks.forEach(task => {
      task.resourceAllocations.forEach(allocation => {
        const current = resourceAllocations.get(allocation.resourceId) || 0;
        resourceAllocations.set(allocation.resourceId, current + allocation.allocation);
      });
    });
    
    const allocations = Array.from(resourceAllocations.values());
    return allocations.length > 0 ? Math.max(...allocations) / 100 : 0;
  }

  private generateId(): string {
    return `timeline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const timelineEstimationService = new TimelineEstimationService();