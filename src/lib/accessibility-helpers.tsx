import React, { useEffect, useRef, useState, useCallback } from 'react';

// Accessibility utilities and helpers
export interface AccessibilityOptions {
  reduceMotion?: boolean;
  highContrast?: boolean;
  fontSize?: 'small' | 'medium' | 'large' | 'xl';
  screenReader?: boolean;
  focusManagement?: boolean;
}

// Screen reader utilities
export class ScreenReaderUtils {
  static announce(message: string, priority: 'polite' | 'assertive' = 'polite') {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }

  static announceNavigation(from: string, to: string) {
    this.announce(`Navigated from ${from} to ${to}`, 'polite');
  }

  static announceError(error: string) {
    this.announce(`Error: ${error}`, 'assertive');
  }

  static announceSuccess(message: string) {
    this.announce(`Success: ${message}`, 'polite');
  }

  static announceLoading(isLoading: boolean, context?: string) {
    const message = isLoading 
      ? `Loading${context ? ` ${context}` : ''}...`
      : `Loading complete${context ? ` for ${context}` : ''}`;
    this.announce(message, 'polite');
  }
}

// Focus management utilities
export class FocusManager {
  private static focusStack: HTMLElement[] = [];

  static pushFocus(element: HTMLElement) {
    if (document.activeElement instanceof HTMLElement) {
      this.focusStack.push(document.activeElement);
    }
    element.focus();
  }

  static popFocus() {
    const previousElement = this.focusStack.pop();
    if (previousElement) {
      previousElement.focus();
    }
  }

  static trapFocus(container: HTMLElement) {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  }

  static getNextFocusableElement(current: HTMLElement, direction: 'next' | 'previous' = 'next') {
    const focusableElements = Array.from(
      document.querySelectorAll(
        'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
      )
    ) as HTMLElement[];

    const currentIndex = focusableElements.indexOf(current);
    
    if (direction === 'next') {
      return focusableElements[currentIndex + 1] || focusableElements[0];
    } else {
      return focusableElements[currentIndex - 1] || focusableElements[focusableElements.length - 1];
    }
  }
}

// Keyboard navigation helpers
export const useKeyboardNavigation = (options: {
  onArrowUp?: () => void;
  onArrowDown?: () => void;
  onArrowLeft?: () => void;
  onArrowRight?: () => void;
  onEnter?: () => void;
  onEscape?: () => void;
  onSpace?: () => void;
  onTab?: (e: KeyboardEvent) => void;
  preventDefault?: boolean;
}) => {
  const {
    onArrowUp,
    onArrowDown,
    onArrowLeft,
    onArrowRight,
    onEnter,
    onEscape,
    onSpace,
    onTab,
    preventDefault = true
  } = options;

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowUp':
        if (onArrowUp) {
          if (preventDefault) e.preventDefault();
          onArrowUp();
        }
        break;
      case 'ArrowDown':
        if (onArrowDown) {
          if (preventDefault) e.preventDefault();
          onArrowDown();
        }
        break;
      case 'ArrowLeft':
        if (onArrowLeft) {
          if (preventDefault) e.preventDefault();
          onArrowLeft();
        }
        break;
      case 'ArrowRight':
        if (onArrowRight) {
          if (preventDefault) e.preventDefault();
          onArrowRight();
        }
        break;
      case 'Enter':
        if (onEnter) {
          if (preventDefault) e.preventDefault();
          onEnter();
        }
        break;
      case 'Escape':
        if (onEscape) {
          if (preventDefault) e.preventDefault();
          onEscape();
        }
        break;
      case ' ':
        if (onSpace) {
          if (preventDefault) e.preventDefault();
          onSpace();
        }
        break;
      case 'Tab':
        if (onTab) {
          onTab(e);
        }
        break;
    }
  }, [onArrowUp, onArrowDown, onArrowLeft, onArrowRight, onEnter, onEscape, onSpace, onTab, preventDefault]);

  return { handleKeyDown };
};

// Accessibility preferences hook
export const useAccessibilityPreferences = () => {
  const [preferences, setPreferences] = useState<AccessibilityOptions>({
    reduceMotion: false,
    highContrast: false,
    fontSize: 'medium',
    screenReader: false,
    focusManagement: true
  });

  useEffect(() => {
    // Check system preferences
    const reduceMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    const highContrast = window.matchMedia('(prefers-contrast: high)').matches;

    setPreferences(prev => ({
      ...prev,
      reduceMotion,
      highContrast
    }));

    // Listen for changes
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const contrastQuery = window.matchMedia('(prefers-contrast: high)');

    const handleMotionChange = (e: MediaQueryListEvent) => {
      setPreferences(prev => ({ ...prev, reduceMotion: e.matches }));
    };

    const handleContrastChange = (e: MediaQueryListEvent) => {
      setPreferences(prev => ({ ...prev, highContrast: e.matches }));
    };

    motionQuery.addEventListener('change', handleMotionChange);
    contrastQuery.addEventListener('change', handleContrastChange);

    return () => {
      motionQuery.removeEventListener('change', handleMotionChange);
      contrastQuery.removeEventListener('change', handleContrastChange);
    };
  }, []);

  const updatePreference = useCallback(<K extends keyof AccessibilityOptions>(
    key: K,
    value: AccessibilityOptions[K]
  ) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
  }, []);

  return { preferences, updatePreference };
};

// ARIA live region hook
export const useAriaLiveRegion = (initialMessage = '') => {
  const [message, setMessage] = useState(initialMessage);
  const regionRef = useRef<HTMLDivElement>(null);

  const announce = useCallback((newMessage: string, priority: 'polite' | 'assertive' = 'polite') => {
    setMessage(newMessage);
    if (regionRef.current) {
      regionRef.current.setAttribute('aria-live', priority);
    }
  }, []);

  const clear = useCallback(() => {
    setMessage('');
  }, []);

  return { message, announce, clear, regionRef };
};

// Focus trap hook
export const useFocusTrap = (isActive: boolean) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    const cleanup = FocusManager.trapFocus(container);

    // Focus first focusable element
    const firstFocusable = container.querySelector(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as HTMLElement;

    if (firstFocusable) {
      firstFocusable.focus();
    }

    return cleanup;
  }, [isActive]);

  return containerRef;
};

// Skip links component
export const SkipLinks = () => {
  return (
    <div className="sr-only focus-within:not-sr-only">
      <a
        href="#main-content"
        className="absolute top-0 left-0 bg-blue-600 text-white px-4 py-2 z-50 focus:relative"
      >
        Skip to main content
      </a>
      <a
        href="#navigation"
        className="absolute top-0 left-20 bg-blue-600 text-white px-4 py-2 z-50 focus:relative"
      >
        Skip to navigation
      </a>
    </div>
  );
};

// Color contrast utilities
export class ColorContrastUtils {
  static getContrastRatio(color1: string, color2: string): number {
    const rgb1 = this.hexToRgb(color1);
    const rgb2 = this.hexToRgb(color2);
    
    if (!rgb1 || !rgb2) return 0;
    
    const l1 = this.getRelativeLuminance(rgb1);
    const l2 = this.getRelativeLuminance(rgb2);
    
    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  static meetsWCAGAA(foreground: string, background: string): boolean {
    return this.getContrastRatio(foreground, background) >= 4.5;
  }

  static meetsWCAGAAA(foreground: string, background: string): boolean {
    return this.getContrastRatio(foreground, background) >= 7;
  }

  private static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  private static getRelativeLuminance(rgb: { r: number; g: number; b: number }): number {
    const { r, g, b } = rgb;
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }
}

// Alt text generator for images
export const generateAltText = (context: string, description?: string) => {
  if (description) return description;
  
  // Basic alt text generation based on context
  const contextMap: Record<string, string> = {
    'user-avatar': 'User profile picture',
    'logo': 'Company logo',
    'icon': 'Icon',
    'chart': 'Data visualization chart',
    'graph': 'Graph showing data trends',
    'diagram': 'Diagram illustration',
    'photo': 'Photograph',
    'illustration': 'Illustration'
  };
  
  return contextMap[context] || 'Image';
};

// Accessible form validation
export const useFormValidation = (fields: Record<string, any>) => {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const validateField = useCallback((name: string, value: any, rules: any) => {
    let error = '';
    
    if (rules.required && (!value || value.toString().trim() === '')) {
      error = `${name} is required`;
    } else if (rules.minLength && value.length < rules.minLength) {
      error = `${name} must be at least ${rules.minLength} characters`;
    } else if (rules.maxLength && value.length > rules.maxLength) {
      error = `${name} must be no more than ${rules.maxLength} characters`;
    } else if (rules.pattern && !rules.pattern.test(value)) {
      error = rules.message || `${name} format is invalid`;
    }
    
    setErrors(prev => ({ ...prev, [name]: error }));
    return error === '';
  }, []);

  const markFieldTouched = useCallback((name: string) => {
    setTouched(prev => ({ ...prev, [name]: true }));
  }, []);

  const isFieldValid = useCallback((name: string) => {
    return touched[name] && !errors[name];
  }, [touched, errors]);

  const hasFieldError = useCallback((name: string) => {
    return touched[name] && !!errors[name];
  }, [touched, errors]);

  const getFieldErrorId = useCallback((name: string) => {
    return hasFieldError(name) ? `${name}-error` : undefined;
  }, [hasFieldError]);

  return {
    errors,
    touched,
    validateField,
    markFieldTouched,
    isFieldValid,
    hasFieldError,
    getFieldErrorId
  };
};

export default {
  ScreenReaderUtils,
  FocusManager,
  useKeyboardNavigation,
  useAccessibilityPreferences,
  useAriaLiveRegion,
  useFocusTrap,
  SkipLinks,
  ColorContrastUtils,
  generateAltText,
  useFormValidation
};