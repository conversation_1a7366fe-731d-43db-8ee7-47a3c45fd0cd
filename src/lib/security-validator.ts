/**
 * Security Validator
 * 
 * Validates and sanitizes user inputs for the brainstorming system
 */

import DOMPurify from 'isomorphic-dompurify';

export interface ValidationResult {
  isValid: boolean;
  sanitizedValue: string;
  errors: string[];
  warnings: string[];
}

export interface SecurityConfig {
  maxLength: number;
  allowHtml: boolean;
  allowScripts: boolean;
  allowImages: boolean;
  allowLinks: boolean;
  customPatterns?: RegExp[];
}

const DEFAULT_CONFIG: SecurityConfig = {
  maxLength: 10000,
  allowHtml: false,
  allowScripts: false,
  allowImages: true,
  allowLinks: true,
  customPatterns: [],
};

class SecurityValidator {
  private config: SecurityConfig;
  private suspiciousPatterns = [
    /javascript:/gi,
    /on\w+\s*=/gi, // Event handlers
    /<script/gi,
    /<iframe/gi,
    /eval\(/gi,
    /expression\(/gi,
    /vbscript:/gi,
    /data:text\/html/gi,
  ];

  constructor(config: Partial<SecurityConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Validate and sanitize text input
   */
  validateText(input: string, fieldName: string = 'input'): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    let sanitizedValue = input;

    // Check length
    if (input.length > this.config.maxLength) {
      errors.push(`${fieldName} exceeds maximum length of ${this.config.maxLength} characters`);
      sanitizedValue = input.substring(0, this.config.maxLength);
    }

    // Check for suspicious patterns
    for (const pattern of this.suspiciousPatterns) {
      if (pattern.test(input)) {
        warnings.push(`Potentially suspicious content detected in ${fieldName}`);
        sanitizedValue = sanitizedValue.replace(pattern, '');
      }
    }

    // Custom pattern checks
    for (const pattern of this.config.customPatterns || []) {
      if (pattern.test(input)) {
        errors.push(`Invalid content detected in ${fieldName}`);
      }
    }

    // Sanitize HTML if needed
    if (!this.config.allowHtml) {
      sanitizedValue = this.stripHtml(sanitizedValue);
    } else {
      sanitizedValue = this.sanitizeHtml(sanitizedValue);
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue,
      errors,
      warnings,
    };
  }

  /**
   * Validate file upload
   */
  validateFile(file: File): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      errors.push(`File size exceeds maximum of 10MB`);
    }

    // Check file type
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/plain',
      'text/markdown',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];

    if (!allowedTypes.includes(file.type)) {
      errors.push(`File type ${file.type} is not allowed`);
    }

    // Check file extension
    const allowedExtensions = [
      '.jpg', '.jpeg', '.png', '.gif', '.webp',
      '.pdf', '.txt', '.md', '.docx',
    ];
    const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    if (!allowedExtensions.includes(extension)) {
      errors.push(`File extension ${extension} is not allowed`);
    }

    // Check for double extensions
    if ((file.name.match(/\./g) || []).length > 1) {
      warnings.push('File has multiple extensions');
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue: file.name,
      errors,
      warnings,
    };
  }

  /**
   * Validate URL
   */
  validateUrl(url: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    let sanitizedValue = url.trim();

    try {
      const urlObj = new URL(sanitizedValue);
      
      // Check protocol
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        errors.push('Only HTTP and HTTPS protocols are allowed');
      }

      // Check for suspicious patterns in URL
      if (this.suspiciousPatterns.some(pattern => pattern.test(url))) {
        errors.push('URL contains suspicious content');
      }

      // Check for localhost/internal IPs
      const internalPatterns = [
        /^localhost/i,
        /^127\./,
        /^192\.168\./,
        /^10\./,
        /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      ];

      if (internalPatterns.some(pattern => pattern.test(urlObj.hostname))) {
        warnings.push('URL points to internal/local resource');
      }

    } catch (error) {
      errors.push('Invalid URL format');
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue,
      errors,
      warnings,
    };
  }

  /**
   * Validate API key format
   */
  validateApiKey(key: string, provider: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    let isValid = true;

    // Remove whitespace
    const sanitizedValue = key.trim();

    // Provider-specific validation
    switch (provider) {
      case 'jira':
        if (!/^[A-Za-z0-9]{24}$/.test(sanitizedValue)) {
          errors.push('Invalid Jira API token format');
          isValid = false;
        }
        break;
      case 'serper':
        if (!/^[a-f0-9]{40}$/.test(sanitizedValue)) {
          errors.push('Invalid Serper API key format');
          isValid = false;
        }
        break;
      case 'openai':
        if (!sanitizedValue.startsWith('sk-') || sanitizedValue.length < 40) {
          errors.push('Invalid OpenAI API key format');
          isValid = false;
        }
        break;
      default:
        if (sanitizedValue.length < 10) {
          warnings.push('API key seems too short');
        }
    }

    // Check for common mistakes
    if (sanitizedValue.includes(' ')) {
      errors.push('API key contains spaces');
      isValid = false;
    }

    if (sanitizedValue.toLowerCase().includes('your-api-key')) {
      errors.push('Please replace placeholder with actual API key');
      isValid = false;
    }

    return {
      isValid,
      sanitizedValue,
      errors,
      warnings,
    };
  }

  /**
   * Validate multi-modal input
   */
  async validateMultiModalInput(
    type: 'text' | 'image' | 'audio' | 'file',
    data: string | File | Blob
  ): Promise<ValidationResult> {
    switch (type) {
      case 'text':
        return this.validateText(data as string, 'text input');
      
      case 'image':
        if (data instanceof File) {
          return this.validateImageFile(data);
        }
        return this.validateImageData(data as string);
      
      case 'audio':
        return this.validateAudioInput(data);
      
      case 'file':
        return this.validateFile(data as File);
      
      default:
        return {
          isValid: false,
          sanitizedValue: '',
          errors: ['Unknown input type'],
          warnings: [],
        };
    }
  }

  /**
   * Strip HTML tags
   */
  private stripHtml(input: string): string {
    return input.replace(/<[^>]*>/g, '');
  }

  /**
   * Sanitize HTML content
   */
  private sanitizeHtml(input: string): string {
    const config = {
      ALLOWED_TAGS: this.config.allowLinks 
        ? ['b', 'i', 'em', 'strong', 'a', 'p', 'br']
        : ['b', 'i', 'em', 'strong', 'p', 'br'],
      ALLOWED_ATTR: this.config.allowLinks ? ['href', 'target'] : [],
      ALLOW_DATA_ATTR: false,
      FORBID_TAGS: ['script', 'style', 'iframe', 'object', 'embed'],
      FORBID_ATTR: ['onerror', 'onload', 'onclick'],
    };

    return DOMPurify.sanitize(input, config);
  }

  /**
   * Validate image file
   */
  private validateImageFile(file: File): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check image specific constraints
    const maxImageSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxImageSize) {
      errors.push('Image size exceeds 5MB limit');
    }

    const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedImageTypes.includes(file.type)) {
      errors.push('Invalid image format. Allowed: JPEG, PNG, GIF, WebP');
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue: file.name,
      errors,
      warnings,
    };
  }

  /**
   * Validate base64 image data
   */
  private validateImageData(data: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if it's a valid data URL
    const dataUrlPattern = /^data:image\/(jpeg|jpg|png|gif|webp);base64,/;
    if (!dataUrlPattern.test(data)) {
      errors.push('Invalid image data format');
    }

    // Check size (approximate)
    const sizeInBytes = (data.length * 3) / 4;
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (sizeInBytes > maxSize) {
      errors.push('Image data exceeds 5MB limit');
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue: data,
      errors,
      warnings,
    };
  }

  /**
   * Validate audio input
   */
  private validateAudioInput(data: string | File | Blob): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (data instanceof File) {
      const allowedAudioTypes = ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/webm'];
      if (!allowedAudioTypes.includes(data.type)) {
        errors.push('Invalid audio format. Allowed: MP3, WAV, OGG, WebM');
      }

      const maxAudioSize = 10 * 1024 * 1024; // 10MB
      if (data.size > maxAudioSize) {
        errors.push('Audio file exceeds 10MB limit');
      }
    }

    return {
      isValid: errors.length === 0,
      sanitizedValue: data instanceof File ? data.name : 'audio input',
      errors,
      warnings,
    };
  }
}

// Export singleton instance
export const securityValidator = new SecurityValidator();

// Export class for custom configurations
export { SecurityValidator };

// Utility functions
export function escapeHtml(unsafe: string): string {
  return unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

export function sanitizeFilename(filename: string): string {
  // Remove or replace invalid characters
  return filename
    .replace(/[<>:"/\\|?*\x00-\x1F]/g, '_')
    .replace(/\.+/g, '.')
    .trim();
}