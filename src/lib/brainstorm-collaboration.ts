/**
 * Real-time Collaboration System
 * 
 * Enables multiple users to collaborate on brainstorming sessions in real-time
 */

import { ChatMessage, Idea, BrainstormSession } from '@/types/brainstorm';

export interface Collaborator {
  id: string;
  name: string;
  avatar?: string;
  color: string;
  isOnline: boolean;
  lastSeen: string;
  cursor?: {
    x: number;
    y: number;
    timestamp: number;
  };
  currentView?: 'chat' | 'mindmap' | 'kanban' | 'matrix' | 'tasks';
}

export interface CollaborationEvent {
  id: string;
  type: 'join' | 'leave' | 'idea-add' | 'idea-edit' | 'idea-move' | 'message' | 'cursor-move' | 'view-change';
  userId: string;
  sessionId: string;
  timestamp: number;
  data: any;
}

export interface CollaborationSession {
  sessionId: string;
  collaborators: Map<string, Collaborator>;
  events: CollaborationEvent[];
  isLocked: boolean;
  lockedBy?: string;
  lockExpiry?: number;
}

class BrainstormCollaborationSystem {
  private sessions: Map<string, CollaborationSession> = new Map();
  private eventHandlers: Map<string, Function[]> = new Map();
  private websocket: WebSocket | null = null;
  private currentUserId: string = '';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor() {
    this.currentUserId = this.generateUserId();
  }

  /**
   * Initialize collaboration for a session
   */
  initializeSession(sessionId: string, currentUser: Omit<Collaborator, 'isOnline' | 'lastSeen'>): void {
    if (!this.sessions.has(sessionId)) {
      this.sessions.set(sessionId, {
        sessionId,
        collaborators: new Map(),
        events: [],
        isLocked: false,
      });
    }

    const session = this.sessions.get(sessionId)!;
    session.collaborators.set(currentUser.id, {
      ...currentUser,
      isOnline: true,
      lastSeen: new Date().toISOString(),
    });

    this.connectWebSocket(sessionId);
    this.broadcastEvent(sessionId, 'join', { user: currentUser });
  }

  /**
   * Join an existing collaboration session
   */
  joinSession(sessionId: string, user: Omit<Collaborator, 'isOnline' | 'lastSeen'>): void {
    this.currentUserId = user.id;
    this.initializeSession(sessionId, user);
  }

  /**
   * Leave a collaboration session
   */
  leaveSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const collaborator = session.collaborators.get(this.currentUserId);
    if (collaborator) {
      collaborator.isOnline = false;
      collaborator.lastSeen = new Date().toISOString();
    }

    this.broadcastEvent(sessionId, 'leave', { userId: this.currentUserId });
  }

  /**
   * Broadcast an idea addition
   */
  broadcastIdeaAdd(sessionId: string, idea: Idea): void {
    this.broadcastEvent(sessionId, 'idea-add', { idea });
  }

  /**
   * Broadcast an idea edit
   */
  broadcastIdeaEdit(sessionId: string, ideaId: string, changes: Partial<Idea>): void {
    this.broadcastEvent(sessionId, 'idea-edit', { ideaId, changes });
  }

  /**
   * Broadcast an idea move (for kanban/mindmap)
   */
  broadcastIdeaMove(sessionId: string, ideaId: string, newPosition: any): void {
    this.broadcastEvent(sessionId, 'idea-move', { ideaId, newPosition });
  }

  /**
   * Broadcast a chat message
   */
  broadcastMessage(sessionId: string, message: ChatMessage): void {
    this.broadcastEvent(sessionId, 'message', { message });
  }

  /**
   * Broadcast cursor movement
   */
  broadcastCursor(sessionId: string, x: number, y: number): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const collaborator = session.collaborators.get(this.currentUserId);
    if (collaborator) {
      collaborator.cursor = { x, y, timestamp: Date.now() };
    }

    // Throttle cursor events to avoid spam
    this.throttle(() => {
      this.broadcastEvent(sessionId, 'cursor-move', { x, y, userId: this.currentUserId });
    }, 100);
  }

  /**
   * Broadcast view change
   */
  broadcastViewChange(sessionId: string, view: Collaborator['currentView']): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const collaborator = session.collaborators.get(this.currentUserId);
    if (collaborator) {
      collaborator.currentView = view;
    }

    this.broadcastEvent(sessionId, 'view-change', { view, userId: this.currentUserId });
  }

  /**
   * Lock session for exclusive editing
   */
  lockSession(sessionId: string, duration: number = 5000): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    if (session.isLocked && session.lockExpiry && Date.now() < session.lockExpiry) {
      return false; // Already locked
    }

    session.isLocked = true;
    session.lockedBy = this.currentUserId;
    session.lockExpiry = Date.now() + duration;

    this.broadcastEvent(sessionId, 'session-lock', {
      lockedBy: this.currentUserId,
      lockExpiry: session.lockExpiry,
    });

    // Auto-unlock after duration
    setTimeout(() => {
      this.unlockSession(sessionId);
    }, duration);

    return true;
  }

  /**
   * Unlock session
   */
  unlockSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.isLocked = false;
    session.lockedBy = undefined;
    session.lockExpiry = undefined;

    this.broadcastEvent(sessionId, 'session-unlock', {});
  }

  /**
   * Get online collaborators for a session
   */
  getOnlineCollaborators(sessionId: string): Collaborator[] {
    const session = this.sessions.get(sessionId);
    if (!session) return [];

    return Array.from(session.collaborators.values()).filter(c => c.isOnline);
  }

  /**
   * Get collaboration activity feed
   */
  getActivityFeed(sessionId: string, limit: number = 20): CollaborationEvent[] {
    const session = this.sessions.get(sessionId);
    if (!session) return [];

    return session.events
      .filter(event => ['idea-add', 'idea-edit', 'message', 'join', 'leave'].includes(event.type))
      .slice(-limit);
  }

  /**
   * Add event listener for collaboration events
   */
  addEventListener(eventType: string, handler: Function): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  /**
   * Remove event listener
   */
  removeEventListener(eventType: string, handler: Function): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Generate conflict resolution for simultaneous edits
   */
  resolveConflict(
    sessionId: string,
    conflictingChanges: { userId: string; changes: any; timestamp: number }[]
  ): any {
    // Simple last-writer-wins resolution
    const sortedChanges = conflictingChanges.sort((a, b) => b.timestamp - a.timestamp);
    const winningChange = sortedChanges[0];

    // Notify about conflict resolution
    this.broadcastEvent(sessionId, 'conflict-resolved', {
      winner: winningChange.userId,
      conflicts: conflictingChanges.length,
    });

    return winningChange.changes;
  }

  // Private methods

  private generateUserId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private connectWebSocket(sessionId: string): void {
    // In a real implementation, this would connect to a WebSocket server
    // For now, we'll simulate with local events
    console.log(`Connecting to collaboration server for session ${sessionId}`);

    // Simulate WebSocket connection
    this.websocket = {
      send: (data: string) => {
        console.log('Sending collaboration event:', data);
      },
      close: () => {
        console.log('Closing collaboration connection');
      }
    } as any;
  }

  private broadcastEvent(sessionId: string, type: CollaborationEvent['type'], data: any): void {
    const event: CollaborationEvent = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      userId: this.currentUserId,
      sessionId,
      timestamp: Date.now(),
      data,
    };

    const session = this.sessions.get(sessionId);
    if (session) {
      session.events.push(event);
      
      // Keep only last 1000 events to prevent memory issues
      if (session.events.length > 1000) {
        session.events = session.events.slice(-1000);
      }
    }

    // Emit to local event handlers
    this.emitEvent(type, event);

    // Send to WebSocket server
    if (this.websocket) {
      this.websocket.send(JSON.stringify(event));
    }
  }

  private emitEvent(eventType: string, event: CollaborationEvent): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(event);
        } catch (error) {
          console.error('Error in collaboration event handler:', error);
        }
      });
    }

    // Also emit to 'all' event handlers
    const allHandlers = this.eventHandlers.get('all');
    if (allHandlers) {
      allHandlers.forEach(handler => {
        try {
          handler(event);
        } catch (error) {
          console.error('Error in collaboration event handler:', error);
        }
      });
    }
  }

  private throttleTimers: Map<string, number> = new Map();

  private throttle(func: Function, delay: number): void {
    const key = func.toString();
    const existingTimer = this.throttleTimers.get(key);
    
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    const timer = window.setTimeout(() => {
      func();
      this.throttleTimers.delete(key);
    }, delay);

    this.throttleTimers.set(key, timer);
  }
}

// Export singleton instance
export const brainstormCollaboration = new BrainstormCollaborationSystem();

// Utility functions for UI components

export function generateCollaboratorColor(userId: string): string {
  const colors = [
    '#8B5CF6', // Violet
    '#3B82F6', // Blue
    '#10B981', // Emerald
    '#F59E0B', // Amber
    '#EF4444', // Red
    '#EC4899', // Pink
    '#14B8A6', // Teal
    '#6366F1', // Indigo
  ];
  
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = userId.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  return colors[Math.abs(hash) % colors.length];
}

export function getRelativeTime(timestamp: string): string {
  const now = Date.now();
  const time = new Date(timestamp).getTime();
  const diff = now - time;
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) return `${days}d ago`;
  if (hours > 0) return `${hours}h ago`;
  if (minutes > 0) return `${minutes}m ago`;
  return 'just now';
}