/**
 * Project Management Integration
 * 
 * Connects brainstorming sessions with external project management tools
 */

import { Idea, GeneratedTask, BrainstormSession } from '@/types/brainstorm';
import { exportBrainstormSession, ExportFormat } from './brainstorm-export';

export interface ProjectManagementProvider {
  id: string;
  name: string;
  icon: string;
  isConnected: boolean;
  authenticate(): Promise<boolean>;
  disconnect(): void;
  createTask(task: TaskData): Promise<string>;
  createProject(project: ProjectData): Promise<string>;
  getProjects(): Promise<ProjectSummary[]>;
  syncTasks(sessionId: string, tasks: GeneratedTask[]): Promise<SyncResult>;
}

export interface TaskData {
  title: string;
  description?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'todo' | 'in_progress' | 'done';
  tags: string[];
  assignee?: string;
  dueDate?: string;
  estimatedHours?: number;
  metadata?: Record<string, any>;
}

export interface ProjectData {
  name: string;
  description?: string;
  tags: string[];
  template?: string;
  members?: string[];
}

export interface ProjectSummary {
  id: string;
  name: string;
  description?: string;
  taskCount: number;
  completedTasks: number;
  members: number;
  lastUpdated: string;
}

export interface SyncResult {
  created: number;
  updated: number;
  failed: number;
  errors: string[];
}

// GitHub Integration
class GitHubProjectProvider implements ProjectManagementProvider {
  id = 'github';
  name = 'GitHub Projects';
  icon = '🐙';
  isConnected = false;
  private accessToken: string | null = null;

  async authenticate(): Promise<boolean> {
    // In a real implementation, this would handle OAuth flow
    const token = prompt('Enter GitHub Personal Access Token:');
    if (token) {
      this.accessToken = token;
      this.isConnected = true;
      localStorage.setItem('github_token', token);
      return true;
    }
    return false;
  }

  disconnect(): void {
    this.accessToken = null;
    this.isConnected = false;
    localStorage.removeItem('github_token');
  }

  async createTask(task: TaskData): Promise<string> {
    if (!this.accessToken) throw new Error('Not authenticated');
    
    // Create GitHub issue
    const issue = {
      title: task.title,
      body: task.description || '',
      labels: task.tags,
    };

    // Mock API call
    console.log('Creating GitHub issue:', issue);
    return `github_issue_${Date.now()}`;
  }

  async createProject(project: ProjectData): Promise<string> {
    if (!this.accessToken) throw new Error('Not authenticated');
    
    // Create GitHub repository or project
    console.log('Creating GitHub project:', project);
    return `github_project_${Date.now()}`;
  }

  async getProjects(): Promise<ProjectSummary[]> {
    if (!this.accessToken) return [];
    
    // Mock projects data
    return [
      {
        id: 'proj_1',
        name: 'Web Application',
        description: 'Main web application project',
        taskCount: 25,
        completedTasks: 15,
        members: 4,
        lastUpdated: new Date().toISOString(),
      }
    ];
  }

  async syncTasks(sessionId: string, tasks: GeneratedTask[]): Promise<SyncResult> {
    const result: SyncResult = { created: 0, updated: 0, failed: 0, errors: [] };
    
    for (const task of tasks) {
      try {
        await this.createTask({
          title: task.title,
          description: task.description,
          priority: task.priority,
          status: task.status === 'completed' ? 'done' : 'todo',
          tags: task.tags,
          estimatedHours: task.estimatedEffort,
        });
        result.created++;
      } catch (error) {
        result.failed++;
        result.errors.push(`Failed to create task "${task.title}": ${error}`);
      }
    }
    
    return result;
  }
}

// Jira Integration
class JiraProvider implements ProjectManagementProvider {
  id = 'jira';
  name = 'Jira';
  icon = '🔷';
  isConnected = false;
  private email: string | null = null;
  private apiToken: string | null = null;
  private domain: string | null = null;
  private projectKey: string | null = null;

  constructor() {
    // Try to restore credentials from localStorage
    this.domain = localStorage.getItem('jira_domain');
    this.email = localStorage.getItem('jira_email');
    this.apiToken = localStorage.getItem('jira_api_token');
    this.projectKey = localStorage.getItem('jira_project_key');
    this.isConnected = !!(this.domain && this.email && this.apiToken);
  }

  async authenticate(): Promise<boolean> {
    const domain = prompt('Enter Jira domain (e.g., company.atlassian.net):');
    const email = prompt('Enter your Jira email:');
    const apiToken = prompt('Enter Jira API Token (get from https://id.atlassian.com/manage/api-tokens):');
    const projectKey = prompt('Enter default project key (e.g., PROJ):');
    
    if (domain && email && apiToken && projectKey) {
      this.domain = domain;
      this.email = email;
      this.apiToken = apiToken;
      this.projectKey = projectKey;
      
      // Test connection
      try {
        await this.testConnection();
        this.isConnected = true;
        
        // Save credentials
        localStorage.setItem('jira_domain', domain);
        localStorage.setItem('jira_email', email);
        localStorage.setItem('jira_api_token', apiToken);
        localStorage.setItem('jira_project_key', projectKey);
        
        return true;
      } catch (error) {
        console.error('Jira authentication failed:', error);
        alert('Failed to connect to Jira. Please check your credentials.');
        return false;
      }
    }
    return false;
  }

  disconnect(): void {
    this.domain = null;
    this.email = null;
    this.apiToken = null;
    this.projectKey = null;
    this.isConnected = false;
    localStorage.removeItem('jira_domain');
    localStorage.removeItem('jira_email');
    localStorage.removeItem('jira_api_token');
    localStorage.removeItem('jira_project_key');
  }

  private getAuthHeader(): string {
    return 'Basic ' + btoa(`${this.email}:${this.apiToken}`);
  }

  private getApiUrl(endpoint: string): string {
    return `https://${this.domain}/rest/api/3/${endpoint}`;
  }

  private async testConnection(): Promise<void> {
    const response = await fetch(this.getApiUrl('myself'), {
      headers: {
        'Authorization': this.getAuthHeader(),
        'Accept': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error('Authentication failed');
    }
  }

  async createTask(task: TaskData): Promise<string> {
    if (!this.apiToken || !this.domain || !this.email) {
      throw new Error('Not authenticated');
    }
    
    // Map priority levels
    const priorityMap: Record<string, string> = {
      'critical': 'Highest',
      'high': 'High',
      'medium': 'Medium',
      'low': 'Low',
    };
    
    const issue = {
      fields: {
        project: { key: this.projectKey },
        summary: task.title,
        description: {
          type: 'doc',
          version: 1,
          content: [
            {
              type: 'paragraph',
              content: [
                {
                  type: 'text',
                  text: task.description || 'Created from brainstorming session',
                },
              ],
            },
          ],
        },
        issuetype: { name: 'Task' },
        priority: { name: priorityMap[task.priority] || 'Medium' },
        labels: task.tags,
      },
    };

    try {
      const response = await fetch(this.getApiUrl('issue'), {
        method: 'POST',
        headers: {
          'Authorization': this.getAuthHeader(),
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(issue),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.errors ? JSON.stringify(errorData.errors) : response.statusText);
      }

      const data = await response.json();
      return data.key;
    } catch (error) {
      console.error('Failed to create Jira issue:', error);
      throw error;
    }
  }

  async createProject(project: ProjectData): Promise<string> {
    // Note: Creating projects requires admin permissions
    // For now, we'll use the default project
    console.log('Using default project for:', project);
    return this.projectKey || 'PROJECT';
  }

  async getProjects(): Promise<ProjectSummary[]> {
    if (!this.apiToken || !this.domain || !this.email) return [];
    
    try {
      const response = await fetch(this.getApiUrl('project/search'), {
        headers: {
          'Authorization': this.getAuthHeader(),
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch projects');
      }

      const data = await response.json();
      
      return data.values.map((project: any) => ({
        id: project.key,
        name: project.name,
        description: project.description || '',
        taskCount: 0, // Would need separate API call to get issue counts
        completedTasks: 0,
        members: 0,
        lastUpdated: new Date().toISOString(),
      }));
    } catch (error) {
      console.error('Failed to fetch Jira projects:', error);
      return [];
    }
  }

  async syncTasks(sessionId: string, tasks: GeneratedTask[]): Promise<SyncResult> {
    const result: SyncResult = { created: 0, updated: 0, failed: 0, errors: [] };
    
    // Create tasks in batches to avoid rate limiting
    const batchSize = 5;
    for (let i = 0; i < tasks.length; i += batchSize) {
      const batch = tasks.slice(i, i + batchSize);
      
      await Promise.all(
        batch.map(async (task) => {
          try {
            await this.createTask({
              title: task.title,
              description: task.description,
              priority: task.priority,
              status: task.status === 'completed' ? 'done' : 'todo',
              tags: task.tags,
              estimatedHours: task.estimatedEffort,
            });
            result.created++;
          } catch (error) {
            result.failed++;
            result.errors.push(`Failed to create task "${task.title}": ${error}`);
          }
        })
      );
      
      // Add delay between batches to respect rate limits
      if (i + batchSize < tasks.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    return result;
  }
}

// Asana Integration
class AsanaProvider implements ProjectManagementProvider {
  id = 'asana';
  name = 'Asana';
  icon = '🎯';
  isConnected = false;
  private accessToken: string | null = null;
  private workspaceGid: string | null = null;
  private projectGid: string | null = null;

  constructor() {
    // Try to restore credentials from localStorage
    this.accessToken = localStorage.getItem('asana_access_token');
    this.workspaceGid = localStorage.getItem('asana_workspace_gid');
    this.projectGid = localStorage.getItem('asana_project_gid');
    this.isConnected = !!this.accessToken;
  }

  async authenticate(): Promise<boolean> {
    const token = prompt('Enter Asana Personal Access Token (get from https://app.asana.com/0/developer-console):');
    
    if (!token) return false;
    
    this.accessToken = token;

    try {
      // Test connection and get workspace
      const workspaces = await this.getWorkspaces();
      if (workspaces.length === 0) {
        throw new Error('No workspaces found');
      }

      // If multiple workspaces, let user choose
      let workspace = workspaces[0];
      if (workspaces.length > 1) {
        const workspaceNames = workspaces.map(w => w.name).join('\n');
        const selectedName = prompt(`Select workspace:\n${workspaceNames}`);
        workspace = workspaces.find(w => w.name === selectedName) || workspaces[0];
      }
      
      this.workspaceGid = workspace.gid;
      
      // Get projects in workspace
      const projects = await this.getProjectsInWorkspace(workspace.gid);
      if (projects.length > 0) {
        const projectNames = projects.map(p => p.name).join('\n');
        const selectedProject = prompt(`Select default project:\n${projectNames}`);
        const project = projects.find(p => p.name === selectedProject);
        if (project) {
          this.projectGid = project.gid;
        }
      }
      
      // Save credentials
      localStorage.setItem('asana_access_token', token);
      localStorage.setItem('asana_workspace_gid', this.workspaceGid);
      if (this.projectGid) {
        localStorage.setItem('asana_project_gid', this.projectGid);
      }
      
      this.isConnected = true;
      return true;
    } catch (error) {
      console.error('Asana authentication failed:', error);
      alert('Failed to connect to Asana. Please check your access token.');
      this.accessToken = null;
      return false;
    }
  }

  disconnect(): void {
    this.accessToken = null;
    this.workspaceGid = null;
    this.projectGid = null;
    this.isConnected = false;
    localStorage.removeItem('asana_access_token');
    localStorage.removeItem('asana_workspace_gid');
    localStorage.removeItem('asana_project_gid');
  }

  private async asanaRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    if (!this.accessToken) throw new Error('Not authenticated');

    const response = await fetch(`https://app.asana.com/api/1.0${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.errors?.[0]?.message || response.statusText);
    }

    const data = await response.json();
    return data.data;
  }

  private async getWorkspaces() {
    return this.asanaRequest('/workspaces');
  }

  private async getProjectsInWorkspace(workspaceGid: string) {
    return this.asanaRequest(`/workspaces/${workspaceGid}/projects`);
  }

  async createTask(task: TaskData): Promise<string> {
    if (!this.accessToken || !this.workspaceGid) {
      throw new Error('Not authenticated');
    }

    const asanaTask = {
      data: {
        name: task.title,
        notes: task.description || '',
        projects: this.projectGid ? [this.projectGid] : undefined,
        workspace: this.workspaceGid,
        tags: task.tags,
        due_on: task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : undefined,
        custom_fields: task.estimatedHours ? {
          'effort_hours': task.estimatedHours
        } : undefined,
      },
    };

    const result = await this.asanaRequest('/tasks', {
      method: 'POST',
      body: JSON.stringify(asanaTask),
    });

    return result.gid;
  }

  async createProject(project: ProjectData): Promise<string> {
    if (!this.accessToken || !this.workspaceGid) {
      throw new Error('Not authenticated');
    }

    const asanaProject = {
      data: {
        name: project.name,
        notes: project.description || '',
        workspace: this.workspaceGid,
        team: project.template, // Optional team GID
      },
    };

    const result = await this.asanaRequest('/projects', {
      method: 'POST',
      body: JSON.stringify(asanaProject),
    });

    return result.gid;
  }

  async getProjects(): Promise<ProjectSummary[]> {
    if (!this.accessToken || !this.workspaceGid) return [];

    try {
      const projects = await this.getProjectsInWorkspace(this.workspaceGid);
      
      // Get additional details for each project
      const projectDetails = await Promise.all(
        projects.map(async (project: any) => {
          try {
            const details = await this.asanaRequest(`/projects/${project.gid}`);
            const tasks = await this.asanaRequest(`/projects/${project.gid}/tasks?opt_fields=completed`);
            
            return {
              id: project.gid,
              name: project.name,
              description: details.notes || '',
              taskCount: tasks.length,
              completedTasks: tasks.filter((t: any) => t.completed).length,
              members: details.members?.length || 0,
              lastUpdated: details.modified_at || new Date().toISOString(),
            };
          } catch {
            return {
              id: project.gid,
              name: project.name,
              description: '',
              taskCount: 0,
              completedTasks: 0,
              members: 0,
              lastUpdated: new Date().toISOString(),
            };
          }
        })
      );

      return projectDetails;
    } catch (error) {
      console.error('Failed to fetch Asana projects:', error);
      return [];
    }
  }

  async syncTasks(sessionId: string, tasks: GeneratedTask[]): Promise<SyncResult> {
    const result: SyncResult = { created: 0, updated: 0, failed: 0, errors: [] };
    
    // Create tasks in batches
    const batchSize = 10;
    for (let i = 0; i < tasks.length; i += batchSize) {
      const batch = tasks.slice(i, i + batchSize);
      
      await Promise.all(
        batch.map(async (task) => {
          try {
            await this.createTask({
              title: task.title,
              description: task.description,
              priority: task.priority,
              status: task.status === 'completed' ? 'done' : 'todo',
              tags: task.tags,
              estimatedHours: task.estimatedEffort,
            });
            result.created++;
          } catch (error) {
            result.failed++;
            result.errors.push(`Failed to create task "${task.title}": ${error}`);
          }
        })
      );
      
      // Add delay between batches to respect rate limits
      if (i + batchSize < tasks.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    return result;
  }
}

// Trello Integration
class TrelloProvider implements ProjectManagementProvider {
  id = 'trello';
  name = 'Trello';
  icon = '📋';
  isConnected = false;
  private apiKey: string | null = null;
  private apiToken: string | null = null;
  private boardId: string | null = null;

  constructor() {
    this.apiKey = localStorage.getItem('trello_api_key');
    this.apiToken = localStorage.getItem('trello_api_token');
    this.boardId = localStorage.getItem('trello_board_id');
    this.isConnected = !!(this.apiKey && this.apiToken);
  }

  async authenticate(): Promise<boolean> {
    const apiKey = prompt('Enter Trello API Key (get from https://trello.com/app-key):');
    if (!apiKey) return false;

    // Generate token URL
    const tokenUrl = `https://trello.com/1/authorize?expiration=never&name=BrainstormingApp&scope=read,write&response_type=token&key=${apiKey}`;
    alert(`Please visit this URL to get your token:\n${tokenUrl}`);
    
    const apiToken = prompt('Enter the token from Trello:');
    if (!apiToken) return false;

    this.apiKey = apiKey;
    this.apiToken = apiToken;

    try {
      // Test connection
      const boards = await this.getBoards();
      if (boards.length > 0) {
        const boardNames = boards.map((b: any) => b.name).join('\n');
        const selectedBoard = prompt(`Select default board:\n${boardNames}`);
        const board = boards.find((b: any) => b.name === selectedBoard);
        if (board) {
          this.boardId = board.id;
        }
      }

      // Save credentials
      localStorage.setItem('trello_api_key', apiKey);
      localStorage.setItem('trello_api_token', apiToken);
      if (this.boardId) {
        localStorage.setItem('trello_board_id', this.boardId);
      }

      this.isConnected = true;
      return true;
    } catch (error) {
      console.error('Trello authentication failed:', error);
      this.apiKey = null;
      this.apiToken = null;
      return false;
    }
  }

  disconnect(): void {
    this.apiKey = null;
    this.apiToken = null;
    this.boardId = null;
    this.isConnected = false;
    localStorage.removeItem('trello_api_key');
    localStorage.removeItem('trello_api_token');
    localStorage.removeItem('trello_board_id');
  }

  private async trelloRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    if (!this.apiKey || !this.apiToken) throw new Error('Not authenticated');

    const url = new URL(`https://api.trello.com/1${endpoint}`);
    url.searchParams.append('key', this.apiKey);
    url.searchParams.append('token', this.apiToken);

    const response = await fetch(url.toString(), options);
    
    if (!response.ok) {
      throw new Error(response.statusText);
    }

    return response.json();
  }

  private async getBoards() {
    return this.trelloRequest('/members/me/boards');
  }

  async createTask(task: TaskData): Promise<string> {
    if (!this.boardId) throw new Error('No board selected');

    // Get lists on the board
    const lists = await this.trelloRequest(`/boards/${this.boardId}/lists`);
    const todoList = lists.find((l: any) => l.name.toLowerCase() === 'to do') || lists[0];

    if (!todoList) throw new Error('No lists found on board');

    const card = await this.trelloRequest('/cards', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: task.title,
        desc: task.description || '',
        idList: todoList.id,
        due: task.dueDate,
        labels: task.tags.join(','),
      }),
    });

    return card.id;
  }

  async createProject(project: ProjectData): Promise<string> {
    const board = await this.trelloRequest('/boards', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: project.name,
        desc: project.description || '',
      }),
    });

    return board.id;
  }

  async getProjects(): Promise<ProjectSummary[]> {
    try {
      const boards = await this.getBoards();
      
      return boards.map((board: any) => ({
        id: board.id,
        name: board.name,
        description: board.desc || '',
        taskCount: 0,
        completedTasks: 0,
        members: board.memberships?.length || 0,
        lastUpdated: board.dateLastActivity || new Date().toISOString(),
      }));
    } catch (error) {
      console.error('Failed to fetch Trello boards:', error);
      return [];
    }
  }

  async syncTasks(sessionId: string, tasks: GeneratedTask[]): Promise<SyncResult> {
    const result: SyncResult = { created: 0, updated: 0, failed: 0, errors: [] };
    
    for (const task of tasks) {
      try {
        await this.createTask({
          title: task.title,
          description: task.description,
          priority: task.priority,
          status: 'todo',
          tags: task.tags,
        });
        result.created++;
      } catch (error) {
        result.failed++;
        result.errors.push(`Failed to create task "${task.title}": ${error}`);
      }
    }
    
    return result;
  }
}

class ProjectManagementIntegration {
  private providers: Map<string, ProjectManagementProvider> = new Map();
  private activeProvider: ProjectManagementProvider | null = null;

  constructor() {
    // Register built-in providers
    this.registerProvider(new GitHubProjectProvider());
    this.registerProvider(new JiraProvider());
    this.registerProvider(new TrelloProvider());
  }

  /**
   * Register a new project management provider
   */
  registerProvider(provider: ProjectManagementProvider): void {
    this.providers.set(provider.id, provider);
  }

  /**
   * Get all available providers
   */
  getProviders(): ProjectManagementProvider[] {
    return Array.from(this.providers.values());
  }

  /**
   * Connect to a provider
   */
  async connectProvider(providerId: string): Promise<boolean> {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    const connected = await provider.authenticate();
    if (connected) {
      this.activeProvider = provider;
    }
    return connected;
  }

  /**
   * Disconnect from current provider
   */
  disconnect(): void {
    if (this.activeProvider) {
      this.activeProvider.disconnect();
      this.activeProvider = null;
    }
  }

  /**
   * Get current provider
   */
  getActiveProvider(): ProjectManagementProvider | null {
    return this.activeProvider;
  }

  /**
   * Convert brainstorming ideas to project tasks
   */
  convertIdeasToTasks(ideas: Idea[]): TaskData[] {
    return ideas
      .filter(idea => idea.status === 'validated' || idea.priority === 'high' || idea.priority === 'critical')
      .map(idea => ({
        title: idea.content,
        description: idea.metadata?.description || '',
        priority: idea.priority || 'medium',
        status: 'todo' as const,
        tags: idea.tags,
        metadata: {
          sourceIdeaId: idea.id,
          brainstormGenerated: true,
        }
      }));
  }

  /**
   * Create a project from a brainstorming session
   */
  async createProjectFromSession(
    session: BrainstormSession,
    ideas: Idea[],
    tasks: GeneratedTask[]
  ): Promise<string> {
    if (!this.activeProvider) {
      throw new Error('No active project management provider');
    }

    const projectData: ProjectData = {
      name: session.title,
      description: `Project created from brainstorming session on ${new Date(session.createdAt).toLocaleDateString()}`,
      tags: Array.from(new Set(ideas.flatMap(idea => idea.tags))),
    };

    const projectId = await this.activeProvider.createProject(projectData);

    // Sync tasks if any
    if (tasks.length > 0) {
      await this.activeProvider.syncTasks(session.id, tasks);
    }

    return projectId;
  }

  /**
   * Export and sync session with project management tool
   */
  async exportAndSync(
    session: BrainstormSession,
    ideas: Idea[],
    tasks: GeneratedTask[],
    options: {
      createProject?: boolean;
      syncTasks?: boolean;
      exportFormat?: ExportFormat;
    } = {}
  ): Promise<{
    projectId?: string;
    syncResult?: SyncResult;
    exportBlob?: Blob;
  }> {
    if (!this.activeProvider) {
      throw new Error('No active project management provider');
    }

    const result: any = {};

    // Create project if requested
    if (options.createProject) {
      result.projectId = await this.createProjectFromSession(session, ideas, tasks);
    }

    // Sync tasks if requested
    if (options.syncTasks && tasks.length > 0) {
      result.syncResult = await this.activeProvider.syncTasks(session.id, tasks);
    }

    // Export session if format specified
    if (options.exportFormat) {
      result.exportBlob = await exportBrainstormSession(
        session,
        ideas,
        [], // clusters
        [], // relationships
        tasks,
        { format: options.exportFormat }
      );
    }

    return result;
  }

  /**
   * Get integration status
   */
  getStatus(): {
    isConnected: boolean;
    providerName?: string;
    projectCount?: number;
  } {
    if (!this.activeProvider) {
      return { isConnected: false };
    }

    return {
      isConnected: this.activeProvider.isConnected,
      providerName: this.activeProvider.name,
    };
  }

  /**
   * Test connection to current provider
   */
  async testConnection(): Promise<boolean> {
    if (!this.activeProvider) return false;
    
    try {
      await this.activeProvider.getProjects();
      return true;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const projectManagementIntegration = new ProjectManagementIntegration();