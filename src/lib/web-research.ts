/**
 * Web Research Integration Service
 * 
 * Provides AI-enhanced web research capabilities for brainstorming sessions
 */

import { api } from '@/lib/api';

export interface ResearchQuery {
  id: string;
  query: string;
  context: string;
  sessionId: string;
  approved: boolean;
  createdAt: string;
}

export interface ResearchResult {
  id: string;
  queryId: string;
  title: string;
  url: string;
  snippet: string;
  summary: string;
  relevanceScore: number;
  source: string;
  timestamp: string;
  cached: boolean;
}

export interface ResearchSummary {
  id: string;
  queryId: string;
  overallSummary: string;
  keyInsights: string[];
  sources: string[];
  confidence: number;
  createdAt: string;
}

// Environment variable for API key
const SERPER_API_KEY = import.meta.env.VITE_SERPER_API_KEY || '';
const SEARCH_API_URL = 'https://google.serper.dev/search';

class WebResearchService {
  private cache = new Map<string, ResearchResult[]>();
  private pendingQueries = new Map<string, ResearchQuery>();
  private approvedQueries = new Set<string>();
  private resultsCache = new Map<string, { results: ResearchResult[], timestamp: number }>();
  private CACHE_DURATION = 15 * 60 * 1000; // 15 minutes

  /**
   * Request research for a given topic with user approval
   */
  async requestResearch(
    query: string,
    context: string,
    sessionId: string,
    autoApprove = false
  ): Promise<string> {
    const queryId = this.generateId();
    const researchQuery: ResearchQuery = {
      id: queryId,
      query,
      context,
      sessionId,
      approved: autoApprove,
      createdAt: new Date().toISOString(),
    };

    this.pendingQueries.set(queryId, researchQuery);

    if (autoApprove) {
      this.approvedQueries.add(queryId);
      await this.executeResearch(queryId);
    }

    return queryId;
  }

  /**
   * Approve a pending research query
   */
  async approveResearch(queryId: string): Promise<void> {
    const query = this.pendingQueries.get(queryId);
    if (!query) {
      throw new Error('Query not found');
    }

    this.approvedQueries.add(queryId);
    query.approved = true;
    await this.executeResearch(queryId);
  }

  /**
   * Reject a pending research query
   */
  rejectResearch(queryId: string): void {
    this.pendingQueries.delete(queryId);
  }

  /**
   * Execute approved research query
   */
  private async executeResearch(queryId: string): Promise<ResearchResult[]> {
    const query = this.pendingQueries.get(queryId);
    if (!query || !this.approvedQueries.has(queryId)) {
      throw new Error('Query not approved or not found');
    }

    // Check cache first
    const cacheKey = this.getCacheKey(query.query, query.context);
    const cached = this.resultsCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.results;
    }

    try {
      // Perform actual web search
      const results = await this.performWebSearch(query);
      
      // Cache results with timestamp
      this.resultsCache.set(cacheKey, {
        results,
        timestamp: Date.now()
      });
      
      return results;
    } catch (error) {
      console.error('Research failed:', error);
      throw new Error('Failed to perform web research');
    }
  }

  /**
   * Perform actual web search using Serper API
   */
  private async performWebSearch(query: ResearchQuery): Promise<ResearchResult[]> {
    // If no API key, fall back to mock results
    if (!SERPER_API_KEY) {
      console.warn('No Serper API key found, using mock results');
      return this.getMockResults(query);
    }

    try {
      // Enhance query with context
      const searchQuery = `${query.query} ${query.context}`;
      
      const response = await fetch(SEARCH_API_URL, {
        method: 'POST',
        headers: {
          'X-API-KEY': SERPER_API_KEY,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: searchQuery,
          num: 10,
          autocorrect: true,
          hl: 'en',
          gl: 'us',
        }),
      });

      if (!response.ok) {
        throw new Error(`Search API error: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Transform Serper results to our format
      const results: ResearchResult[] = [];
      
      // Process organic results
      if (data.organic) {
        for (const item of data.organic) {
          results.push({
            id: this.generateId(),
            queryId: query.id,
            title: item.title,
            url: item.link,
            snippet: item.snippet || '',
            summary: await this.generateItemSummary(item, query.context),
            relevanceScore: this.calculateRelevance(item, query),
            source: this.extractSource(item.link),
            timestamp: new Date().toISOString(),
            cached: false,
          });
        }
      }
      
      // Process answer box if available
      if (data.answerBox) {
        results.unshift({
          id: this.generateId(),
          queryId: query.id,
          title: data.answerBox.title || 'Quick Answer',
          url: data.answerBox.link || '#',
          snippet: data.answerBox.snippet || data.answerBox.answer || '',
          summary: data.answerBox.answer || data.answerBox.snippet || '',
          relevanceScore: 0.95,
          source: 'Featured Answer',
          timestamp: new Date().toISOString(),
          cached: false,
        });
      }
      
      // Sort by relevance
      return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
      
    } catch (error) {
      console.error('Serper API error:', error);
      // Fall back to mock results
      return this.getMockResults(query);
    }
  }

  /**
   * Get mock results for development/fallback
   */
  private getMockResults(query: ResearchQuery): ResearchResult[] {
    return [
      {
        id: this.generateId(),
        queryId: query.id,
        title: `Research on ${query.query}`,
        url: `https://example.com/research/${encodeURIComponent(query.query)}`,
        snippet: `Comprehensive overview of ${query.query} including key concepts, methodologies, and recent developments in the field.`,
        summary: `This source provides detailed insights into ${query.query}, covering fundamental principles and practical applications relevant to ${query.context}.`,
        relevanceScore: 0.9,
        source: 'Academic Journal',
        timestamp: new Date().toISOString(),
        cached: false,
      },
      {
        id: this.generateId(),
        queryId: query.id,
        title: `Best Practices for ${query.query}`,
        url: `https://example.com/best-practices/${encodeURIComponent(query.query)}`,
        snippet: `Industry best practices and proven methodologies for implementing ${query.query} in real-world scenarios.`,
        summary: `Practical guide offering actionable insights and proven strategies for ${query.query} implementation.`,
        relevanceScore: 0.85,
        source: 'Industry Report',
        timestamp: new Date().toISOString(),
        cached: false,
      },
    ];
  }

  /**
   * Generate research summary from results
   */
  async generateSummary(queryId: string): Promise<ResearchSummary> {
    const query = this.pendingQueries.get(queryId);
    if (!query) {
      throw new Error('Query not found');
    }

    const cacheKey = this.getCacheKey(query.query, query.context);
    const results = this.cache.get(cacheKey) || [];

    if (results.length === 0) {
      throw new Error('No research results available');
    }

    // Use AI to generate comprehensive summary
    const summaryPrompt = `
Based on the following research results for "${query.query}" in the context of "${query.context}":

${results.map((result, i) => `
${i + 1}. ${result.title}
   Source: ${result.source}
   Summary: ${result.summary}
   Relevance: ${result.relevanceScore}
`).join('\n')}

Please provide:
1. An overall summary (2-3 sentences)
2. 3-5 key insights
3. Confidence level (0-1)

Format as JSON:
{
  "overallSummary": "...",
  "keyInsights": ["...", "...", "..."],
  "confidence": 0.85
}
    `;

    try {
      // In production, call actual AI API
      const aiResponse = await this.callAI(summaryPrompt);
      const parsed = JSON.parse(aiResponse);

      const summary: ResearchSummary = {
        id: this.generateId(),
        queryId,
        overallSummary: parsed.overallSummary,
        keyInsights: parsed.keyInsights,
        sources: results.map(r => r.url),
        confidence: parsed.confidence,
        createdAt: new Date().toISOString(),
      };

      return summary;
    } catch (error) {
      // Fallback summary
      return {
        id: this.generateId(),
        queryId,
        overallSummary: `Research on ${query.query} yielded ${results.length} relevant sources with insights applicable to ${query.context}.`,
        keyInsights: results.slice(0, 3).map(r => r.snippet),
        sources: results.map(r => r.url),
        confidence: 0.7,
        createdAt: new Date().toISOString(),
      };
    }
  }

  /**
   * Get research history for a session
   */
  getResearchHistory(sessionId: string): ResearchQuery[] {
    return Array.from(this.pendingQueries.values())
      .filter(query => query.sessionId === sessionId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  /**
   * Get cached results for a query
   */
  getCachedResults(queryId: string): ResearchResult[] | null {
    const query = this.pendingQueries.get(queryId);
    if (!query) return null;

    const cacheKey = this.getCacheKey(query.query, query.context);
    return this.cache.get(cacheKey) || null;
  }

  /**
   * Clear cache for memory management
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get pending approval requests
   */
  getPendingApprovals(): ResearchQuery[] {
    return Array.from(this.pendingQueries.values())
      .filter(query => !query.approved);
  }

  /**
   * Generate summary for a search result item
   */
  private async generateItemSummary(item: any, context: string): Promise<string> {
    // In production, use AI to generate contextual summary
    return item.snippet || `Information related to ${context} from ${this.extractSource(item.link)}`;
  }

  /**
   * Calculate relevance score based on various factors
   */
  private calculateRelevance(item: any, query: ResearchQuery): number {
    let score = 0.5; // Base score
    
    // Title match
    if (item.title?.toLowerCase().includes(query.query.toLowerCase())) {
      score += 0.2;
    }
    
    // Snippet match
    if (item.snippet?.toLowerCase().includes(query.query.toLowerCase())) {
      score += 0.15;
    }
    
    // Context match
    if (item.snippet?.toLowerCase().includes(query.context.toLowerCase())) {
      score += 0.1;
    }
    
    // Position bonus (higher position = more relevant)
    if (item.position !== undefined && item.position < 3) {
      score += 0.05;
    }
    
    return Math.min(score, 0.99); // Cap at 0.99
  }

  /**
   * Extract source name from URL
   */
  private extractSource(url: string): string {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.replace('www.', '');
      
      // Map common domains to friendly names
      const sourceMap: Record<string, string> = {
        'wikipedia.org': 'Wikipedia',
        'github.com': 'GitHub',
        'stackoverflow.com': 'Stack Overflow',
        'medium.com': 'Medium',
        'arxiv.org': 'arXiv',
        'scholar.google.com': 'Google Scholar',
        'nature.com': 'Nature',
        'sciencedirect.com': 'ScienceDirect',
      };
      
      return sourceMap[hostname] || hostname.charAt(0).toUpperCase() + hostname.slice(1);
    } catch {
      return 'Web Source';
    }
  }

  /**
   * Clean expired cache entries
   */
  private cleanExpiredCache(): void {
    const now = Date.now();
    for (const [key, value] of this.resultsCache.entries()) {
      if (now - value.timestamp > this.CACHE_DURATION) {
        this.resultsCache.delete(key);
      }
    }
  }

  private getCacheKey(query: string, context: string): string {
    return `${query.toLowerCase().trim()}_${context.toLowerCase().trim()}`;
  }

  private generateId(): string {
    return `research_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async callAI(prompt: string): Promise<string> {
    // Integration with actual AI API
    try {
      const response = await api.chat.create({
        messages: [{ role: 'user', content: prompt }],
        model: 'gpt-4',
        temperature: 0.7,
      });
      
      return response.content;
    } catch (error) {
      // Fallback to mock response
      await new Promise(resolve => setTimeout(resolve, 500));
      
      return JSON.stringify({
        overallSummary: "Research indicates multiple approaches and methodologies with varying degrees of effectiveness across different contexts.",
        keyInsights: [
          "Industry best practices emphasize iterative approaches",
          "Case studies show higher success rates with structured methodologies",
          "Recent developments focus on automation and AI integration"
        ],
        confidence: 0.82
      });
    }
  }
}

export const webResearchService = new WebResearchService();