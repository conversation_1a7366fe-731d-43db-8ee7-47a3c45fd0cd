import { invoke } from '@tauri-apps/api/core';

// Simple browser-compatible EventEmitter replacement
class SimpleEventEmitter {
  private events: Map<string, Function[]> = new Map();

  on(event: string, callback: Function) {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(callback);
  }

  off(event: string, callback: Function) {
    const callbacks = this.events.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  emit(event: string, ...args: any[]) {
    const callbacks = this.events.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(...args));
    }
  }

  removeAllListeners(event?: string) {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }
}

export interface VoiceConfig {
  provider: 'whisper' | 'azure' | 'google' | 'aws';
  language: string;
  quality: 'low' | 'medium' | 'high';
  realTimeProcessing: boolean;
  voiceCommands: boolean;
  speakerDiarization: boolean;
  punctuation: boolean;
  profanityFilter: boolean;
}

export interface TranscriptionResult {
  id: string;
  text: string;
  confidence: number;
  segments: TranscriptionSegment[];
  language: string;
  duration: number;
  speakerLabels?: SpeakerLabel[];
  processedAt: string;
  provider: string;
}

export interface TranscriptionSegment {
  start: number;
  end: number;
  text: string;
  confidence: number;
  speaker?: string;
}

export interface SpeakerLabel {
  speaker: string;
  start: number;
  end: number;
  confidence: number;
}

export interface VoiceCommand {
  command: string;
  action: string;
  parameters?: Record<string, any>;
  confidence: number;
}

export interface VoiceNote {
  id: string;
  transcription: TranscriptionResult;
  extractedIdeas: string[];
  sentiment: 'positive' | 'negative' | 'neutral';
  summary: string;
  actionItems: string[];
  createdAt: string;
}

const VOICE_COMMANDS = {
  'create idea': { action: 'create_idea', requiresText: true },
  'add tag': { action: 'add_tag', requiresText: true },
  'set priority': { action: 'set_priority', requiresText: true },
  'create cluster': { action: 'create_cluster', requiresText: true },
  'save session': { action: 'save_session', requiresText: false },
  'export session': { action: 'export_session', requiresText: false },
  'switch view': { action: 'switch_view', requiresText: true },
  'start recording': { action: 'start_recording', requiresText: false },
  'stop recording': { action: 'stop_recording', requiresText: false },
  'pause brainstorm': { action: 'pause_session', requiresText: false },
  'resume brainstorm': { action: 'resume_session', requiresText: false },
};

export class EnhancedVoiceIntegration extends SimpleEventEmitter {
  private config: VoiceConfig;
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private isRecording = false;
  private isProcessing = false;
  private stream: MediaStream | null = null;
  private processingQueue: AudioData[] = [];
  private realTimeProcessor: Worker | null = null;

  constructor(config: VoiceConfig) {
    super();
    this.config = config;
    this.initializeRealTimeProcessor();
  }

  // Initialize Web Worker for real-time processing
  private initializeRealTimeProcessor(): void {
    if (this.config.realTimeProcessing && typeof Worker !== 'undefined') {
      // Create worker for real-time audio processing
      const workerCode = `
        self.onmessage = function(e) {
          const { audioData, config } = e.data;
          // Process audio data in chunks for real-time transcription
          self.postMessage({ type: 'processed', data: audioData });
        };
      `;
      
      const blob = new Blob([workerCode], { type: 'application/javascript' });
      this.realTimeProcessor = new Worker(URL.createObjectURL(blob));
      
      this.realTimeProcessor.onmessage = (e) => {
        if (e.data.type === 'processed') {
          this.handleRealTimeAudio(e.data.data);
        }
      };
    }
  }

  // Start voice recording
  async startRecording(): Promise<void> {
    if (this.isRecording) {
      throw new Error('Recording is already in progress');
    }

    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: this.config.quality === 'high' ? 48000 : 16000,
        },
      });

      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType: 'audio/webm',
      });

      this.audioChunks = [];
      this.isRecording = true;

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
          
          // Real-time processing
          if (this.config.realTimeProcessing && this.realTimeProcessor) {
            const reader = new FileReader();
            reader.onload = () => {
              this.realTimeProcessor?.postMessage({
                audioData: reader.result,
                config: this.config,
              });
            };
            reader.readAsArrayBuffer(event.data);
          }
        }
      };

      this.mediaRecorder.onstop = async () => {
        this.isRecording = false;
        await this.processRecording();
      };

      // Start recording with intervals for real-time processing
      const interval = this.config.realTimeProcessing ? 1000 : undefined;
      this.mediaRecorder.start(interval);

      this.emit('recordingStarted');
    } catch (error) {
      throw new Error(`Failed to start recording: ${error}`);
    }
  }

  // Stop voice recording
  async stopRecording(): Promise<TranscriptionResult> {
    if (!this.isRecording || !this.mediaRecorder) {
      throw new Error('No recording in progress');
    }

    return new Promise((resolve, reject) => {
      this.mediaRecorder!.onstop = async () => {
        try {
          const result = await this.processRecording();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      };

      this.mediaRecorder!.stop();
      this.cleanup();
    });
  }

  // Process recorded audio
  private async processRecording(): Promise<TranscriptionResult> {
    if (this.audioChunks.length === 0) {
      throw new Error('No audio data to process');
    }

    this.isProcessing = true;
    this.emit('processingStarted');

    try {
      // Combine audio chunks
      const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
      
      // Convert to base64 for Tauri backend
      const audioData = await this.blobToBase64(audioBlob);
      
      // Send to Tauri backend for transcription
      const result = await invoke<TranscriptionResult>('transcribe_audio', {
        audioData,
        config: this.config,
      });

      // Process voice commands if enabled
      if (this.config.voiceCommands) {
        const commands = this.extractVoiceCommands(result.text);
        if (commands.length > 0) {
          this.emit('voiceCommands', commands);
        }
      }

      this.emit('transcriptionComplete', result);
      return result;
    } catch (error) {
      this.emit('processingError', error);
      throw error;
    } finally {
      this.isProcessing = false;
      this.emit('processingComplete');
    }
  }

  // Handle real-time audio processing
  private async handleRealTimeAudio(audioData: ArrayBuffer): Promise<void> {
    if (!this.config.realTimeProcessing) return;

    try {
      // Send chunk to backend for real-time transcription
      const chunkResult = await invoke<Partial<TranscriptionResult>>('transcribe_audio_chunk', {
        audioData: Array.from(new Uint8Array(audioData)),
        config: this.config,
      });

      if (chunkResult.text) {
        this.emit('partialTranscription', chunkResult);
        
        // Check for voice commands in real-time
        if (this.config.voiceCommands) {
          const commands = this.extractVoiceCommands(chunkResult.text);
          if (commands.length > 0) {
            this.emit('voiceCommands', commands);
          }
        }
      }
    } catch (error) {
      console.error('Real-time processing error:', error);
    }
  }

  // Extract voice commands from text
  private extractVoiceCommands(text: string): VoiceCommand[] {
    const commands: VoiceCommand[] = [];
    const lowercaseText = text.toLowerCase();

    for (const [trigger, config] of Object.entries(VOICE_COMMANDS)) {
      const index = lowercaseText.indexOf(trigger);
      if (index !== -1) {
        let parameters: Record<string, any> = {};
        
        if (config.requiresText) {
          // Extract text after command
          const afterCommand = text.substring(index + trigger.length).trim();
          if (afterCommand) {
            parameters.text = afterCommand;
          }
        }

        commands.push({
          command: trigger,
          action: config.action,
          parameters,
          confidence: 0.9, // Simple confidence for demo
        });
      }
    }

    return commands;
  }

  // Create voice note with AI processing
  async createVoiceNote(transcription: TranscriptionResult): Promise<VoiceNote> {
    try {
      // Use AI to extract ideas, sentiment, and action items
      const analysis = await invoke<{
        extractedIdeas: string[];
        sentiment: string;
        summary: string;
        actionItems: string[];
      }>('analyze_voice_content', {
        text: transcription.text,
      });

      const voiceNote: VoiceNote = {
        id: `voice_note_${Date.now()}`,
        transcription,
        extractedIdeas: analysis.extractedIdeas,
        sentiment: analysis.sentiment as 'positive' | 'negative' | 'neutral',
        summary: analysis.summary,
        actionItems: analysis.actionItems,
        createdAt: new Date().toISOString(),
      };

      // Save voice note
      await invoke('save_voice_note', { voiceNote });

      this.emit('voiceNoteCreated', voiceNote);
      return voiceNote;
    } catch (error) {
      throw new Error(`Failed to create voice note: ${error}`);
    }
  }

  // Convert blob to base64
  private async blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result.split(',')[1]); // Remove data:audio/webm;base64, prefix
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  // Cleanup resources
  private cleanup(): void {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    this.mediaRecorder = null;
    this.audioChunks = [];
  }

  // Update configuration
  updateConfig(newConfig: Partial<VoiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Reinitialize real-time processor if needed
    if (newConfig.realTimeProcessing !== undefined) {
      if (this.realTimeProcessor) {
        this.realTimeProcessor.terminate();
        this.realTimeProcessor = null;
      }
      if (newConfig.realTimeProcessing) {
        this.initializeRealTimeProcessor();
      }
    }
  }

  // Get recording status
  getStatus(): {
    isRecording: boolean;
    isProcessing: boolean;
    duration: number;
  } {
    return {
      isRecording: this.isRecording,
      isProcessing: this.isProcessing,
      duration: this.isRecording && this.mediaRecorder 
        ? Date.now() - (this.mediaRecorder as any).startTime || 0
        : 0,
    };
  }

  // Test audio input
  async testAudioInput(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch {
      return false;
    }
  }

  // Get available audio devices
  async getAudioDevices(): Promise<MediaDeviceInfo[]> {
    const devices = await navigator.mediaDevices.enumerateDevices();
    return devices.filter(device => device.kind === 'audioinput');
  }

  // Destroy instance
  destroy(): void {
    this.cleanup();
    if (this.realTimeProcessor) {
      this.realTimeProcessor.terminate();
      this.realTimeProcessor = null;
    }
    this.removeAllListeners();
  }
}

interface AudioData {
  data: ArrayBuffer;
  timestamp: number;
}

// Create singleton instance
export const voiceIntegration = new EnhancedVoiceIntegration({
  provider: 'whisper',
  language: 'en-US',
  quality: 'medium',
  realTimeProcessing: false,
  voiceCommands: true,
  speakerDiarization: false,
  punctuation: true,
  profanityFilter: false,
});

// Helper functions
export async function transcribeAudioFile(file: File, config?: Partial<VoiceConfig>): Promise<TranscriptionResult> {
  const audioData = await file.arrayBuffer();
  const base64Data = btoa(String.fromCharCode(...new Uint8Array(audioData)));
  
  return invoke<TranscriptionResult>('transcribe_audio', {
    audioData: base64Data,
    config: { ...voiceIntegration['config'], ...config },
  });
}

export async function getVoiceNotes(sessionId?: string): Promise<VoiceNote[]> {
  return invoke<VoiceNote[]>('get_voice_notes', { sessionId });
}