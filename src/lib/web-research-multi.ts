import { invoke } from '@tauri-apps/api/core';

export interface SearchProvider {
  name: string;
  type: 'serper' | 'duckduckgo' | 'bing' | 'custom';
  enabled: boolean;
  priority: number;
  config?: Record<string, any>;
}

export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  date?: string;
  source?: string;
  provider: string;
}

export interface SearchOptions {
  maxResults?: number;
  searchType?: 'web' | 'news' | 'images' | 'scholar';
  dateRange?: 'day' | 'week' | 'month' | 'year' | 'all';
  language?: string;
  region?: string;
  safeSearch?: boolean;
}

// Default search providers with fallback order
const DEFAULT_PROVIDERS: SearchProvider[] = [
  {
    name: 'Serper',
    type: 'serper',
    enabled: true,
    priority: 1,
    config: {
      requiresApiKey: true,
    },
  },
  {
    name: 'DuckDuckGo',
    type: 'duckduckgo',
    enabled: true,
    priority: 2,
    config: {
      requiresApiKey: false,
      rateLimit: 50, // requests per minute
    },
  },
  {
    name: 'Bing',
    type: 'bing',
    enabled: false,
    priority: 3,
    config: {
      requiresApiKey: true,
    },
  },
];

export class MultiSearchProvider {
  private providers: SearchProvider[];
  private searchCache: Map<string, { results: SearchResult[]; timestamp: number }>;
  private cacheExpiry: number = 3600000; // 1 hour in milliseconds

  constructor(providers: SearchProvider[] = DEFAULT_PROVIDERS) {
    this.providers = providers.sort((a, b) => a.priority - b.priority);
    this.searchCache = new Map();
  }

  // Set provider configuration
  async setProviderConfig(providerName: string, config: Record<string, any>): Promise<void> {
    const provider = this.providers.find(p => p.name === providerName);
    if (provider) {
      provider.config = { ...provider.config, ...config };
      await invoke('save_search_provider_config', {
        provider: providerName,
        config: JSON.stringify(provider.config),
      });
    }
  }

  // Enable/disable a provider
  setProviderEnabled(providerName: string, enabled: boolean): void {
    const provider = this.providers.find(p => p.name === providerName);
    if (provider) {
      provider.enabled = enabled;
    }
  }

  // Get enabled providers in priority order
  getEnabledProviders(): SearchProvider[] {
    return this.providers.filter(p => p.enabled);
  }

  // Perform search with automatic fallback
  async search(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
    // Check cache first
    const cacheKey = this.getCacheKey(query, options);
    const cached = this.searchCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.results;
    }

    const enabledProviders = this.getEnabledProviders();
    let lastError: Error | null = null;

    // Try each provider in priority order
    for (const provider of enabledProviders) {
      try {
        const results = await this.searchWithProvider(provider, query, options);
        
        // Cache successful results
        this.searchCache.set(cacheKey, {
          results,
          timestamp: Date.now(),
        });

        return results;
      } catch (error) {
        console.error(`Search failed with ${provider.name}:`, error);
        lastError = error as Error;
        
        // Continue to next provider
        continue;
      }
    }

    // All providers failed
    throw new Error(
      `All search providers failed. Last error: ${lastError?.message || 'Unknown error'}`
    );
  }

  // Search with a specific provider
  private async searchWithProvider(
    provider: SearchProvider,
    query: string,
    options: SearchOptions
  ): Promise<SearchResult[]> {
    switch (provider.type) {
      case 'serper':
        return this.searchWithSerper(query, options, provider.config);
      case 'duckduckgo':
        return this.searchWithDuckDuckGo(query, options);
      case 'bing':
        return this.searchWithBing(query, options, provider.config);
      case 'custom':
        return this.searchWithCustomProvider(query, options, provider.config);
      default:
        throw new Error(`Unknown provider type: ${provider.type}`);
    }
  }

  // Serper API implementation
  private async searchWithSerper(
    query: string,
    options: SearchOptions,
    config?: Record<string, any>
  ): Promise<SearchResult[]> {
    if (!config?.apiKey) {
      throw new Error('Serper API key not configured');
    }

    const response = await fetch('https://google.serper.dev/search', {
      method: 'POST',
      headers: {
        'X-API-KEY': config.apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        q: query,
        num: options.maxResults || 10,
        gl: options.region,
        hl: options.language,
      }),
    });

    if (!response.ok) {
      throw new Error(`Serper API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    return data.organic.map((result: any) => ({
      title: result.title,
      url: result.link,
      snippet: result.snippet,
      date: result.date,
      provider: 'Serper',
    }));
  }

  // DuckDuckGo implementation (no API key required)
  private async searchWithDuckDuckGo(
    query: string,
    options: SearchOptions
  ): Promise<SearchResult[]> {
    // Use Tauri backend to avoid CORS issues
    const results = await invoke<SearchResult[]>('search_duckduckgo', {
      query,
      maxResults: options.maxResults || 10,
    });

    return results.map(r => ({ ...r, provider: 'DuckDuckGo' }));
  }

  // Bing Search API implementation
  private async searchWithBing(
    query: string,
    options: SearchOptions,
    config?: Record<string, any>
  ): Promise<SearchResult[]> {
    if (!config?.apiKey) {
      throw new Error('Bing API key not configured');
    }

    const params = new URLSearchParams({
      q: query,
      count: String(options.maxResults || 10),
      mkt: options.region || 'en-US',
      safeSearch: options.safeSearch ? 'Strict' : 'Off',
    });

    const response = await fetch(
      `https://api.bing.microsoft.com/v7.0/search?${params}`,
      {
        headers: {
          'Ocp-Apim-Subscription-Key': config.apiKey,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Bing API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    return data.webPages.value.map((result: any) => ({
      title: result.name,
      url: result.url,
      snippet: result.snippet,
      date: result.dateLastCrawled,
      provider: 'Bing',
    }));
  }

  // Custom provider implementation
  private async searchWithCustomProvider(
    query: string,
    options: SearchOptions,
    config?: Record<string, any>
  ): Promise<SearchResult[]> {
    if (!config?.endpoint) {
      throw new Error('Custom provider endpoint not configured');
    }

    const response = await fetch(config.endpoint, {
      method: config.method || 'POST',
      headers: config.headers || { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        query,
        ...options,
        ...config.additionalParams,
      }),
    });

    if (!response.ok) {
      throw new Error(`Custom provider error: ${response.statusText}`);
    }

    const data = await response.json();
    
    // Map custom response format to standard format
    const resultPath = config.resultPath || 'results';
    const results = this.getNestedValue(data, resultPath) || [];
    
    return results.map((result: any) => ({
      title: this.getNestedValue(result, config.titlePath || 'title'),
      url: this.getNestedValue(result, config.urlPath || 'url'),
      snippet: this.getNestedValue(result, config.snippetPath || 'snippet'),
      date: this.getNestedValue(result, config.datePath || 'date'),
      provider: config.name || 'Custom',
    }));
  }

  // Helper to get nested object values using dot notation
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  // Generate cache key
  private getCacheKey(query: string, options: SearchOptions): string {
    return `${query}_${JSON.stringify(options)}`;
  }

  // Clear cache
  clearCache(): void {
    this.searchCache.clear();
  }

  // Get search statistics
  async getSearchStats(): Promise<{
    totalSearches: number;
    providerUsage: Record<string, number>;
    failureRate: Record<string, number>;
  }> {
    return invoke('get_search_stats');
  }

  // Validate provider configuration
  async validateProvider(provider: SearchProvider): Promise<boolean> {
    try {
      // Perform a test search
      await this.searchWithProvider(provider, 'test', { maxResults: 1 });
      return true;
    } catch {
      return false;
    }
  }

  // Batch search across multiple queries
  async batchSearch(
    queries: string[],
    options: SearchOptions = {}
  ): Promise<Map<string, SearchResult[]>> {
    const results = new Map<string, SearchResult[]>();
    
    // Process in parallel with rate limiting
    const batchSize = 3;
    for (let i = 0; i < queries.length; i += batchSize) {
      const batch = queries.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(query => 
          this.search(query, options)
            .then(r => ({ query, results: r }))
            .catch(() => ({ query, results: [] }))
        )
      );
      
      batchResults.forEach(({ query, results: searchResults }) => {
        results.set(query, searchResults);
      });
      
      // Rate limiting delay
      if (i + batchSize < queries.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    return results;
  }
}

// Export singleton instance
export const searchProvider = new MultiSearchProvider();

// Helper function for brainstorming integration
export async function searchForIdeaContext(
  ideaContent: string,
  options?: SearchOptions
): Promise<SearchResult[]> {
  // Generate search query from idea content
  const query = ideaContent.length > 100 
    ? ideaContent.substring(0, 100) + '...'
    : ideaContent;
  
  return searchProvider.search(query, {
    maxResults: 5,
    searchType: 'web',
    ...options,
  });
}