/**
 * Simplified Cross-Session Workflow Orchestrator
 * 
 * A stable version that provides basic workflow functionality without complex type dependencies
 */

import { invoke } from '@tauri-apps/api/core';

export interface SimpleWorkflow {
  id: string;
  name: string;
  description: string;
  sessions: SimpleSessionRef[];
  status: 'draft' | 'active' | 'completed' | 'archived';
  createdAt: Date;
  updatedAt: Date;
}

export interface SimpleSessionRef {
  id: string;
  type: 'brainstorm' | 'claude' | 'orchestra';
  name: string;
  projectPath?: string;
  status: 'pending' | 'active' | 'completed' | 'failed';
  createdAt: Date;
}

class SimpleCrossSessionOrchestrator {
  private workflows: Map<string, SimpleWorkflow> = new Map();

  /**
   * Create a new workflow
   */
  async createWorkflow(options: {
    name: string;
    description: string;
    initialSessionType: 'brainstorm' | 'claude' | 'orchestra';
    projectPath?: string;
  }): Promise<SimpleWorkflow> {
    const workflowId = `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const workflow: SimpleWorkflow = {
      id: workflowId,
      name: options.name,
      description: options.description,
      sessions: [],
      status: 'draft',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Create initial session reference
    const initialSession: SimpleSessionRef = {
      id: `${options.initialSessionType}_${Date.now()}`,
      type: options.initialSessionType,
      name: `${options.initialSessionType.charAt(0).toUpperCase() + options.initialSessionType.slice(1)} Session`,
      projectPath: options.projectPath,
      status: 'active',
      createdAt: new Date()
    };
    
    workflow.sessions.push(initialSession);
    workflow.status = 'active';
    
    this.workflows.set(workflowId, workflow);
    
    // Store in backend
    try {
      await invoke('initialize_session_orchestration', {
        sessionId: workflowId,
        sessionType: 'workflow',
        orchestrationPreferences: {
          initialSessionType: options.initialSessionType,
          projectPath: options.projectPath
        }
      });
    } catch (error) {
      console.error('Failed to initialize workflow in backend:', error);
    }

    return workflow;
  }

  /**
   * Add a session to workflow
   */
  async addSessionToWorkflow(
    workflowId: string,
    sessionType: 'brainstorm' | 'claude' | 'orchestra',
    options: {
      name?: string;
      projectPath?: string;
    } = {}
  ): Promise<SimpleSessionRef> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    const session: SimpleSessionRef = {
      id: `${sessionType}_${Date.now()}`,
      type: sessionType,
      name: options.name || `${sessionType.charAt(0).toUpperCase() + sessionType.slice(1)} Session`,
      projectPath: options.projectPath,
      status: 'active',
      createdAt: new Date()
    };

    workflow.sessions.push(session);
    workflow.updatedAt = new Date();

    return session;
  }

  /**
   * Get workflow status
   */
  getWorkflowStatus(workflowId: string): SimpleWorkflow | null {
    return this.workflows.get(workflowId) || null;
  }

  /**
   * List all workflows
   */
  listWorkflows(): SimpleWorkflow[] {
    return Array.from(this.workflows.values());
  }

  /**
   * Archive workflow
   */
  archiveWorkflow(workflowId: string): void {
    const workflow = this.workflows.get(workflowId);
    if (workflow) {
      workflow.status = 'archived';
      workflow.updatedAt = new Date();
    }
  }

  /**
   * Create a simple transition between sessions
   */
  async createTransition(
    workflowId: string,
    fromSessionId: string,
    toSessionType: 'brainstorm' | 'claude' | 'orchestra',
    options: {
      projectPath?: string;
    } = {}
  ): Promise<SimpleSessionRef> {
    try {
      const newSession = await this.addSessionToWorkflow(workflowId, toSessionType, {
        projectPath: options.projectPath
      });

      // Link sessions in backend
      await invoke('link_sessions', {
        sourceSessionId: fromSessionId,
        targetSessionId: newSession.id,
        linkType: 'transition'
      });

      return newSession;
    } catch (error) {
      console.error('Failed to create transition:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const simpleCrossSessionOrchestrator = new SimpleCrossSessionOrchestrator();