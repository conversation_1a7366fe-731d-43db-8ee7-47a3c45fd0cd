import { invoke } from "@tauri-apps/api/core";

export interface BrainstormMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface BrainstormOptions {
  sessionId: string;
  messages: BrainstormMessage[];
  model: string;
  temperature?: number;
  maxTokens?: number;
}

export interface BrainstormStreamEvent {
  session_id: string;
  content: string;
  accumulated: string;
}

export interface BrainstormCompleteEvent {
  session_id: string;
  content: string;
}

export interface BrainstormErrorEvent {
  session_id: string;
  error: string;
}

/**
 * Brainstorm API for conversational AI using Claude Code CLI
 * 
 * IMPORTANT: This is completely separate from Claude Code sessions!
 * - Brainstorming sessions are independent and isolated
 * - No integration with regular Claude Code project sessions
 * - Uses separate storage, state management, and session lifecycle
 * - This ensures brainstorming doesn't interfere with coding workflows
 */
export const brainstormApi = {
  /**
   * Send a brainstorm chat request with streaming response
   */
  async chat(options: BrainstormOptions): Promise<void> {
    return invoke('brainstorm_chat', {
      request: {
        sessionId: options.sessionId,
        messages: options.messages,
        model: options.model,
        temperature: options.temperature,
        maxTokens: options.maxTokens
      }
    });
  },

  /**
   * Cancel a brainstorm session
   */
  async cancel(sessionId: string): Promise<void> {
    return invoke('cancel_brainstorm', { session_id: sessionId });
  }
};