import { invoke } from '@tauri-apps/api/core';

export interface MediaAttachment {
  id: string;
  idea_id: string;
  file_name: string;
  file_type: string;
  file_size: number;
  mime_type: string;
  created_at: string;
  local_path: string;
}

export interface UploadMediaRequest {
  idea_id: string;
  file_name: string;
  mime_type: string;
  data: number[]; // Vec<u8> in Rust becomes number[] in JS
}

// Convert File or Blob to byte array
async function fileToByteArray(file: File | Blob): Promise<number[]> {
  const arrayBuffer = await file.arrayBuffer();
  return Array.from(new Uint8Array(arrayBuffer));
}

// Upload a media attachment for an idea
export async function uploadMediaAttachment(
  ideaId: string,
  file: File
): Promise<MediaAttachment> {
  const data = await fileToByteArray(file);
  
  const request: UploadMediaRequest = {
    idea_id: ideaId,
    file_name: file.name,
    mime_type: file.type || 'application/octet-stream',
    data,
  };

  return await invoke<MediaAttachment>('upload_media_attachment', { request });
}

// Get media attachment data
export async function getMediaAttachment(
  attachmentId: string
): Promise<Uint8Array> {
  const data = await invoke<number[]>('get_media_attachment', { 
    attachmentId 
  });
  return new Uint8Array(data);
}

// Delete a media attachment
export async function deleteMediaAttachment(
  attachmentId: string
): Promise<void> {
  return await invoke('delete_media_attachment', { attachmentId });
}

// Get all media attachments for an idea
export async function getMediaAttachmentsForIdea(
  ideaId: string
): Promise<MediaAttachment[]> {
  return await invoke<MediaAttachment[]>('get_media_attachments_for_idea', { 
    ideaId 
  });
}

// Create object URL for displaying media
export function createMediaObjectURL(data: Uint8Array, mimeType: string): string {
  const blob = new Blob([data], { type: mimeType });
  return URL.createObjectURL(blob);
}

// Helper to determine if a file is an image
export function isImageFile(mimeType: string): boolean {
  return mimeType.startsWith('image/');
}

// Helper to determine if a file is audio
export function isAudioFile(mimeType: string): boolean {
  return mimeType.startsWith('audio/');
}

// Helper to get file icon based on mime type
export function getFileIcon(mimeType: string): string {
  if (isImageFile(mimeType)) return '🖼️';
  if (isAudioFile(mimeType)) return '🎵';
  if (mimeType === 'application/pdf') return '📄';
  return '📎';
}

// Helper to format file size
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}