/**
 * Centralized Claude Session Manager
 * 
 * Provides a unified interface for managing Claude Code sessions with proper
 * state management, lifecycle control, and event handling.
 */

import { listen, type UnlistenFn } from "@tauri-apps/api/event";
import { api } from "./api";
import type {
  SessionData,
  SessionState,
  SessionStatus,
  ClaudeMessage,
  QueuedPrompt,
  Checkpoint,
  SessionEvent,
  SessionEventCallback,
  StateUpdateCallback,
  UnsubscribeFn,
  ModelType,
  MessageType,
  MessageContent,
  SessionSettings,
  SessionMetadata
} from "../types/session";

/**
 * Session creation options
 */
export interface CreateSessionOptions {
  projectPath: string;
  initialPrompt?: string;
  model?: ModelType;
  settings?: Partial<SessionSettings>;
}

/**
 * Session resume options
 */
export interface ResumeSessionOptions {
  sessionId: string;
  projectId: string;
  projectPath: string;
}

/**
 * Default session settings
 */
const DEFAULT_SESSION_SETTINGS: SessionSettings = {
  autoCheckpoint: true,
  checkpointInterval: 10,
  maxMessages: 1000,
  previewEnabled: false,
  exportFormat: 'markdown',
  autoScroll: true
};

/**
 * Centralized session manager class
 */
export class ClaudeSessionManager {
  private currentSession: SessionData | null = null;
  private sessionState: SessionState;
  private eventListeners: Map<string, SessionEventCallback[]> = new Map();
  private stateUpdateCallbacks: StateUpdateCallback[] = [];
  private unlistenFunctions: UnlistenFn[] = [];
  private isListening = false;

  constructor() {
    this.sessionState = this.createInitialState();
  }

  /**
   * Create initial session state
   */
  private createInitialState(): SessionState {
    return {
      currentSession: null,
      isLoading: false,
      error: null,
      isStreaming: false,
      streamingMessage: null,
      ui: {
        showTimeline: false,
        showPreview: false,
        showSettings: false,
        previewUrl: '',
        queueCollapsed: false
      },
      performance: {
        virtualScrollEnabled: true,
        messageCache: new Map(),
        lastScrollPosition: 0
      }
    };
  }

  /**
   * Create a new Claude session
   */
  async createSession(options: CreateSessionOptions): Promise<SessionData> {
    try {
      this.updateState({ isLoading: true, error: null });

      // Generate session ID
      const sessionId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const projectId = options.projectPath.replace(/[^a-zA-Z0-9]/g, '-');
      
      // Create session data
      const sessionData: SessionData = {
        id: sessionId,
        projectPath: options.projectPath,
        projectId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'idle',
        metadata: {
          totalTokens: 0,
          messageCount: 0,
          lastActivity: new Date().toISOString(),
          tags: [],
          custom: {}
        },
        messages: [],
        checkpoints: [],
        settings: { ...DEFAULT_SESSION_SETTINGS, ...options.settings },
        queuedPrompts: []
      };

      // Set as current session
      this.currentSession = sessionData;
      this.updateState({ 
        currentSession: sessionData,
        isLoading: false 
      });

      // Set up event listeners
      await this.setupEventListeners(sessionId);

      // Send initial prompt if provided
      if (options.initialPrompt) {
        await this.sendPrompt(options.initialPrompt, options.model || 'sonnet');
      }

      // Emit session created event
      this.emitEvent({
        type: 'session_created',
        sessionId,
        projectPath: options.projectPath
      });

      return sessionData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.updateState({ 
        isLoading: false, 
        error: `Failed to create session: ${errorMessage}` 
      });
      throw error;
    }
  }

  /**
   * Resume an existing Claude session
   */
  async resumeSession(options: ResumeSessionOptions): Promise<SessionData> {
    try {
      this.updateState({ isLoading: true, error: null });

      // Load session history
      const history = await api.loadSessionHistory(options.sessionId, options.projectId);
      
      // Convert history to messages
      const messages: ClaudeMessage[] = history.map((entry, index) => ({
        id: `${options.sessionId}-${index}`,
        type: (entry.type || 'assistant') as MessageType,
        content: this.parseMessageContent(entry),
        timestamp: entry.timestamp || new Date().toISOString(),
        status: 'complete',
        usage: entry.usage,
        metadata: entry.metadata || {}
      }));

      // Calculate metadata
      const totalTokens = messages.reduce((total, msg) => {
        if (msg.usage) {
          return total + msg.usage.input_tokens + msg.usage.output_tokens;
        }
        return total;
      }, 0);

      // Create session data
      const sessionData: SessionData = {
        id: options.sessionId,
        projectPath: options.projectPath,
        projectId: options.projectId,
        createdAt: new Date().toISOString(), // We don't have original creation time
        updatedAt: new Date().toISOString(),
        status: 'idle',
        metadata: {
          totalTokens,
          messageCount: messages.length,
          lastActivity: new Date().toISOString(),
          tags: [],
          custom: {}
        },
        messages,
        checkpoints: [], // TODO: Load checkpoints if available
        settings: DEFAULT_SESSION_SETTINGS,
        queuedPrompts: []
      };

      // Set as current session
      this.currentSession = sessionData;
      this.updateState({ 
        currentSession: sessionData,
        isLoading: false 
      });

      // Check if session is still active and reconnect
      await this.checkAndReconnectActiveSession(options.sessionId);

      // Emit session resumed event
      this.emitEvent({
        type: 'session_resumed',
        sessionId: options.sessionId
      });

      return sessionData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.updateState({ 
        isLoading: false, 
        error: `Failed to resume session: ${errorMessage}` 
      });
      throw error;
    }
  }

  /**
   * Terminate the current session
   */
  async terminateSession(): Promise<void> {
    if (!this.currentSession) {
      throw new Error('No active session to terminate');
    }

    try {
      const sessionId = this.currentSession.id;

      // Cancel any active execution
      if (this.sessionState.isStreaming) {
        await api.cancelClaudeExecution(sessionId);
      }

      // Clean up event listeners
      this.cleanupEventListeners();

      // Update session status
      this.currentSession.status = 'terminated';
      this.currentSession.updatedAt = new Date().toISOString();

      // Clear state
      this.updateState({
        currentSession: null,
        isLoading: false,
        isStreaming: false,
        streamingMessage: null,
        error: null
      });

      // Emit termination event
      this.emitEvent({
        type: 'session_terminated',
        sessionId
      });

      // Clear current session
      this.currentSession = null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.updateState({ 
        error: `Failed to terminate session: ${errorMessage}` 
      });
      throw error;
    }
  }

  /**
   * Send a prompt to the current session
   */
  async sendPrompt(prompt: string, model: ModelType = 'sonnet'): Promise<void> {
    if (!this.currentSession) {
      throw new Error('No active session');
    }

    // If already streaming, queue the prompt
    if (this.sessionState.isStreaming) {
      this.queuePrompt(prompt, model);
      return;
    }

    try {
      this.updateState({ 
        isStreaming: true, 
        error: null 
      });

      // Update session status
      this.currentSession.status = 'streaming';
      this.currentSession.updatedAt = new Date().toISOString();

      // Add user message to session
      const userMessage: ClaudeMessage = {
        id: `${this.currentSession.id}-${Date.now()}`,
        type: 'user',
        content: [{ type: 'text', text: prompt }],
        timestamp: new Date().toISOString(),
        status: 'complete',
        metadata: {}
      };

      this.addMessage(userMessage);

      // Execute the prompt
      if (this.currentSession.messages.length === 1) {
        // First prompt - start new session
        await api.executeClaudeCode(this.currentSession.projectPath, prompt, model);
      } else {
        // Resume existing session
        await api.resumeClaudeCode(
          this.currentSession.projectPath, 
          this.currentSession.id, 
          prompt, 
          model
        );
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.updateState({ 
        isStreaming: false,
        error: `Failed to send prompt: ${errorMessage}` 
      });
      
      // Update session status
      if (this.currentSession) {
        this.currentSession.status = 'error';
      }

      throw error;
    }
  }

  /**
   * Queue a prompt for later execution
   */
  queuePrompt(prompt: string, model: ModelType = 'sonnet', priority: number = 0): void {
    if (!this.currentSession) {
      throw new Error('No active session');
    }

    const queuedPrompt: QueuedPrompt = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      prompt,
      model,
      timestamp: new Date().toISOString(),
      priority
    };

    // Add to session queue (sorted by priority)
    this.currentSession.queuedPrompts.push(queuedPrompt);
    this.currentSession.queuedPrompts.sort((a, b) => b.priority - a.priority);

    // Update state
    this.updateState({
      currentSession: { ...this.currentSession }
    });
  }

  /**
   * Cancel current execution
   */
  async cancelExecution(): Promise<void> {
    if (!this.currentSession || !this.sessionState.isStreaming) {
      return;
    }

    try {
      await api.cancelClaudeExecution(this.currentSession.id);
      
      this.updateState({
        isStreaming: false,
        streamingMessage: null
      });

      // Update session status
      this.currentSession.status = 'idle';
      this.currentSession.queuedPrompts = []; // Clear queue on cancel

      // Add cancellation message
      const cancelMessage: ClaudeMessage = {
        id: `${this.currentSession.id}-cancel-${Date.now()}`,
        type: 'system',
        content: [{ type: 'text', text: 'Execution cancelled by user' }],
        timestamp: new Date().toISOString(),
        status: 'complete',
        metadata: { subtype: 'cancellation' }
      };

      this.addMessage(cancelMessage);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.updateState({ 
        error: `Failed to cancel execution: ${errorMessage}` 
      });
      throw error;
    }
  }

  /**
   * Get current session state
   */
  getSessionState(): SessionState {
    return { ...this.sessionState };
  }

  /**
   * Subscribe to state updates
   */
  subscribeToUpdates(callback: StateUpdateCallback): UnsubscribeFn {
    this.stateUpdateCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.stateUpdateCallbacks.indexOf(callback);
      if (index > -1) {
        this.stateUpdateCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Subscribe to session events
   */
  addEventListener(eventType: string, callback: SessionEventCallback): UnsubscribeFn {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    
    this.eventListeners.get(eventType)!.push(callback);
    
    // Return unsubscribe function
    return () => {
      const callbacks = this.eventListeners.get(eventType);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  /**
   * Get current session data
   */
  getCurrentSession(): SessionData | null {
    return this.currentSession ? { ...this.currentSession } : null;
  }

  /**
   * Update session settings
   */
  updateSessionSettings(settings: Partial<SessionSettings>): void {
    if (!this.currentSession) {
      throw new Error('No active session');
    }

    this.currentSession.settings = {
      ...this.currentSession.settings,
      ...settings
    };

    this.currentSession.updatedAt = new Date().toISOString();

    this.updateState({
      currentSession: { ...this.currentSession }
    });
  }

  /**
   * Private: Setup event listeners for session
   */
  private async setupEventListeners(sessionId: string): Promise<void> {
    if (this.isListening) {
      this.cleanupEventListeners();
    }

    try {
      // Listen for session-specific events
      const outputUnlisten = await listen<string>(`claude-output:${sessionId}`, (event) => {
        this.handleStreamMessage(event.payload);
      });

      const errorUnlisten = await listen<string>(`claude-error:${sessionId}`, (event) => {
        this.handleStreamError(event.payload);
      });

      const completeUnlisten = await listen<boolean>(`claude-complete:${sessionId}`, (event) => {
        this.handleStreamComplete(event.payload);
      });

      // Also listen for generic events (for session initialization)
      const genericOutputUnlisten = await listen<string>('claude-output', (event) => {
        this.handleStreamMessage(event.payload);
      });

      const genericErrorUnlisten = await listen<string>('claude-error', (event) => {
        this.handleStreamError(event.payload);
      });

      const genericCompleteUnlisten = await listen<boolean>('claude-complete', (event) => {
        this.handleStreamComplete(event.payload);
      });

      this.unlistenFunctions = [
        outputUnlisten,
        errorUnlisten,
        completeUnlisten,
        genericOutputUnlisten,
        genericErrorUnlisten,
        genericCompleteUnlisten
      ];

      this.isListening = true;
    } catch (error) {
      console.error('Failed to setup event listeners:', error);
      throw error;
    }
  }

  /**
   * Private: Clean up event listeners
   */
  private cleanupEventListeners(): void {
    this.unlistenFunctions.forEach(unlisten => unlisten());
    this.unlistenFunctions = [];
    this.isListening = false;
  }

  /**
   * Private: Handle streaming message
   */
  private handleStreamMessage(payload: string): void {
    try {
      const message = JSON.parse(payload);
      
      // Handle session initialization
      if (message.type === 'system' && message.subtype === 'init' && message.session_id) {
        if (this.currentSession && !this.currentSession.id.includes(message.session_id)) {
          // Update session ID if it changed
          this.currentSession.id = message.session_id;
          this.updateState({ currentSession: { ...this.currentSession } });
        }
      }

      // Convert to ClaudeMessage format
      const claudeMessage: ClaudeMessage = {
        id: `${this.currentSession?.id || 'unknown'}-${Date.now()}`,
        type: (message.type || 'assistant') as MessageType,
        content: this.parseMessageContent(message),
        timestamp: message.timestamp || new Date().toISOString(),
        status: message.type === 'system' ? 'complete' : 'streaming',
        usage: message.usage,
        metadata: message.metadata || {}
      };

      // If streaming, update streaming message, otherwise add complete message
      if (claudeMessage.status === 'streaming') {
        this.updateState({ streamingMessage: claudeMessage });
      } else {
        this.addMessage(claudeMessage);
      }

      // Emit message event
      if (this.currentSession) {
        this.emitEvent({
          type: 'message_received',
          sessionId: this.currentSession.id,
          message: claudeMessage
        });
      }
    } catch (error) {
      console.error('Failed to parse stream message:', error, payload);
    }
  }

  /**
   * Private: Handle streaming error
   */
  private handleStreamError(payload: string): void {
    this.updateState({
      isStreaming: false,
      streamingMessage: null,
      error: payload
    });

    if (this.currentSession) {
      this.currentSession.status = 'error';
      
      this.emitEvent({
        type: 'error_occurred',
        sessionId: this.currentSession.id,
        error: payload
      });
    }
  }

  /**
   * Private: Handle streaming completion
   */
  private handleStreamComplete(success: boolean): void {
    this.updateState({
      isStreaming: false,
      streamingMessage: null
    });

    if (this.currentSession) {
      this.currentSession.status = success ? 'idle' : 'error';
      this.currentSession.updatedAt = new Date().toISOString();

      // Process next queued prompt if available
      if (success && this.currentSession.queuedPrompts.length > 0) {
        const nextPrompt = this.currentSession.queuedPrompts.shift()!;
        this.currentSession.queuedPrompts = [...this.currentSession.queuedPrompts];
        
        // Process next prompt after a short delay
        setTimeout(() => {
          this.sendPrompt(nextPrompt.prompt, nextPrompt.model);
        }, 100);
      }

      this.updateState({ currentSession: { ...this.currentSession } });
    }
  }

  /**
   * Private: Check and reconnect to active session
   */
  private async checkAndReconnectActiveSession(sessionId: string): Promise<void> {
    try {
      const activeSessions = await api.listRunningClaudeSessions();
      const activeSession = activeSessions.find((s: any) => {
        if ('process_type' in s && s.process_type && 'ClaudeSession' in s.process_type) {
          return (s.process_type as any).ClaudeSession.session_id === sessionId;
        }
        return false;
      });

      if (activeSession && this.currentSession) {
        console.log('Reconnecting to active session:', sessionId);
        this.currentSession.status = 'streaming';
        this.updateState({ 
          isStreaming: true,
          currentSession: { ...this.currentSession }
        });
        
        // Set up listeners for the active session
        await this.setupEventListeners(sessionId);
      }
    } catch (error) {
      console.error('Failed to check for active sessions:', error);
    }
  }

  /**
   * Private: Add message to current session
   */
  private addMessage(message: ClaudeMessage): void {
    if (!this.currentSession) return;

    this.currentSession.messages.push(message);
    this.currentSession.metadata.messageCount = this.currentSession.messages.length;
    this.currentSession.metadata.lastActivity = new Date().toISOString();
    
    // Update token count if usage is available
    if (message.usage) {
      this.currentSession.metadata.totalTokens += 
        message.usage.input_tokens + message.usage.output_tokens;
    }

    this.currentSession.updatedAt = new Date().toISOString();

    this.updateState({
      currentSession: { ...this.currentSession }
    });
  }

  /**
   * Private: Parse message content from raw message
   */
  private parseMessageContent(message: any): MessageContent[] {
    if (!message.message?.content) {
      if (message.result) {
        return [{ type: 'text', text: message.result }];
      }
      return [{ type: 'text', text: JSON.stringify(message) }];
    }

    if (Array.isArray(message.message.content)) {
      return message.message.content;
    }

    if (typeof message.message.content === 'string') {
      return [{ type: 'text', text: message.message.content }];
    }

    return [{ type: 'text', text: JSON.stringify(message.message.content) }];
  }

  /**
   * Private: Update session state and notify subscribers
   */
  private updateState(updates: Partial<SessionState>): void {
    this.sessionState = {
      ...this.sessionState,
      ...updates
    };

    // Notify all subscribers
    this.stateUpdateCallbacks.forEach(callback => {
      try {
        callback(this.sessionState);
      } catch (error) {
        console.error('Error in state update callback:', error);
      }
    });
  }

  /**
   * Private: Emit session event
   */
  private emitEvent(event: SessionEvent): void {
    const callbacks = this.eventListeners.get(event.type) || [];
    callbacks.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error('Error in event callback:', error);
      }
    });
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.cleanupEventListeners();
    this.eventListeners.clear();
    this.stateUpdateCallbacks = [];
    this.currentSession = null;
    this.sessionState = this.createInitialState();
  }
}

// Export singleton instance
export const sessionManager = new ClaudeSessionManager();