/**
 * Cross-Session Memory System
 * 
 * Enables knowledge retention and learning across brainstorming sessions
 */

import { Idea, BrainstormSession, IdeaCluster } from '@/types/brainstorm';

export interface MemoryEntry {
  id: string;
  type: 'insight' | 'pattern' | 'concept' | 'lesson' | 'connection';
  content: string;
  sourceSessionIds: string[];
  relatedIdeaIds: string[];
  tags: string[];
  confidence: number; // 0-1 score of how valuable this memory is
  createdAt: string;
  lastAccessed: string;
  accessCount: number;
  metadata?: {
    [key: string]: any;
  };
}

export interface MemoryGraph {
  entries: MemoryEntry[];
  connections: {
    id: string;
    sourceId: string;
    targetId: string;
    strength: number; // 0-1 connection strength
    type: 'builds-on' | 'contradicts' | 'relates-to' | 'enables';
  }[];
}

class BrainstormMemorySystem {
  private memories: Map<string, MemoryEntry> = new Map();
  private connections: Map<string, { sourceId: string; targetId: string; strength: number; type: string }> = new Map();
  
  /**
   * Extract insights from a completed brainstorming session
   */
  extractMemoriesFromSession(
    session: BrainstormSession,
    ideas: Idea[],
    clusters: IdeaCluster[]
  ): MemoryEntry[] {
    const memories: MemoryEntry[] = [];
    
    // Extract pattern insights from clusters
    clusters.forEach(cluster => {
      if (cluster.ideaIds.length >= 3) {
        const clusterIdeas = ideas.filter(idea => cluster.ideaIds.includes(idea.id));
        const commonTags = this.findCommonTags(clusterIdeas);
        
        if (commonTags.length > 0) {
          memories.push({
            id: `pattern_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: 'pattern',
            content: `Pattern identified: Ideas related to ${cluster.theme} often involve ${commonTags.join(', ')}`,
            sourceSessionIds: [session.id],
            relatedIdeaIds: cluster.ideaIds,
            tags: [...commonTags, cluster.theme.toLowerCase()],
            confidence: Math.min(0.9, cluster.ideaIds.length / 10),
            createdAt: new Date().toISOString(),
            lastAccessed: new Date().toISOString(),
            accessCount: 1,
            metadata: {
              clusterId: cluster.id,
              clusterSize: cluster.ideaIds.length
            }
          });
        }
      }
    });
    
    // Extract insights from high-value ideas
    const highValueIdeas = ideas.filter(idea => 
      idea.priority === 'critical' || idea.priority === 'high'
    );
    
    highValueIdeas.forEach(idea => {
      if (idea.tags.length > 0) {
        memories.push({
          id: `insight_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: 'insight',
          content: `High-value insight: "${idea.content}" demonstrates importance of ${idea.tags.join(', ')}`,
          sourceSessionIds: [session.id],
          relatedIdeaIds: [idea.id],
          tags: idea.tags,
          confidence: idea.priority === 'critical' ? 0.9 : 0.7,
          createdAt: new Date().toISOString(),
          lastAccessed: new Date().toISOString(),
          accessCount: 1,
          metadata: {
            ideaPriority: idea.priority,
            ideaStatus: idea.status
          }
        });
      }
    });
    
    // Extract conceptual learnings from session progression
    const sessionLearnings = this.analyzeSessionProgression(session, ideas);
    memories.push(...sessionLearnings);
    
    return memories;
  }
  
  /**
   * Store memories and update connections
   */
  storeMemories(newMemories: MemoryEntry[]): void {
    newMemories.forEach(memory => {
      this.memories.set(memory.id, memory);
      this.updateConnections(memory);
    });
  }
  
  /**
   * Retrieve relevant memories for a new session
   */
  getRelevantMemories(
    sessionTags: string[],
    sessionContext?: string,
    limit: number = 10
  ): MemoryEntry[] {
    const scoredMemories = Array.from(this.memories.values())
      .map(memory => ({
        memory,
        score: this.calculateRelevanceScore(memory, sessionTags, sessionContext)
      }))
      .filter(({ score }) => score > 0.3)
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
    
    // Update access tracking
    scoredMemories.forEach(({ memory }) => {
      memory.lastAccessed = new Date().toISOString();
      memory.accessCount++;
    });
    
    return scoredMemories.map(({ memory }) => memory);
  }
  
  /**
   * Find similar sessions based on memory patterns
   */
  findSimilarSessions(currentSession: BrainstormSession): string[] {
    const currentTags = this.extractSessionTags(currentSession);
    const sessionScores = new Map<string, number>();
    
    Array.from(this.memories.values()).forEach(memory => {
      const tagOverlap = memory.tags.filter(tag => currentTags.includes(tag)).length;
      const relevanceScore = tagOverlap / Math.max(memory.tags.length, currentTags.length);
      
      memory.sourceSessionIds.forEach(sessionId => {
        sessionScores.set(
          sessionId,
          (sessionScores.get(sessionId) || 0) + relevanceScore * memory.confidence
        );
      });
    });
    
    return Array.from(sessionScores.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([sessionId]) => sessionId);
  }
  
  /**
   * Generate memory-based suggestions for a session
   */
  generateMemoryBasedSuggestions(
    sessionTags: string[],
    existingIdeas: Idea[]
  ): { content: string; reason: string; confidence: number }[] {
    const relevantMemories = this.getRelevantMemories(sessionTags);
    const suggestions: { content: string; reason: string; confidence: number }[] = [];
    
    relevantMemories.forEach(memory => {
      if (memory.type === 'pattern') {
        suggestions.push({
          content: `Consider exploring ${memory.tags.join(', ')} based on previous successful patterns`,
          reason: `Pattern from previous session: ${memory.content}`,
          confidence: memory.confidence
        });
      } else if (memory.type === 'insight') {
        const relatedConcepts = this.findRelatedConcepts(memory);
        if (relatedConcepts.length > 0) {
          suggestions.push({
            content: `Build on previous insight: ${relatedConcepts.join(', ')}`,
            reason: memory.content,
            confidence: memory.confidence * 0.8
          });
        }
      }
    });
    
    return suggestions
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 5);
  }
  
  /**
   * Clean up old or low-value memories
   */
  cleanupMemories(): void {
    const cutoffDate = new Date();
    cutoffDate.setMonth(cutoffDate.getMonth() - 6); // Remove memories older than 6 months
    
    const toRemove: string[] = [];
    
    this.memories.forEach((memory, id) => {
      const memoryAge = new Date(memory.createdAt);
      const isOld = memoryAge < cutoffDate;
      const hasLowValue = memory.confidence < 0.3 && memory.accessCount < 2;
      
      if (isOld || hasLowValue) {
        toRemove.push(id);
      }
    });
    
    toRemove.forEach(id => {
      this.memories.delete(id);
      // Also remove connections involving this memory
      this.connections.forEach((connection, connId) => {
        if (connection.sourceId === id || connection.targetId === id) {
          this.connections.delete(connId);
        }
      });
    });
    
    console.log(`Cleaned up ${toRemove.length} old memories`);
  }
  
  /**
   * Export memory data for backup
   */
  exportMemories(): MemoryGraph {
    return {
      entries: Array.from(this.memories.values()),
      connections: Array.from(this.connections.values()).map((conn, i) => ({
        id: `conn_${i}`,
        ...conn
      }))
    };
  }
  
  /**
   * Import memory data from backup
   */
  importMemories(data: MemoryGraph): void {
    this.memories.clear();
    this.connections.clear();
    
    data.entries.forEach(entry => {
      this.memories.set(entry.id, entry);
    });
    
    data.connections.forEach(connection => {
      this.connections.set(connection.id, connection);
    });
  }
  
  // Private helper methods
  
  private findCommonTags(ideas: Idea[]): string[] {
    const tagCounts = new Map<string, number>();
    const totalIdeas = ideas.length;
    
    ideas.forEach(idea => {
      idea.tags.forEach(tag => {
        tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
      });
    });
    
    return Array.from(tagCounts.entries())
      .filter(([, count]) => count >= Math.ceil(totalIdeas * 0.6)) // At least 60% of ideas
      .map(([tag]) => tag);
  }
  
  private analyzeSessionProgression(session: BrainstormSession, ideas: Idea[]): MemoryEntry[] {
    const memories: MemoryEntry[] = [];
    
    // Analyze idea evolution patterns
    const earlyIdeas = ideas.slice(0, Math.ceil(ideas.length * 0.3));
    const lateIdeas = ideas.slice(Math.floor(ideas.length * 0.7));
    
    const earlyTags = new Set(earlyIdeas.flatMap(idea => idea.tags));
    const lateTags = new Set(lateIdeas.flatMap(idea => idea.tags));
    
    const emergingTags = Array.from(lateTags).filter(tag => !earlyTags.has(tag));
    
    if (emergingTags.length > 0) {
      memories.push({
        id: `evolution_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: 'lesson',
        content: `Session evolution: Started broad, converged on ${emergingTags.join(', ')}`,
        sourceSessionIds: [session.id],
        relatedIdeaIds: lateIdeas.map(idea => idea.id),
        tags: emergingTags,
        confidence: 0.6,
        createdAt: new Date().toISOString(),
        lastAccessed: new Date().toISOString(),
        accessCount: 1,
        metadata: {
          sessionProgression: true,
          emergingTopics: emergingTags
        }
      });
    }
    
    return memories;
  }
  
  private updateConnections(newMemory: MemoryEntry): void {
    // Find connections with existing memories
    Array.from(this.memories.values()).forEach(existingMemory => {
      if (existingMemory.id === newMemory.id) return;
      
      const tagOverlap = newMemory.tags.filter(tag => existingMemory.tags.includes(tag)).length;
      const maxTags = Math.max(newMemory.tags.length, existingMemory.tags.length);
      const strength = tagOverlap / maxTags;
      
      if (strength > 0.3) {
        const connectionId = `${newMemory.id}_${existingMemory.id}`;
        this.connections.set(connectionId, {
          sourceId: newMemory.id,
          targetId: existingMemory.id,
          strength,
          type: this.determineConnectionType(newMemory, existingMemory)
        });
      }
    });
  }
  
  private determineConnectionType(memory1: MemoryEntry, memory2: MemoryEntry): string {
    if (memory1.type === 'insight' && memory2.type === 'pattern') {
      return 'builds-on';
    } else if (memory1.type === memory2.type) {
      return 'relates-to';
    } else {
      return 'relates-to';
    }
  }
  
  private calculateRelevanceScore(
    memory: MemoryEntry,
    sessionTags: string[],
    context?: string
  ): number {
    let score = 0;
    
    // Tag overlap
    const tagOverlap = memory.tags.filter(tag => sessionTags.includes(tag)).length;
    score += (tagOverlap / Math.max(memory.tags.length, sessionTags.length)) * 0.4;
    
    // Confidence weight
    score += memory.confidence * 0.3;
    
    // Recency weight (newer memories get slight boost)
    const age = Date.now() - new Date(memory.createdAt).getTime();
    const ageWeeks = age / (1000 * 60 * 60 * 24 * 7);
    score += Math.max(0, (1 - ageWeeks / 52)) * 0.1; // Decay over a year
    
    // Access frequency
    score += Math.min(memory.accessCount / 10, 0.2);
    
    // Context relevance
    if (context && memory.content.toLowerCase().includes(context.toLowerCase())) {
      score += 0.2;
    }
    
    return Math.min(score, 1);
  }
  
  private extractSessionTags(session: BrainstormSession): string[] {
    // Extract tags from session title and messages
    const tags = new Set<string>();
    
    // Add words from title
    session.title.toLowerCase().split(/\s+/).forEach(word => {
      if (word.length > 3) tags.add(word);
    });
    
    return Array.from(tags);
  }
  
  private findRelatedConcepts(memory: MemoryEntry): string[] {
    const relatedMemories = Array.from(this.connections.values())
      .filter(conn => conn.sourceId === memory.id || conn.targetId === memory.id)
      .map(conn => {
        const relatedId = conn.sourceId === memory.id ? conn.targetId : conn.sourceId;
        return this.memories.get(relatedId);
      })
      .filter(Boolean) as MemoryEntry[];
    
    return Array.from(new Set(relatedMemories.flatMap(m => m.tags)));
  }
}

// Export singleton instance
export const brainstormMemory = new BrainstormMemorySystem();