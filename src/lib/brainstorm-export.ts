/**
 * Multi-Format Export for Brainstorming Sessions
 * 
 * Supports exporting to Markdown, JSON, PDF, and Mermaid diagrams
 */

import { 
  BrainstormSession, 
  Idea, 
  IdeaCluster, 
  GeneratedTask,
  IdeaRelationship,
  ViewType
} from '@/types/brainstorm';
import { format } from 'date-fns';
import html2canvas from 'html2canvas';

export type ExportFormat = 'markdown' | 'json' | 'pdf' | 'mermaid' | 'csv' | 'jira' | 'asana' | 'trello';

export interface ExportOptions {
  format: ExportFormat;
  includeMetadata?: boolean;
  includeTimestamps?: boolean;
  includeTasks?: boolean;
  includeRelationships?: boolean;
  viewType?: ViewType;
}

const DEFAULT_OPTIONS: ExportOptions = {
  format: 'markdown',
  includeMetadata: true,
  includeTimestamps: true,
  includeTasks: false,
  includeRelationships: true,
};

/**
 * Main export function that delegates to format-specific exporters
 */
export async function exportBrainstormSession(
  session: BrainstormSession,
  ideas: Idea[],
  clusters: IdeaCluster[],
  relationships: IdeaRelationship[],
  tasks: GeneratedTask[],
  options: Partial<ExportOptions> = {}
): Promise<Blob> {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  switch (opts.format) {
    case 'markdown':
      return exportToMarkdown(session, ideas, clusters, relationships, tasks, opts);
    case 'json':
      return exportToJSON(session, ideas, clusters, relationships, tasks, opts);
    case 'pdf':
      return exportToPDF(session, ideas, clusters, relationships, tasks, opts);
    case 'mermaid':
      return exportToMermaid(session, ideas, clusters, relationships, opts);
    case 'csv':
      return exportToCSV(ideas, tasks, opts);
    default:
      throw new Error(`Unsupported export format: ${opts.format}`);
  }
}

/**
 * Export to Markdown format
 */
function exportToMarkdown(
  session: BrainstormSession,
  ideas: Idea[],
  clusters: IdeaCluster[],
  relationships: IdeaRelationship[],
  tasks: GeneratedTask[],
  options: ExportOptions
): Blob {
  let markdown = `# ${session.title}\n\n`;
  
  if (options.includeMetadata) {
    markdown += `**Created:** ${format(new Date(session.createdAt), 'PPP')}\n`;
    markdown += `**Last Updated:** ${format(new Date(session.updatedAt), 'PPP')}\n`;
    markdown += `**Total Ideas:** ${ideas.length}\n`;
    markdown += `**Total Clusters:** ${clusters.length}\n\n`;
  }
  
  // Session summary
  markdown += `## Summary\n\n`;
  markdown += `This brainstorming session generated ${ideas.length} ideas`;
  if (clusters.length > 0) {
    markdown += ` organized into ${clusters.length} clusters`;
  }
  if (tasks.length > 0 && options.includeTasks) {
    markdown += ` and ${tasks.length} actionable tasks`;
  }
  markdown += `.\n\n`;
  
  // Ideas by cluster
  if (clusters.length > 0) {
    markdown += `## Ideas by Theme\n\n`;
    
    clusters.forEach(cluster => {
      markdown += `### ${cluster.name}\n`;
      markdown += `*${cluster.theme}*\n\n`;
      
      const clusterIdeas = ideas.filter(idea => cluster.ideaIds.includes(idea.id));
      clusterIdeas.forEach(idea => {
        markdown += `- **${idea.content}**`;
        if (idea.priority) {
          markdown += ` [${idea.priority}]`;
        }
        if (idea.tags.length > 0) {
          markdown += ` (${idea.tags.join(', ')})`;
        }
        markdown += '\n';
        if (idea.metadata?.description) {
          markdown += `  - ${idea.metadata.description}\n`;
        }
      });
      markdown += '\n';
    });
    
    // Unclustered ideas
    const clusteredIds = new Set(clusters.flatMap(c => c.ideaIds));
    const unclusteredIdeas = ideas.filter(idea => !clusteredIds.has(idea.id));
    
    if (unclusteredIdeas.length > 0) {
      markdown += `### Uncategorized Ideas\n\n`;
      unclusteredIdeas.forEach(idea => {
        markdown += `- **${idea.content}**`;
        if (idea.priority) {
          markdown += ` [${idea.priority}]`;
        }
        markdown += '\n';
      });
      markdown += '\n';
    }
  } else {
    // All ideas in one list
    markdown += `## Ideas\n\n`;
    ideas.forEach(idea => {
      markdown += `- **${idea.content}**`;
      if (idea.priority) {
        markdown += ` [${idea.priority}]`;
      }
      if (idea.tags.length > 0) {
        markdown += ` (${idea.tags.join(', ')})`;
      }
      markdown += '\n';
    });
    markdown += '\n';
  }
  
  // Relationships
  if (options.includeRelationships && relationships.length > 0) {
    markdown += `## Idea Relationships\n\n`;
    relationships.forEach(rel => {
      const source = ideas.find(i => i.id === rel.sourceId);
      const target = ideas.find(i => i.id === rel.targetId);
      if (source && target) {
        markdown += `- "${source.content}" **${rel.type}** "${target.content}"`;
        if (rel.label) {
          markdown += ` (${rel.label})`;
        }
        markdown += '\n';
      }
    });
    markdown += '\n';
  }
  
  // Generated tasks
  if (options.includeTasks && tasks.length > 0) {
    markdown += `## Generated Tasks\n\n`;
    
    const tasksByPriority = tasks.reduce((acc, task) => {
      if (!acc[task.priority]) acc[task.priority] = [];
      acc[task.priority].push(task);
      return acc;
    }, {} as Record<string, GeneratedTask[]>);
    
    ['critical', 'high', 'medium', 'low'].forEach(priority => {
      const priorityTasks = tasksByPriority[priority];
      if (priorityTasks && priorityTasks.length > 0) {
        markdown += `### ${priority.charAt(0).toUpperCase() + priority.slice(1)} Priority\n\n`;
        priorityTasks.forEach(task => {
          markdown += `- [ ] **${task.title}**`;
          if (task.estimatedEffort) {
            markdown += ` (${task.estimatedEffort}h)`;
          }
          markdown += '\n';
          if (task.description) {
            markdown += `  - ${task.description}\n`;
          }
        });
        markdown += '\n';
      }
    });
  }
  
  // Chat history (optional)
  if (options.includeTimestamps) {
    markdown += `## Session Timeline\n\n`;
    session.messages.slice(-10).forEach(msg => {
      markdown += `**${format(new Date(msg.timestamp), 'HH:mm')}** - `;
      if (msg.type === 'user') {
        markdown += `User: ${msg.content}\n`;
      } else {
        markdown += `AI: ${msg.content.substring(0, 100)}...`;
        if (msg.extractedIdeas && msg.extractedIdeas.length > 0) {
          markdown += ` (extracted ${msg.extractedIdeas.length} ideas)`;
        }
        markdown += '\n';
      }
    });
  }
  
  return new Blob([markdown], { type: 'text/markdown' });
}

/**
 * Export to JSON format
 */
function exportToJSON(
  session: BrainstormSession,
  ideas: Idea[],
  clusters: IdeaCluster[],
  relationships: IdeaRelationship[],
  tasks: GeneratedTask[],
  options: ExportOptions
): Blob {
  const data: any = {
    session: {
      id: session.id,
      title: session.title,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      metadata: session.metadata,
    },
    ideas: ideas,
    clusters: clusters,
  };
  
  if (options.includeRelationships) {
    data.relationships = relationships;
  }
  
  if (options.includeTasks) {
    data.tasks = tasks;
  }
  
  if (options.includeTimestamps) {
    data.messages = session.messages;
  }
  
  return new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
}

/**
 * Export to PDF format with proper PDF generation
 */
async function exportToPDF(
  session: BrainstormSession,
  ideas: Idea[],
  clusters: IdeaCluster[],
  relationships: IdeaRelationship[],
  tasks: GeneratedTask[],
  options: ExportOptions
): Promise<Blob> {
  try {
    // Dynamic import of jsPDF for client-side PDF generation
    const { jsPDF } = await import('jspdf');
    
    // Create a new PDF document
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    let yPosition = 20;
    const pageHeight = 297; // A4 height in mm
    const margin = 20;
    const lineHeight = 7;
    const maxWidth = 170; // A4 width minus margins

    // Helper function to add text with word wrapping
    const addText = (text: string, fontSize: number = 10, isBold: boolean = false) => {
      doc.setFontSize(fontSize);
      doc.setFont('helvetica', isBold ? 'bold' : 'normal');
      
      const lines = doc.splitTextToSize(text, maxWidth);
      
      // Check if we need a new page
      if (yPosition + (lines.length * lineHeight) > pageHeight - margin) {
        doc.addPage();
        yPosition = margin;
      }
      
      doc.text(lines, margin, yPosition);
      yPosition += lines.length * lineHeight;
    };

    const addSpacing = (space: number = 5) => {
      yPosition += space;
    };

    // Title
    addText(session.title, 18, true);
    addSpacing(10);

    // Metadata
    if (options.includeMetadata) {
      addText(`Created: ${format(new Date(session.createdAt), 'PPP')}`, 10);
      addText(`Last Updated: ${format(new Date(session.updatedAt), 'PPP')}`, 10);
      addText(`Total Ideas: ${ideas.length}`, 10);
      addText(`Total Clusters: ${clusters.length}`, 10);
      addSpacing(10);
    }

    // Summary
    addText('Summary', 14, true);
    addSpacing(3);
    const summaryText = `This brainstorming session generated ${ideas.length} ideas${
      clusters.length > 0 ? ` organized into ${clusters.length} clusters` : ''
    }${tasks.length > 0 && options.includeTasks ? ` and ${tasks.length} actionable tasks` : ''}.`;
    addText(summaryText);
    addSpacing(10);

    // Ideas by cluster
    if (clusters.length > 0) {
      addText('Ideas by Theme', 14, true);
      addSpacing(5);
      
      clusters.forEach(cluster => {
        addText(cluster.name, 12, true);
        addText(cluster.theme, 10);
        addSpacing(3);
        
        const clusterIdeas = ideas.filter(idea => cluster.ideaIds.includes(idea.id));
        clusterIdeas.forEach(idea => {
          let ideaText = `• ${idea.content}`;
          if (idea.priority) ideaText += ` [${idea.priority}]`;
          if (idea.tags.length > 0) ideaText += ` (${idea.tags.join(', ')})`;
          
          addText(ideaText, 9);
          
          if (idea.metadata?.description) {
            addText(`  - ${idea.metadata.description}`, 8);
          }
        });
        addSpacing(5);
      });
      
      // Unclustered ideas
      const clusteredIds = new Set(clusters.flatMap(c => c.ideaIds));
      const unclusteredIdeas = ideas.filter(idea => !clusteredIds.has(idea.id));
      
      if (unclusteredIdeas.length > 0) {
        addText('Uncategorized Ideas', 12, true);
        addSpacing(3);
        unclusteredIdeas.forEach(idea => {
          let ideaText = `• ${idea.content}`;
          if (idea.priority) ideaText += ` [${idea.priority}]`;
          addText(ideaText, 9);
        });
        addSpacing(5);
      }
    } else {
      // All ideas in one list
      addText('Ideas', 14, true);
      addSpacing(5);
      ideas.forEach(idea => {
        let ideaText = `• ${idea.content}`;
        if (idea.priority) ideaText += ` [${idea.priority}]`;
        if (idea.tags.length > 0) ideaText += ` (${idea.tags.join(', ')})`;
        addText(ideaText, 9);
      });
      addSpacing(10);
    }

    // Relationships
    if (options.includeRelationships && relationships.length > 0) {
      addText('Idea Relationships', 14, true);
      addSpacing(5);
      relationships.forEach(rel => {
        const source = ideas.find(i => i.id === rel.sourceId);
        const target = ideas.find(i => i.id === rel.targetId);
        if (source && target) {
          let relText = `• "${source.content}" ${rel.type} "${target.content}"`;
          if (rel.label) relText += ` (${rel.label})`;
          addText(relText, 9);
        }
      });
      addSpacing(10);
    }

    // Generated tasks
    if (options.includeTasks && tasks.length > 0) {
      addText('Generated Tasks', 14, true);
      addSpacing(5);
      
      const tasksByPriority = tasks.reduce((acc, task) => {
        if (!acc[task.priority]) acc[task.priority] = [];
        acc[task.priority].push(task);
        return acc;
      }, {} as Record<string, GeneratedTask[]>);
      
      ['critical', 'high', 'medium', 'low'].forEach(priority => {
        const priorityTasks = tasksByPriority[priority];
        if (priorityTasks && priorityTasks.length > 0) {
          addText(`${priority.charAt(0).toUpperCase() + priority.slice(1)} Priority`, 12, true);
          addSpacing(3);
          priorityTasks.forEach(task => {
            let taskText = `☐ ${task.title}`;
            if (task.estimatedEffort) taskText += ` (${task.estimatedEffort}h)`;
            addText(taskText, 9);
            if (task.description) {
              addText(`  - ${task.description}`, 8);
            }
          });
          addSpacing(5);
        }
      });
    }

    // Footer
    addSpacing(10);
    addText(`Generated on ${new Date().toLocaleDateString()} by Claudia Brainstorming`, 8);

    // Convert to blob
    const pdfOutput = doc.output('blob');
    return pdfOutput;
  } catch (error) {
    console.error('PDF generation failed, falling back to HTML:', error);
    
    // Fallback to HTML version
    const markdownBlob = exportToMarkdown(session, ideas, clusters, relationships, tasks, options);
    const markdown = await markdownBlob.text();
    
    const html = generatePrintableHTML(session.title, markdown);
    return new Blob([html], { type: 'text/html' });
  }
}

/**
 * Export to Mermaid diagram format
 */
function exportToMermaid(
  session: BrainstormSession,
  ideas: Idea[],
  clusters: IdeaCluster[],
  relationships: IdeaRelationship[],
  options: ExportOptions
): Blob {
  let mermaid = 'graph TD\n';
  
  // Add clusters as subgraphs
  clusters.forEach((cluster, idx) => {
    mermaid += `  subgraph cluster${idx}["${cluster.name}"]\n`;
    cluster.ideaIds.forEach(ideaId => {
      const idea = ideas.find(i => i.id === ideaId);
      if (idea) {
        const nodeId = `idea_${ideaId.replace(/[^a-zA-Z0-9]/g, '_')}`;
        mermaid += `    ${nodeId}["${idea.content.substring(0, 30)}..."]\n`;
      }
    });
    mermaid += '  end\n';
  });
  
  // Add unclustered ideas
  const clusteredIds = new Set(clusters.flatMap(c => c.ideaIds));
  ideas.filter(idea => !clusteredIds.has(idea.id)).forEach(idea => {
    const nodeId = `idea_${idea.id.replace(/[^a-zA-Z0-9]/g, '_')}`;
    mermaid += `  ${nodeId}["${idea.content.substring(0, 30)}..."]\n`;
  });
  
  // Add relationships
  if (options.includeRelationships) {
    relationships.forEach(rel => {
      const sourceId = `idea_${rel.sourceId.replace(/[^a-zA-Z0-9]/g, '_')}`;
      const targetId = `idea_${rel.targetId.replace(/[^a-zA-Z0-9]/g, '_')}`;
      const label = rel.label || rel.type;
      mermaid += `  ${sourceId} -->|${label}| ${targetId}\n`;
    });
  }
  
  // Style nodes by priority
  mermaid += '\n  %% Styling\n';
  ideas.forEach(idea => {
    const nodeId = `idea_${idea.id.replace(/[^a-zA-Z0-9]/g, '_')}`;
    if (idea.priority === 'critical') {
      mermaid += `  style ${nodeId} fill:#ef4444,color:#fff\n`;
    } else if (idea.priority === 'high') {
      mermaid += `  style ${nodeId} fill:#f59e0b,color:#fff\n`;
    }
  });
  
  return new Blob([mermaid], { type: 'text/plain' });
}

/**
 * Export to CSV format
 */
function exportToCSV(
  ideas: Idea[],
  tasks: GeneratedTask[],
  options: ExportOptions
): Blob {
  const headers = ['Type', 'Content', 'Status', 'Priority', 'Tags', 'Created'];
  const rows = [headers];
  
  // Add ideas
  ideas.forEach(idea => {
    rows.push([
      'Idea',
      `"${idea.content.replace(/"/g, '""')}"`,
      idea.status,
      idea.priority || '',
      idea.tags.join('; '),
      format(new Date(idea.createdAt), 'yyyy-MM-dd HH:mm'),
    ]);
  });
  
  // Add tasks if included
  if (options.includeTasks) {
    tasks.forEach(task => {
      rows.push([
        'Task',
        `"${task.title.replace(/"/g, '""')}"`,
        task.status,
        task.priority,
        task.tags.join('; '),
        format(new Date(task.createdAt), 'yyyy-MM-dd HH:mm'),
      ]);
    });
  }
  
  const csv = rows.map(row => row.join(',')).join('\n');
  return new Blob([csv], { type: 'text/csv' });
}

/**
 * Simple markdown to HTML converter
 */
function markdownToHTML(markdown: string): string {
  return markdown
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/^\* (.+)/gim, '<li>$1</li>')
    .replace(/^- (.+)/gim, '<li>$1</li>')
    .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.+?)\*/g, '<em>$1</em>')
    .replace(/\n\n/g, '</p><p>')
    .replace(/^/, '<p>')
    .replace(/$/, '</p>')
    .replace(/<\/li>\n/g, '</li>')
    .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
}

/**
 * Generate printable HTML document
 */
function generatePrintableHTML(title: string, markdown: string): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>${title}</title>
      <style>
        body { 
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 40px 20px;
        }
        h1, h2, h3 { color: #2563eb; }
        h1 { border-bottom: 3px solid #2563eb; padding-bottom: 10px; }
        h2 { margin-top: 30px; }
        h3 { color: #4b5563; }
        ul { padding-left: 20px; }
        li { margin: 8px 0; }
        strong { color: #1f2937; }
        em { color: #6b7280; }
        @media print {
          body { margin: 0; padding: 20px; }
        }
      </style>
    </head>
    <body>
      ${markdownToHTML(markdown)}
    </body>
    </html>
  `;
}

/**
 * Generate a downloadable file
 */
export function downloadFile(blob: Blob, filename: string) {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

/**
 * Get appropriate file extension for export format
 */
export function getFileExtension(format: ExportFormat): string {
  switch (format) {
    case 'markdown': return '.md';
    case 'json': return '.json';
    case 'pdf': return '.pdf';
    case 'mermaid': return '.mmd';
    case 'csv': return '.csv';
    default: return '.txt';
  }
}