/**
 * Brainstorming Templates System
 * 
 * Provides predefined templates for common brainstorming scenarios
 */

import { ChatMessage, Idea, IdeaStatus } from '@/types/brainstorm';

export interface BrainstormTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'product' | 'technical' | 'creative' | 'strategy' | 'problem-solving';
  prompts: string[];
  initialIdeas?: Partial<Idea>[];
  suggestedTags: string[];
  metadata?: {
    estimatedDuration?: string;
    difficulty?: 'beginner' | 'intermediate' | 'advanced';
    participantCount?: string;
  };
}

export const brainstormTemplates: BrainstormTemplate[] = [
  {
    id: 'product-features',
    name: 'Product Feature Ideation',
    description: 'Generate ideas for new product features and improvements',
    icon: '🚀',
    category: 'product',
    prompts: [
      'What pain points are users currently experiencing?',
      'What features do competitors offer that we don\'t?',
      'How can we make the user experience more delightful?',
      'What would make users recommend our product to others?',
      'What integrations would provide the most value?'
    ],
    suggestedTags: ['feature', 'user-experience', 'enhancement', 'integration', 'innovation'],
    metadata: {
      estimatedDuration: '45-60 minutes',
      difficulty: 'intermediate',
      participantCount: '3-8'
    }
  },
  {
    id: 'technical-architecture',
    name: 'Technical Architecture Planning',
    description: 'Design system architecture and technical solutions',
    icon: '🏗️',
    category: 'technical',
    prompts: [
      'What are the key system components and their interactions?',
      'What are the potential scalability bottlenecks?',
      'Which design patterns would best solve this problem?',
      'What are the security considerations?',
      'How can we ensure maintainability and extensibility?'
    ],
    suggestedTags: ['architecture', 'scalability', 'security', 'patterns', 'infrastructure'],
    metadata: {
      estimatedDuration: '60-90 minutes',
      difficulty: 'advanced',
      participantCount: '2-5'
    }
  },
  {
    id: 'bug-root-cause',
    name: 'Bug Root Cause Analysis',
    description: 'Systematically analyze and solve complex bugs',
    icon: '🐛',
    category: 'problem-solving',
    prompts: [
      'What are the exact symptoms and error messages?',
      'When did this issue first appear?',
      'What recent changes might have caused this?',
      'Can we reproduce this consistently?',
      'What are potential workarounds?',
      'What is the long-term fix?'
    ],
    suggestedTags: ['bug', 'debugging', 'root-cause', 'fix', 'workaround'],
    metadata: {
      estimatedDuration: '30-45 minutes',
      difficulty: 'intermediate',
      participantCount: '1-3'
    }
  },
  {
    id: 'creative-campaign',
    name: 'Creative Campaign Development',
    description: 'Brainstorm creative concepts for marketing campaigns',
    icon: '🎨',
    category: 'creative',
    prompts: [
      'What emotions do we want to evoke?',
      'What\'s our unique angle or hook?',
      'Which channels will be most effective?',
      'What visual elements will stand out?',
      'How can we make this shareable?',
      'What\'s the call to action?'
    ],
    suggestedTags: ['campaign', 'creative', 'marketing', 'branding', 'content'],
    metadata: {
      estimatedDuration: '60-90 minutes',
      difficulty: 'intermediate',
      participantCount: '4-10'
    }
  },
  {
    id: 'startup-pivot',
    name: 'Startup Pivot Strategy',
    description: 'Explore pivot options and strategic directions',
    icon: '🔄',
    category: 'strategy',
    prompts: [
      'What\'s not working with our current approach?',
      'What assets and strengths can we leverage?',
      'What market opportunities are we seeing?',
      'How can we test new directions quickly?',
      'What would success look like in 6 months?',
      'What are the risks of each option?'
    ],
    suggestedTags: ['pivot', 'strategy', 'market', 'opportunity', 'risk'],
    metadata: {
      estimatedDuration: '90-120 minutes',
      difficulty: 'advanced',
      participantCount: '3-6'
    }
  },
  {
    id: 'user-research',
    name: 'User Research Planning',
    description: 'Plan comprehensive user research initiatives',
    icon: '🔍',
    category: 'product',
    prompts: [
      'What do we need to learn about our users?',
      'What assumptions need validation?',
      'Which research methods would work best?',
      'Who is our target participant profile?',
      'What are the key questions to ask?',
      'How will we analyze and act on findings?'
    ],
    suggestedTags: ['research', 'user', 'validation', 'insights', 'methodology'],
    metadata: {
      estimatedDuration: '45-60 minutes',
      difficulty: 'intermediate',
      participantCount: '2-4'
    }
  },
  {
    id: 'api-design',
    name: 'API Design Workshop',
    description: 'Design clean and intuitive API interfaces',
    icon: '🔌',
    category: 'technical',
    prompts: [
      'What resources need to be exposed?',
      'What are the main use cases?',
      'How should we handle authentication?',
      'What\'s the versioning strategy?',
      'How do we ensure consistency?',
      'What rate limiting is needed?'
    ],
    suggestedTags: ['api', 'design', 'rest', 'graphql', 'authentication'],
    metadata: {
      estimatedDuration: '60-75 minutes',
      difficulty: 'advanced',
      participantCount: '2-4'
    }
  },
  {
    id: 'team-retrospective',
    name: 'Team Retrospective',
    description: 'Reflect on team performance and improvements',
    icon: '🤝',
    category: 'strategy',
    prompts: [
      'What went well this sprint/quarter?',
      'What didn\'t go as planned?',
      'What did we learn?',
      'What should we start doing?',
      'What should we stop doing?',
      'What should we continue doing?'
    ],
    suggestedTags: ['retrospective', 'team', 'improvement', 'process', 'feedback'],
    metadata: {
      estimatedDuration: '60 minutes',
      difficulty: 'beginner',
      participantCount: '3-10'
    }
  },
  {
    id: 'content-strategy',
    name: 'Content Strategy Session',
    description: 'Plan content creation and distribution strategy',
    icon: '📝',
    category: 'creative',
    prompts: [
      'Who is our target audience?',
      'What content formats work best?',
      'What topics resonate most?',
      'How often should we publish?',
      'Which channels should we prioritize?',
      'How do we measure success?'
    ],
    suggestedTags: ['content', 'strategy', 'audience', 'distribution', 'metrics'],
    metadata: {
      estimatedDuration: '45-60 minutes',
      difficulty: 'intermediate',
      participantCount: '2-5'
    }
  },
  {
    id: 'crisis-response',
    name: 'Crisis Response Planning',
    description: 'Develop action plans for potential crises',
    icon: '🚨',
    category: 'problem-solving',
    prompts: [
      'What are the potential crisis scenarios?',
      'Who are the key stakeholders?',
      'What\'s our communication strategy?',
      'What are the immediate actions?',
      'How do we minimize damage?',
      'What are the recovery steps?'
    ],
    suggestedTags: ['crisis', 'planning', 'communication', 'response', 'recovery'],
    metadata: {
      estimatedDuration: '90-120 minutes',
      difficulty: 'advanced',
      participantCount: '4-8'
    }
  }
];

/**
 * Apply a template to start a new brainstorming session
 */
export function applyTemplate(
  templateId: string,
  sessionId: string
): {
  initialMessage: ChatMessage;
  suggestedIdeas: Partial<Idea>[];
} {
  const template = brainstormTemplates.find(t => t.id === templateId);
  
  if (!template) {
    throw new Error(`Template ${templateId} not found`);
  }

  // Create initial message with template prompts
  const initialMessage: ChatMessage = {
    id: `msg_${Date.now()}`,
    type: 'assistant',
    content: `Let's start a ${template.name} session! Here are some prompts to guide our brainstorming:\n\n${
      template.prompts.map((prompt, i) => `${i + 1}. ${prompt}`).join('\n')
    }\n\nFeel free to explore these questions or take the discussion in any direction that feels productive.`,
    timestamp: Date.now(),
  };

  // Create suggested ideas from template
  const suggestedIdeas: Partial<Idea>[] = template.initialIdeas || [];
  
  // Add prompt-based ideas
  template.prompts.slice(0, 3).forEach((prompt, i) => {
    suggestedIdeas.push({
      content: prompt,
      status: IdeaStatus.TO_EXPLORE,
      tags: [template.category, ...template.suggestedTags.slice(0, 2)],
      priority: i === 0 ? 'high' : 'medium',
      metadata: {
        isPrompt: true,
        templateId: template.id,
      }
    });
  });

  return {
    initialMessage,
    suggestedIdeas,
  };
}

/**
 * Get templates by category
 */
export function getTemplatesByCategory(category: BrainstormTemplate['category']): BrainstormTemplate[] {
  return brainstormTemplates.filter(t => t.category === category);
}

/**
 * Get template recommendations based on keywords
 */
export function getTemplateRecommendations(keywords: string[]): BrainstormTemplate[] {
  const keywordsLower = keywords.map(k => k.toLowerCase());
  
  return brainstormTemplates
    .map(template => {
      let score = 0;
      
      // Check name and description
      const searchText = `${template.name} ${template.description}`.toLowerCase();
      keywordsLower.forEach(keyword => {
        if (searchText.includes(keyword)) score += 2;
      });
      
      // Check tags
      template.suggestedTags.forEach(tag => {
        if (keywordsLower.includes(tag.toLowerCase())) score += 1;
      });
      
      // Check prompts
      template.prompts.forEach(prompt => {
        keywordsLower.forEach(keyword => {
          if (prompt.toLowerCase().includes(keyword)) score += 0.5;
        });
      });
      
      return { template, score };
    })
    .filter(({ score }) => score > 0)
    .sort((a, b) => b.score - a.score)
    .map(({ template }) => template);
}

/**
 * Create a custom template from a session
 */
export function createTemplateFromSession(
  session: {
    title: string;
    messages: ChatMessage[];
    ideas: Idea[];
    tags: string[];
  }
): Omit<BrainstormTemplate, 'id'> {
  // Extract prompts from user messages
  const prompts = session.messages
    .filter(m => m.type === 'user')
    .map(m => m.content)
    .filter(content => content.endsWith('?'))
    .slice(0, 6);
  
  // Get most common tags
  const tagCounts = session.ideas.reduce((acc, idea) => {
    idea.tags.forEach(tag => {
      acc[tag] = (acc[tag] || 0) + 1;
    });
    return acc;
  }, {} as Record<string, number>);
  
  const suggestedTags = Object.entries(tagCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([tag]) => tag);
  
  // Determine category based on tags
  let category: BrainstormTemplate['category'] = 'creative';
  if (suggestedTags.some(tag => ['api', 'architecture', 'technical', 'code'].includes(tag))) {
    category = 'technical';
  } else if (suggestedTags.some(tag => ['product', 'feature', 'user'].includes(tag))) {
    category = 'product';
  } else if (suggestedTags.some(tag => ['strategy', 'planning', 'business'].includes(tag))) {
    category = 'strategy';
  } else if (suggestedTags.some(tag => ['bug', 'issue', 'problem'].includes(tag))) {
    category = 'problem-solving';
  }
  
  return {
    name: session.title,
    description: `Custom template based on "${session.title}" session`,
    icon: '✨',
    category,
    prompts,
    suggestedTags,
    metadata: {
      estimatedDuration: '60 minutes',
      difficulty: 'intermediate',
    }
  };
}