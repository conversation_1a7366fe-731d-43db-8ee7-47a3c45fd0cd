/**
 * Universal Session Persistence Service
 * 
 * Unified persistence layer that handles all session types (<PERSON>, Brain<PERSON>, Orchestra)
 * with relationship tracking, backup/recovery, and cross-platform storage.
 * 
 * Replaces fragmented storage systems with a centralized approach.
 */

import { appDataDir, join } from '@tauri-apps/api/path';
import { readTextFile, writeTextFile, exists, mkdir, readDir, remove } from '@tauri-apps/plugin-fs';
import type {
  UnifiedSessionInfo,
  SessionRelationship,
  SessionExportData,
  SessionBackup,
  UnifiedSessionSettings
} from '../types/unified-session';

// Storage paths
const SESSIONS_DIR = 'sessions';
const RELATIONSHIPS_DIR = 'relationships';
const BACKUPS_DIR = 'backups';
const SETTINGS_FILE = 'session-settings.json';
const INDEX_FILE = 'session-index.json';

// Storage interfaces
interface SessionIndex {
  version: string;
  lastUpdated: string;
  sessions: {
    [sessionId: string]: {
      type: 'claude' | 'brainstorm' | 'orchestra';
      title: string;
      projectPath?: string;
      createdAt: string;
      updatedAt: string;
      status: string;
      filePath: string;
    };
  };
  relationships: {
    [relationshipId: string]: {
      parentSessionId: string;
      childSessionId: string;
      relationshipType: string;
      createdAt: string;
      filePath: string;
    };
  };
}

interface StorageAdapter {
  ensureDirectoryExists(path: string): Promise<void>;
  readFile(path: string): Promise<string>;
  writeFile(path: string, content: string): Promise<void>;
  deleteFile(path: string): Promise<void>;
  listFiles(path: string): Promise<string[]>;
  fileExists(path: string): Promise<boolean>;
}

/**
 * Tauri-based storage adapter for desktop application
 */
class TauriStorageAdapter implements StorageAdapter {
  private basePath: string | null = null;

  private async getBasePath(): Promise<string> {
    if (!this.basePath) {
      this.basePath = await join(await appDataDir(), 'claudia');
    }
    return this.basePath;
  }

  async ensureDirectoryExists(relativePath: string): Promise<void> {
    const fullPath = await join(await this.getBasePath(), relativePath);
    
    try {
      if (!(await exists(fullPath))) {
        await mkdir(fullPath, { recursive: true });
      }
    } catch (error) {
      console.error(`Failed to create directory ${fullPath}:`, error);
      throw new Error(`Failed to create directory: ${error}`);
    }
  }

  async readFile(relativePath: string): Promise<string> {
    const fullPath = await join(await this.getBasePath(), relativePath);
    
    try {
      return await readTextFile(fullPath);
    } catch (error) {
      throw new Error(`Failed to read file ${relativePath}: ${error}`);
    }
  }

  async writeFile(relativePath: string, content: string): Promise<void> {
    const fullPath = await join(await this.getBasePath(), relativePath);
    
    try {
      // Ensure directory exists
      const dirPath = fullPath.substring(0, fullPath.lastIndexOf('/'));
      if (!(await exists(dirPath))) {
        await mkdir(dirPath, { recursive: true });
      }
      
      await writeTextFile(fullPath, content);
    } catch (error) {
      throw new Error(`Failed to write file ${relativePath}: ${error}`);
    }
  }

  async deleteFile(relativePath: string): Promise<void> {
    const fullPath = await join(await this.getBasePath(), relativePath);
    
    try {
      if (await exists(fullPath)) {
        await remove(fullPath);
      }
    } catch (error) {
      throw new Error(`Failed to delete file ${relativePath}: ${error}`);
    }
  }

  async listFiles(relativePath: string): Promise<string[]> {
    const fullPath = await join(await this.getBasePath(), relativePath);
    
    try {
      if (!(await exists(fullPath))) {
        return [];
      }
      
      const entries = await readDir(fullPath);
      return entries
        .filter(entry => entry.isFile) // Only files, not directories
        .map(entry => entry.name || '');
    } catch (error) {
      console.error(`Failed to list files in ${relativePath}:`, error);
      return [];
    }
  }

  async fileExists(relativePath: string): Promise<boolean> {
    const fullPath = await join(await this.getBasePath(), relativePath);
    
    try {
      return await exists(fullPath);
    } catch (error) {
      return false;
    }
  }
}

/**
 * Universal Persistence Service
 */
export class UniversalPersistenceService {
  private storage: StorageAdapter;
  private sessionIndex: SessionIndex | null = null;
  private isInitialized = false;

  constructor(storage?: StorageAdapter) {
    this.storage = storage || new TauriStorageAdapter();
  }

  /**
   * Initialize persistence service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Ensure required directories exist
      await this.storage.ensureDirectoryExists(SESSIONS_DIR);
      await this.storage.ensureDirectoryExists(RELATIONSHIPS_DIR);
      await this.storage.ensureDirectoryExists(BACKUPS_DIR);

      // Load or create session index
      await this.loadSessionIndex();
      
      this.isInitialized = true;
      console.log('Universal Persistence Service initialized');
    } catch (error) {
      console.error('Failed to initialize persistence service:', error);
      throw error;
    }
  }

  /**
   * Save a session to persistent storage
   */
  async saveSession(session: UnifiedSessionInfo): Promise<void> {
    await this.ensureInitialized();

    try {
      const fileName = `${session.type}_${session.id}.json`;
      const filePath = await join(SESSIONS_DIR, fileName);
      
      // Prepare session data for storage
      const sessionData = {
        ...session,
        persistedAt: new Date().toISOString(),
        version: '1.0.0'
      };

      // Write session file
      await this.storage.writeFile(filePath, JSON.stringify(sessionData, null, 2));

      // Update session index
      await this.updateSessionIndex(session, filePath);

      console.log(`Session saved: ${session.type}:${session.id}`);
    } catch (error) {
      console.error(`Failed to save session ${session.id}:`, error);
      throw error;
    }
  }

  /**
   * Load a session from persistent storage
   */
  async loadSession(sessionId: string): Promise<UnifiedSessionInfo | null> {
    await this.ensureInitialized();

    try {
      const indexEntry = this.sessionIndex?.sessions[sessionId];
      if (!indexEntry) {
        return null;
      }

      const sessionData = await this.storage.readFile(indexEntry.filePath);
      const session = JSON.parse(sessionData) as UnifiedSessionInfo;

      console.log(`Session loaded: ${session.type}:${session.id}`);
      return session;
    } catch (error) {
      console.error(`Failed to load session ${sessionId}:`, error);
      return null;
    }
  }

  /**
   * Delete a session from persistent storage
   */
  async deleteSession(sessionId: string): Promise<void> {
    await this.ensureInitialized();

    try {
      const indexEntry = this.sessionIndex?.sessions[sessionId];
      if (!indexEntry) {
        return;
      }

      // Delete session file
      await this.storage.deleteFile(indexEntry.filePath);

      // Remove from index
      if (this.sessionIndex) {
        delete this.sessionIndex.sessions[sessionId];
        await this.saveSessionIndex();
      }

      console.log(`Session deleted: ${sessionId}`);
    } catch (error) {
      console.error(`Failed to delete session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Load all sessions from persistent storage
   */
  async loadAllSessions(): Promise<UnifiedSessionInfo[]> {
    await this.ensureInitialized();

    const sessions: UnifiedSessionInfo[] = [];

    if (!this.sessionIndex) {
      return sessions;
    }

    for (const [sessionId] of Object.entries(this.sessionIndex.sessions)) {
      try {
        const session = await this.loadSession(sessionId);
        if (session) {
          sessions.push(session);
        }
      } catch (error) {
        console.error(`Failed to load session ${sessionId}:`, error);
        // Continue loading other sessions
      }
    }

    return sessions;
  }

  /**
   * Save a session relationship
   */
  async saveRelationship(relationship: SessionRelationship): Promise<void> {
    await this.ensureInitialized();

    try {
      const fileName = `relationship_${relationship.id}.json`;
      const filePath = await join(RELATIONSHIPS_DIR, fileName);

      const relationshipData = {
        ...relationship,
        persistedAt: new Date().toISOString(),
        version: '1.0.0'
      };

      await this.storage.writeFile(filePath, JSON.stringify(relationshipData, null, 2));

      // Update relationships index
      if (this.sessionIndex) {
        this.sessionIndex.relationships[relationship.id] = {
          parentSessionId: relationship.parentSessionId,
          childSessionId: relationship.childSessionId,
          relationshipType: relationship.relationshipType,
          createdAt: relationship.createdAt,
          filePath
        };
        await this.saveSessionIndex();
      }

      console.log(`Relationship saved: ${relationship.id}`);
    } catch (error) {
      console.error(`Failed to save relationship ${relationship.id}:`, error);
      throw error;
    }
  }

  /**
   * Load all session relationships
   */
  async loadAllRelationships(): Promise<SessionRelationship[]> {
    await this.ensureInitialized();

    const relationships: SessionRelationship[] = [];

    if (!this.sessionIndex) {
      return relationships;
    }

    for (const [relationshipId, indexEntry] of Object.entries(this.sessionIndex.relationships)) {
      try {
        const relationshipData = await this.storage.readFile(indexEntry.filePath);
        const relationship = JSON.parse(relationshipData) as SessionRelationship;
        relationships.push(relationship);
      } catch (error) {
        console.error(`Failed to load relationship ${relationshipId}:`, error);
        // Continue loading other relationships
      }
    }

    return relationships;
  }

  /**
   * Save session settings
   */
  async saveSettings(settings: UnifiedSessionSettings): Promise<void> {
    await this.ensureInitialized();

    try {
      const settingsData = {
        ...settings,
        savedAt: new Date().toISOString(),
        version: '1.0.0'
      };

      await this.storage.writeFile(SETTINGS_FILE, JSON.stringify(settingsData, null, 2));
      console.log('Session settings saved');
    } catch (error) {
      console.error('Failed to save session settings:', error);
      throw error;
    }
  }

  /**
   * Load session settings
   */
  async loadSettings(): Promise<UnifiedSessionSettings | null> {
    await this.ensureInitialized();

    try {
      if (!(await this.storage.fileExists(SETTINGS_FILE))) {
        return null;
      }

      const settingsData = await this.storage.readFile(SETTINGS_FILE);
      const settings = JSON.parse(settingsData) as UnifiedSessionSettings;
      
      console.log('Session settings loaded');
      return settings;
    } catch (error) {
      console.error('Failed to load session settings:', error);
      return null;
    }
  }

  /**
   * Create a backup of all sessions and relationships
   */
  async createBackup(name: string, description?: string): Promise<SessionBackup> {
    await this.ensureInitialized();

    try {
      const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const sessions = await this.loadAllSessions();
      const relationships = await this.loadAllRelationships();
      const settings = await this.loadSettings();

      const exportData: SessionExportData = {
        version: '1.0.0',
        exportedAt: new Date().toISOString(),
        sessions,
        relationships,
        settings: settings || {} as UnifiedSessionSettings
      };

      const backupData: SessionBackup = {
        id: backupId,
        name,
        description,
        createdAt: new Date().toISOString(),
        sessionIds: sessions.map(s => s.id),
        data: exportData,
        size: JSON.stringify(exportData).length,
        compressed: false
      };

      const fileName = `${backupId}.json`;
      const filePath = await join(BACKUPS_DIR, fileName);

      await this.storage.writeFile(filePath, JSON.stringify(backupData, null, 2));

      console.log(`Backup created: ${name} (${backupId})`);
      return backupData;
    } catch (error) {
      console.error('Failed to create backup:', error);
      throw error;
    }
  }

  /**
   * List available backups
   */
  async listBackups(): Promise<SessionBackup[]> {
    await this.ensureInitialized();

    try {
      const backupFiles = await this.storage.listFiles(BACKUPS_DIR);
      const backups: SessionBackup[] = [];

      for (const fileName of backupFiles.filter(f => f.endsWith('.json'))) {
        try {
          const filePath = await join(BACKUPS_DIR, fileName);
          const backupData = await this.storage.readFile(filePath);
          const backup = JSON.parse(backupData) as SessionBackup;
          backups.push(backup);
        } catch (error) {
          console.error(`Failed to load backup ${fileName}:`, error);
        }
      }

      return backups.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    } catch (error) {
      console.error('Failed to list backups:', error);
      return [];
    }
  }

  /**
   * Restore from backup
   */
  async restoreFromBackup(backupId: string, options: { 
    overwriteExisting?: boolean;
    selectiveSessions?: string[];
  } = {}): Promise<void> {
    await this.ensureInitialized();

    try {
      const fileName = `${backupId}.json`;
      const filePath = await join(BACKUPS_DIR, fileName);

      if (!(await this.storage.fileExists(filePath))) {
        throw new Error(`Backup not found: ${backupId}`);
      }

      const backupData = await this.storage.readFile(filePath);
      const backup = JSON.parse(backupData) as SessionBackup;

      const { sessions, relationships } = backup.data;
      
      // Filter sessions if selective restore
      const sessionsToRestore = options.selectiveSessions 
        ? sessions.filter(s => options.selectiveSessions!.includes(s.id))
        : sessions;

      // Restore sessions
      for (const session of sessionsToRestore) {
        if (!options.overwriteExisting && this.sessionIndex?.sessions[session.id]) {
          console.log(`Skipping existing session: ${session.id}`);
          continue;
        }
        
        await this.saveSession(session);
      }

      // Restore relationships
      for (const relationship of relationships) {
        await this.saveRelationship(relationship);
      }

      console.log(`Restored from backup: ${backup.name} (${backupId})`);
    } catch (error) {
      console.error(`Failed to restore from backup ${backupId}:`, error);
      throw error;
    }
  }

  /**
   * Clean up orphaned files and optimize storage
   */
  async cleanup(): Promise<void> {
    await this.ensureInitialized();

    try {
      // Clean up orphaned session files
      const sessionFiles = await this.storage.listFiles(SESSIONS_DIR);
      const validSessionFiles = new Set(Object.values(this.sessionIndex?.sessions || {}).map(s => s.filePath.split('/').pop()));

      for (const fileName of sessionFiles) {
        if (!validSessionFiles.has(fileName)) {
          console.log(`Removing orphaned session file: ${fileName}`);
          await this.storage.deleteFile(await join(SESSIONS_DIR, fileName));
        }
      }

      // Clean up orphaned relationship files
      const relationshipFiles = await this.storage.listFiles(RELATIONSHIPS_DIR);
      const validRelationshipFiles = new Set(Object.values(this.sessionIndex?.relationships || {}).map(r => r.filePath.split('/').pop()));

      for (const fileName of relationshipFiles) {
        if (!validRelationshipFiles.has(fileName)) {
          console.log(`Removing orphaned relationship file: ${fileName}`);
          await this.storage.deleteFile(await join(RELATIONSHIPS_DIR, fileName));
        }
      }

      console.log('Storage cleanup completed');
    } catch (error) {
      console.error('Failed to clean up storage:', error);
      throw error;
    }
  }

  // Private helper methods

  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  private async loadSessionIndex(): Promise<void> {
    try {
      if (await this.storage.fileExists(INDEX_FILE)) {
        const indexData = await this.storage.readFile(INDEX_FILE);
        this.sessionIndex = JSON.parse(indexData);
      } else {
        this.sessionIndex = {
          version: '1.0.0',
          lastUpdated: new Date().toISOString(),
          sessions: {},
          relationships: {}
        };
        await this.saveSessionIndex();
      }
    } catch (error) {
      console.error('Failed to load session index:', error);
      // Create new index if loading fails
      this.sessionIndex = {
        version: '1.0.0',
        lastUpdated: new Date().toISOString(),
        sessions: {},
        relationships: {}
      };
    }
  }

  private async saveSessionIndex(): Promise<void> {
    if (!this.sessionIndex) return;

    try {
      this.sessionIndex.lastUpdated = new Date().toISOString();
      await this.storage.writeFile(INDEX_FILE, JSON.stringify(this.sessionIndex, null, 2));
    } catch (error) {
      console.error('Failed to save session index:', error);
      throw error;
    }
  }

  private async updateSessionIndex(session: UnifiedSessionInfo, filePath: string): Promise<void> {
    if (!this.sessionIndex) return;

    this.sessionIndex.sessions[session.id] = {
      type: session.type,
      title: session.title,
      projectPath: session.projectPath,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      status: session.status,
      filePath
    };

    await this.saveSessionIndex();
  }
}

// Export singleton instance
export const universalPersistence = new UniversalPersistenceService();