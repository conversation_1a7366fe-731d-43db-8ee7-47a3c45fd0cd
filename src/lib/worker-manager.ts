/**
 * Worker Manager
 * 
 * Manages web workers for computationally expensive operations
 */

import type { Idea, Message } from '@/types/brainstorm';

interface WorkerTask<T> {
  id: string;
  type: string;
  resolve: (value: T) => void;
  reject: (error: Error) => void;
  timeout?: NodeJS.Timeout;
}

export class WorkerManager {
  private clusteringWorker: Worker | null = null;
  private extractionWorker: Worker | null = null;
  private tasks = new Map<string, WorkerTask<any>>();
  private initialized = false;

  /**
   * Initialize workers
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Create clustering worker
      this.clusteringWorker = new Worker(
        new URL('../workers/clustering.worker.ts', import.meta.url),
        { type: 'module' }
      );

      this.clusteringWorker.addEventListener('message', (event) => {
        this.handleWorkerMessage('clustering', event);
      });

      this.clusteringWorker.addEventListener('error', (error) => {
        console.error('Clustering worker error:', error);
        this.rejectAllTasks('clustering', new Error('Worker crashed'));
      });

      // Create extraction worker
      this.extractionWorker = new Worker(
        new URL('../workers/idea-extraction.worker.ts', import.meta.url),
        { type: 'module' }
      );

      this.extractionWorker.addEventListener('message', (event) => {
        this.handleWorkerMessage('extraction', event);
      });

      this.extractionWorker.addEventListener('error', (error) => {
        console.error('Extraction worker error:', error);
        this.rejectAllTasks('extraction', new Error('Worker crashed'));
      });

      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize workers:', error);
      throw new Error('Web Workers are not supported in this environment');
    }
  }

  /**
   * Terminate all workers
   */
  terminate(): void {
    this.clusteringWorker?.terminate();
    this.extractionWorker?.terminate();
    this.clusteringWorker = null;
    this.extractionWorker = null;
    this.initialized = false;
    
    // Reject all pending tasks
    for (const task of this.tasks.values()) {
      task.reject(new Error('Workers terminated'));
      if (task.timeout) clearTimeout(task.timeout);
    }
    this.tasks.clear();
  }

  /**
   * Perform clustering on ideas using web worker
   */
  async clusterIdeas(
    ideas: Idea[],
    options?: {
      algorithm?: 'kmeans' | 'hierarchical' | 'density';
      maxClusters?: number;
      minClusterSize?: number;
      similarityThreshold?: number;
    }
  ): Promise<{
    clusters: Array<{
      id: string;
      name: string;
      ideaIds: string[];
      quality: number;
    }>;
    stats?: {
      totalIdeas: number;
      clusterCount: number;
      avgClusterSize: number;
      avgQuality: number;
    };
  }> {
    await this.initialize();

    const taskId = `cluster_${Date.now()}_${Math.random()}`;
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.tasks.delete(taskId);
        reject(new Error('Clustering operation timed out'));
      }, 30000); // 30 second timeout

      this.tasks.set(taskId, {
        id: taskId,
        type: 'clustering',
        resolve,
        reject,
        timeout
      });

      this.clusteringWorker!.postMessage({
        taskId,
        type: 'cluster',
        ideas,
        options
      });
    });
  }

  /**
   * Re-cluster with different parameters
   */
  async reclusterIdeas(
    ideas: Idea[],
    options?: {
      maxClusters?: number;
      minClusterSize?: number;
    }
  ): Promise<{
    clusters: Array<{
      id: string;
      name: string;
      ideaIds: string[];
      quality: number;
    }>;
  }> {
    await this.initialize();

    const taskId = `recluster_${Date.now()}_${Math.random()}`;
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.tasks.delete(taskId);
        reject(new Error('Re-clustering operation timed out'));
      }, 30000);

      this.tasks.set(taskId, {
        id: taskId,
        type: 'clustering',
        resolve,
        reject,
        timeout
      });

      this.clusteringWorker!.postMessage({
        taskId,
        type: 'recluster',
        ideas,
        options
      });
    });
  }

  /**
   * Find optimal number of clusters
   */
  async optimizeClusters(
    ideas: Idea[]
  ): Promise<{
    clusters: Array<{
      id: string;
      name: string;
      ideaIds: string[];
      quality: number;
    }>;
    optimal: {
      k: number;
      results: Array<{ k: number; quality: number }>;
    };
  }> {
    await this.initialize();

    const taskId = `optimize_${Date.now()}_${Math.random()}`;
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.tasks.delete(taskId);
        reject(new Error('Optimization operation timed out'));
      }, 60000); // 60 second timeout for optimization

      this.tasks.set(taskId, {
        id: taskId,
        type: 'clustering',
        resolve,
        reject,
        timeout
      });

      this.clusteringWorker!.postMessage({
        taskId,
        type: 'optimize',
        ideas,
        options: {}
      });
    });
  }

  /**
   * Extract ideas from messages using web worker
   */
  async extractIdeas(
    messages: Message[],
    sessionId: string,
    options?: {
      minConfidence?: number;
      maxIdeasPerMessage?: number;
      includeMetadata?: boolean;
    }
  ): Promise<{
    ideas: Idea[];
    stats: {
      messagesProcessed: number;
      ideasExtracted: number;
      averageConfidence?: number;
    };
  }> {
    await this.initialize();

    const taskId = `extract_${Date.now()}_${Math.random()}`;
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.tasks.delete(taskId);
        reject(new Error('Extraction operation timed out'));
      }, 30000);

      this.tasks.set(taskId, {
        id: taskId,
        type: 'extraction',
        resolve,
        reject,
        timeout
      });

      this.extractionWorker!.postMessage({
        taskId,
        type: 'extract',
        messages,
        sessionId,
        options
      });
    });
  }

  /**
   * Batch extract ideas with progress updates
   */
  async batchExtractIdeas(
    messages: Message[],
    sessionId: string,
    options?: {
      minConfidence?: number;
      maxIdeasPerMessage?: number;
      includeMetadata?: boolean;
    },
    onProgress?: (processed: number, total: number, currentIdeas: number) => void
  ): Promise<{
    ideas: Idea[];
    stats: {
      messagesProcessed: number;
      ideasExtracted: number;
    };
  }> {
    await this.initialize();

    const taskId = `batch_${Date.now()}_${Math.random()}`;
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.tasks.delete(taskId);
        reject(new Error('Batch extraction operation timed out'));
      }, 120000); // 2 minute timeout for batch operations

      const task: WorkerTask<any> & { onProgress?: typeof onProgress } = {
        id: taskId,
        type: 'extraction',
        resolve,
        reject,
        timeout,
        onProgress
      };

      this.tasks.set(taskId, task);

      this.extractionWorker!.postMessage({
        taskId,
        type: 'batch',
        messages,
        sessionId,
        options
      });
    });
  }

  /**
   * Analyze text for idea potential
   */
  async analyzeText(
    text: string
  ): Promise<{
    ideaCount: number;
    categories: Record<string, number>;
    overallSentiment: 'positive' | 'negative' | 'neutral';
    actionableRatio: number;
    keywords: string[];
  }> {
    await this.initialize();

    const taskId = `analyze_${Date.now()}_${Math.random()}`;
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.tasks.delete(taskId);
        reject(new Error('Analysis operation timed out'));
      }, 10000); // 10 second timeout

      this.tasks.set(taskId, {
        id: taskId,
        type: 'extraction',
        resolve,
        reject,
        timeout
      });

      this.extractionWorker!.postMessage({
        taskId,
        type: 'analyze',
        text,
        sessionId: 'analysis' // Dummy session ID for analysis
      });
    });
  }

  /**
   * Handle messages from workers
   */
  private handleWorkerMessage(workerType: string, event: MessageEvent): void {
    const { taskId, type, ...data } = event.data;

    const task = this.tasks.get(taskId);
    if (!task) {
      console.warn(`No task found for ${taskId}`);
      return;
    }

    // Handle progress updates
    if (type === 'progress' && 'onProgress' in task) {
      task.onProgress?.(data.processed, data.total, data.currentIdeas);
      return;
    }

    // Handle completion
    if (type === 'success') {
      if (task.timeout) clearTimeout(task.timeout);
      this.tasks.delete(taskId);
      task.resolve(data);
    } else if (type === 'error') {
      if (task.timeout) clearTimeout(task.timeout);
      this.tasks.delete(taskId);
      task.reject(new Error(data.error));
    }
  }

  /**
   * Reject all tasks for a specific worker
   */
  private rejectAllTasks(workerType: string, error: Error): void {
    for (const [taskId, task] of this.tasks.entries()) {
      if (
        (workerType === 'clustering' && task.type === 'clustering') ||
        (workerType === 'extraction' && task.type === 'extraction')
      ) {
        if (task.timeout) clearTimeout(task.timeout);
        task.reject(error);
        this.tasks.delete(taskId);
      }
    }
  }

  /**
   * Get worker status
   */
  getStatus(): {
    initialized: boolean;
    pendingTasks: number;
    workers: {
      clustering: boolean;
      extraction: boolean;
    };
  } {
    return {
      initialized: this.initialized,
      pendingTasks: this.tasks.size,
      workers: {
        clustering: this.clusteringWorker !== null,
        extraction: this.extractionWorker !== null
      }
    };
  }
}

// Export singleton instance
export const workerManager = new WorkerManager();

// Also export class for testing
export default WorkerManager;