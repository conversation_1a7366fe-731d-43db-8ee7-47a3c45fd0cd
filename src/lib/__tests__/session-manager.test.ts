/**
 * Tests for the Claude Session Manager
 */

import { ClaudeSessionManager } from '../session-manager';
import type { SessionData, SessionState } from '../../types/session';

// Mock the Tauri API
jest.mock('@tauri-apps/api/event', () => ({
  listen: jest.fn(() => Promise.resolve(() => {})),
}));

jest.mock('../api', () => ({
  api: {
    executeClaudeCode: jest.fn(),
    resumeClaudeCode: jest.fn(),
    cancelClaudeExecution: jest.fn(),
    loadSessionHistory: jest.fn(() => Promise.resolve([])),
    listRunningClaudeSessions: jest.fn(() => Promise.resolve([])),
  }
}));

describe('ClaudeSessionManager', () => {
  let sessionManager: ClaudeSessionManager;

  beforeEach(() => {
    sessionManager = new ClaudeSessionManager();
  });

  afterEach(() => {
    sessionManager.destroy();
  });

  describe('Session Creation', () => {
    it('should create a new session with correct initial state', async () => {
      const projectPath = '/test/project';
      
      const session = await sessionManager.createSession({
        projectPath
      });

      expect(session).toBeDefined();
      expect(session.projectPath).toBe(projectPath);
      expect(session.status).toBe('idle');
      expect(session.messages).toHaveLength(0);
      expect(session.queuedPrompts).toHaveLength(0);
    });

    it('should generate unique session IDs', async () => {
      const session1 = await sessionManager.createSession({
        projectPath: '/test/project1'
      });
      
      const session2 = await sessionManager.createSession({
        projectPath: '/test/project2'
      });

      expect(session1.id).not.toBe(session2.id);
    });

    it('should apply custom settings when provided', async () => {
      const customSettings = {
        autoCheckpoint: false,
        maxMessages: 500
      };

      const session = await sessionManager.createSession({
        projectPath: '/test/project',
        settings: customSettings
      });

      expect(session.settings.autoCheckpoint).toBe(false);
      expect(session.settings.maxMessages).toBe(500);
    });
  });

  describe('State Management', () => {
    it('should return initial state correctly', () => {
      const state = sessionManager.getSessionState();
      
      expect(state.currentSession).toBeNull();
      expect(state.isLoading).toBe(false);
      expect(state.isStreaming).toBe(false);
      expect(state.error).toBeNull();
    });

    it('should update state when session is created', async () => {
      const session = await sessionManager.createSession({
        projectPath: '/test/project'
      });

      const state = sessionManager.getSessionState();
      
      expect(state.currentSession).toBeDefined();
      expect(state.currentSession?.id).toBe(session.id);
    });

    it('should notify subscribers of state changes', async () => {
      const mockCallback = jest.fn();
      
      sessionManager.subscribeToUpdates(mockCallback);
      
      await sessionManager.createSession({
        projectPath: '/test/project'
      });

      expect(mockCallback).toHaveBeenCalled();
    });
  });

  describe('Prompt Queue Management', () => {
    it('should queue prompts when session is streaming', async () => {
      const session = await sessionManager.createSession({
        projectPath: '/test/project'
      });

      // Simulate streaming state
      sessionManager['updateState']({ isStreaming: true });

      sessionManager.queuePrompt('Test prompt', 'sonnet');

      const state = sessionManager.getSessionState();
      expect(state.currentSession?.queuedPrompts).toHaveLength(1);
      expect(state.currentSession?.queuedPrompts[0].prompt).toBe('Test prompt');
    });

    it('should sort queued prompts by priority', async () => {
      const session = await sessionManager.createSession({
        projectPath: '/test/project'
      });

      sessionManager.queuePrompt('Low priority', 'sonnet', 1);
      sessionManager.queuePrompt('High priority', 'sonnet', 10);
      sessionManager.queuePrompt('Medium priority', 'sonnet', 5);

      const state = sessionManager.getSessionState();
      const prompts = state.currentSession?.queuedPrompts || [];
      
      expect(prompts[0].prompt).toBe('High priority');
      expect(prompts[1].prompt).toBe('Medium priority');
      expect(prompts[2].prompt).toBe('Low priority');
    });
  });

  describe('Session Termination', () => {
    it('should terminate session and clean up state', async () => {
      await sessionManager.createSession({
        projectPath: '/test/project'
      });

      await sessionManager.terminateSession();

      const state = sessionManager.getSessionState();
      expect(state.currentSession).toBeNull();
      expect(state.isLoading).toBe(false);
      expect(state.isStreaming).toBe(false);
    });

    it('should throw error when terminating non-existent session', async () => {
      await expect(sessionManager.terminateSession()).rejects.toThrow('No active session to terminate');
    });
  });

  describe('Error Handling', () => {
    it('should handle session creation errors gracefully', async () => {
      // Mock API to throw error
      const mockApi = require('../api').api;
      mockApi.executeClaudeCode.mockRejectedValueOnce(new Error('Test error'));

      const session = await sessionManager.createSession({
        projectPath: '/test/project',
        initialPrompt: 'Test prompt'
      });

      // Session should still be created, but error should be in state
      expect(session).toBeDefined();
      
      // Wait for async error handling
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const state = sessionManager.getSessionState();
      expect(state.error).toContain('Failed to send prompt');
    });
  });

  describe('Event System', () => {
    it('should emit session created event', async () => {
      const mockCallback = jest.fn();
      
      sessionManager.addEventListener('session_created', mockCallback);
      
      const session = await sessionManager.createSession({
        projectPath: '/test/project'
      });

      expect(mockCallback).toHaveBeenCalledWith({
        type: 'session_created',
        sessionId: session.id,
        projectPath: '/test/project'
      });
    });

    it('should allow unsubscribing from events', async () => {
      const mockCallback = jest.fn();
      
      const unsubscribe = sessionManager.addEventListener('session_created', mockCallback);
      unsubscribe();
      
      await sessionManager.createSession({
        projectPath: '/test/project'
      });

      expect(mockCallback).not.toHaveBeenCalled();
    });
  });
});