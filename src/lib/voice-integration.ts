/**
 * Voice Integration System
 * 
 * Advanced voice input with transcription, commands, and AI processing
 */

import { brainstormMemory } from './brainstorm-memory';
import { ideaExtractor } from '@/services';
import { useBrainstormStore } from '@/stores/brainstormStore';

export interface VoiceSession {
  id: string;
  sessionId: string;
  isRecording: boolean;
  transcript: string;
  confidence: number;
  language: string;
  startTime: number;
  endTime?: number;
  audioData?: Blob;
  extractedIdeas: string[];
  voiceCommands: VoiceCommand[];
}

export interface VoiceCommand {
  command: string;
  parameters: string[];
  confidence: number;
  action: () => void;
}

export interface VoiceSettings {
  language: string;
  autoTranscribe: boolean;
  autoExtractIdeas: boolean;
  enableCommands: boolean;
  noiseReduction: boolean;
  speakerDetection: boolean;
}

class VoiceIntegrationSystem {
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private recognition: SpeechRecognition | null = null;
  private currentSession: VoiceSession | null = null;
  private settings: VoiceSettings = {
    language: 'en-US',
    autoTranscribe: true,
    autoExtractIdeas: true,
    enableCommands: true,
    noiseReduction: true,
    speakerDetection: false,
  };

  private commandPatterns = [
    {
      pattern: /add idea (.+)/i,
      action: (params: string[], sessionId: string) => {
        const { addIdea } = useBrainstormStore.getState();
        addIdea(sessionId, params[0], { tags: ['voice-input'] });
        return `Added idea: ${params[0]}`;
      }
    },
    {
      pattern: /create cluster (.+)/i,
      action: (params: string[], sessionId: string) => {
        // Create a new cluster with the given name
        console.log(`Creating cluster: ${params[0]}`);
        return `Created cluster: ${params[0]}`;
      }
    },
    {
      pattern: /switch to (chat|mindmap|kanban|matrix|tasks)/i,
      action: (params: string[], sessionId: string) => {
        // Trigger view change
        console.log(`Switching to ${params[0]} view`);
        return `Switched to ${params[0]} view`;
      }
    },
    {
      pattern: /save session/i,
      action: (params: string[], sessionId: string) => {
        // Save current session
        console.log('Saving session');
        return 'Session saved';
      }
    },
    {
      pattern: /export as (markdown|json|pdf)/i,
      action: (params: string[], sessionId: string) => {
        // Trigger export
        console.log(`Exporting as ${params[0]}`);
        return `Exporting session as ${params[0]}`;
      }
    }
  ];

  constructor() {
    this.initializeSpeechRecognition();
  }

  /**
   * Initialize speech recognition API
   */
  private initializeSpeechRecognition(): void {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      console.warn('Speech recognition not supported in this browser');
      return;
    }

    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    this.recognition = new SpeechRecognition();
    
    this.recognition.continuous = true;
    this.recognition.interimResults = true;
    this.recognition.lang = this.settings.language;

    this.recognition.onstart = () => {
      console.log('Voice recognition started');
    };

    this.recognition.onresult = (event: SpeechRecognitionEvent) => {
      this.handleSpeechResult(event);
    };

    this.recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
      console.error('Speech recognition error:', event.error);
    };

    this.recognition.onend = () => {
      if (this.currentSession?.isRecording) {
        // Restart recognition if we're still recording
        this.recognition?.start();
      }
    };
  }

  /**
   * Start voice recording and transcription
   */
  async startVoiceSession(sessionId: string): Promise<VoiceSession> {
    try {
      // Get audio stream
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: this.settings.noiseReduction,
          autoGainControl: true,
        }
      });

      // Initialize media recorder
      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      this.audioChunks = [];
      
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
        if (this.currentSession) {
          this.currentSession.audioData = audioBlob;
          this.currentSession.endTime = Date.now();
        }
        
        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      };

      // Create voice session
      this.currentSession = {
        id: `voice_${Date.now()}`,
        sessionId,
        isRecording: true,
        transcript: '',
        confidence: 0,
        language: this.settings.language,
        startTime: Date.now(),
        extractedIdeas: [],
        voiceCommands: [],
      };

      // Start recording
      this.mediaRecorder.start(1000); // Collect data every second
      
      // Start speech recognition
      if (this.recognition && this.settings.autoTranscribe) {
        this.recognition.start();
      }

      return this.currentSession;
    } catch (error) {
      console.error('Failed to start voice session:', error);
      throw error;
    }
  }

  /**
   * Stop voice session
   */
  stopVoiceSession(): VoiceSession | null {
    if (!this.currentSession) return null;

    this.currentSession.isRecording = false;
    this.currentSession.endTime = Date.now();

    // Stop media recorder
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
    }

    // Stop speech recognition
    if (this.recognition) {
      this.recognition.stop();
    }

    const session = this.currentSession;
    this.currentSession = null;

    // Process final transcript
    if (session.transcript && this.settings.autoExtractIdeas) {
      this.extractIdeasFromTranscript(session);
    }

    return session;
  }

  /**
   * Process speech recognition results
   */
  private handleSpeechResult(event: SpeechRecognitionEvent): void {
    if (!this.currentSession) return;

    let finalTranscript = '';
    let interimTranscript = '';
    let totalConfidence = 0;
    let resultCount = 0;

    for (let i = event.resultIndex; i < event.results.length; i++) {
      const result = event.results[i];
      const transcript = result[0].transcript;
      
      if (result.isFinal) {
        finalTranscript += transcript + ' ';
        totalConfidence += result[0].confidence;
        resultCount++;
        
        // Check for voice commands
        if (this.settings.enableCommands) {
          this.processVoiceCommands(transcript);
        }
      } else {
        interimTranscript += transcript;
      }
    }

    // Update session transcript
    if (finalTranscript) {
      this.currentSession.transcript += finalTranscript;
      this.currentSession.confidence = resultCount > 0 ? totalConfidence / resultCount : 0;
    }

    // Emit event for UI updates
    this.emitTranscriptUpdate({
      final: finalTranscript.trim(),
      interim: interimTranscript.trim(),
      confidence: this.currentSession.confidence,
    });
  }

  /**
   * Process voice commands
   */
  private processVoiceCommands(transcript: string): void {
    if (!this.currentSession) return;

    for (const commandPattern of this.commandPatterns) {
      const match = transcript.match(commandPattern.pattern);
      if (match) {
        const parameters = match.slice(1);
        
        try {
          const result = commandPattern.action(parameters, this.currentSession.sessionId);
          
          const command: VoiceCommand = {
            command: match[0],
            parameters,
            confidence: 0.9, // High confidence for pattern matches
            action: () => commandPattern.action(parameters, this.currentSession!.sessionId),
          };
          
          this.currentSession.voiceCommands.push(command);
          
          // Emit command executed event
          this.emitCommandExecuted({
            command: command.command,
            result,
            parameters,
          });
          
          console.log(`Voice command executed: ${command.command} -> ${result}`);
        } catch (error) {
          console.error('Error executing voice command:', error);
        }
        
        break; // Only execute first matching command
      }
    }
  }

  /**
   * Extract ideas from transcript using AI
   */
  private async extractIdeasFromTranscript(session: VoiceSession): Promise<void> {
    if (!session.transcript.trim()) return;

    try {
      // Create a mock message for idea extraction
      const mockMessage = {
        id: `voice_msg_${Date.now()}`,
        type: 'assistant' as const,
        content: session.transcript,
        timestamp: Date.now(),
      };

      // Extract ideas using existing extractor
      const extractedIdeas = ideaExtractor.extractIdeasFromMessage(mockMessage);
      
      if (extractedIdeas.length > 0) {
        const { addIdeaFull } = useBrainstormStore.getState();
        
        extractedIdeas.forEach(ideaData => {
          const ideaId = addIdeaFull({
            ...ideaData,
            tags: [...(ideaData.tags || []), 'voice-extracted'],
            metadata: {
              ...ideaData.metadata,
              voiceSessionId: session.id,
              extractedFromVoice: true,
            }
          } as any);
          
          session.extractedIdeas.push(ideaId);
        });

        console.log(`Extracted ${extractedIdeas.length} ideas from voice transcript`);
      }
    } catch (error) {
      console.error('Failed to extract ideas from transcript:', error);
    }
  }

  /**
   * Update voice settings
   */
  updateSettings(newSettings: Partial<VoiceSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    
    // Update speech recognition language if changed
    if (this.recognition && newSettings.language) {
      this.recognition.lang = newSettings.language;
    }
  }

  /**
   * Get current voice settings
   */
  getSettings(): VoiceSettings {
    return { ...this.settings };
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages(): string[] {
    return [
      'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN',
      'es-ES', 'es-MX', 'fr-FR', 'de-DE', 'it-IT',
      'pt-BR', 'zh-CN', 'ja-JP', 'ko-KR', 'ru-RU',
      'ar-SA', 'hi-IN', 'nl-NL', 'sv-SE', 'da-DK',
    ];
  }

  /**
   * Check if voice features are supported
   */
  isSupported(): boolean {
    return !!(
      navigator.mediaDevices &&
      navigator.mediaDevices.getUserMedia &&
      (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
    );
  }

  /**
   * Convert audio to text using Web Speech API
   */
  async transcribeAudio(audioBlob: Blob): Promise<string> {
    // In a real implementation, this would use a more robust transcription service
    // For now, we'll return a placeholder
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve('Transcribed text from audio blob');
      }, 1000);
    });
  }

  // Event emitters for UI integration
  private eventHandlers: Map<string, Function[]> = new Map();

  addEventListener(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  removeEventListener(event: string, handler: Function): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emitTranscriptUpdate(data: { final: string; interim: string; confidence: number }): void {
    const handlers = this.eventHandlers.get('transcript-update');
    if (handlers) {
      handlers.forEach(handler => handler(data));
    }
  }

  private emitCommandExecuted(data: { command: string; result: string; parameters: string[] }): void {
    const handlers = this.eventHandlers.get('command-executed');
    if (handlers) {
      handlers.forEach(handler => handler(data));
    }
  }
}

// Export singleton instance
export const voiceIntegration = new VoiceIntegrationSystem();