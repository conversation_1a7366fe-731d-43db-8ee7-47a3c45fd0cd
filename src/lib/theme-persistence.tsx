import React, { useState, useEffect, useCallback } from 'react';

// Theme configuration interface
export interface ThemeConfig {
  mode: 'light' | 'dark' | 'auto';
  glassmorphism: {
    enabled: boolean;
    intensity: 'subtle' | 'medium' | 'strong';
    blur: number;
    opacity: number;
  };
  animations: {
    enabled: boolean;
    reduceMotion: boolean;
    duration: 'fast' | 'normal' | 'slow';
  };
  colors: {
    primary: string;
    accent: string;
    background: string;
    surface: string;
  };
  typography: {
    fontSize: 'small' | 'medium' | 'large' | 'xl';
    fontFamily: string;
  };
  accessibility: {
    highContrast: boolean;
    focusRings: boolean;
    screenReader: boolean;
  };
  performance: {
    enableGPUAcceleration: boolean;
    enableVirtualization: boolean;
    prefersReducedData: boolean;
  };
}

// Default theme configuration
export const defaultThemeConfig: ThemeConfig = {
  mode: 'auto',
  glassmorphism: {
    enabled: true,
    intensity: 'medium',
    blur: 16,
    opacity: 0.8
  },
  animations: {
    enabled: true,
    reduceMotion: false,
    duration: 'normal'
  },
  colors: {
    primary: '#3b82f6',
    accent: '#8b5cf6',
    background: '#ffffff',
    surface: '#f8fafc'
  },
  typography: {
    fontSize: 'medium',
    fontFamily: 'Inter, system-ui, sans-serif'
  },
  accessibility: {
    highContrast: false,
    focusRings: true,
    screenReader: false
  },
  performance: {
    enableGPUAcceleration: true,
    enableVirtualization: true,
    prefersReducedData: false
  }
};

// Theme storage utilities
class ThemeStorage {
  private static readonly STORAGE_KEY = 'claudia-theme-config';
  private static readonly VERSION = '1.0.0';

  static save(config: ThemeConfig): void {
    try {
      const data = {
        version: this.VERSION,
        config,
        timestamp: Date.now()
      };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save theme config:', error);
    }
  }

  static load(): ThemeConfig | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return null;

      const data = JSON.parse(stored);
      
      // Check version compatibility
      if (data.version !== this.VERSION) {
        console.log('Theme config version mismatch, using defaults');
        return null;
      }

      return data.config;
    } catch (error) {
      console.warn('Failed to load theme config:', error);
      return null;
    }
  }

  static clear(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear theme config:', error);
    }
  }

  static export(): string {
    const config = this.load();
    return JSON.stringify(config || defaultThemeConfig, null, 2);
  }

  static import(configJson: string): boolean {
    try {
      const config = JSON.parse(configJson) as ThemeConfig;
      this.save(config);
      return true;
    } catch (error) {
      console.error('Failed to import theme config:', error);
      return false;
    }
  }
}

// System preferences detection
class SystemPreferences {
  static getColorScheme(): 'light' | 'dark' {
    if (typeof window === 'undefined') return 'light';
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }

  static getReducedMotion(): boolean {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  static getHighContrast(): boolean {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-contrast: high)').matches;
  }

  static getReducedData(): boolean {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-reduced-data: reduce)').matches;
  }

  static getForcedColors(): boolean {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(forced-colors: active)').matches;
  }
}

// Theme hook
export const useTheme = () => {
  const [config, setConfig] = useState<ThemeConfig>(() => {
    const stored = ThemeStorage.load();
    if (stored) {
      return stored;
    }

    // Apply system preferences to default config
    const systemConfig = { ...defaultThemeConfig };
    systemConfig.animations.reduceMotion = SystemPreferences.getReducedMotion();
    systemConfig.accessibility.highContrast = SystemPreferences.getHighContrast();
    systemConfig.performance.prefersReducedData = SystemPreferences.getReducedData();
    
    return systemConfig;
  });

  const [resolvedMode, setResolvedMode] = useState<'light' | 'dark'>(() => {
    if (config.mode === 'auto') {
      return SystemPreferences.getColorScheme();
    }
    return config.mode;
  });

  // Update theme configuration
  const updateConfig = useCallback((updates: Partial<ThemeConfig>) => {
    setConfig(prev => {
      const newConfig = { ...prev, ...updates };
      ThemeStorage.save(newConfig);
      return newConfig;
    });
  }, []);

  // Update specific sections
  const updateGlassmorphism = useCallback((updates: Partial<ThemeConfig['glassmorphism']>) => {
    updateConfig({
      glassmorphism: { ...config.glassmorphism, ...updates }
    });
  }, [config.glassmorphism, updateConfig]);

  const updateAnimations = useCallback((updates: Partial<ThemeConfig['animations']>) => {
    updateConfig({
      animations: { ...config.animations, ...updates }
    });
  }, [config.animations, updateConfig]);

  const updateColors = useCallback((updates: Partial<ThemeConfig['colors']>) => {
    updateConfig({
      colors: { ...config.colors, ...updates }
    });
  }, [config.colors, updateConfig]);

  const updateTypography = useCallback((updates: Partial<ThemeConfig['typography']>) => {
    updateConfig({
      typography: { ...config.typography, ...updates }
    });
  }, [config.typography, updateConfig]);

  const updateAccessibility = useCallback((updates: Partial<ThemeConfig['accessibility']>) => {
    updateConfig({
      accessibility: { ...config.accessibility, ...updates }
    });
  }, [config.accessibility, updateConfig]);

  const updatePerformance = useCallback((updates: Partial<ThemeConfig['performance']>) => {
    updateConfig({
      performance: { ...config.performance, ...updates }
    });
  }, [config.performance, updateConfig]);

  // Reset to defaults
  const resetToDefaults = useCallback(() => {
    ThemeStorage.clear();
    setConfig(defaultThemeConfig);
  }, []);

  // Toggle dark mode
  const toggleDarkMode = useCallback(() => {
    const newMode = resolvedMode === 'light' ? 'dark' : 'light';
    updateConfig({ mode: newMode });
  }, [resolvedMode, updateConfig]);

  // Export/Import
  const exportConfig = useCallback(() => {
    return ThemeStorage.export();
  }, []);

  const importConfig = useCallback((configJson: string) => {
    if (ThemeStorage.import(configJson)) {
      const imported = ThemeStorage.load();
      if (imported) {
        setConfig(imported);
        return true;
      }
    }
    return false;
  }, []);

  // Listen for system preference changes
  useEffect(() => {
    if (config.mode !== 'auto') {
      setResolvedMode(config.mode);
      return;
    }

    const updateResolvedMode = () => {
      setResolvedMode(SystemPreferences.getColorScheme());
    };

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    updateResolvedMode();
    
    mediaQuery.addEventListener('change', updateResolvedMode);
    return () => mediaQuery.removeEventListener('change', updateResolvedMode);
  }, [config.mode]);

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement;
    
    // Apply mode
    root.setAttribute('data-theme', resolvedMode);
    root.classList.toggle('dark', resolvedMode === 'dark');

    // Apply glassmorphism settings
    root.style.setProperty('--glass-blur', `${config.glassmorphism.blur}px`);
    root.style.setProperty('--glass-opacity', config.glassmorphism.opacity.toString());
    root.classList.toggle('glassmorphism-disabled', !config.glassmorphism.enabled);

    // Apply animation settings
    root.classList.toggle('reduce-motion', config.animations.reduceMotion || !config.animations.enabled);
    root.style.setProperty('--animation-duration', {
      fast: '0.1s',
      normal: '0.3s',
      slow: '0.6s'
    }[config.animations.duration]);

    // Apply colors
    root.style.setProperty('--color-primary', config.colors.primary);
    root.style.setProperty('--color-accent', config.colors.accent);
    root.style.setProperty('--color-background', config.colors.background);
    root.style.setProperty('--color-surface', config.colors.surface);

    // Apply typography
    root.style.setProperty('--font-family', config.typography.fontFamily);
    root.classList.remove('text-sm', 'text-base', 'text-lg', 'text-xl');
    const fontSizeClass = {
      small: 'text-sm',
      medium: 'text-base',
      large: 'text-lg',
      xl: 'text-xl'
    }[config.typography.fontSize];
    root.classList.add(fontSizeClass);

    // Apply accessibility settings
    root.classList.toggle('high-contrast', config.accessibility.highContrast);
    root.classList.toggle('hide-focus-rings', !config.accessibility.focusRings);

    // Apply performance settings
    root.classList.toggle('gpu-acceleration', config.performance.enableGPUAcceleration);
    root.classList.toggle('virtualization-enabled', config.performance.enableVirtualization);
  }, [config, resolvedMode]);

  return {
    config,
    resolvedMode,
    updateConfig,
    updateGlassmorphism,
    updateAnimations,
    updateColors,
    updateTypography,
    updateAccessibility,
    updatePerformance,
    resetToDefaults,
    toggleDarkMode,
    exportConfig,
    importConfig,
    isLight: resolvedMode === 'light',
    isDark: resolvedMode === 'dark'
  };
};

// Theme provider context
export const ThemeContext = React.createContext<{
  config: ThemeConfig;
  resolvedMode: 'light' | 'dark';
  updateConfig: (updates: Partial<ThemeConfig>) => void;
  toggleDarkMode: () => void;
  resetToDefaults: () => void;
} | null>(null);

// Theme provider component
interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const theme = useTheme();

  return (
    <ThemeContext.Provider value={theme}>
      {children}
    </ThemeContext.Provider>
  );
};

// Hook to use theme context
export const useThemeContext = () => {
  const context = React.useContext(ThemeContext);
  if (!context) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  return context;
};

// Theme preset utilities
export const ThemePresets = {
  minimal: {
    ...defaultThemeConfig,
    glassmorphism: {
      enabled: false,
      intensity: 'subtle' as const,
      blur: 8,
      opacity: 0.6
    },
    animations: {
      enabled: true,
      reduceMotion: false,
      duration: 'fast' as const
    }
  },

  premium: {
    ...defaultThemeConfig,
    glassmorphism: {
      enabled: true,
      intensity: 'strong' as const,
      blur: 24,
      opacity: 0.9
    },
    animations: {
      enabled: true,
      reduceMotion: false,
      duration: 'normal' as const
    }
  },

  accessible: {
    ...defaultThemeConfig,
    animations: {
      enabled: false,
      reduceMotion: true,
      duration: 'fast' as const
    },
    accessibility: {
      highContrast: true,
      focusRings: true,
      screenReader: true
    },
    glassmorphism: {
      enabled: false,
      intensity: 'subtle' as const,
      blur: 0,
      opacity: 1
    }
  },

  performance: {
    ...defaultThemeConfig,
    glassmorphism: {
      enabled: false,
      intensity: 'subtle' as const,
      blur: 8,
      opacity: 0.8
    },
    animations: {
      enabled: true,
      reduceMotion: false,
      duration: 'fast' as const
    },
    performance: {
      enableGPUAcceleration: true,
      enableVirtualization: true,
      prefersReducedData: true
    }
  }
};

export default {
  useTheme,
  ThemeProvider,
  useThemeContext,
  ThemeStorage,
  SystemPreferences,
  ThemePresets,
  defaultThemeConfig
};