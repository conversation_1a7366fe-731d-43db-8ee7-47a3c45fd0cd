import { invoke } from '@tauri-apps/api/core';

// TypeScript interfaces matching the Rust structs
export interface ToolbarButtons {
  claude: boolean;
  settings: boolean;
  usage: boolean;
  mcp: boolean;
  agents: boolean;
  marketplace: boolean;
  brainstorming: boolean;
}

export interface ToolbarConfig {
  visible: boolean;
  position: 'top' | 'bottom';
  buttons: ToolbarButtons;
  size: 'small' | 'medium' | 'large';
}

export interface WindowConfig {
  default_width: number;
  default_height: number;
  always_on_top: boolean;
  minimize_to_tray: boolean;
  start_maximized: boolean;
}

export interface EditorConfig {
  line_numbers: boolean;
  word_wrap: boolean;
  minimap: boolean;
  font_size: number;
  tab_size: number;
}

export interface AppConfig {
  toolbar: ToolbarConfig;
  window: WindowConfig;
  editor: EditorConfig;
  version: string;
}

export interface WindowInfo {
  width: number;
  height: number;
  x: number;
  y: number;
  isMaximized: boolean;
  isAlwaysOnTop: boolean;
}

// Default configuration
export const defaultAppConfig: AppConfig = {
  toolbar: {
    visible: true,
    position: 'top',
    buttons: {
      claude: true,
      settings: true,
      usage: true,
      mcp: true,
      agents: true,
      marketplace: true,
      brainstorming: true,
    },
    size: 'medium',
  },
  window: {
    default_width: 1200,
    default_height: 800,
    always_on_top: false,
    minimize_to_tray: false,
    start_maximized: false,
  },
  editor: {
    line_numbers: true,
    word_wrap: true,
    minimap: true,
    font_size: 14,
    tab_size: 2,
  },
  version: '1.0.0',
};

// API functions
export const appConfigApi = {
  /**
   * Save app configuration to persistent storage
   */
  async saveConfig(config: AppConfig): Promise<void> {
    try {
      await invoke('save_app_config', { config });
    } catch (error) {
      console.error('Failed to save app config:', error);
      throw new Error(`Failed to save configuration: ${error}`);
    }
  },

  /**
   * Load app configuration from persistent storage
   */
  async loadConfig(): Promise<AppConfig> {
    try {
      const config = await invoke<AppConfig>('load_app_config');
      return config;
    } catch (error) {
      console.error('Failed to load app config:', error);
      // Return default config if loading fails
      return defaultAppConfig;
    }
  },

  /**
   * Apply window configuration to the current window
   */
  async applyWindowConfig(config: WindowConfig): Promise<void> {
    try {
      await invoke('apply_window_config', { config });
    } catch (error) {
      console.error('Failed to apply window config:', error);
      throw new Error(`Failed to apply window configuration: ${error}`);
    }
  },

  /**
   * Get current window information
   */
  async getWindowInfo(): Promise<WindowInfo> {
    try {
      const info = await invoke<WindowInfo>('get_window_info');
      return info;
    } catch (error) {
      console.error('Failed to get window info:', error);
      throw new Error(`Failed to get window information: ${error}`);
    }
  },

  /**
   * Reset app configuration to defaults
   */
  async resetConfig(): Promise<AppConfig> {
    try {
      const config = await invoke<AppConfig>('reset_app_config');
      return config;
    } catch (error) {
      console.error('Failed to reset app config:', error);
      throw new Error(`Failed to reset configuration: ${error}`);
    }
  },

  /**
   * Export app configuration as JSON string
   */
  async exportConfig(): Promise<string> {
    try {
      const configJson = await invoke<string>('export_app_config');
      return configJson;
    } catch (error) {
      console.error('Failed to export app config:', error);
      throw new Error(`Failed to export configuration: ${error}`);
    }
  },

  /**
   * Import app configuration from JSON string
   */
  async importConfig(configJson: string): Promise<AppConfig> {
    try {
      const config = await invoke<AppConfig>('import_app_config', { configJson });
      return config;
    } catch (error) {
      console.error('Failed to import app config:', error);
      throw new Error(`Failed to import configuration: ${error}`);
    }
  },
};

// React hook for app configuration
import { useState, useEffect, useCallback } from 'react';

export const useAppConfig = () => {
  const [config, setConfig] = useState<AppConfig>(defaultAppConfig);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load config on mount
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setLoading(true);
        setError(null);
        const loadedConfig = await appConfigApi.loadConfig();
        setConfig(loadedConfig);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load configuration');
        console.error('Failed to load app config:', err);
      } finally {
        setLoading(false);
      }
    };

    loadConfig();
  }, []);

  // Save config
  const saveConfig = useCallback(async (newConfig: AppConfig) => {
    try {
      setError(null);
      await appConfigApi.saveConfig(newConfig);
      setConfig(newConfig);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save configuration');
      throw err;
    }
  }, []);

  // Update specific sections
  const updateToolbarConfig = useCallback(async (updates: Partial<ToolbarConfig>) => {
    const newConfig = {
      ...config,
      toolbar: { ...config.toolbar, ...updates }
    };
    await saveConfig(newConfig);
  }, [config, saveConfig]);

  const updateWindowConfig = useCallback(async (updates: Partial<WindowConfig>) => {
    const newConfig = {
      ...config,
      window: { ...config.window, ...updates }
    };
    await saveConfig(newConfig);
  }, [config, saveConfig]);

  const updateEditorConfig = useCallback(async (updates: Partial<EditorConfig>) => {
    const newConfig = {
      ...config,
      editor: { ...config.editor, ...updates }
    };
    await saveConfig(newConfig);
  }, [config, saveConfig]);

  // Apply window config
  const applyWindowConfig = useCallback(async () => {
    try {
      await appConfigApi.applyWindowConfig(config.window);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to apply window configuration');
      throw err;
    }
  }, [config.window]);

  // Reset to defaults
  const resetToDefaults = useCallback(async () => {
    try {
      setError(null);
      const defaultConfig = await appConfigApi.resetConfig();
      setConfig(defaultConfig);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reset configuration');
      throw err;
    }
  }, []);

  // Export config
  const exportConfig = useCallback(async () => {
    try {
      return await appConfigApi.exportConfig();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export configuration');
      throw err;
    }
  }, []);

  // Import config
  const importConfig = useCallback(async (configJson: string) => {
    try {
      setError(null);
      const importedConfig = await appConfigApi.importConfig(configJson);
      setConfig(importedConfig);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to import configuration');
      throw err;
    }
  }, []);

  return {
    config,
    loading,
    error,
    saveConfig,
    updateToolbarConfig,
    updateWindowConfig,
    updateEditorConfig,
    applyWindowConfig,
    resetToDefaults,
    exportConfig,
    importConfig,
  };
};
