/**
 * Sync Manager for Bi-directional External Tool Integration
 * 
 * Handles real-time synchronization with external project management tools
 */

import { ProjectManagementProvider } from './project-management-integration';
import { useBrainstormStore } from '@/stores/brainstormStore';
import type { GeneratedTask, Idea } from '@/types/brainstorm';

export interface SyncConfig {
  provider: ProjectManagementProvider;
  sessionId: string;
  syncInterval?: number; // in milliseconds
  autoSync?: boolean;
  conflictResolution?: 'local' | 'remote' | 'merge' | 'manual';
}

export interface SyncStatus {
  isRunning: boolean;
  lastSync?: Date;
  pendingChanges: number;
  conflicts: SyncConflict[];
  errors: SyncError[];
}

export interface SyncConflict {
  id: string;
  type: 'task' | 'idea';
  localVersion: any;
  remoteVersion: any;
  detectedAt: Date;
}

export interface SyncError {
  timestamp: Date;
  message: string;
  retry: boolean;
}

export interface WebhookPayload {
  event: 'task.created' | 'task.updated' | 'task.deleted' | 'project.updated';
  data: any;
  timestamp: string;
  source: string;
}

class SyncManager {
  private syncConfigs = new Map<string, SyncConfig>();
  private syncIntervals = new Map<string, NodeJS.Timeout>();
  private syncStatus = new Map<string, SyncStatus>();
  private webhookHandlers = new Map<string, (payload: WebhookPayload) => Promise<void>>();
  private changeQueue = new Map<string, Set<string>>();

  /**
   * Register a sync configuration for a session
   */
  registerSync(sessionId: string, config: SyncConfig): void {
    this.syncConfigs.set(sessionId, config);
    this.syncStatus.set(sessionId, {
      isRunning: false,
      pendingChanges: 0,
      conflicts: [],
      errors: []
    });
    this.changeQueue.set(sessionId, new Set());

    // Register webhook handler
    this.webhookHandlers.set(sessionId, async (payload) => {
      await this.handleWebhook(sessionId, payload);
    });

    // Start auto-sync if enabled
    if (config.autoSync && config.syncInterval) {
      this.startAutoSync(sessionId);
    }
  }

  /**
   * Start automatic synchronization
   */
  startAutoSync(sessionId: string): void {
    const config = this.syncConfigs.get(sessionId);
    if (!config || !config.syncInterval) return;

    // Clear existing interval
    this.stopAutoSync(sessionId);

    // Set up new interval
    const interval = setInterval(async () => {
      await this.sync(sessionId);
    }, config.syncInterval);

    this.syncIntervals.set(sessionId, interval);
  }

  /**
   * Stop automatic synchronization
   */
  stopAutoSync(sessionId: string): void {
    const interval = this.syncIntervals.get(sessionId);
    if (interval) {
      clearInterval(interval);
      this.syncIntervals.delete(sessionId);
    }
  }

  /**
   * Perform manual synchronization
   */
  async sync(sessionId: string): Promise<SyncStatus> {
    const config = this.syncConfigs.get(sessionId);
    const status = this.syncStatus.get(sessionId);
    
    if (!config || !status) {
      throw new Error(`No sync configuration found for session ${sessionId}`);
    }

    if (status.isRunning) {
      console.warn('Sync already in progress');
      return status;
    }

    status.isRunning = true;
    status.lastSync = new Date();

    try {
      // 1. Pull remote changes
      await this.pullRemoteChanges(sessionId, config);
      
      // 2. Push local changes
      await this.pushLocalChanges(sessionId, config);
      
      // 3. Resolve conflicts if any
      if (status.conflicts.length > 0) {
        await this.resolveConflicts(sessionId, config);
      }
      
      // 4. Clear processed changes
      this.changeQueue.get(sessionId)?.clear();
      status.pendingChanges = 0;
      
    } catch (error) {
      status.errors.push({
        timestamp: new Date(),
        message: error instanceof Error ? error.message : 'Unknown error',
        retry: true
      });
    } finally {
      status.isRunning = false;
    }

    return status;
  }

  /**
   * Pull changes from remote
   */
  private async pullRemoteChanges(sessionId: string, config: SyncConfig): Promise<void> {
    const { provider } = config;
    const store = useBrainstormStore.getState();
    
    // Get remote projects
    const remoteProjects = await provider.getProjects();
    
    for (const project of remoteProjects) {
      // Check if project is linked to this session
      const sessionData = store.sessions[sessionId];
      if (!sessionData?.metadata?.externalProjectId === project.id) continue;
      
      // Fetch project tasks
      const remoteTasks = await this.fetchRemoteTasks(provider, project.id);
      
      // Compare with local tasks
      const localTasks = store.getTasksBySession(sessionId);
      
      for (const remoteTask of remoteTasks) {
        const localTask = localTasks.find(t => 
          t.metadata?.externalId === remoteTask.id
        );
        
        if (!localTask) {
          // New remote task - import it
          await this.importRemoteTask(sessionId, remoteTask);
        } else if (this.hasRemoteChanges(localTask, remoteTask)) {
          // Task updated remotely
          await this.handleRemoteUpdate(sessionId, localTask, remoteTask, config);
        }
      }
      
      // Check for deleted tasks
      for (const localTask of localTasks) {
        const exists = remoteTasks.some(rt => 
          rt.id === localTask.metadata?.externalId
        );
        
        if (!exists && localTask.metadata?.externalId) {
          // Task deleted remotely
          await this.handleRemoteDeletion(sessionId, localTask, config);
        }
      }
    }
  }

  /**
   * Push local changes to remote
   */
  private async pushLocalChanges(sessionId: string, config: SyncConfig): Promise<void> {
    const { provider } = config;
    const store = useBrainstormStore.getState();
    const changes = this.changeQueue.get(sessionId) || new Set();
    
    for (const changeId of changes) {
      const [type, id] = changeId.split(':');
      
      if (type === 'task') {
        const task = store.tasks[id];
        if (!task) continue;
        
        try {
          if (task.metadata?.externalId) {
            // Update existing remote task
            await this.updateRemoteTask(provider, task);
          } else {
            // Create new remote task
            const externalId = await provider.createTask({
              title: task.title,
              description: task.description,
              priority: task.priority,
              status: task.status === 'completed' ? 'done' : 'todo',
              tags: task.tags,
              estimatedHours: task.estimatedEffort
            });
            
            // Update local task with external ID
            store.updateTask(task.id, {
              metadata: {
                ...task.metadata,
                externalId,
                lastSyncedAt: new Date().toISOString()
              }
            });
          }
        } catch (error) {
          console.error(`Failed to sync task ${id}:`, error);
          // Keep in queue for retry
          continue;
        }
      }
      
      // Remove from queue if successful
      changes.delete(changeId);
    }
  }

  /**
   * Handle webhook from external tool
   */
  private async handleWebhook(sessionId: string, payload: WebhookPayload): Promise<void> {
    const store = useBrainstormStore.getState();
    const status = this.syncStatus.get(sessionId);
    
    if (!status) return;
    
    switch (payload.event) {
      case 'task.created':
        await this.importRemoteTask(sessionId, payload.data);
        break;
        
      case 'task.updated':
        const localTask = Object.values(store.tasks).find(t => 
          t.metadata?.externalId === payload.data.id
        );
        
        if (localTask) {
          await this.handleRemoteUpdate(
            sessionId, 
            localTask, 
            payload.data,
            this.syncConfigs.get(sessionId)!
          );
        }
        break;
        
      case 'task.deleted':
        const taskToDelete = Object.values(store.tasks).find(t => 
          t.metadata?.externalId === payload.data.id
        );
        
        if (taskToDelete) {
          await this.handleRemoteDeletion(
            sessionId,
            taskToDelete,
            this.syncConfigs.get(sessionId)!
          );
        }
        break;
    }
  }

  /**
   * Import a remote task
   */
  private async importRemoteTask(sessionId: string, remoteTask: any): Promise<void> {
    const store = useBrainstormStore.getState();
    
    // Create local task from remote data
    const task: GeneratedTask = {
      id: `task_${sessionId}_${Date.now()}`,
      ideaId: '', // Will be linked later
      title: remoteTask.summary || remoteTask.name,
      description: remoteTask.description || '',
      priority: this.mapRemotePriority(remoteTask.priority),
      status: this.mapRemoteStatus(remoteTask.status),
      estimatedEffort: remoteTask.timeEstimate || 0,
      tags: remoteTask.labels || [],
      assignee: remoteTask.assignee?.email || null,
      dependencies: [],
      createdAt: remoteTask.created || new Date().toISOString(),
      updatedAt: remoteTask.updated || new Date().toISOString(),
      metadata: {
        externalId: remoteTask.id,
        externalSystem: remoteTask.source || 'unknown',
        lastSyncedAt: new Date().toISOString()
      }
    };
    
    store.addTask(sessionId, task);
  }

  /**
   * Handle remote update
   */
  private async handleRemoteUpdate(
    sessionId: string,
    localTask: GeneratedTask,
    remoteTask: any,
    config: SyncConfig
  ): Promise<void> {
    const store = useBrainstormStore.getState();
    const status = this.syncStatus.get(sessionId)!;
    
    // Check for conflicts
    const localModified = new Date(localTask.updatedAt);
    const remoteModified = new Date(remoteTask.updated);
    const lastSync = new Date(localTask.metadata?.lastSyncedAt || 0);
    
    if (localModified > lastSync && remoteModified > lastSync) {
      // Conflict detected
      status.conflicts.push({
        id: localTask.id,
        type: 'task',
        localVersion: localTask,
        remoteVersion: remoteTask,
        detectedAt: new Date()
      });
      
      // Apply conflict resolution strategy
      switch (config.conflictResolution) {
        case 'remote':
          // Remote wins
          await this.applyRemoteChanges(localTask.id, remoteTask);
          break;
          
        case 'local':
          // Local wins - push to remote
          this.changeQueue.get(sessionId)?.add(`task:${localTask.id}`);
          break;
          
        case 'merge':
          // Attempt to merge
          await this.mergeTaskVersions(localTask, remoteTask);
          break;
          
        case 'manual':
          // Keep in conflicts for manual resolution
          break;
      }
    } else if (remoteModified > localModified) {
      // Remote is newer, no conflict
      await this.applyRemoteChanges(localTask.id, remoteTask);
    }
  }

  /**
   * Handle remote deletion
   */
  private async handleRemoteDeletion(
    sessionId: string,
    localTask: GeneratedTask,
    config: SyncConfig
  ): Promise<void> {
    const store = useBrainstormStore.getState();
    
    if (config.conflictResolution === 'local') {
      // Re-create on remote
      this.changeQueue.get(sessionId)?.add(`task:${localTask.id}`);
    } else {
      // Delete locally
      store.deleteTask(localTask.id);
    }
  }

  /**
   * Apply remote changes to local task
   */
  private async applyRemoteChanges(taskId: string, remoteTask: any): Promise<void> {
    const store = useBrainstormStore.getState();
    
    store.updateTask(taskId, {
      title: remoteTask.summary || remoteTask.name,
      description: remoteTask.description || '',
      priority: this.mapRemotePriority(remoteTask.priority),
      status: this.mapRemoteStatus(remoteTask.status),
      assignee: remoteTask.assignee?.email || null,
      metadata: {
        ...store.tasks[taskId].metadata,
        lastSyncedAt: new Date().toISOString()
      }
    });
  }

  /**
   * Merge task versions
   */
  private async mergeTaskVersions(
    localTask: GeneratedTask,
    remoteTask: any
  ): Promise<void> {
    const store = useBrainstormStore.getState();
    
    // Simple merge strategy - combine changes
    const merged = {
      title: remoteTask.summary || localTask.title,
      description: remoteTask.description || localTask.description,
      priority: localTask.priority, // Keep local priority
      status: this.mapRemoteStatus(remoteTask.status),
      assignee: remoteTask.assignee?.email || localTask.assignee,
      tags: [...new Set([...localTask.tags, ...(remoteTask.labels || [])])],
      metadata: {
        ...localTask.metadata,
        lastSyncedAt: new Date().toISOString()
      }
    };
    
    store.updateTask(localTask.id, merged);
  }

  /**
   * Resolve conflicts
   */
  private async resolveConflicts(sessionId: string, config: SyncConfig): Promise<void> {
    const status = this.syncStatus.get(sessionId)!;
    
    // Auto-resolve based on strategy
    for (const conflict of status.conflicts) {
      if (config.conflictResolution !== 'manual') {
        // Conflict already handled in handleRemoteUpdate
        status.conflicts = status.conflicts.filter(c => c.id !== conflict.id);
      }
    }
  }

  /**
   * Track local change
   */
  trackChange(sessionId: string, type: 'task' | 'idea', id: string): void {
    const changes = this.changeQueue.get(sessionId);
    if (changes) {
      changes.add(`${type}:${id}`);
      const status = this.syncStatus.get(sessionId);
      if (status) {
        status.pendingChanges = changes.size;
      }
    }
  }

  /**
   * Get sync status
   */
  getStatus(sessionId: string): SyncStatus | undefined {
    return this.syncStatus.get(sessionId);
  }

  /**
   * Get conflicts for manual resolution
   */
  getConflicts(sessionId: string): SyncConflict[] {
    return this.syncStatus.get(sessionId)?.conflicts || [];
  }

  /**
   * Resolve conflict manually
   */
  async resolveConflict(
    sessionId: string,
    conflictId: string,
    resolution: 'local' | 'remote' | 'merged',
    mergedData?: any
  ): Promise<void> {
    const status = this.syncStatus.get(sessionId);
    const conflict = status?.conflicts.find(c => c.id === conflictId);
    
    if (!conflict) return;
    
    switch (resolution) {
      case 'local':
        // Keep local version, push to remote
        this.changeQueue.get(sessionId)?.add(`${conflict.type}:${conflict.id}`);
        break;
        
      case 'remote':
        // Apply remote version
        await this.applyRemoteChanges(conflict.id, conflict.remoteVersion);
        break;
        
      case 'merged':
        // Apply merged data
        if (mergedData && conflict.type === 'task') {
          useBrainstormStore.getState().updateTask(conflict.id, mergedData);
          this.changeQueue.get(sessionId)?.add(`task:${conflict.id}`);
        }
        break;
    }
    
    // Remove resolved conflict
    if (status) {
      status.conflicts = status.conflicts.filter(c => c.id !== conflictId);
    }
  }

  /**
   * Clear sync data
   */
  clearSync(sessionId: string): void {
    this.stopAutoSync(sessionId);
    this.syncConfigs.delete(sessionId);
    this.syncStatus.delete(sessionId);
    this.webhookHandlers.delete(sessionId);
    this.changeQueue.delete(sessionId);
  }

  // Helper methods
  
  private async fetchRemoteTasks(provider: any, projectId: string): Promise<any[]> {
    // Implementation depends on provider API
    // This is a placeholder
    return [];
  }

  private async updateRemoteTask(provider: ProjectManagementProvider, task: GeneratedTask): Promise<void> {
    // Implementation depends on provider API
    // This is a placeholder
  }

  private mapRemotePriority(remotePriority: string): GeneratedTask['priority'] {
    const priorityMap: Record<string, GeneratedTask['priority']> = {
      'Highest': 'critical',
      'High': 'high',
      'Medium': 'medium',
      'Low': 'low',
      'Lowest': 'low'
    };
    return priorityMap[remotePriority] || 'medium';
  }

  private mapRemoteStatus(remoteStatus: string): GeneratedTask['status'] {
    const statusMap: Record<string, GeneratedTask['status']> = {
      'To Do': 'pending',
      'In Progress': 'in-progress',
      'Done': 'completed',
      'Closed': 'completed'
    };
    return statusMap[remoteStatus] || 'pending';
  }

  private hasRemoteChanges(localTask: GeneratedTask, remoteTask: any): boolean {
    return new Date(remoteTask.updated) > new Date(localTask.metadata?.lastSyncedAt || 0);
  }
}

// Export singleton instance
export const syncManager = new SyncManager();

// Also export class for testing
export default SyncManager;