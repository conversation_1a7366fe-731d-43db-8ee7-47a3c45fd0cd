import { invoke } from '@tauri-apps/api/core';
import { Idea, IdeaCluster, Priority, IdeaStatus } from '@/types/brainstorm';

export interface AsanaConfig {
  accessToken: string;
  workspaceGid: string;
  projectGid: string;
}

export interface AsanaTask {
  name: string;
  notes?: string;
  projects?: string[];
  tags?: string[];
  due_on?: string;
  assignee?: string;
  custom_fields?: Record<string, any>;
  parent?: string;
}

export interface AsanaSection {
  gid: string;
  name: string;
}

export interface AsanaCreateResponse {
  data: {
    gid: string;
    name: string;
    permalink_url: string;
  };
}

const ASANA_PRIORITY_CUSTOM_FIELD = 'priority_level'; // May vary by workspace

export class AsanaIntegration {
  private config: AsanaConfig;
  private baseUrl = 'https://app.asana.com/api/1.0';
  private headers: HeadersInit;

  constructor(config: AsanaConfig) {
    this.config = config;
    this.headers = {
      'Authorization': `Bearer ${config.accessToken}`,
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    };
  }

  // Test connection to Asana
  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/users/me`, {
        headers: this.headers,
      });
      return response.ok;
    } catch (error) {
      console.error('Asana connection test failed:', error);
      return false;
    }
  }

  // Get project details
  async getProject(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/projects/${this.config.projectGid}`, {
      headers: this.headers,
    });
    
    if (!response.ok) {
      throw new Error(`Failed to get project: ${response.statusText}`);
    }
    
    const result = await response.json();
    return result.data;
  }

  // Get project sections
  async getProjectSections(): Promise<AsanaSection[]> {
    const response = await fetch(
      `${this.baseUrl}/projects/${this.config.projectGid}/sections`,
      { headers: this.headers }
    );
    
    if (!response.ok) {
      throw new Error(`Failed to get sections: ${response.statusText}`);
    }
    
    const result = await response.json();
    return result.data;
  }

  // Create or get section for a cluster
  async ensureSection(sectionName: string): Promise<string> {
    const sections = await this.getProjectSections();
    const existing = sections.find(s => s.name === sectionName);
    
    if (existing) {
      return existing.gid;
    }

    // Create new section
    const response = await fetch(
      `${this.baseUrl}/projects/${this.config.projectGid}/sections`,
      {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify({
          data: { name: sectionName },
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to create section: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data.gid;
  }

  // Create a single Asana task from an idea
  async createTask(idea: Idea, sectionGid?: string): Promise<AsanaCreateResponse> {
    const task: AsanaTask = {
      name: idea.content,
      notes: this.formatNotes(idea),
      projects: [this.config.projectGid],
    };

    // Add tags
    if (idea.tags.length > 0) {
      const tagGids = await this.ensureTags(idea.tags);
      task.tags = tagGids;
    }

    const requestBody: any = { data: task };

    // Add to specific section if provided
    if (sectionGid) {
      requestBody.data.memberships = [{
        project: this.config.projectGid,
        section: sectionGid,
      }];
    }

    const response = await fetch(`${this.baseUrl}/tasks`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to create Asana task: ${error}`);
    }

    return response.json();
  }

  // Create multiple tasks from ideas
  async createTasksFromIdeas(
    ideas: Idea[],
    clusters: IdeaCluster[],
    organizeBySections: boolean = true
  ): Promise<AsanaCreateResponse[]> {
    const createdTasks: AsanaCreateResponse[] = [];
    const sectionMap: Record<string, string> = {};

    // Create sections for clusters if requested
    if (organizeBySections && clusters.length > 0) {
      for (const cluster of clusters) {
        try {
          const sectionGid = await this.ensureSection(cluster.name);
          sectionMap[cluster.id] = sectionGid;
        } catch (error) {
          console.error(`Failed to create section for cluster ${cluster.name}:`, error);
        }
      }
    }

    // Create tasks for ideas
    for (const idea of ideas) {
      try {
        const sectionGid = idea.cluster ? sectionMap[idea.cluster] : undefined;
        const task = await this.createTask(idea, sectionGid);
        createdTasks.push(task);

        // Create subtasks if available
        if (idea.metadata?.subtasks && idea.metadata.subtasks.length > 0) {
          await this.createSubtasks(task.data.gid, idea.metadata.subtasks);
        }
      } catch (error) {
        console.error(`Failed to create task for idea ${idea.id}:`, error);
      }
    }

    return createdTasks;
  }

  // Create subtasks for a parent task
  async createSubtasks(parentGid: string, subtasks: string[]): Promise<AsanaCreateResponse[]> {
    const createdSubtasks: AsanaCreateResponse[] = [];

    for (const subtask of subtasks) {
      const subtaskData: AsanaTask = {
        name: subtask,
        parent: parentGid,
      };

      try {
        const response = await fetch(`${this.baseUrl}/tasks`, {
          method: 'POST',
          headers: this.headers,
          body: JSON.stringify({ data: subtaskData }),
        });

        if (response.ok) {
          const created = await response.json();
          createdSubtasks.push(created);
        }
      } catch (error) {
        console.error(`Failed to create subtask: ${subtask}`, error);
      }
    }

    return createdSubtasks;
  }

  // Ensure tags exist and return their GIDs
  private async ensureTags(tagNames: string[]): Promise<string[]> {
    const tagGids: string[] = [];

    // Get existing tags in workspace
    const response = await fetch(
      `${this.baseUrl}/workspaces/${this.config.workspaceGid}/tags`,
      { headers: this.headers }
    );

    if (!response.ok) {
      console.error('Failed to get tags');
      return [];
    }

    const result = await response.json();
    const existingTags = result.data;

    for (const tagName of tagNames) {
      const existing = existingTags.find((t: any) => t.name === tagName);
      
      if (existing) {
        tagGids.push(existing.gid);
      } else {
        // Create new tag
        try {
          const createResponse = await fetch(
            `${this.baseUrl}/tags`,
            {
              method: 'POST',
              headers: this.headers,
              body: JSON.stringify({
                data: {
                  name: tagName,
                  workspace: this.config.workspaceGid,
                },
              }),
            }
          );

          if (createResponse.ok) {
            const newTag = await createResponse.json();
            tagGids.push(newTag.data.gid);
          }
        } catch (error) {
          console.error(`Failed to create tag: ${tagName}`, error);
        }
      }
    }

    return tagGids;
  }

  // Format idea notes for Asana
  private formatNotes(idea: Idea): string {
    let notes = '';

    if (idea.metadata?.description) {
      notes += `${idea.metadata.description}\n\n`;
    }

    notes += `**Details:**\n`;
    notes += `• Status: ${idea.status}\n`;
    notes += `• Priority: ${idea.priority || 'Not set'}\n`;
    notes += `• Created: ${new Date(idea.createdAt).toLocaleString()}\n`;
    
    if (idea.impact) {
      notes += `• Impact: ${idea.impact}/10\n`;
    }
    if (idea.effort) {
      notes += `• Effort: ${idea.effort}/10\n`;
    }

    if (idea.connections.length > 0) {
      notes += `\n**Related Ideas:**\n`;
      notes += `This idea is connected to ${idea.connections.length} other ideas.\n`;
    }

    if (idea.attachments && idea.attachments.length > 0) {
      notes += `\n**Attachments:**\n`;
      notes += `${idea.attachments.length} file(s) attached to this idea.\n`;
    }

    notes += `\n_Generated from Claudia Brainstorming Session_`;

    return notes;
  }

  // Batch create tasks with progress callback
  async batchCreateTasks(
    ideas: Idea[],
    onProgress?: (current: number, total: number) => void
  ): Promise<{ success: AsanaCreateResponse[]; failed: Array<{ idea: Idea; error: string }> }> {
    const results = {
      success: [] as AsanaCreateResponse[],
      failed: [] as Array<{ idea: Idea; error: string }>,
    };

    for (let i = 0; i < ideas.length; i++) {
      try {
        const task = await this.createTask(ideas[i]);
        results.success.push(task);
      } catch (error) {
        results.failed.push({
          idea: ideas[i],
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

      if (onProgress) {
        onProgress(i + 1, ideas.length);
      }

      // Rate limiting - Asana allows 150 requests per minute
      await new Promise(resolve => setTimeout(resolve, 400));
    }

    return results;
  }

  // Create a project board view for the brainstorming session
  async createBoardView(sessionTitle: string): Promise<any> {
    // Note: This would require using Asana's newer API features
    // Currently, board views are created through the UI
    console.log('Board view creation is not yet supported via API');
    return null;
  }
}

// Store Asana configuration securely using Tauri
export async function saveAsanaConfig(config: AsanaConfig): Promise<void> {
  await invoke('save_integration_config', {
    integration: 'asana',
    config: JSON.stringify(config),
  });
}

export async function loadAsanaConfig(): Promise<AsanaConfig | null> {
  try {
    const configStr = await invoke<string>('load_integration_config', {
      integration: 'asana',
    });
    return JSON.parse(configStr);
  } catch {
    return null;
  }
}