import { invoke } from '@tauri-apps/api/core';
import { Idea, IdeaCluster, Priority, IdeaStatus } from '@/types/brainstorm';

export interface JiraConfig {
  domain: string; // e.g., 'yourcompany.atlassian.net'
  email: string;
  apiToken: string;
  projectKey: string;
}

export interface JiraIssue {
  fields: {
    project: { key: string };
    summary: string;
    description: string;
    issuetype: { name: string };
    priority?: { name: string };
    labels?: string[];
    components?: Array<{ name: string }>;
    customfield_10001?: string; // Epic link
    parent?: { key: string }; // For subtasks
  };
}

export interface JiraCreateResponse {
  id: string;
  key: string;
  self: string;
}

const JIRA_PRIORITY_MAP: Record<Priority, string> = {
  [Priority.LOW]: 'Low',
  [Priority.MEDIUM]: 'Medium',
  [Priority.HIGH]: 'High',
  [Priority.CRITICAL]: 'Highest',
};

const JIRA_STATUS_MAP: Record<IdeaStatus, string> = {
  [IdeaStatus.TO_EXPLORE]: 'To Do',
  [IdeaStatus.IN_PROGRESS]: 'In Progress',
  [IdeaStatus.VALIDATED]: 'Done',
  [IdeaStatus.ARCHIVED]: 'Done',
};

export class JiraIntegration {
  private config: JiraConfig;
  private baseUrl: string;
  private headers: HeadersInit;

  constructor(config: JiraConfig) {
    this.config = config;
    this.baseUrl = `https://${config.domain}/rest/api/3`;
    this.headers = {
      'Authorization': `Basic ${btoa(`${config.email}:${config.apiToken}`)}`,
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    };
  }

  // Test connection to Jira
  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/myself`, {
        headers: this.headers,
      });
      return response.ok;
    } catch (error) {
      console.error('Jira connection test failed:', error);
      return false;
    }
  }

  // Get project details
  async getProject(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/project/${this.config.projectKey}`, {
      headers: this.headers,
    });
    
    if (!response.ok) {
      throw new Error(`Failed to get project: ${response.statusText}`);
    }
    
    return response.json();
  }

  // Get issue types for the project
  async getIssueTypes(): Promise<any[]> {
    const project = await this.getProject();
    return project.issueTypes || [];
  }

  // Create a single Jira issue from an idea
  async createIssue(idea: Idea, issueType: string = 'Task'): Promise<JiraCreateResponse> {
    const issue: JiraIssue = {
      fields: {
        project: { key: this.config.projectKey },
        summary: idea.content,
        description: this.formatDescription(idea),
        issuetype: { name: issueType },
        labels: idea.tags,
      },
    };

    // Add priority if available
    if (idea.priority) {
      issue.fields.priority = { name: JIRA_PRIORITY_MAP[idea.priority] };
    }

    // Add cluster as component if available
    if (idea.cluster) {
      issue.fields.components = [{ name: idea.cluster }];
    }

    const response = await fetch(`${this.baseUrl}/issue`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify(issue),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to create Jira issue: ${error}`);
    }

    return response.json();
  }

  // Create multiple issues with epic grouping
  async createIssuesFromIdeas(
    ideas: Idea[],
    clusters: IdeaCluster[],
    createEpics: boolean = true
  ): Promise<JiraCreateResponse[]> {
    const createdIssues: JiraCreateResponse[] = [];
    const epicMap: Record<string, string> = {};

    // Create epics for clusters if requested
    if (createEpics && clusters.length > 0) {
      for (const cluster of clusters) {
        const epicIssue: JiraIssue = {
          fields: {
            project: { key: this.config.projectKey },
            summary: cluster.name,
            description: cluster.theme,
            issuetype: { name: 'Epic' },
            labels: ['brainstorm-cluster'],
          },
        };

        try {
          const epic = await this.createIssue(cluster as any, 'Epic');
          epicMap[cluster.id] = epic.key;
          createdIssues.push(epic);
        } catch (error) {
          console.error(`Failed to create epic for cluster ${cluster.name}:`, error);
        }
      }
    }

    // Create issues for ideas
    for (const idea of ideas) {
      try {
        const issue = await this.createIssue(idea);
        
        // Link to epic if available
        if (idea.cluster && epicMap[idea.cluster]) {
          await this.linkToEpic(issue.key, epicMap[idea.cluster]);
        }
        
        createdIssues.push(issue);
      } catch (error) {
        console.error(`Failed to create issue for idea ${idea.id}:`, error);
      }
    }

    return createdIssues;
  }

  // Link an issue to an epic
  private async linkToEpic(issueKey: string, epicKey: string): Promise<void> {
    // This might need to use a custom field ID specific to your Jira instance
    const response = await fetch(`${this.baseUrl}/issue/${issueKey}`, {
      method: 'PUT',
      headers: this.headers,
      body: JSON.stringify({
        fields: {
          customfield_10001: epicKey, // Epic link field - may vary by instance
        },
      }),
    });

    if (!response.ok) {
      console.error(`Failed to link ${issueKey} to epic ${epicKey}`);
    }
  }

  // Create subtasks for an issue
  async createSubtasks(
    parentKey: string,
    subtasks: string[]
  ): Promise<JiraCreateResponse[]> {
    const createdSubtasks: JiraCreateResponse[] = [];

    for (const subtask of subtasks) {
      const subtaskIssue: JiraIssue = {
        fields: {
          project: { key: this.config.projectKey },
          summary: subtask,
          description: `Subtask of ${parentKey}`,
          issuetype: { name: 'Sub-task' },
          parent: { key: parentKey },
        },
      };

      try {
        const response = await fetch(`${this.baseUrl}/issue`, {
          method: 'POST',
          headers: this.headers,
          body: JSON.stringify(subtaskIssue),
        });

        if (response.ok) {
          const created = await response.json();
          createdSubtasks.push(created);
        }
      } catch (error) {
        console.error(`Failed to create subtask: ${subtask}`, error);
      }
    }

    return createdSubtasks;
  }

  // Format idea description for Jira
  private formatDescription(idea: Idea): string {
    let description = '';

    if (idea.metadata?.description) {
      description += `${idea.metadata.description}\n\n`;
    }

    description += `h3. Details\n`;
    description += `* Status: ${JIRA_STATUS_MAP[idea.status]}\n`;
    description += `* Created: ${new Date(idea.createdAt).toLocaleString()}\n`;
    
    if (idea.impact) {
      description += `* Impact: ${idea.impact}/10\n`;
    }
    if (idea.effort) {
      description += `* Effort: ${idea.effort}/10\n`;
    }

    if (idea.metadata?.subtasks && idea.metadata.subtasks.length > 0) {
      description += `\nh3. Subtasks\n`;
      idea.metadata.subtasks.forEach(subtask => {
        description += `* ${subtask}\n`;
      });
    }

    if (idea.connections.length > 0) {
      description += `\nh3. Related Ideas\n`;
      description += `This idea is connected to ${idea.connections.length} other ideas.\n`;
    }

    if (idea.attachments && idea.attachments.length > 0) {
      description += `\nh3. Attachments\n`;
      description += `${idea.attachments.length} file(s) attached to this idea.\n`;
    }

    description += `\n_Generated from Claudia Brainstorming Session_`;

    return description;
  }

  // Batch create issues with progress callback
  async batchCreateIssues(
    ideas: Idea[],
    onProgress?: (current: number, total: number) => void
  ): Promise<{ success: JiraCreateResponse[]; failed: Array<{ idea: Idea; error: string }> }> {
    const results = {
      success: [] as JiraCreateResponse[],
      failed: [] as Array<{ idea: Idea; error: string }>,
    };

    for (let i = 0; i < ideas.length; i++) {
      try {
        const issue = await this.createIssue(ideas[i]);
        results.success.push(issue);
      } catch (error) {
        results.failed.push({
          idea: ideas[i],
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

      if (onProgress) {
        onProgress(i + 1, ideas.length);
      }

      // Rate limiting - Jira recommends no more than 10 requests per second
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return results;
  }
}

// Store Jira configuration securely using Tauri
export async function saveJiraConfig(config: JiraConfig): Promise<void> {
  await invoke('save_integration_config', {
    integration: 'jira',
    config: JSON.stringify(config),
  });
}

export async function loadJiraConfig(): Promise<JiraConfig | null> {
  try {
    const configStr = await invoke<string>('load_integration_config', {
      integration: 'jira',
    });
    return JSON.parse(configStr);
  } catch {
    return null;
  }
}