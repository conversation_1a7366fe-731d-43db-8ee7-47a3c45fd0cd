import { invoke } from '@tauri-apps/api/core';
import { Idea, IdeaCluster, Priority, IdeaStatus } from '@/types/brainstorm';

export interface TrelloConfig {
  apiKey: string;
  apiToken: string;
  boardId: string;
}

export interface TrelloCard {
  name: string;
  desc?: string;
  idList: string;
  idLabels?: string[];
  due?: string;
  pos?: number | 'top' | 'bottom';
  idMembers?: string[];
  idChecklists?: string[];
}

export interface TrelloList {
  id: string;
  name: string;
  pos: number;
}

export interface TrelloLabel {
  id: string;
  name: string;
  color: string;
}

export interface TrelloCreateResponse {
  id: string;
  name: string;
  url: string;
  shortUrl: string;
}

const TRELLO_LABEL_COLORS: Record<Priority, string> = {
  [Priority.LOW]: 'green',
  [Priority.MEDIUM]: 'yellow',
  [Priority.HIGH]: 'orange',
  [Priority.CRITICAL]: 'red',
};

export class TrelloIntegration {
  private config: TrelloConfig;
  private baseUrl = 'https://api.trello.com/1';
  private authParams: string;

  constructor(config: TrelloConfig) {
    this.config = config;
    this.authParams = `key=${config.apiKey}&token=${config.apiToken}`;
  }

  // Test connection to Trello
  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(
        `${this.baseUrl}/members/me?${this.authParams}`
      );
      return response.ok;
    } catch (error) {
      console.error('Trello connection test failed:', error);
      return false;
    }
  }

  // Get board details
  async getBoard(): Promise<any> {
    const response = await fetch(
      `${this.baseUrl}/boards/${this.config.boardId}?${this.authParams}`
    );
    
    if (!response.ok) {
      throw new Error(`Failed to get board: ${response.statusText}`);
    }
    
    return response.json();
  }

  // Get all lists in the board
  async getBoardLists(): Promise<TrelloList[]> {
    const response = await fetch(
      `${this.baseUrl}/boards/${this.config.boardId}/lists?${this.authParams}`
    );
    
    if (!response.ok) {
      throw new Error(`Failed to get lists: ${response.statusText}`);
    }
    
    return response.json();
  }

  // Get all labels in the board
  async getBoardLabels(): Promise<TrelloLabel[]> {
    const response = await fetch(
      `${this.baseUrl}/boards/${this.config.boardId}/labels?${this.authParams}`
    );
    
    if (!response.ok) {
      throw new Error(`Failed to get labels: ${response.statusText}`);
    }
    
    return response.json();
  }

  // Create or get list for a specific status or cluster
  async ensureList(listName: string): Promise<string> {
    const lists = await this.getBoardLists();
    const existing = lists.find(l => l.name === listName);
    
    if (existing) {
      return existing.id;
    }

    // Create new list
    const response = await fetch(
      `${this.baseUrl}/boards/${this.config.boardId}/lists?${this.authParams}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: listName,
          pos: 'bottom',
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to create list: ${response.statusText}`);
    }

    const newList = await response.json();
    return newList.id;
  }

  // Create or get labels for priorities and tags
  async ensureLabels(idea: Idea): Promise<string[]> {
    const labels = await this.getBoardLabels();
    const labelIds: string[] = [];

    // Priority label
    if (idea.priority) {
      const priorityColor = TRELLO_LABEL_COLORS[idea.priority];
      const priorityLabel = labels.find(
        l => l.color === priorityColor && l.name === idea.priority
      );

      if (priorityLabel) {
        labelIds.push(priorityLabel.id);
      } else {
        // Create new priority label
        const response = await fetch(
          `${this.baseUrl}/boards/${this.config.boardId}/labels?${this.authParams}`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: idea.priority,
              color: priorityColor,
            }),
          }
        );

        if (response.ok) {
          const newLabel = await response.json();
          labelIds.push(newLabel.id);
        }
      }
    }

    // Tag labels
    for (const tag of idea.tags) {
      const tagLabel = labels.find(l => l.name === tag);
      
      if (tagLabel) {
        labelIds.push(tagLabel.id);
      } else {
        // Create new tag label with a default color
        const response = await fetch(
          `${this.baseUrl}/boards/${this.config.boardId}/labels?${this.authParams}`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: tag,
              color: 'blue',
            }),
          }
        );

        if (response.ok) {
          const newLabel = await response.json();
          labelIds.push(newLabel.id);
        }
      }
    }

    return labelIds;
  }

  // Create a single Trello card from an idea
  async createCard(idea: Idea, listId: string): Promise<TrelloCreateResponse> {
    const labelIds = await this.ensureLabels(idea);
    
    const card: TrelloCard = {
      name: idea.content,
      desc: this.formatDescription(idea),
      idList: listId,
      idLabels: labelIds,
      pos: 'bottom',
    };

    const response = await fetch(
      `${this.baseUrl}/cards?${this.authParams}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(card),
      }
    );

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to create Trello card: ${error}`);
    }

    const createdCard = await response.json();

    // Add checklist for subtasks if available
    if (idea.metadata?.subtasks && idea.metadata.subtasks.length > 0) {
      await this.addChecklist(createdCard.id, 'Subtasks', idea.metadata.subtasks);
    }

    return createdCard;
  }

  // Add a checklist to a card
  async addChecklist(cardId: string, checklistName: string, items: string[]): Promise<void> {
    // Create checklist
    const checklistResponse = await fetch(
      `${this.baseUrl}/checklists?${this.authParams}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          idCard: cardId,
          name: checklistName,
        }),
      }
    );

    if (!checklistResponse.ok) {
      console.error('Failed to create checklist');
      return;
    }

    const checklist = await checklistResponse.json();

    // Add items to checklist
    for (const item of items) {
      await fetch(
        `${this.baseUrl}/checklists/${checklist.id}/checkItems?${this.authParams}`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: item,
            pos: 'bottom',
          }),
        }
      );
    }
  }

  // Create multiple cards from ideas
  async createCardsFromIdeas(
    ideas: Idea[],
    clusters: IdeaCluster[],
    organizeByStatus: boolean = true,
    organizeByClusters: boolean = false
  ): Promise<TrelloCreateResponse[]> {
    const createdCards: TrelloCreateResponse[] = [];
    const listMap: Record<string, string> = {};

    if (organizeByStatus) {
      // Create lists for different statuses
      const statuses = Object.values(IdeaStatus);
      for (const status of statuses) {
        const listId = await this.ensureList(this.getStatusListName(status));
        listMap[status] = listId;
      }
    } else if (organizeByClusters && clusters.length > 0) {
      // Create lists for clusters
      for (const cluster of clusters) {
        const listId = await this.ensureList(cluster.name);
        listMap[cluster.id] = listId;
      }
      
      // Create a list for unclustered ideas
      const unclusteredListId = await this.ensureList('Uncategorized');
      listMap['unclustered'] = unclusteredListId;
    } else {
      // Use the first available list or create a default one
      const lists = await this.getBoardLists();
      const defaultListId = lists[0]?.id || await this.ensureList('Ideas');
      listMap['default'] = defaultListId;
    }

    // Create cards for ideas
    for (const idea of ideas) {
      try {
        let listId: string;
        
        if (organizeByStatus) {
          listId = listMap[idea.status];
        } else if (organizeByClusters) {
          listId = idea.cluster ? listMap[idea.cluster] : listMap['unclustered'];
        } else {
          listId = listMap['default'];
        }

        const card = await this.createCard(idea, listId);
        createdCards.push(card);
      } catch (error) {
        console.error(`Failed to create card for idea ${idea.id}:`, error);
      }
    }

    return createdCards;
  }

  // Get appropriate list name for status
  private getStatusListName(status: IdeaStatus): string {
    const statusNames: Record<IdeaStatus, string> = {
      [IdeaStatus.TO_EXPLORE]: 'To Explore',
      [IdeaStatus.IN_PROGRESS]: 'In Progress',
      [IdeaStatus.VALIDATED]: 'Validated',
      [IdeaStatus.ARCHIVED]: 'Archived',
    };
    return statusNames[status];
  }

  // Format idea description for Trello
  private formatDescription(idea: Idea): string {
    let desc = '';

    if (idea.metadata?.description) {
      desc += `${idea.metadata.description}\n\n`;
    }

    desc += `**Details:**\n`;
    desc += `- Status: ${idea.status}\n`;
    desc += `- Created: ${new Date(idea.createdAt).toLocaleString()}\n`;
    
    if (idea.impact) {
      desc += `- Impact: ${idea.impact}/10\n`;
    }
    if (idea.effort) {
      desc += `- Effort: ${idea.effort}/10\n`;
    }

    if (idea.cluster) {
      desc += `- Cluster: ${idea.cluster}\n`;
    }

    if (idea.connections.length > 0) {
      desc += `\n**Related Ideas:**\n`;
      desc += `This idea is connected to ${idea.connections.length} other ideas.\n`;
    }

    if (idea.attachments && idea.attachments.length > 0) {
      desc += `\n**Attachments:**\n`;
      desc += `${idea.attachments.length} file(s) attached to this idea.\n`;
    }

    desc += `\n---\n_Generated from Claudia Brainstorming Session_`;

    return desc;
  }

  // Batch create cards with progress callback
  async batchCreateCards(
    ideas: Idea[],
    listId: string,
    onProgress?: (current: number, total: number) => void
  ): Promise<{ success: TrelloCreateResponse[]; failed: Array<{ idea: Idea; error: string }> }> {
    const results = {
      success: [] as TrelloCreateResponse[],
      failed: [] as Array<{ idea: Idea; error: string }>,
    };

    for (let i = 0; i < ideas.length; i++) {
      try {
        const card = await this.createCard(ideas[i], listId);
        results.success.push(card);
      } catch (error) {
        results.failed.push({
          idea: ideas[i],
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

      if (onProgress) {
        onProgress(i + 1, ideas.length);
      }

      // Rate limiting - Trello allows 300 requests per 10 seconds per token
      await new Promise(resolve => setTimeout(resolve, 350));
    }

    return results;
  }

  // Archive all cards in a list
  async archiveList(listId: string): Promise<void> {
    await fetch(
      `${this.baseUrl}/lists/${listId}/closed?${this.authParams}&value=true`,
      { method: 'PUT' }
    );
  }

  // Create a template board for future brainstorming sessions
  async createTemplateBoard(name: string, description?: string): Promise<any> {
    const response = await fetch(
      `${this.baseUrl}/boards?${this.authParams}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name,
          desc: description || 'Brainstorming session template board',
          defaultLists: false,
        }),
      }
    );

    if (!response.ok) {
      throw new Error('Failed to create template board');
    }

    const board = await response.json();

    // Create default lists
    const defaultLists = ['To Explore', 'In Progress', 'Validated', 'Archived'];
    for (const listName of defaultLists) {
      await fetch(
        `${this.baseUrl}/boards/${board.id}/lists?${this.authParams}`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name: listName }),
        }
      );
    }

    return board;
  }
}

// Store Trello configuration securely using Tauri
export async function saveTrelloConfig(config: TrelloConfig): Promise<void> {
  await invoke('save_integration_config', {
    integration: 'trello',
    config: JSON.stringify(config),
  });
}

export async function loadTrelloConfig(): Promise<TrelloConfig | null> {
  try {
    const configStr = await invoke<string>('load_integration_config', {
      integration: 'trello',
    });
    return JSON.parse(configStr);
  } catch {
    return null;
  }
}