import { useEffect, useRef, useState, useCallback } from 'react';

// Performance monitoring utilities
export interface PerformanceMetrics {
  renderTime: number;
  reRenderCount: number;
  memoryUsage?: number;
  componentName: string;
  timestamp: number;
}

export interface ComponentStats {
  averageRenderTime: number;
  totalRenders: number;
  maxRenderTime: number;
  minRenderTime: number;
  lastRender: number;
}

class PerformanceTracker {
  private metrics: Map<string, PerformanceMetrics[]> = new Map();
  private observers: Map<string, PerformanceObserver> = new Map();
  private isEnabled = process.env.NODE_ENV === 'development';

  startTracking(componentName: string) {
    if (!this.isEnabled) return;

    const startTime = performance.now();
    return {
      end: () => {
        const endTime = performance.now();
        const renderTime = endTime - startTime;
        
        this.recordMetric({
          componentName,
          renderTime,
          reRenderCount: this.getMetrics(componentName).length + 1,
          memoryUsage: this.getMemoryUsage(),
          timestamp: Date.now()
        });
      }
    };
  }

  private recordMetric(metric: PerformanceMetrics) {
    const existing = this.metrics.get(metric.componentName) || [];
    existing.push(metric);
    
    // Keep only last 100 metrics per component
    if (existing.length > 100) {
      existing.splice(0, existing.length - 100);
    }
    
    this.metrics.set(metric.componentName, existing);
  }

  getMetrics(componentName: string): PerformanceMetrics[] {
    return this.metrics.get(componentName) || [];
  }

  getStats(componentName: string): ComponentStats | null {
    const metrics = this.getMetrics(componentName);
    if (metrics.length === 0) return null;

    const renderTimes = metrics.map(m => m.renderTime);
    
    return {
      averageRenderTime: renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length,
      totalRenders: metrics.length,
      maxRenderTime: Math.max(...renderTimes),
      minRenderTime: Math.min(...renderTimes),
      lastRender: metrics[metrics.length - 1].timestamp
    };
  }

  getAllStats(): Record<string, ComponentStats> {
    const stats: Record<string, ComponentStats> = {};
    for (const componentName of this.metrics.keys()) {
      const componentStats = this.getStats(componentName);
      if (componentStats) {
        stats[componentName] = componentStats;
      }
    }
    return stats;
  }

  private getMemoryUsage(): number | undefined {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return undefined;
  }

  clearMetrics(componentName?: string) {
    if (componentName) {
      this.metrics.delete(componentName);
    } else {
      this.metrics.clear();
    }
  }

  logPerformanceReport() {
    if (!this.isEnabled) return;

    const stats = this.getAllStats();
    console.group('🚀 Component Performance Report');
    
    Object.entries(stats).forEach(([name, stat]) => {
      console.log(`📊 ${name}:`, {
        'Avg Render Time': `${stat.averageRenderTime.toFixed(2)}ms`,
        'Total Renders': stat.totalRenders,
        'Max Render Time': `${stat.maxRenderTime.toFixed(2)}ms`,
        'Min Render Time': `${stat.minRenderTime.toFixed(2)}ms`
      });
    });
    
    console.groupEnd();
  }
}

export const performanceTracker = new PerformanceTracker();

// React hook for performance monitoring
export const usePerformanceMonitor = (componentName: string) => {
  const renderCountRef = useRef(0);
  const startTimeRef = useRef<number>();

  useEffect(() => {
    renderCountRef.current += 1;
    startTimeRef.current = performance.now();

    return () => {
      if (startTimeRef.current) {
        const renderTime = performance.now() - startTimeRef.current;
        performanceTracker.startTracking(componentName).end();
      }
    };
  });

  const getComponentStats = useCallback(() => {
    return performanceTracker.getStats(componentName);
  }, [componentName]);

  return {
    renderCount: renderCountRef.current,
    getStats: getComponentStats
  };
};

// Bundle size analyzer
export class BundleAnalyzer {
  private static loadedModules = new Set<string>();
  private static moduleSize = new Map<string, number>();

  static trackModule(moduleName: string, size?: number) {
    this.loadedModules.add(moduleName);
    if (size) {
      this.moduleSize.set(moduleName, size);
    }
  }

  static getLoadedModules() {
    return Array.from(this.loadedModules);
  }

  static getTotalSize() {
    return Array.from(this.moduleSize.values()).reduce((a, b) => a + b, 0);
  }

  static getModuleReport() {
    const modules = Array.from(this.moduleSize.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10);

    console.group('📦 Bundle Size Report');
    modules.forEach(([name, size]) => {
      console.log(`${name}: ${(size / 1024).toFixed(2)}KB`);
    });
    console.log(`Total tracked size: ${(this.getTotalSize() / 1024).toFixed(2)}KB`);
    console.groupEnd();
  }
}

// Memory leak detector
export class MemoryLeakDetector {
  private static listeners = new Map<string, number>();
  private static intervals = new Map<string, NodeJS.Timeout>();

  static trackEventListener(event: string, element?: Element) {
    const key = `${event}:${element?.tagName || 'document'}`;
    this.listeners.set(key, (this.listeners.get(key) || 0) + 1);
  }

  static untrackEventListener(event: string, element?: Element) {
    const key = `${event}:${element?.tagName || 'document'}`;
    const count = this.listeners.get(key) || 0;
    if (count > 0) {
      this.listeners.set(key, count - 1);
    }
  }

  static trackInterval(id: string, interval: NodeJS.Timeout) {
    this.intervals.set(id, interval);
  }

  static clearInterval(id: string) {
    const interval = this.intervals.get(id);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(id);
    }
  }

  static getReport() {
    console.group('🔍 Memory Leak Detection Report');
    
    if (this.listeners.size > 0) {
      console.log('Event Listeners:', Object.fromEntries(this.listeners));
    }
    
    if (this.intervals.size > 0) {
      console.log('Active Intervals:', this.intervals.size);
    }
    
    console.groupEnd();
  }

  static cleanup() {
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals.clear();
  }
}

// Performance optimization hook
export const useOptimizedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList,
  options: { throttle?: number; debounce?: number } = {}
) => {
  const { throttle, debounce } = options;
  const lastCallTime = useRef(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();

      if (debounce) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = setTimeout(() => callback(...args), debounce);
        return;
      }

      if (throttle && now - lastCallTime.current < throttle) {
        return;
      }

      lastCallTime.current = now;
      return callback(...args);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    deps
  );
};

// Performance component wrapper
export const withPerformanceMonitoring = <P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) => {
  const name = componentName || Component.displayName || Component.name || 'Anonymous';
  
  return React.memo((props: P) => {
    const tracker = performanceTracker.startTracking(name);
    
    useEffect(() => {
      return () => tracker.end();
    }, [tracker]);

    return <Component {...props} />;
  });
};

// Lazy loading utilities
export const createLazyComponent = <T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  const LazyComponent = React.lazy(importFunc);
  
  return (props: React.ComponentProps<T>) => (
    <React.Suspense fallback={fallback ? <fallback /> : <div>Loading...</div>}>
      <LazyComponent {...props} />
    </React.Suspense>
  );
};

// Development tools
export const DevTools = {
  showPerformanceReport: () => performanceTracker.logPerformanceReport(),
  showBundleReport: () => BundleAnalyzer.getModuleReport(),
  showMemoryReport: () => MemoryLeakDetector.getReport(),
  clearMetrics: () => performanceTracker.clearMetrics(),
  
  startProfiling: (componentName?: string) => {
    console.log(`🎯 Starting performance profiling${componentName ? ` for ${componentName}` : ''}...`);
    const startTime = performance.now();
    
    return {
      stop: () => {
        const duration = performance.now() - startTime;
        console.log(`⏱️ Profiling completed in ${duration.toFixed(2)}ms`);
        performanceTracker.logPerformanceReport();
      }
    };
  }
};

// Global performance setup
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).__CLAUDIA_PERF__ = DevTools;
  
  // Log performance report every 30 seconds in development
  setInterval(() => {
    const stats = performanceTracker.getAllStats();
    if (Object.keys(stats).length > 0) {
      performanceTracker.logPerformanceReport();
    }
  }, 30000);
}