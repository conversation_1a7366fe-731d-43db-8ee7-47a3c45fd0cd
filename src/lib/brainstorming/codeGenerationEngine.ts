/**
 * Code Generation Engine for Brainstorming
 *
 * Simplified code generation system that transforms brainstormed ideas into implementation artifacts.
 * Provides intelligent code scaffolding, testing, documentation focused on brainstorming workflows.
 *
 * NOTE: This is now completely separate from Claude CLI integration to maintain session separation.
 */

import {
  Idea,
  IdeaStatus,
  BrainstormSession,
  ChatMessage,
  BrainstormTemplate
} from '@/types/brainstorm';
import { EnhancedChoice, CodeGenerationConfig } from './enhancedChoiceEngine';

// Brainstorming-specific context (no longer dependent on Claude CLI)
export interface BrainstormingContext {
  sessionId: string;
  currentIdeas: Idea[];
  recentMessages: ChatMessage[];
  activeTemplate?: BrainstormTemplate;
  goals: string[];
  constraints: string[];
}

// Code generation interfaces (simplified, no CLI dependency)
export interface CodeGenerationRequest {
  prompt: string;
  type: 'component' | 'service' | 'utility' | 'test' | 'config' | 'documentation';
  language: string;
  framework?: string;
  context: BrainstormingContext;
  requirements: string[];
  outputFormat: 'inline' | 'file' | 'snippet';
  includeTests?: boolean;
  includeDocumentation?: boolean;
}

export interface CodeGeneration {
  id: string;
  request: CodeGenerationRequest;
  code: string;
  explanation: string;
  improvements: string[];
  metadata: {
    generatedAt: string;
    language: string;
    framework?: string;
    estimatedComplexity: 'low' | 'medium' | 'high';
    dependencies: string[];
  };
}

// Code generation templates and patterns
export interface CodeTemplate {
  id: string;
  name: string;
  description: string;
  type: CodeGeneration['request']['type'];
  language: string;
  framework?: string;
  template: string;
  variables: TemplateVariable[];
  dependencies: string[];
  examples: string[];
  category: 'component' | 'service' | 'utility' | 'test' | 'config' | 'documentation';
}

export interface TemplateVariable {
  name: string;
  type: 'string' | 'boolean' | 'array' | 'object';
  description: string;
  required: boolean;
  defaultValue?: any;
}

export interface CodeGenerationPlan {
  id: string;
  name: string;
  description: string;
  sourceIdeas: string[];
  steps: CodeGenerationStep[];
  estimatedDuration: string;
  complexity: 'simple' | 'moderate' | 'complex';
  prerequisites: string[];
  deliverables: string[];
}

export interface CodeGenerationStep {
  id: string;
  name: string;
  description: string;
  type: CodeGeneration['request']['type'];
  dependencies: string[];
  template?: CodeTemplate;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: string;
}

export interface GenerationResult {
  plan: CodeGenerationPlan;
  generations: CodeGeneration[];
  summary: GenerationSummary;
  nextSteps: string[];
  integrationGuide?: string;
}

export interface GenerationSummary {
  totalFiles: number;
  linesOfCode: number;
  languages: string[];
  frameworks: string[];
  testCoverage?: number;
  codeQualityScore?: number;
  suggestions: string[];
}

export interface CodeAnalysis {
  complexity: number;
  maintainability: number;
  testability: number; 
  reusability: number;
  suggestions: CodeSuggestion[];
}

export interface CodeSuggestion {
  type: 'improvement' | 'refactor' | 'optimization' | 'security' | 'best-practice';
  message: string;
  severity: 'low' | 'medium' | 'high';
  codeSnippet?: string;
  suggestedFix?: string;
}

// Predefined code templates
const CODE_TEMPLATES: CodeTemplate[] = [
  {
    id: 'react-component',
    name: 'React Component',
    description: 'Functional React component with TypeScript',
    type: 'component',
    language: 'typescript',
    framework: 'react',
    category: 'component',
    template: `
import React, { useState, useEffect } from 'react';
import { {{propsInterface}} } from './types';

export const {{componentName}}: React.FC<{{propsInterface}}> = ({
  {{props}}
}) => {
  {{stateVariables}}

  {{effects}}

  {{handlers}}

  return (
    <div className="{{className}}">
      {{jsx}}
    </div>
  );
};
    `,
    variables: [
      {
        name: 'componentName',
        type: 'string',
        description: 'Name of the React component',
        required: true
      },
      {
        name: 'propsInterface',
        type: 'string',
        description: 'TypeScript interface for props',
        required: true
      },
      {
        name: 'props',
        type: 'array',
        description: 'Component props',
        required: false,
        defaultValue: []
      },
      {
        name: 'className',
        type: 'string',
        description: 'CSS class name',
        required: false,
        defaultValue: 'component'
      }
    ],
    dependencies: ['react', '@types/react'],
    examples: [
      'UserProfile component with avatar and user info',
      'SearchBox component with autocomplete',
      'Modal component with overlay and animations'
    ]
  },
  {
    id: 'api-endpoint',
    name: 'REST API Endpoint',
    description: 'Express.js API endpoint with validation and error handling',
    type: 'api',
    language: 'typescript',
    framework: 'express',
    category: 'service',
    template: `
import { Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';
import { {{serviceImport}} } from '../services';

// Validation middleware
export const validate{{endpointName}} = [
  {{validationRules}},
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  }
];

// {{httpMethod}} {{path}}
export const {{handlerName}} = async (
  req: Request<{{paramTypes}}, {{responseType}}, {{bodyType}}>,
  res: Response<{{responseType}}>,
  next: NextFunction
) => {
  try {
    {{implementation}}
    
    res.status({{statusCode}}).json(result);
  } catch (error) {
    next(error);
  }
};
    `,
    variables: [
      {
        name: 'endpointName',
        type: 'string',
        description: 'Name of the API endpoint',
        required: true
      },
      {
        name: 'httpMethod',
        type: 'string',
        description: 'HTTP method (GET, POST, PUT, DELETE)',
        required: true
      },
      {
        name: 'path',
        type: 'string',
        description: 'API endpoint path',
        required: true
      },
      {
        name: 'statusCode',
        type: 'string',
        description: 'Success status code',
        required: false,
        defaultValue: '200'
      }
    ],
    dependencies: ['express', 'express-validator'],
    examples: [
      'POST /api/users - Create new user',
      'GET /api/products/:id - Get product by ID',
      'PUT /api/orders/:id/status - Update order status'
    ]
  },
  {
    id: 'test-suite',
    name: 'Test Suite',
    description: 'Comprehensive test suite with unit and integration tests',
    type: 'test',
    language: 'typescript',
    framework: 'jest',
    category: 'test',
    template: `
import { {{testImports}} } from '{{importPath}}';
import { {{mockImports}} } from '../__mocks__';

describe('{{testSuiteName}}', () => {
  {{setupAndTeardown}}

  describe('{{featureDescription}}', () => {
    {{unitTests}}
  });

  describe('Integration Tests', () => {
    {{integrationTests}}
  });

  describe('Edge Cases', () => {
    {{edgeCaseTests}}
  });
});
    `,
    variables: [
      {
        name: 'testSuiteName',
        type: 'string',
        description: 'Name of the test suite',
        required: true
      },
      {
        name: 'featureDescription',
        type: 'string',
        description: 'Description of feature being tested',
        required: true
      },
      {
        name: 'testImports',
        type: 'array',
        description: 'Items to import for testing',
        required: true
      }
    ],
    dependencies: ['jest', '@types/jest'],
    examples: [
      'UserService test suite with CRUD operations',
      'React component test with user interactions',
      'API endpoint test with various scenarios'
    ]
  }
];

export class CodeGenerationEngine {
  private templates: Map<string, CodeTemplate> = new Map();
  private generationHistory: Map<string, CodeGeneration[]> = new Map();
  private planHistory: Map<string, CodeGenerationPlan[]> = new Map();

  constructor() {
    // Initialize with predefined templates
    CODE_TEMPLATES.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  /**
   * Generate a comprehensive code generation plan from ideas
   */
  async createGenerationPlan(
    ideas: Idea[],
    context: BrainstormingContext,
    preferences?: {
      language?: string;
      framework?: string;
      includeTests?: boolean;
      includeDocumentation?: boolean;
      targetComplexity?: 'simple' | 'moderate' | 'complex';
    }
  ): Promise<CodeGenerationPlan> {
    const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
    
    // Analyze ideas and create generation steps
    const analysisResult = await this.analyzeIdeasForCodeGeneration(ideas, context);
    const steps = await this.createGenerationSteps(analysisResult, preferences);
    
    const plan: CodeGenerationPlan = {
      id: planId,
      name: `Implementation Plan for ${ideas.length} Ideas`,
      description: this.generatePlanDescription(ideas, analysisResult),
      sourceIdeas: ideas.map(idea => idea.id),
      steps,
      estimatedDuration: this.calculateEstimatedDuration(steps),
      complexity: this.assessPlanComplexity(steps, analysisResult),
      prerequisites: this.identifyPrerequisites(analysisResult, preferences),
      deliverables: this.identifyDeliverables(steps)
    };

    // Store plan in history
    const sessionPlans = this.planHistory.get(context.sessionId) || [];
    sessionPlans.push(plan);
    this.planHistory.set(context.sessionId, sessionPlans);

    return plan;
  }

  /**
   * Execute a code generation plan
   */
  async executePlan(
    plan: CodeGenerationPlan,
    context: BrainstormingContext,
    options?: {
      parallel?: boolean;
      dryRun?: boolean;
      stopOnError?: boolean;
    }
  ): Promise<GenerationResult> {
    const generations: CodeGeneration[] = [];
    const errors: string[] = [];

    try {
      if (options?.parallel) {
        // Execute steps in parallel where possible
        const parallelGroups = this.groupStepsByDependencies(plan.steps);
        
        for (const group of parallelGroups) {
          const groupGenerations = await Promise.allSettled(
            group.map(step => this.executeGenerationStep(step, context, plan))
          );
          
          groupGenerations.forEach((result, index) => {
            if (result.status === 'fulfilled') {
              generations.push(result.value);
            } else {
              errors.push(`Step ${group[index].id}: ${result.reason}`);
              if (options?.stopOnError) {
                throw new Error(`Failed to execute step: ${result.reason}`);
              }
            }
          });
        }
      } else {
        // Execute steps sequentially
        for (const step of plan.steps) {
          try {
            const generation = await this.executeGenerationStep(step, context, plan);
            generations.push(generation);
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            errors.push(`Step ${step.id}: ${errorMessage}`);
            
            if (options?.stopOnError) {
              throw error;
            }
          }
        }
      }

      // Analyze generated code
      const summary = await this.generateSummary(generations);
      const nextSteps = this.generateNextSteps(plan, generations);
      const integrationGuide = await this.generateIntegrationGuide(generations, context);

      const result: GenerationResult = {
        plan,
        generations,
        summary,
        nextSteps,
        integrationGuide
      };

      // Store generations in history
      const sessionGenerations = this.generationHistory.get(context.sessionId) || [];
      sessionGenerations.push(...generations);
      this.generationHistory.set(context.sessionId, sessionGenerations);

      return result;
    } catch (error) {
      throw new Error(`Plan execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate code from a single choice/trigger
   */
  async generateFromChoice(
    choice: EnhancedChoice,
    context: BrainstormingContext
  ): Promise<CodeGeneration> {
    if (!choice.codeGenerationTrigger) {
      throw new Error('Choice does not have code generation trigger');
    }

    const request = this.buildGenerationRequest(choice, context);
    return this.generateCodeFromRequest(request);
  }

  /**
   * Generate code from a request (replaces CLI adapter functionality)
   */
  private async generateCodeFromRequest(request: CodeGenerationRequest): Promise<CodeGeneration> {
    const generationId = `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Find appropriate template
    const template = this.findBestTemplate(request.type, request.language, request.framework);

    // Generate code using template-based approach
    const code = this.generateCodeFromTemplate(template, request);

    // Create code generation result
    const generation: CodeGeneration = {
      id: generationId,
      request,
      code,
      explanation: `Generated ${request.type} code based on brainstorming ideas`,
      improvements: this.generateImprovementSuggestions(code, request),
      metadata: {
        generatedAt: new Date().toISOString(),
        language: request.language,
        framework: request.framework,
        estimatedComplexity: this.estimateComplexity(code),
        dependencies: this.extractDependencies(code, request.language)
      }
    };

    return generation;
  }

  /**
   * Find the best template for the given parameters
   */
  private findBestTemplate(type: string, language: string, framework?: string): CodeTemplate | null {
    return CODE_TEMPLATES.find(template =>
      template.type === type &&
      template.language === language &&
      (!framework || template.framework === framework)
    ) || null;
  }

  /**
   * Generate code from template
   */
  private generateCodeFromTemplate(template: CodeTemplate | null, request: CodeGenerationRequest): string {
    if (!template) {
      return this.generateBasicCode(request);
    }

    let code = template.template;

    // Replace template variables with actual values
    const variables = this.extractVariablesFromRequest(request);
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      code = code.replace(regex, value);
    }

    return code;
  }

  /**
   * Generate basic code when no template is available
   */
  private generateBasicCode(request: CodeGenerationRequest): string {
    const ideas = request.context.currentIdeas.map(idea => idea.content).join('\n// ');

    return `// Generated from brainstorming ideas:
// ${ideas}

// TODO: Implement ${request.type} for ${request.prompt}
// Language: ${request.language}
${request.framework ? `// Framework: ${request.framework}` : ''}

// Implementation placeholder
export const placeholder = {
  // Add your implementation here
};`;
  }

  /**
   * Extract variables from request for template substitution
   */
  private extractVariablesFromRequest(request: CodeGenerationRequest): Record<string, string> {
    const ideas = request.context.currentIdeas;
    const mainIdea = ideas[0]?.content || 'Component';

    return {
      componentName: this.toPascalCase(mainIdea.split(' ')[0] || 'Component'),
      propsInterface: `${this.toPascalCase(mainIdea.split(' ')[0] || 'Component')}Props`,
      description: request.prompt,
      ideas: ideas.map(idea => `// ${idea.content}`).join('\n  ')
    };
  }

  /**
   * Convert string to PascalCase
   */
  private toPascalCase(str: string): string {
    return str.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toUpperCase() : word.toUpperCase();
    }).replace(/\s+/g, '');
  }

  /**
   * Generate improvement suggestions
   */
  private generateImprovementSuggestions(code: string, request: CodeGenerationRequest): string[] {
    const suggestions: string[] = [];

    if (request.includeTests) {
      suggestions.push('Add comprehensive unit tests');
    }

    if (request.includeDocumentation) {
      suggestions.push('Add JSDoc documentation');
    }

    if (code.includes('TODO')) {
      suggestions.push('Complete TODO items');
    }

    suggestions.push('Consider error handling and edge cases');
    suggestions.push('Add proper TypeScript types');

    return suggestions;
  }

  /**
   * Estimate code complexity
   */
  private estimateComplexity(code: string): 'low' | 'medium' | 'high' {
    const lines = code.split('\n').length;
    const complexity = (code.match(/if|for|while|switch|catch/g) || []).length;

    if (lines < 50 && complexity < 5) return 'low';
    if (lines < 200 && complexity < 15) return 'medium';
    return 'high';
  }

  /**
   * Extract dependencies from code
   */
  private extractDependencies(code: string, language: string): string[] {
    const dependencies: string[] = [];

    if (language === 'typescript' || language === 'javascript') {
      const imports = code.match(/import.*from\s+['"]([^'"]+)['"]/g) || [];
      dependencies.push(...imports.map(imp => imp.match(/['"]([^'"]+)['"]$/)?.[1] || '').filter(Boolean));
    }

    return [...new Set(dependencies)];
  }

  /**
   * Analyze existing code and suggest improvements
   */
  async analyzeCode(
    code: string,
    language: string,
    context: BrainstormingContext
  ): Promise<CodeAnalysis> {
    // Simple analysis - in production, use more sophisticated tools
    const lines = code.split('\n').length;
    const complexity = this.calculateComplexity(code);
    const suggestions = await this.generateCodeSuggestions(code, language, context);

    return {
      complexity,
      maintainability: this.assessMaintainability(code, language),
      testability: this.assessTestability(code, language),
      reusability: this.assessReusability(code, context),
      suggestions
    };
  }

  /**
   * Get available code templates
   */
  getAvailableTemplates(
    type?: CodeGeneration['request']['type'],
    language?: string,
    framework?: string
  ): CodeTemplate[] {
    return Array.from(this.templates.values()).filter(template => {
      if (type && template.type !== type) return false;
      if (language && template.language !== language) return false;
      if (framework && template.framework !== framework) return false;
      return true;
    });
  }

  /**
   * Get generation history for a session
   */
  getGenerationHistory(sessionId: string): CodeGeneration[] {
    return this.generationHistory.get(sessionId) || [];
  }

  /**
   * Get plan history for a session
   */
  getPlanHistory(sessionId: string): CodeGenerationPlan[] {
    return this.planHistory.get(sessionId) || [];
  }

  private async analyzeIdeasForCodeGeneration(
    ideas: Idea[],
    context: BrainstormingContext
  ): Promise<any> {
    // Analyze ideas to determine what can be generated
    return {
      components: ideas.filter(idea => this.isComponentIdea(idea)),
      services: ideas.filter(idea => this.isServiceIdea(idea)),
      utilities: ideas.filter(idea => this.isUtilityIdea(idea)),
      tests: ideas.filter(idea => this.isTestIdea(idea)),
      patterns: this.extractPatterns(ideas),
      dependencies: this.identifyDependencies(ideas),
      architecture: this.analyzeArchitecture(ideas, context)
    };
  }

  private async createGenerationSteps(
    analysis: any,
    preferences?: any
  ): Promise<CodeGenerationStep[]> {
    const steps: CodeGenerationStep[] = [];

    // Create component steps
    analysis.components.forEach((idea: Idea, index: number) => {
      steps.push({
        id: `component_${index}`,
        name: `Generate ${this.extractComponentName(idea)} Component`,
        description: idea.content,
        type: 'component',
        dependencies: [],
        priority: 'high',
        estimatedTime: '15-30 minutes'
      });
    });

    // Create service steps
    analysis.services.forEach((idea: Idea, index: number) => {
      steps.push({
        id: `service_${index}`,
        name: `Generate ${this.extractServiceName(idea)} Service`,
        description: idea.content,
        type: 'api',
        dependencies: [],
        priority: 'high',
        estimatedTime: '20-40 minutes'
      });
    });

    // Add test steps if requested
    if (preferences?.includeTests) {
      steps.forEach(step => {
        if (step.type === 'component' || step.type === 'api') {
          steps.push({
            id: `test_${step.id}`,
            name: `Generate Tests for ${step.name}`,
            description: `Comprehensive tests for ${step.description}`,
            type: 'test',
            dependencies: [step.id],
            priority: 'medium',
            estimatedTime: '10-20 minutes'
          });
        }
      });
    }

    return steps;
  }

  private async executeGenerationStep(
    step: CodeGenerationStep,
    context: BrainstormingContext,
    plan: CodeGenerationPlan
  ): Promise<CodeGeneration> {
    const sourceIdeas = plan.sourceIdeas
      .map(ideaId => context.currentIdeas.find(idea => idea.id === ideaId))
      .filter(Boolean) as Idea[];

    const request: CodeGenerationRequest = {
      prompt: `Generate ${step.type} code: ${step.description}`,
      type: step.type,
      language: 'typescript', // Default, could be configurable
      context,
      requirements: [step.description],
      outputFormat: 'inline'
    };

    return this.generateCodeFromRequest(request);
  }

  private buildGenerationRequest(
    choice: EnhancedChoice,
    context: BrainstormingContext
  ): CodeGenerationRequest {
    const config = choice.codeGenerationTrigger!;
    
    return {
      prompt: choice.text,
      type: config.type,
      language: config.language || 'typescript',
      framework: config.framework,
      context,
      requirements: config.contextRequired,
      outputFormat: 'inline'
    };
  }

  private async generateSummary(generations: CodeGeneration[]): Promise<GenerationSummary> {
    const languages = [...new Set(generations.map(g => g.language))];
    const frameworks = [...new Set(generations.map(g => g.framework).filter(Boolean))] as string[];
    const totalLines = generations.reduce((sum, g) => 
      sum + (g.generatedCode.split('\n').length || 0), 0
    );

    return {
      totalFiles: generations.length,
      linesOfCode: totalLines,
      languages,
      frameworks,
      suggestions: this.generateOverallSuggestions(generations)
    };
  }

  private generateNextSteps(
    plan: CodeGenerationPlan,
    generations: CodeGeneration[]
  ): string[] {
    const nextSteps: string[] = [];

    if (generations.length < plan.steps.length) {
      nextSteps.push('Complete remaining generation steps');
    }

    nextSteps.push('Review and test generated code');
    nextSteps.push('Integrate with existing codebase');
    nextSteps.push('Add comprehensive documentation');
    nextSteps.push('Set up CI/CD pipeline');

    return nextSteps;
  }

  private async generateIntegrationGuide(
    generations: CodeGeneration[],
    context: BrainstormingContext
  ): Promise<string> {
    let guide = '# Integration Guide\n\n';
    guide += '## Generated Components\n\n';
    
    generations.forEach((gen, index) => {
      guide += `### ${index + 1}. ${gen.request.type} (${gen.language})\n`;
      if (gen.fileName) {
        guide += `**File:** ${gen.fileName}\n`;
      }
      if (gen.explanation) {
        guide += `**Description:** ${gen.explanation}\n`;
      }
      guide += '\n';
    });

    guide += '## Integration Steps\n\n';
    guide += '1. Review generated code for consistency\n';
    guide += '2. Install required dependencies\n';
    guide += '3. Configure build system\n';
    guide += '4. Run tests to ensure compatibility\n';
    guide += '5. Update documentation\n';

    return guide;
  }

  // Helper methods for idea analysis
  private isComponentIdea(idea: Idea): boolean {
    const componentKeywords = ['component', 'ui', 'interface', 'widget', 'element'];
    return componentKeywords.some(keyword => 
      idea.content.toLowerCase().includes(keyword)
    );
  }

  private isServiceIdea(idea: Idea): boolean {
    const serviceKeywords = ['api', 'service', 'endpoint', 'server', 'backend'];
    return serviceKeywords.some(keyword => 
      idea.content.toLowerCase().includes(keyword)
    );
  }

  private isUtilityIdea(idea: Idea): boolean {
    const utilityKeywords = ['helper', 'utility', 'function', 'tool', 'library'];
    return utilityKeywords.some(keyword => 
      idea.content.toLowerCase().includes(keyword)
    );
  }

  private isTestIdea(idea: Idea): boolean {
    const testKeywords = ['test', 'testing', 'validation', 'verification'];
    return testKeywords.some(keyword => 
      idea.content.toLowerCase().includes(keyword)
    );
  }

  private extractPatterns(ideas: Idea[]): string[] {
    // Extract common patterns from ideas
    return [];
  }

  private identifyDependencies(ideas: Idea[]): string[] {
    // Identify common dependencies from ideas
    return [];
  }

  private analyzeArchitecture(ideas: Idea[], context: BrainstormingContext): any {
    // Analyze architectural patterns
    return {};
  }

  private extractComponentName(idea: Idea): string {
    // Extract component name from idea content
    const words = idea.content.split(' ');
    return words.find(word => word.length > 3) || 'Component';
  }

  private extractServiceName(idea: Idea): string {
    // Extract service name from idea content
    const words = idea.content.split(' ');
    return words.find(word => word.length > 3) || 'Service';
  }

  private calculateEstimatedDuration(steps: CodeGenerationStep[]): string {
    const totalMinutes = steps.reduce((sum, step) => {
      const timeStr = step.estimatedTime;
      const match = timeStr.match(/(\d+)-(\d+)/);
      if (match) {
        return sum + parseInt(match[2]); // Use upper bound
      }
      return sum + 30; // Default
    }, 0);

    if (totalMinutes < 60) {
      return `${totalMinutes} minutes`;
    } else {
      const hours = Math.ceil(totalMinutes / 60);
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    }
  }

  private assessPlanComplexity(steps: CodeGenerationStep[], analysis: any): 'simple' | 'moderate' | 'complex' {
    if (steps.length <= 3) return 'simple';
    if (steps.length <= 8) return 'moderate';
    return 'complex';
  }

  private identifyPrerequisites(analysis: any, preferences?: any): string[] {
    const prerequisites: string[] = [];
    
    if (preferences?.language) {
      prerequisites.push(`${preferences.language} development environment`);
    }
    
    if (preferences?.framework) {
      prerequisites.push(`${preferences.framework} framework knowledge`);
    }

    return prerequisites;
  }

  private identifyDeliverables(steps: CodeGenerationStep[]): string[] {
    return steps.map(step => `${step.type} implementation: ${step.name}`);
  }

  private groupStepsByDependencies(steps: CodeGenerationStep[]): CodeGenerationStep[][] {
    // Group steps that can be executed in parallel
    const groups: CodeGenerationStep[][] = [];
    const remaining = [...steps];
    
    while (remaining.length > 0) {
      const currentGroup = remaining.filter(step => 
        step.dependencies.every(dep => 
          !remaining.some(s => s.id === dep)
        )
      );
      
      if (currentGroup.length === 0) {
        // Avoid infinite loop - add remaining steps
        groups.push(remaining);
        break;
      }
      
      groups.push(currentGroup);
      currentGroup.forEach(step => {
        const index = remaining.indexOf(step);
        if (index > -1) remaining.splice(index, 1);
      });
    }
    
    return groups;
  }

  private calculateComplexity(code: string): number {
    // Simple complexity calculation
    const cyclomaticKeywords = ['if', 'else', 'for', 'while', 'switch', 'case', 'catch'];
    let complexity = 1; // Base complexity
    
    cyclomaticKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = code.match(regex);
      if (matches) {
        complexity += matches.length;
      }
    });
    
    return complexity;
  }

  private assessMaintainability(code: string, language: string): number {
    // Simple maintainability assessment (0-100)
    let score = 100;
    
    const lines = code.split('\n').length;
    if (lines > 200) score -= 20;
    if (lines > 500) score -= 30;
    
    return Math.max(score, 0);
  }

  private assessTestability(code: string, language: string): number {
    // Simple testability assessment (0-100)
    let score = 100;
    
    // Look for testable patterns
    if (!code.includes('export')) score -= 30;
    if (code.includes('console.log')) score -= 10;
    
    return Math.max(score, 0);
  }

  private assessReusability(code: string, context: BrainstormingContext): number {
    // Simple reusability assessment (0-100)
    let score = 100;
    
    // Generic patterns increase reusability
    if (code.includes('interface') || code.includes('type')) score += 10;
    if (code.includes('generic') || code.includes('<T>')) score += 15;
    
    return Math.min(score, 100);
  }

  private async generateCodeSuggestions(
    code: string,
    language: string,
    context: BrainstormingContext
  ): Promise<CodeSuggestion[]> {
    const suggestions: CodeSuggestion[] = [];
    
    // Simple suggestion generation
    if (code.includes('any')) {
      suggestions.push({
        type: 'best-practice',
        message: 'Consider using specific types instead of "any"',
        severity: 'medium'
      });
    }
    
    if (!code.includes('test') && !code.includes('spec')) {
      suggestions.push({
        type: 'improvement',
        message: 'Add unit tests for better code coverage',
        severity: 'high'
      });
    }
    
    return suggestions;
  }

  private generateOverallSuggestions(generations: CodeGeneration[]): string[] {
    const suggestions: string[] = [];
    
    suggestions.push('Review all generated code for consistency');
    suggestions.push('Add comprehensive error handling');
    suggestions.push('Implement proper logging');
    suggestions.push('Add performance monitoring');
    
    return suggestions;
  }
}

// Export singleton instance
export const codeGenerationEngine = new CodeGenerationEngine();