/**
 * Dedicated Brainstorming Session Manager
 * 
 * Completely independent session management for brainstorming workflows.
 * This manager is entirely separate from Claude Code sessions and provides
 * its own lifecycle, persistence, and state management.
 */

import { BrainstormSession, Idea, ChatMessage, BrainstormTemplate, TemplateType } from '@/types/brainstorm';
import { brainstormApi } from '@/lib/brainstorm-api';

export interface BrainstormingSessionConfig {
  title: string;
  description?: string;
  template?: TemplateType;
  goals: string[];
  tags: string[];
  mode: 'exploration' | 'structured' | 'collaborative';
  maxParticipants?: number;
}

export interface SessionMetrics {
  ideasGenerated: number;
  messagesExchanged: number;
  timeSpent: number; // in minutes
  participantCount: number;
  templatesUsed: string[];
  lastActivity: string;
}

export interface SessionExportData {
  session: BrainstormSession;
  ideas: Idea[];
  messages: ChatMessage[];
  metrics: SessionMetrics;
  exportedAt: string;
  version: string;
}

export interface SessionSearchOptions {
  query?: string;
  tags?: string[];
  template?: TemplateType;
  dateRange?: {
    start: string;
    end: string;
  };
  sortBy?: 'created' | 'updated' | 'ideas' | 'activity';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
}

/**
 * Dedicated session manager for brainstorming workflows
 */
export class DedicatedBrainstormingSessionManager {
  private sessions: Map<string, BrainstormSession> = new Map();
  private sessionMetrics: Map<string, SessionMetrics> = new Map();
  private activeSessionId: string | null = null;
  private eventListeners: Map<string, Function[]> = new Map();

  constructor() {
    this.loadSessionsFromStorage();
    this.setupAutoSave();
  }

  /**
   * Create a new brainstorming session
   */
  async createSession(config: BrainstormingSessionConfig): Promise<string> {
    const sessionId = `brainstorm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const session: BrainstormSession = {
      id: sessionId,
      title: config.title,
      description: config.description || '',
      template: config.template,
      goals: config.goals,
      tags: config.tags,
      mode: config.mode,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true,
      participants: [],
      settings: {
        maxParticipants: config.maxParticipants || 10,
        allowAnonymous: false,
        autoSave: true,
        collaborationEnabled: config.mode === 'collaborative'
      }
    };

    // Initialize session metrics
    const metrics: SessionMetrics = {
      ideasGenerated: 0,
      messagesExchanged: 0,
      timeSpent: 0,
      participantCount: 1,
      templatesUsed: config.template ? [config.template] : [],
      lastActivity: new Date().toISOString()
    };

    this.sessions.set(sessionId, session);
    this.sessionMetrics.set(sessionId, metrics);
    this.activeSessionId = sessionId;

    await this.persistSession(sessionId);
    this.emit('sessionCreated', { sessionId, session });

    return sessionId;
  }

  /**
   * Switch to a different session
   */
  async switchSession(sessionId: string): Promise<void> {
    if (!this.sessions.has(sessionId)) {
      throw new Error(`Session ${sessionId} not found`);
    }

    // Save current session state if there was an active one
    if (this.activeSessionId) {
      await this.saveSessionState(this.activeSessionId);
    }

    this.activeSessionId = sessionId;
    this.updateSessionActivity(sessionId);
    this.emit('sessionSwitched', { sessionId });
  }

  /**
   * Get the current active session
   */
  getCurrentSession(): BrainstormSession | null {
    if (!this.activeSessionId) return null;
    return this.sessions.get(this.activeSessionId) || null;
  }

  /**
   * Get all sessions with optional filtering
   */
  getAllSessions(options?: SessionSearchOptions): BrainstormSession[] {
    let sessions = Array.from(this.sessions.values());

    // Apply filters
    if (options?.query) {
      const query = options.query.toLowerCase();
      sessions = sessions.filter(session => 
        session.title.toLowerCase().includes(query) ||
        session.description.toLowerCase().includes(query) ||
        session.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    if (options?.tags?.length) {
      sessions = sessions.filter(session =>
        options.tags!.some(tag => session.tags.includes(tag))
      );
    }

    if (options?.template) {
      sessions = sessions.filter(session => session.template === options.template);
    }

    if (options?.dateRange) {
      sessions = sessions.filter(session => {
        const created = new Date(session.createdAt);
        const start = new Date(options.dateRange!.start);
        const end = new Date(options.dateRange!.end);
        return created >= start && created <= end;
      });
    }

    // Apply sorting
    if (options?.sortBy) {
      sessions.sort((a, b) => {
        let aValue: any, bValue: any;
        
        switch (options.sortBy) {
          case 'created':
            aValue = new Date(a.createdAt);
            bValue = new Date(b.createdAt);
            break;
          case 'updated':
            aValue = new Date(a.updatedAt);
            bValue = new Date(b.updatedAt);
            break;
          case 'ideas':
            aValue = this.sessionMetrics.get(a.id)?.ideasGenerated || 0;
            bValue = this.sessionMetrics.get(b.id)?.ideasGenerated || 0;
            break;
          case 'activity':
            aValue = new Date(this.sessionMetrics.get(a.id)?.lastActivity || a.updatedAt);
            bValue = new Date(this.sessionMetrics.get(b.id)?.lastActivity || b.updatedAt);
            break;
          default:
            aValue = a.title;
            bValue = b.title;
        }

        if (options.sortOrder === 'desc') {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        } else {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        }
      });
    }

    // Apply limit
    if (options?.limit) {
      sessions = sessions.slice(0, options.limit);
    }

    return sessions;
  }

  /**
   * Update session metadata
   */
  async updateSession(sessionId: string, updates: Partial<BrainstormSession>): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    const updatedSession = {
      ...session,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    this.sessions.set(sessionId, updatedSession);
    await this.persistSession(sessionId);
    this.emit('sessionUpdated', { sessionId, session: updatedSession });
  }

  /**
   * Delete a session
   */
  async deleteSession(sessionId: string): Promise<void> {
    if (!this.sessions.has(sessionId)) {
      throw new Error(`Session ${sessionId} not found`);
    }

    this.sessions.delete(sessionId);
    this.sessionMetrics.delete(sessionId);

    if (this.activeSessionId === sessionId) {
      this.activeSessionId = null;
    }

    await this.removeSessionFromStorage(sessionId);
    this.emit('sessionDeleted', { sessionId });
  }

  /**
   * Get session metrics
   */
  getSessionMetrics(sessionId: string): SessionMetrics | null {
    return this.sessionMetrics.get(sessionId) || null;
  }

  /**
   * Update session metrics
   */
  updateSessionMetrics(sessionId: string, updates: Partial<SessionMetrics>): void {
    const current = this.sessionMetrics.get(sessionId);
    if (!current) return;

    const updated = {
      ...current,
      ...updates,
      lastActivity: new Date().toISOString()
    };

    this.sessionMetrics.set(sessionId, updated);
    this.updateSessionActivity(sessionId);
  }

  /**
   * Export session data
   */
  async exportSession(sessionId: string): Promise<SessionExportData> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    const metrics = this.sessionMetrics.get(sessionId) || {
      ideasGenerated: 0,
      messagesExchanged: 0,
      timeSpent: 0,
      participantCount: 0,
      templatesUsed: [],
      lastActivity: new Date().toISOString()
    };

    // Get ideas and messages from the brainstorm store
    // This would typically come from the useBrainstormStore
    const ideas: Idea[] = []; // TODO: Get from store
    const messages: ChatMessage[] = []; // TODO: Get from store

    return {
      session,
      ideas,
      messages,
      metrics,
      exportedAt: new Date().toISOString(),
      version: '1.0.0'
    };
  }

  /**
   * Import session data
   */
  async importSession(data: SessionExportData): Promise<string> {
    const sessionId = `imported_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const session: BrainstormSession = {
      ...data.session,
      id: sessionId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.sessions.set(sessionId, session);
    this.sessionMetrics.set(sessionId, data.metrics);

    await this.persistSession(sessionId);
    this.emit('sessionImported', { sessionId, session });

    return sessionId;
  }

  /**
   * Save current session state
   */
  async saveSessionState(sessionId: string): Promise<void> {
    await this.persistSession(sessionId);
    this.emit('sessionSaved', { sessionId });
  }

  // Private helper methods
  private async persistSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    const metrics = this.sessionMetrics.get(sessionId);
    
    if (session) {
      localStorage.setItem(`brainstorm_session_${sessionId}`, JSON.stringify({
        session,
        metrics
      }));
    }
  }

  private async removeSessionFromStorage(sessionId: string): Promise<void> {
    localStorage.removeItem(`brainstorm_session_${sessionId}`);
  }

  private loadSessionsFromStorage(): void {
    const keys = Object.keys(localStorage).filter(key => key.startsWith('brainstorm_session_'));
    
    for (const key of keys) {
      try {
        const data = JSON.parse(localStorage.getItem(key) || '{}');
        if (data.session && data.metrics) {
          this.sessions.set(data.session.id, data.session);
          this.sessionMetrics.set(data.session.id, data.metrics);
        }
      } catch (error) {
        console.warn(`Failed to load session from ${key}:`, error);
      }
    }
  }

  private setupAutoSave(): void {
    // Auto-save every 30 seconds
    setInterval(() => {
      if (this.activeSessionId) {
        this.saveSessionState(this.activeSessionId);
      }
    }, 30000);
  }

  private updateSessionActivity(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.updatedAt = new Date().toISOString();
      this.sessions.set(sessionId, session);
    }
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event) || [];
    listeners.forEach(listener => listener(data));
  }

  /**
   * Subscribe to session events
   */
  on(event: string, listener: Function): () => void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    
    this.eventListeners.get(event)!.push(listener);
    
    // Return unsubscribe function
    return () => {
      const listeners = this.eventListeners.get(event) || [];
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    };
  }
}

// Export singleton instance
export const dedicatedBrainstormingSessionManager = new DedicatedBrainstormingSessionManager();
