/**
 * Enhanced Choice Engine for Dynamic Brainstorming Flows
 * 
 * This engine generates contextual choices based on session state, previous responses,
 * and predefined flow templates. It supports progressive disclosure and adaptive experiences.
 */

import { 
  BrainstormSession, 
  ChatMessage, 
  Idea, 
  BrainstormTemplate,
  IdeaStatus,
  Priority 
} from '@/types/brainstorm';
import { brainstormTemplates } from '@/lib/brainstorm-templates';

// Enhanced choice interface extending the basic BrainstormingChoice
export interface EnhancedChoice {
  id: string;
  text: string;
  description?: string;
  category: 'topic' | 'template' | 'follow-up' | 'code-action' | 'analysis' | 'export';
  
  // Dynamic properties
  contextualRelevance: number; // 0-1 score based on session context
  followUpSuggestions: FollowUpSuggestion[];
  codeGenerationTrigger?: CodeGenerationConfig;
  
  // Flow control
  nextFlow?: string;
  requiredContext?: string[];
  conditionalDisplay?: ChoiceCondition;
  
  // UI properties
  icon?: string;
  color?: string;
  badge?: string;
  priority: 'low' | 'medium' | 'high';
  
  // Metadata
  estimatedDuration?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  prerequisites?: string[];
}

export interface FollowUpSuggestion {
  text: string;
  type: 'clarification' | 'expansion' | 'alternative' | 'code-request' | 'template-apply';
  priority: number;
  autoGenerated: boolean;
  triggerKeywords?: string[];
}

export interface CodeGenerationConfig {
  type: 'component' | 'function' | 'class' | 'api' | 'test' | 'config';
  language?: string;
  framework?: string;
  contextRequired: string[];
  templatePrompt: string;
}

export interface ChoiceCondition {
  minIdeas?: number;
  maxIdeas?: number;
  requiresTags?: string[];
  excludesTags?: string[];
  sessionAge?: number; // minutes
  messageCount?: number;
  hasCodeGenerated?: boolean;
}

export interface ChoiceContext {
  session: BrainstormSession;
  recentMessages: ChatMessage[];
  currentIdeas: Idea[];
  userInput?: string;
  selectedTemplate?: BrainstormTemplate;
  previousChoices: string[];
}

// Base choice templates for different scenarios
const BASE_CHOICES: Omit<EnhancedChoice, 'id' | 'contextualRelevance'>[] = [
  {
    text: "Explore core features and functionality",
    description: "Deep dive into the essential features your idea needs",
    category: "topic",
    followUpSuggestions: [
      {
        text: "What's the minimum viable feature set?",
        type: "clarification",
        priority: 1,
        autoGenerated: false
      },
      {
        text: "How do these features connect to user needs?",
        type: "expansion",
        priority: 2,
        autoGenerated: false
      }
    ],
    icon: "🔧",
    color: "#3b82f6",
    priority: "high"
  },
  {
    text: "Analyze technical implementation approaches",
    description: "Examine different ways to build and architect your solution",
    category: "analysis",
    followUpSuggestions: [
      {
        text: "What are the scalability considerations?",
        type: "expansion",
        priority: 1,
        autoGenerated: false
      },
      {
        text: "Which technology stack would work best?",
        type: "clarification",
        priority: 2,
        autoGenerated: false
      }
    ],
    codeGenerationTrigger: {
      type: "component",
      contextRequired: ["architecture", "requirements"],
      templatePrompt: "Generate a technical implementation plan for: "
    },
    icon: "⚙️",
    color: "#8b5cf6",
    priority: "high",
    conditionalDisplay: {
      minIdeas: 2,
      requiresTags: ["technical", "implementation", "architecture"]
    }
  },
  {
    text: "Create user personas and journey maps",
    description: "Define who will use your solution and how",
    category: "analysis",
    followUpSuggestions: [
      {
        text: "What are the primary user pain points?",
        type: "clarification",
        priority: 1,
        autoGenerated: false
      },
      {
        text: "How do different user types interact with the solution?",
        type: "expansion",
        priority: 2,
        autoGenerated: false
      }
    ],
    icon: "👥",
    color: "#10b981",
    priority: "medium"
  },
  {
    text: "Generate code scaffolding",
    description: "Create initial code structure based on your ideas",
    category: "code-action",
    followUpSuggestions: [
      {
        text: "What programming language should we use?",
        type: "clarification",
        priority: 1,
        autoGenerated: false
      },
      {
        text: "Should we include tests and documentation?",
        type: "expansion",
        priority: 2,
        autoGenerated: false
      }
    ],
    codeGenerationTrigger: {
      type: "component",
      contextRequired: ["ideas", "requirements"],
      templatePrompt: "Generate code scaffolding for: "
    },
    icon: "💻",
    color: "#f59e0b",
    priority: "medium",
    conditionalDisplay: {
      minIdeas: 3,
      messageCount: 5
    }
  },
  {
    text: "Explore alternative approaches",
    description: "Consider different ways to solve the same problem",
    category: "topic",
    followUpSuggestions: [
      {
        text: "What if we approached this differently?",
        type: "alternative",
        priority: 1,
        autoGenerated: false
      },
      {
        text: "Are there simpler solutions?",
        type: "clarification",
        priority: 2,
        autoGenerated: false
      }
    ],
    icon: "🔀",
    color: "#ef4444",
    priority: "medium",
    conditionalDisplay: {
      minIdeas: 4
    }
  },
  {
    text: "Create project roadmap and timeline",
    description: "Plan the implementation phases and milestones",
    category: "analysis",
    followUpSuggestions: [
      {
        text: "What are the key milestones?",
        type: "clarification",
        priority: 1,
        autoGenerated: false
      },
      {
        text: "How should we prioritize features?",
        type: "expansion",
        priority: 2,
        autoGenerated: false
      }
    ],
    icon: "🗺️",
    color: "#06b6d4",
    priority: "medium",
    conditionalDisplay: {
      minIdeas: 5,
      sessionAge: 15
    }
  }
];

// Template-based choices
const TEMPLATE_CHOICES: Omit<EnhancedChoice, 'id' | 'contextualRelevance'>[] = [
  {
    text: "Apply SWOT Analysis",
    description: "Analyze Strengths, Weaknesses, Opportunities, and Threats",
    category: "template",
    followUpSuggestions: [
      {
        text: "Focus on identifying unique strengths",
        type: "expansion",
        priority: 1,
        autoGenerated: true,
        triggerKeywords: ["strength", "advantage", "unique"]
      }
    ],
    icon: "📊",
    color: "#8b5cf6",
    priority: "high",
    nextFlow: "swot-analysis"
  },
  {
    text: "Run Design Thinking Workshop",
    description: "Follow structured design thinking methodology",
    category: "template",
    followUpSuggestions: [
      {
        text: "Start with empathy mapping",
        type: "template-apply",
        priority: 1,
        autoGenerated: true
      }
    ],
    icon: "🎨",
    color: "#f59e0b",
    priority: "high",
    nextFlow: "design-thinking"
  }
];

export class EnhancedChoiceEngine {
  private choiceHistory: Map<string, string[]> = new Map();
  private contextAnalyzer: ContextAnalyzer;

  constructor() {
    this.contextAnalyzer = new ContextAnalyzer();
  }

  /**
   * Generate contextual choices based on current session state
   */
  async generateChoices(context: ChoiceContext): Promise<EnhancedChoice[]> {
    const baseChoices = this.getFilteredBaseChoices(context);
    const templateChoices = this.getFilteredTemplateChoices(context);
    const dynamicChoices = await this.generateDynamicChoices(context);
    
    const allChoices = [...baseChoices, ...templateChoices, ...dynamicChoices];
    
    // Calculate contextual relevance and sort
    const scoredChoices = allChoices.map(choice => ({
      ...choice,
      id: this.generateChoiceId(choice),
      contextualRelevance: this.calculateRelevance(choice, context)
    }));

    // Sort by relevance and priority
    return scoredChoices
      .sort((a, b) => {
        if (a.priority !== b.priority) {
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        }
        return b.contextualRelevance - a.contextualRelevance;
      })
      .slice(0, 8); // Limit to top 8 choices
  }

  /**
   * Generate follow-up choices based on a selected choice
   */
  async generateFollowUps(
    selectedChoice: EnhancedChoice,
    context: ChoiceContext
  ): Promise<EnhancedChoice[]> {
    const followUps: EnhancedChoice[] = [];
    
    // Add predefined follow-ups
    selectedChoice.followUpSuggestions.forEach((suggestion, index) => {
      followUps.push({
        id: `followup_${selectedChoice.id}_${index}`,
        text: suggestion.text,
        description: `Follow-up to: ${selectedChoice.text}`,
        category: 'follow-up',
        contextualRelevance: 0.8,
        followUpSuggestions: [],
        icon: this.getFollowUpIcon(suggestion.type),
        color: "#64748b",
        priority: suggestion.priority === 1 ? 'high' : 'medium'
      });
    });

    // Generate dynamic follow-ups based on choice category
    if (selectedChoice.category === 'code-action') {
      followUps.push(...this.generateCodeFollowUps(selectedChoice, context));
    } else if (selectedChoice.category === 'analysis') {
      followUps.push(...this.generateAnalysisFollowUps(selectedChoice, context));
    }

    return followUps.slice(0, 5);
  }

  /**
   * Process choice selection and update session context
   */
  async processChoiceSelection(
    choice: EnhancedChoice,
    context: ChoiceContext
  ): Promise<{
    promptSuggestion?: string;
    followUpChoices: EnhancedChoice[];
    triggerCodeGeneration?: CodeGenerationConfig;
    nextFlow?: string;
  }> {
    // Track choice in history
    const sessionHistory = this.choiceHistory.get(context.session.id) || [];
    sessionHistory.push(choice.id);
    this.choiceHistory.set(context.session.id, sessionHistory);

    // Generate prompt suggestion
    const promptSuggestion = this.generatePromptSuggestion(choice, context);
    
    // Generate follow-ups
    const followUpChoices = await this.generateFollowUps(choice, context);

    return {
      promptSuggestion,
      followUpChoices,
      triggerCodeGeneration: choice.codeGenerationTrigger,
      nextFlow: choice.nextFlow
    };
  }

  private getFilteredBaseChoices(context: ChoiceContext): Omit<EnhancedChoice, 'id' | 'contextualRelevance'>[] {
    return BASE_CHOICES.filter(choice => 
      this.meetsConditions(choice.conditionalDisplay, context)
    );
  }

  private getFilteredTemplateChoices(context: ChoiceContext): Omit<EnhancedChoice, 'id' | 'contextualRelevance'>[] {
    return TEMPLATE_CHOICES.filter(choice =>
      this.meetsConditions(choice.conditionalDisplay, context)
    );
  }

  private async generateDynamicChoices(context: ChoiceContext): Promise<Omit<EnhancedChoice, 'id' | 'contextualRelevance'>[]> {
    const dynamicChoices: Omit<EnhancedChoice, 'id' | 'contextualRelevance'>[] = [];

    // Generate choices based on recent message content
    const keywords = this.contextAnalyzer.extractKeywords(context.recentMessages);
    
    // Generate idea-specific choices
    if (context.currentIdeas.length > 0) {
      dynamicChoices.push(...this.generateIdeaBasedChoices(context.currentIdeas));
    }

    // Generate keyword-based choices
    if (keywords.length > 0) {
      dynamicChoices.push(...this.generateKeywordBasedChoices(keywords));
    }

    return dynamicChoices;
  }

  private generateIdeaBasedChoices(ideas: Idea[]): Omit<EnhancedChoice, 'id' | 'contextualRelevance'>[] {
    const choices: Omit<EnhancedChoice, 'id' | 'contextualRelevance'>[] = [];
    
    // Prioritization choice if many ideas
    if (ideas.length >= 5) {
      choices.push({
        text: "Prioritize and organize existing ideas",
        description: `Organize your ${ideas.length} ideas by impact and effort`,
        category: "analysis",
        followUpSuggestions: [
          {
            text: "Create an impact vs effort matrix",
            type: "template-apply",
            priority: 1,
            autoGenerated: true
          }
        ],
        icon: "📋",
        color: "#06b6d4",
        priority: "high"
      });
    }

    // Implementation choice if validated ideas exist
    const validatedIdeas = ideas.filter(idea => idea.status === IdeaStatus.VALIDATED);
    if (validatedIdeas.length >= 2) {
      choices.push({
        text: "Create implementation plan",
        description: "Plan how to implement your validated ideas",
        category: "code-action",
        followUpSuggestions: [
          {
            text: "Break down into development tasks",
            type: "expansion",
            priority: 1,
            autoGenerated: true
          }
        ],
        codeGenerationTrigger: {
          type: "component",
          contextRequired: ["validated-ideas"],
          templatePrompt: "Create implementation plan for validated ideas: "
        },
        icon: "🚀",
        color: "#10b981",
        priority: "high"
      });
    }

    return choices;
  }

  private generateKeywordBasedChoices(keywords: string[]): Omit<EnhancedChoice, 'id' | 'contextualRelevance'>[] {
    const choices: Omit<EnhancedChoice, 'id' | 'contextualRelevance'>[] = [];
    
    // Technical keywords
    const techKeywords = ['api', 'database', 'architecture', 'scalability', 'performance'];
    if (keywords.some(k => techKeywords.includes(k.toLowerCase()))) {
      choices.push({
        text: "Deep dive into technical architecture",
        description: "Explore technical implementation details",
        category: "analysis",
        followUpSuggestions: [
          {
            text: "Design system architecture diagram",
            type: "code-request",
            priority: 1,
            autoGenerated: true
          }
        ],
        icon: "🏗️",
        color: "#8b5cf6",
        priority: "high"
      });
    }

    // User experience keywords
    const uxKeywords = ['user', 'interface', 'experience', 'usability', 'design'];
    if (keywords.some(k => uxKeywords.includes(k.toLowerCase()))) {
      choices.push({
        text: "Focus on user experience design",
        description: "Explore UX/UI considerations and user flows",
        category: "analysis",
        followUpSuggestions: [
          {
            text: "Create user journey maps",
            type: "template-apply",
            priority: 1,
            autoGenerated: true
          }
        ],
        icon: "👤",
        color: "#f59e0b",
        priority: "high"
      });
    }

    return choices;
  }

  private meetsConditions(condition: ChoiceCondition | undefined, context: ChoiceContext): boolean {
    if (!condition) return true;

    const { session, currentIdeas, recentMessages } = context;

    if (condition.minIdeas && currentIdeas.length < condition.minIdeas) return false;
    if (condition.maxIdeas && currentIdeas.length > condition.maxIdeas) return false;
    if (condition.messageCount && recentMessages.length < condition.messageCount) return false;

    if (condition.requiresTags && condition.requiresTags.length > 0) {
      const sessionTags = new Set(currentIdeas.flatMap(idea => idea.tags));
      if (!condition.requiresTags.some(tag => sessionTags.has(tag))) return false;
    }

    if (condition.excludesTags && condition.excludesTags.length > 0) {
      const sessionTags = new Set(currentIdeas.flatMap(idea => idea.tags));
      if (condition.excludesTags.some(tag => sessionTags.has(tag))) return false;
    }

    if (condition.sessionAge) {
      const sessionAgeMinutes = (Date.now() - new Date(session.createdAt).getTime()) / (1000 * 60);
      if (sessionAgeMinutes < condition.sessionAge) return false;
    }

    return true;
  }

  private calculateRelevance(choice: Omit<EnhancedChoice, 'id' | 'contextualRelevance'>, context: ChoiceContext): number {
    let relevance = 0.5; // Base relevance

    // Boost based on current ideas
    if (choice.category === 'analysis' && context.currentIdeas.length > 3) {
      relevance += 0.2;
    }

    if (choice.category === 'code-action' && context.currentIdeas.length > 5) {
      relevance += 0.3;
    }

    // Boost based on recent message content
    const recentContent = context.recentMessages
      .slice(-3)
      .map(m => m.content.toLowerCase())
      .join(' ');

    if (choice.description && recentContent.includes(choice.description.toLowerCase().split(' ')[0])) {
      relevance += 0.2;
    }

    // Boost based on priority
    const priorityBoost = { high: 0.1, medium: 0.05, low: 0 };
    relevance += priorityBoost[choice.priority];

    return Math.min(relevance, 1.0);
  }

  private generateChoiceId(choice: Omit<EnhancedChoice, 'id' | 'contextualRelevance'>): string {
    return `choice_${choice.category}_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  private generatePromptSuggestion(choice: EnhancedChoice, context: ChoiceContext): string {
    const basePrompts = {
      'topic': `Let's explore ${choice.text.toLowerCase()}`,
      'template': `Apply ${choice.text} to our current discussion`,
      'follow-up': choice.text,
      'code-action': `${choice.text} based on our ideas so far`,
      'analysis': `${choice.text} for our current concept`,
      'export': choice.text
    };

    return basePrompts[choice.category] || choice.text;
  }

  private generateCodeFollowUps(selectedChoice: EnhancedChoice, context: ChoiceContext): EnhancedChoice[] {
    return [
      {
        id: `code_followup_${Date.now()}_1`,
        text: "Add comprehensive tests",
        description: "Generate unit and integration tests",
        category: 'code-action',
        contextualRelevance: 0.7,
        followUpSuggestions: [],
        codeGenerationTrigger: {
          type: 'test',
          contextRequired: ['generated-code'],
          templatePrompt: 'Generate comprehensive tests for: '
        },
        icon: "🧪",
        color: "#10b981",
        priority: "medium"
      },
      {
        id: `code_followup_${Date.now()}_2`,
        text: "Generate documentation",
        description: "Create API docs and usage examples",
        category: 'code-action',
        contextualRelevance: 0.6,
        followUpSuggestions: [],
        icon: "📚",
        color: "#06b6d4",
        priority: "medium"
      }
    ];
  }

  private generateAnalysisFollowUps(selectedChoice: EnhancedChoice, context: ChoiceContext): EnhancedChoice[] {
    return [
      {
        id: `analysis_followup_${Date.now()}_1`,
        text: "Create visual diagrams",
        description: "Generate charts and visual representations",
        category: 'analysis',
        contextualRelevance: 0.8,
        followUpSuggestions: [],
        icon: "📊",
        color: "#8b5cf6",
        priority: "medium"
      },
      {
        id: `analysis_followup_${Date.now()}_2`,
        text: "Identify next steps",
        description: "Define actionable next steps",
        category: 'analysis',
        contextualRelevance: 0.7,
        followUpSuggestions: [],
        icon: "➡️",
        color: "#f59e0b",
        priority: "medium"
      }
    ];
  }

  private getFollowUpIcon(type: FollowUpSuggestion['type']): string {
    const icons = {
      'clarification': "❓",
      'expansion': "🔍",
      'alternative': "🔀",
      'code-request': "💻",
      'template-apply': "📋"
    };
    return icons[type] || "💡";
  }
}

/**
 * Context Analyzer for extracting insights from session state
 */
class ContextAnalyzer {
  extractKeywords(messages: ChatMessage[]): string[] {
    const text = messages
      .slice(-5) // Last 5 messages
      .map(m => m.content)
      .join(' ')
      .toLowerCase();

    // Simple keyword extraction (in production, use more sophisticated NLP)
    const keywords = text
      .split(/\s+/)
      .filter(word => word.length > 3)
      .filter(word => !this.isStopWord(word))
      .reduce((acc, word) => {
        acc[word] = (acc[word] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    return Object.entries(keywords)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
      'by', 'from', 'as', 'is', 'was', 'are', 'were', 'be', 'been', 'being',
      'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'should',
      'could', 'can', 'may', 'might', 'must', 'shall', 'this', 'that', 'these',
      'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her',
      'us', 'them', 'my', 'your', 'his', 'its', 'our', 'their'
    ]);
    return stopWords.has(word);
  }
}

// Export singleton instance
export const enhancedChoiceEngine = new EnhancedChoiceEngine();