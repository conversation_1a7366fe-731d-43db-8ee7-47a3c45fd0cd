/**
 * Session Restoration Service
 * 
 * Handles proper restoration and continuity of brainstorming sessions
 */

import { useBrainstormStore } from '@/stores/brainstormStore';
import { usePersonaStore } from '@/stores/personaStore';
import { BrainstormSession, ChatMessage } from '@/types/brainstorm';
import { BrainstormingPersona } from '@/types/persona';

export interface SessionState {
  sessionId: string;
  messages: ChatMessage[];
  currentPersonaId?: string;
  isProcessing: boolean;
  lastActivity: string;
  scrollPosition: number;
  inputValue: string;
  selectedModel: string;
  showChoices: boolean;
  activeView: string;
  sidebarState: {
    isOpen: boolean;
    activePanel: string;
  };
}

export interface RestorationContext {
  preserveInput: boolean;
  preserveScrollPosition: boolean;
  preservePersona: boolean;
  preserveUIState: boolean;
  autoResume: boolean;
}

class SessionRestorationService {
  private sessionStates = new Map<string, SessionState>();
  private activeSessionId: string | null = null;
  private restorationCallbacks = new Map<string, () => void>();

  /**
   * Save current session state before switching
   */
  saveSessionState(
    sessionId: string,
    state: Partial<SessionState>
  ): void {
    const existingState = this.sessionStates.get(sessionId);
    const newState: SessionState = {
      sessionId,
      messages: [],
      isProcessing: false,
      lastActivity: new Date().toISOString(),
      scrollPosition: 0,
      inputValue: '',
      selectedModel: 'sonnet',
      showChoices: true,
      activeView: 'chat',
      sidebarState: {
        isOpen: true,
        activePanel: 'personas'
      },
      ...existingState,
      ...state
    };

    this.sessionStates.set(sessionId, newState);
    this.persistSessionState(sessionId, newState);
  }

  /**
   * Restore session state when switching back
   */
  restoreSessionState(
    sessionId: string,
    context: RestorationContext = {
      preserveInput: true,
      preserveScrollPosition: true,
      preservePersona: true,
      preserveUIState: true,
      autoResume: false
    }
  ): SessionState | null {
    const savedState = this.sessionStates.get(sessionId) || this.loadSessionState(sessionId);
    
    if (!savedState) {
      return null;
    }

    // Update active session
    this.activeSessionId = sessionId;

    // Restore persona if needed
    if (context.preservePersona && savedState.currentPersonaId) {
      const personaStore = usePersonaStore.getState();
      personaStore.setActivePersona(savedState.currentPersonaId);
    }

    // Restore brainstorm store session
    const brainstormStore = useBrainstormStore.getState();
    brainstormStore.setCurrentSession(sessionId);

    // Trigger restoration callbacks
    const callback = this.restorationCallbacks.get(sessionId);
    if (callback) {
      callback();
    }

    return savedState;
  }

  /**
   * Create a new session with initial state
   */
  createSessionState(sessionId: string, initialState?: Partial<SessionState>): SessionState {
    const state: SessionState = {
      sessionId,
      messages: [],
      isProcessing: false,
      lastActivity: new Date().toISOString(),
      scrollPosition: 0,
      inputValue: '',
      selectedModel: 'sonnet',
      showChoices: true,
      activeView: 'chat',
      sidebarState: {
        isOpen: true,
        activePanel: 'personas'
      },
      ...initialState
    };

    this.sessionStates.set(sessionId, state);
    this.activeSessionId = sessionId;
    
    return state;
  }

  /**
   * Update session activity timestamp
   */
  updateSessionActivity(sessionId: string): void {
    const state = this.sessionStates.get(sessionId);
    if (state) {
      state.lastActivity = new Date().toISOString();
      this.sessionStates.set(sessionId, state);
      this.persistSessionState(sessionId, state);
    }
  }

  /**
   * Check if session should auto-resume
   */
  shouldAutoResume(sessionId: string): boolean {
    const state = this.sessionStates.get(sessionId);
    if (!state) return false;

    // Auto-resume if last activity was within 30 minutes and was processing
    const lastActivity = new Date(state.lastActivity);
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    
    return lastActivity > thirtyMinutesAgo && state.isProcessing;
  }

  /**
   * Get session continuation prompt
   */
  getSessionContinuationPrompt(sessionId: string): string | null {
    const state = this.sessionStates.get(sessionId);
    if (!state || !state.inputValue) return null;

    return state.inputValue;
  }

  /**
   * Register callback for session restoration
   */
  onSessionRestore(sessionId: string, callback: () => void): void {
    this.restorationCallbacks.set(sessionId, callback);
  }

  /**
   * Remove session restoration callback
   */
  removeSessionRestoreCallback(sessionId: string): void {
    this.restorationCallbacks.delete(sessionId);
  }

  /**
   * Get all session states for debugging
   */
  getAllSessionStates(): Map<string, SessionState> {
    return new Map(this.sessionStates);
  }

  /**
   * Clean up old session states
   */
  cleanupOldSessions(maxAge: number = 7 * 24 * 60 * 60 * 1000): void {
    const cutoff = new Date(Date.now() - maxAge);
    
    for (const [sessionId, state] of this.sessionStates.entries()) {
      const lastActivity = new Date(state.lastActivity);
      if (lastActivity < cutoff) {
        this.sessionStates.delete(sessionId);
        this.removeSessionFromStorage(sessionId);
      }
    }
  }

  /**
   * Export session state for backup
   */
  exportSessionState(sessionId: string): SessionState | null {
    return this.sessionStates.get(sessionId) || null;
  }

  /**
   * Import session state from backup
   */
  importSessionState(state: SessionState): void {
    this.sessionStates.set(state.sessionId, state);
    this.persistSessionState(state.sessionId, state);
  }

  /**
   * Get active session ID
   */
  getActiveSessionId(): string | null {
    return this.activeSessionId;
  }

  /**
   * Switch to session with proper state management
   */
  async switchToSession(
    sessionId: string,
    context?: RestorationContext
  ): Promise<SessionState | null> {
    // Save current session state if switching from another session
    if (this.activeSessionId && this.activeSessionId !== sessionId) {
      // This would be called by the component to save its current state
      console.log(`Switching from ${this.activeSessionId} to ${sessionId}`);
    }

    // Restore the target session
    return this.restoreSessionState(sessionId, context);
  }

  /**
   * Pause session (save state and mark as paused)
   */
  pauseSession(sessionId: string, state: Partial<SessionState>): void {
    this.saveSessionState(sessionId, {
      ...state,
      isProcessing: false,
      lastActivity: new Date().toISOString()
    });
  }

  /**
   * Resume session (restore state and mark as active)
   */
  resumeSession(sessionId: string): SessionState | null {
    const state = this.restoreSessionState(sessionId, {
      preserveInput: true,
      preserveScrollPosition: true,
      preservePersona: true,
      preserveUIState: true,
      autoResume: true
    });

    if (state) {
      // Mark as active
      this.saveSessionState(sessionId, {
        ...state,
        lastActivity: new Date().toISOString()
      });
    }

    return state;
  }

  // Private methods
  private persistSessionState(sessionId: string, state: SessionState): void {
    try {
      localStorage.setItem(
        `brainstorm_session_state_${sessionId}`,
        JSON.stringify(state)
      );
    } catch (error) {
      console.error('Failed to persist session state:', error);
    }
  }

  private loadSessionState(sessionId: string): SessionState | null {
    try {
      const stored = localStorage.getItem(`brainstorm_session_state_${sessionId}`);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Failed to load session state:', error);
      return null;
    }
  }

  private removeSessionFromStorage(sessionId: string): void {
    try {
      localStorage.removeItem(`brainstorm_session_state_${sessionId}`);
    } catch (error) {
      console.error('Failed to remove session state:', error);
    }
  }
}

// Export singleton instance
export const sessionRestoration = new SessionRestorationService();

// React hook for session restoration
export function useSessionRestoration(sessionId: string) {
  const saveState = (state: Partial<SessionState>) => {
    sessionRestoration.saveSessionState(sessionId, state);
  };

  const restoreState = (context?: RestorationContext) => {
    return sessionRestoration.restoreSessionState(sessionId, context);
  };

  const updateActivity = () => {
    sessionRestoration.updateSessionActivity(sessionId);
  };

  const shouldAutoResume = () => {
    return sessionRestoration.shouldAutoResume(sessionId);
  };

  const getContinuationPrompt = () => {
    return sessionRestoration.getSessionContinuationPrompt(sessionId);
  };

  const pauseSession = (state: Partial<SessionState>) => {
    sessionRestoration.pauseSession(sessionId, state);
  };

  const resumeSession = () => {
    return sessionRestoration.resumeSession(sessionId);
  };

  return {
    saveState,
    restoreState,
    updateActivity,
    shouldAutoResume,
    getContinuationPrompt,
    pauseSession,
    resumeSession
  };
}
