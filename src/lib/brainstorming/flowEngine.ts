/**
 * Flow Engine for Progressive Brainstorming Experiences
 * 
 * This engine manages multi-step brainstorming flows, handles flow transitions,
 * and provides structured guidance through complex ideation processes.
 */

import {
  BrainstormSession,
  ChatMessage,
  Idea,
  BrainstormTemplate,
  TemplateType
} from '@/types/brainstorm';
import { EnhancedChoice, ChoiceContext } from './enhancedChoiceEngine';

// Flow-specific interfaces
export interface BrainstormingFlow {
  id: string;
  name: string;
  description: string;
  category: 'guided' | 'template-based' | 'custom' | 'analysis';
  steps: FlowStep[];
  estimatedDuration: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  prerequisites?: string[];
  
  // Flow metadata
  tags: string[];
  icon?: string;
  color?: string;
  
  // Flow behavior
  allowSkipSteps: boolean;
  allowBacktrack: boolean;
  autoAdvance: boolean;
  
  // Completion criteria
  completionCriteria?: FlowCompletionCriteria;
}

export interface FlowStep {
  id: string;
  name: string;
  description: string;
  type: 'choice-selection' | 'template-application' | 'code-generation' | 'idea-capture' | 'analysis' | 'review';
  
  // Step content
  prompt: string;
  instructions?: string;
  examples?: string[];
  
  // Step choices and validation
  choices: EnhancedChoice[];
  validationRules?: ValidationRule[];
  requiredInputs?: string[];
  
  // Flow control
  next: (choice?: EnhancedChoice, context?: FlowStepContext) => string | null;
  onEnter?: (context: FlowStepContext) => Promise<void>;
  onExit?: (context: FlowStepContext, result: FlowStepResult) => Promise<void>;
  
  // UI properties
  showProgress: boolean;
  allowSkip: boolean;
  estimatedTime?: string;
}

export interface ValidationRule {
  id: string;
  type: 'required' | 'minLength' | 'maxLength' | 'pattern' | 'custom';
  message: string;
  value?: any;
  validator?: (input: any, context: FlowStepContext) => boolean;
}

export interface FlowStepContext {
  session: BrainstormSession;
  currentFlow: BrainstormingFlow;
  currentStepIndex: number;
  flowData: Map<string, any>;
  previousSteps: FlowStepResult[];
  ideas: Idea[];
  messages: ChatMessage[];
}

export interface FlowStepResult {
  stepId: string;
  selectedChoices: EnhancedChoice[];
  userInputs: Record<string, any>;
  extractedIdeas: string[];
  generatedContent?: string;
  completedAt: string;
  duration: number; // milliseconds
}

export interface FlowCompletionCriteria {
  minStepsCompleted?: number;
  requiredSteps?: string[];
  minIdeasGenerated?: number;
  customValidator?: (context: FlowStepContext) => boolean;
}

export interface FlowTransition {
  fromStep: string;
  toStep: string;
  condition?: (context: FlowStepContext) => boolean;
  action?: (context: FlowStepContext) => Promise<void>;
}

export interface FlowResult {
  flowId: string;
  sessionId: string;
  completedSteps: FlowStepResult[];
  generatedIdeas: Idea[];
  finalOutput?: string;
  isCompleted: boolean;
  duration: number;
  completedAt: string;
  summary: FlowSummary;
}

export interface FlowSummary {
  totalSteps: number;
  completedSteps: number;
  ideasGenerated: number;
  keyInsights: string[];
  nextRecommendations: string[];
}

// Predefined flows
const PREDEFINED_FLOWS: BrainstormingFlow[] = [
  {
    id: 'product-discovery',
    name: 'Product Discovery Workshop',
    description: 'Systematic approach to discovering and validating product ideas',
    category: 'guided',
    estimatedDuration: '45-60 minutes',
    difficulty: 'intermediate',
    tags: ['product', 'discovery', 'validation', 'user-research'],
    icon: '🔍',
    color: '#3b82f6',
    allowSkipSteps: false,
    allowBacktrack: true,
    autoAdvance: false,
    steps: [
      {
        id: 'problem-identification',
        name: 'Problem Identification',
        description: 'Identify and define the core problem you want to solve',
        type: 'idea-capture',
        prompt: 'What specific problem are you trying to solve? Describe the pain points your users experience.',
        instructions: 'Be specific and focus on real user problems. Avoid solution-oriented thinking at this stage.',
        examples: [
          'Users struggle to find relevant information quickly in our app',
          'Small business owners spend too much time on manual invoicing',
          'Remote teams lack effective async collaboration tools'
        ],
        choices: [],
        validationRules: [
          {
            id: 'min-length',
            type: 'minLength',
            message: 'Please provide at least 20 characters describing the problem',
            value: 20
          }
        ],
        next: () => 'user-research',
        showProgress: true,
        allowSkip: false,
        estimatedTime: '10 minutes'
      },
      {
        id: 'user-research',
        name: 'User Research',
        description: 'Define target users and understand their needs',
        type: 'analysis',
        prompt: 'Who are your target users? What are their goals, motivations, and constraints?',
        instructions: 'Create detailed user personas including demographics, behaviors, and pain points.',
        choices: [
          {
            id: 'persona-creation',
            text: 'Create detailed user personas',
            category: 'analysis',
            contextualRelevance: 1.0,
            followUpSuggestions: [],
            priority: 'high'
          },
          {
            id: 'journey-mapping',
            text: 'Map user journey and touchpoints',
            category: 'analysis',
            contextualRelevance: 0.9,
            followUpSuggestions: [],
            priority: 'high'
          }
        ],
        next: () => 'solution-ideation',
        showProgress: true,
        allowSkip: false,
        estimatedTime: '15 minutes'
      },
      {
        id: 'solution-ideation',
        name: 'Solution Ideation',
        description: 'Generate potential solutions to the identified problems',
        type: 'idea-capture',
        prompt: 'Now that you understand the problem and users, what solutions could address these needs?',
        instructions: 'Generate multiple solution ideas without filtering. Quantity over quality at this stage.',
        choices: [
          {
            id: 'brainstorm-solutions',
            text: 'Generate solution ideas',
            category: 'topic',
            contextualRelevance: 1.0,
            followUpSuggestions: [],
            priority: 'high'
          },
          {
            id: 'competitive-analysis',
            text: 'Analyze existing solutions',
            category: 'analysis',
            contextualRelevance: 0.8,
            followUpSuggestions: [],
            priority: 'medium'
          }
        ],
        next: () => 'solution-validation',
        showProgress: true,
        allowSkip: false,
        estimatedTime: '15 minutes'
      },
      {
        id: 'solution-validation',
        name: 'Solution Validation',
        description: 'Evaluate and prioritize your solution ideas',
        type: 'analysis',
        prompt: 'Which solutions are most viable? Consider impact, feasibility, and user value.',
        instructions: 'Use frameworks like Impact/Effort matrix to prioritize solutions.',
        choices: [
          {
            id: 'impact-effort-matrix',
            text: 'Create impact vs effort matrix',
            category: 'analysis',
            contextualRelevance: 1.0,
            followUpSuggestions: [],
            priority: 'high'
          },
          {
            id: 'feasibility-analysis',
            text: 'Analyze technical feasibility',
            category: 'analysis',
            contextualRelevance: 0.9,
            followUpSuggestions: [],
            priority: 'high'
          }
        ],
        next: () => 'next-steps',
        showProgress: true,
        allowSkip: false,
        estimatedTime: '10 minutes'
      },
      {
        id: 'next-steps',
        name: 'Action Planning',
        description: 'Define concrete next steps and implementation plan',
        type: 'analysis',
        prompt: 'What are the immediate next steps to validate and implement your top solutions?',
        instructions: 'Create actionable tasks with timelines and success criteria.',
        choices: [
          {
            id: 'create-mvp-plan',
            text: 'Plan minimum viable product',
            category: 'analysis',
            contextualRelevance: 1.0,
            followUpSuggestions: [],
            priority: 'high'
          },
          {
            id: 'validation-experiments',
            text: 'Design validation experiments',
            category: 'analysis',
            contextualRelevance: 0.9,
            followUpSuggestions: [],
            priority: 'high'
          }
        ],
        next: () => null, // End of flow
        showProgress: true,
        allowSkip: false,
        estimatedTime: '10 minutes'
      }
    ],
    completionCriteria: {
      minStepsCompleted: 4,
      requiredSteps: ['problem-identification', 'solution-ideation'],
      minIdeasGenerated: 3
    }
  },
  {
    id: 'technical-architecture',
    name: 'Technical Architecture Design',
    description: 'Structured approach to designing system architecture',
    category: 'template-based',
    estimatedDuration: '60-90 minutes',
    difficulty: 'advanced',
    tags: ['technical', 'architecture', 'system-design', 'scalability'],
    icon: '🏗️',
    color: '#8b5cf6',
    allowSkipSteps: true,
    allowBacktrack: true,
    autoAdvance: false,
    steps: [
      {
        id: 'requirements-gathering',
        name: 'Requirements Analysis',
        description: 'Define functional and non-functional requirements',
        type: 'analysis',
        prompt: 'What are the key requirements for your system? Include performance, scalability, and integration needs.',
        choices: [
          {
            id: 'functional-requirements',
            text: 'Define functional requirements',
            category: 'analysis',
            contextualRelevance: 1.0,
            followUpSuggestions: [],
            priority: 'high'
          },
          {
            id: 'non-functional-requirements',
            text: 'Specify non-functional requirements',
            category: 'analysis',
            contextualRelevance: 1.0,
            followUpSuggestions: [],
            priority: 'high'
          }
        ],
        next: () => 'system-components',
        showProgress: true,
        allowSkip: false
      },
      {
        id: 'system-components',
        name: 'Component Architecture',
        description: 'Design the main system components and their interactions',
        type: 'code-generation',
        prompt: 'What are the core components of your system? How do they interact?',
        choices: [
          {
            id: 'component-diagram',
            text: 'Create component architecture diagram',
            category: 'code-action',
            contextualRelevance: 1.0,
            followUpSuggestions: [],
            priority: 'high',
            codeGenerationTrigger: {
              type: 'component',
              contextRequired: ['requirements'],
              templatePrompt: 'Generate system architecture diagram for: '
            }
          }
        ],
        next: () => 'data-architecture',
        showProgress: true,
        allowSkip: false
      },
      {
        id: 'data-architecture',
        name: 'Data Architecture',
        description: 'Design data models and storage solutions',
        type: 'code-generation',
        prompt: 'How will you structure and store your data? What are the key entities and relationships?',
        choices: [
          {
            id: 'data-model',
            text: 'Design data models',
            category: 'code-action',
            contextualRelevance: 1.0,
            followUpSuggestions: [],
            priority: 'high',
            codeGenerationTrigger: {
              type: 'class',
              contextRequired: ['requirements', 'components'],
              templatePrompt: 'Generate data models for: '
            }
          }
        ],
        next: () => 'api-design',
        showProgress: true,
        allowSkip: true
      },
      {
        id: 'api-design',
        name: 'API Design',
        description: 'Design the API interfaces and contracts',
        type: 'code-generation',
        prompt: 'What APIs will your system expose? Define the key endpoints and data contracts.',
        choices: [
          {
            id: 'rest-api',
            text: 'Design REST API',
            category: 'code-action',
            contextualRelevance: 1.0,
            followUpSuggestions: [],
            priority: 'high',
            codeGenerationTrigger: {
              type: 'api',
              contextRequired: ['data-models'],
              templatePrompt: 'Generate REST API specification for: '
            }
          }
        ],
        next: () => null,
        showProgress: true,
        allowSkip: true
      }
    ],
    completionCriteria: {
      minStepsCompleted: 3,
      requiredSteps: ['requirements-gathering', 'system-components']
    }
  }
];

export class FlowEngine {
  private activeFlows: Map<string, FlowState> = new Map();
  private flowHistory: Map<string, FlowResult[]> = new Map();

  constructor() {
    // Initialize with predefined flows
  }

  /**
   * Start a new flow for a session
   */
  async startFlow(
    flowId: string,
    sessionId: string,
    context: ChoiceContext
  ): Promise<FlowState> {
    const flow = this.getFlowById(flowId);
    if (!flow) {
      throw new Error(`Flow ${flowId} not found`);
    }

    const flowState: FlowState = {
      flowId,
      sessionId,
      currentStepIndex: 0,
      flowData: new Map(),
      stepResults: [],
      startedAt: new Date().toISOString(),
      isCompleted: false,
      isPaused: false
    };

    this.activeFlows.set(sessionId, flowState);

    // Initialize first step
    await this.initializeStep(flow.steps[0], flowState, context);

    return flowState;
  }

  /**
   * Execute a flow step
   */
  async executeStep(
    sessionId: string,
    stepId: string,
    choice?: EnhancedChoice,
    userInputs?: Record<string, any>
  ): Promise<FlowStepResult> {
    const flowState = this.activeFlows.get(sessionId);
    if (!flowState) {
      throw new Error(`No active flow found for session ${sessionId}`);
    }

    const flow = this.getFlowById(flowState.flowId);
    if (!flow) {
      throw new Error(`Flow ${flowState.flowId} not found`);
    }

    const currentStep = flow.steps[flowState.currentStepIndex];
    if (currentStep.id !== stepId) {
      throw new Error(`Step mismatch: expected ${currentStep.id}, got ${stepId}`);
    }

    const stepStartTime = Date.now();

    // Validate inputs if required
    if (currentStep.validationRules) {
      this.validateStepInputs(currentStep.validationRules, userInputs || {}, {
        session: {} as BrainstormSession, // Will be populated from context
        currentFlow: flow,
        currentStepIndex: flowState.currentStepIndex,
        flowData: flowState.flowData,
        previousSteps: flowState.stepResults,
        ideas: [],
        messages: []
      });
    }

    // Create step result
    const stepResult: FlowStepResult = {
      stepId,
      selectedChoices: choice ? [choice] : [],
      userInputs: userInputs || {},
      extractedIdeas: [], // Will be populated by idea extraction
      completedAt: new Date().toISOString(),
      duration: Date.now() - stepStartTime
    };

    // Execute step-specific logic
    if (currentStep.onExit) {
      await currentStep.onExit({
        session: {} as BrainstormSession,
        currentFlow: flow,
        currentStepIndex: flowState.currentStepIndex,
        flowData: flowState.flowData,
        previousSteps: flowState.stepResults,
        ideas: [],
        messages: []
      }, stepResult);
    }

    // Store step result
    flowState.stepResults.push(stepResult);

    // Determine next step
    const nextStepId = currentStep.next(choice, {
      session: {} as BrainstormSession,
      currentFlow: flow,
      currentStepIndex: flowState.currentStepIndex,
      flowData: flowState.flowData,
      previousSteps: flowState.stepResults,
      ideas: [],
      messages: []
    });

    if (nextStepId) {
      // Move to next step
      const nextStepIndex = flow.steps.findIndex(step => step.id === nextStepId);
      if (nextStepIndex !== -1) {
        flowState.currentStepIndex = nextStepIndex;
        await this.initializeStep(flow.steps[nextStepIndex], flowState, {} as ChoiceContext);
      }
    } else {
      // Flow completed
      await this.completeFlow(flowState);
    }

    return stepResult;
  }

  /**
   * Get current step for a session
   */
  getCurrentStep(sessionId: string): FlowStep | null {
    const flowState = this.activeFlows.get(sessionId);
    if (!flowState || flowState.isCompleted) return null;

    const flow = this.getFlowById(flowState.flowId);
    if (!flow) return null;

    return flow.steps[flowState.currentStepIndex];
  }

  /**
   * Get flow progress
   */
  getFlowProgress(sessionId: string): FlowProgress | null {
    const flowState = this.activeFlows.get(sessionId);
    if (!flowState) return null;

    const flow = this.getFlowById(flowState.flowId);
    if (!flow) return null;

    return {
      flowId: flowState.flowId,
      flowName: flow.name,
      currentStepIndex: flowState.currentStepIndex,
      totalSteps: flow.steps.length,
      completedSteps: flowState.stepResults.length,
      progressPercentage: (flowState.stepResults.length / flow.steps.length) * 100,
      estimatedTimeRemaining: this.estimateTimeRemaining(flow, flowState),
      canGoBack: flow.allowBacktrack && flowState.currentStepIndex > 0,
      canSkip: flow.allowSkipSteps && flow.steps[flowState.currentStepIndex]?.allowSkip
    };
  }

  /**
   * Navigate to previous step
   */
  async goToPreviousStep(sessionId: string): Promise<FlowStep | null> {
    const flowState = this.activeFlows.get(sessionId);
    if (!flowState) return null;

    const flow = this.getFlowById(flowState.flowId);
    if (!flow || !flow.allowBacktrack || flowState.currentStepIndex === 0) {
      return null;
    }

    flowState.currentStepIndex--;
    
    // Remove the last step result
    flowState.stepResults.pop();

    const previousStep = flow.steps[flowState.currentStepIndex];
    await this.initializeStep(previousStep, flowState, {} as ChoiceContext);

    return previousStep;
  }

  /**
   * Skip current step
   */
  async skipCurrentStep(sessionId: string): Promise<FlowStep | null> {
    const flowState = this.activeFlows.get(sessionId);
    if (!flowState) return null;

    const flow = this.getFlowById(flowState.flowId);
    if (!flow || !flow.allowSkipSteps) return null;

    const currentStep = flow.steps[flowState.currentStepIndex];
    if (!currentStep.allowSkip) return null;

    // Create a skipped step result
    const skippedResult: FlowStepResult = {
      stepId: currentStep.id,
      selectedChoices: [],
      userInputs: { skipped: true },
      extractedIdeas: [],
      completedAt: new Date().toISOString(),
      duration: 0
    };

    flowState.stepResults.push(skippedResult);

    // Move to next step
    const nextStepId = currentStep.next(undefined, {
      session: {} as BrainstormSession,
      currentFlow: flow,
      currentStepIndex: flowState.currentStepIndex,
      flowData: flowState.flowData,
      previousSteps: flowState.stepResults,
      ideas: [],
      messages: []
    });

    if (nextStepId) {
      const nextStepIndex = flow.steps.findIndex(step => step.id === nextStepId);
      if (nextStepIndex !== -1) {
        flowState.currentStepIndex = nextStepIndex;
        const nextStep = flow.steps[nextStepIndex];
        await this.initializeStep(nextStep, flowState, {} as ChoiceContext);
        return nextStep;
      }
    } else {
      await this.completeFlow(flowState);
    }

    return null;
  }

  /**
   * Pause a flow
   */
  pauseFlow(sessionId: string): boolean {
    const flowState = this.activeFlows.get(sessionId);
    if (!flowState || flowState.isCompleted) return false;

    flowState.isPaused = true;
    flowState.pausedAt = new Date().toISOString();
    return true;
  }

  /**
   * Resume a paused flow
   */
  resumeFlow(sessionId: string): boolean {
    const flowState = this.activeFlows.get(sessionId);
    if (!flowState || flowState.isCompleted || !flowState.isPaused) return false;

    flowState.isPaused = false;
    delete flowState.pausedAt;
    return true;
  }

  /**
   * Get available flows
   */
  getAvailableFlows(): BrainstormingFlow[] {
    return PREDEFINED_FLOWS;
  }

  /**
   * Get flow by ID
   */
  getFlowById(flowId: string): BrainstormingFlow | null {
    return PREDEFINED_FLOWS.find(flow => flow.id === flowId) || null;
  }

  private async initializeStep(
    step: FlowStep,
    flowState: FlowState,
    context: ChoiceContext
  ): Promise<void> {
    if (step.onEnter) {
      await step.onEnter({
        session: {} as BrainstormSession,
        currentFlow: this.getFlowById(flowState.flowId)!,
        currentStepIndex: flowState.currentStepIndex,
        flowData: flowState.flowData,
        previousSteps: flowState.stepResults,
        ideas: [],
        messages: []
      });
    }
  }

  private async completeFlow(flowState: FlowState): Promise<void> {
    flowState.isCompleted = true;
    flowState.completedAt = new Date().toISOString();

    // Create flow result
    const flowResult: FlowResult = {
      flowId: flowState.flowId,
      sessionId: flowState.sessionId,
      completedSteps: flowState.stepResults,
      generatedIdeas: [], // Will be populated from session
      isCompleted: true,
      duration: Date.now() - new Date(flowState.startedAt).getTime(),
      completedAt: flowState.completedAt,
      summary: this.generateFlowSummary(flowState)
    };

    // Store in history
    const sessionHistory = this.flowHistory.get(flowState.sessionId) || [];
    sessionHistory.push(flowResult);
    this.flowHistory.set(flowState.sessionId, sessionHistory);

    // Remove from active flows
    this.activeFlows.delete(flowState.sessionId);
  }

  private generateFlowSummary(flowState: FlowState): FlowSummary {
    const flow = this.getFlowById(flowState.flowId)!;
    
    return {
      totalSteps: flow.steps.length,
      completedSteps: flowState.stepResults.length,
      ideasGenerated: flowState.stepResults.reduce((total, result) => 
        total + result.extractedIdeas.length, 0
      ),
      keyInsights: this.extractKeyInsights(flowState.stepResults),
      nextRecommendations: this.generateNextRecommendations(flowState)
    };
  }

  private extractKeyInsights(stepResults: FlowStepResult[]): string[] {
    // Simple implementation - extract key insights from step results
    const insights: string[] = [];
    
    stepResults.forEach(result => {
      if (result.generatedContent) {
        // Extract first sentence as insight
        const firstSentence = result.generatedContent.split('.')[0];
        if (firstSentence.length > 20) {
          insights.push(firstSentence.trim() + '.');
        }
      }
    });

    return insights.slice(0, 5);
  }

  private generateNextRecommendations(flowState: FlowState): string[] {
    const flow = this.getFlowById(flowState.flowId)!;
    
    // Generate recommendations based on flow type and results
    const recommendations: string[] = [];
    
    if (flow.category === 'guided') {
      recommendations.push('Create a detailed implementation plan');
      recommendations.push('Validate your ideas with user testing');
      recommendations.push('Develop a minimum viable product');
    } else if (flow.category === 'template-based') {
      recommendations.push('Generate code scaffolding');
      recommendations.push('Create technical documentation');
      recommendations.push('Set up development environment');
    }

    return recommendations;
  }

  private validateStepInputs(
    rules: ValidationRule[],
    inputs: Record<string, any>,
    context: FlowStepContext
  ): void {
    for (const rule of rules) {
      switch (rule.type) {
        case 'required':
          if (!inputs[rule.id] || inputs[rule.id].toString().trim() === '') {
            throw new Error(rule.message);
          }
          break;
        case 'minLength':
          if (inputs[rule.id] && inputs[rule.id].toString().length < rule.value) {
            throw new Error(rule.message);
          }
          break;
        case 'maxLength':
          if (inputs[rule.id] && inputs[rule.id].toString().length > rule.value) {
            throw new Error(rule.message);
          }
          break;
        case 'custom':
          if (rule.validator && !rule.validator(inputs[rule.id], context)) {
            throw new Error(rule.message);
          }
          break;
      }
    }
  }

  private estimateTimeRemaining(flow: BrainstormingFlow, flowState: FlowState): string {
    const remainingSteps = flow.steps.length - flowState.currentStepIndex;
    const avgTimePerStep = flow.estimatedDuration.includes('-') 
      ? parseInt(flow.estimatedDuration.split('-')[1]) / flow.steps.length
      : parseInt(flow.estimatedDuration) / flow.steps.length;
    
    const estimatedMinutes = Math.ceil(remainingSteps * avgTimePerStep);
    return `${estimatedMinutes} minutes`;
  }
}

// Supporting interfaces
interface FlowState {
  flowId: string;
  sessionId: string;
  currentStepIndex: number;
  flowData: Map<string, any>;
  stepResults: FlowStepResult[];
  startedAt: string;
  pausedAt?: string;
  completedAt?: string;
  isCompleted: boolean;
  isPaused: boolean;
}

interface FlowProgress {
  flowId: string;
  flowName: string;
  currentStepIndex: number;
  totalSteps: number;
  completedSteps: number;
  progressPercentage: number;
  estimatedTimeRemaining: string;
  canGoBack: boolean;
  canSkip: boolean;
}

// Export singleton instance
export const flowEngine = new FlowEngine();