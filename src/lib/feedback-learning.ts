/**
 * Feedback and Learning System
 * 
 * Collects user feedback and adapts AI behavior for improved brainstorming
 */

export interface FeedbackEntry {
  id: string;
  messageId: string;
  sessionId: string;
  type: 'thumbs_up' | 'thumbs_down' | 'detailed';
  rating: number; // 1-5 scale
  category: 'relevance' | 'creativity' | 'clarity' | 'usefulness' | 'accuracy';
  comment?: string;
  context: string;
  aiPersona: string;
  timestamp: string;
}

export interface UserPreferences {
  id: string;
  userId: string;
  preferredPersonas: string[];
  responseStyle: 'concise' | 'detailed' | 'creative' | 'analytical';
  topicPreferences: string[];
  feedbackPatterns: {
    highRatedCategories: string[];
    lowRatedCategories: string[];
    commonComplaints: string[];
    commonPraises: string[];
  };
  adaptationSettings: {
    enableLearning: boolean;
    learningRate: number; // 0-1
    confidenceThreshold: number; // 0-1
  };
  lastUpdated: string;
}

export interface FeedbackAnalytics {
  totalFeedback: number;
  averageRating: number;
  categoryBreakdown: Record<string, {
    count: number;
    averageRating: number;
    trend: 'improving' | 'declining' | 'stable';
  }>;
  personaPerformance: Record<string, {
    count: number;
    averageRating: number;
    strongCategories: string[];
    weakCategories: string[];
  }>;
  improvementSuggestions: string[];
  learningInsights: string[];
}

class FeedbackLearningSystem {
  private feedback: Map<string, FeedbackEntry> = new Map();
  private preferences: Map<string, UserPreferences> = new Map();
  private analytics: FeedbackAnalytics | null = null;

  /**
   * Submit feedback for an AI response
   */
  submitFeedback(feedback: Omit<FeedbackEntry, 'id' | 'timestamp'>): string {
    const id = this.generateId();
    const entry: FeedbackEntry = {
      ...feedback,
      id,
      timestamp: new Date().toISOString(),
    };

    this.feedback.set(id, entry);
    this.updateUserPreferences(feedback.sessionId, entry);
    this.invalidateAnalytics();

    return id;
  }

  /**
   * Get feedback for a specific message
   */
  getFeedbackForMessage(messageId: string): FeedbackEntry[] {
    return Array.from(this.feedback.values())
      .filter(entry => entry.messageId === messageId);
  }

  /**
   * Get feedback for a session
   */
  getFeedbackForSession(sessionId: string): FeedbackEntry[] {
    return Array.from(this.feedback.values())
      .filter(entry => entry.sessionId === sessionId)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  /**
   * Get user preferences
   */
  getUserPreferences(userId: string): UserPreferences {
    if (!this.preferences.has(userId)) {
      this.preferences.set(userId, this.createDefaultPreferences(userId));
    }
    return this.preferences.get(userId)!;
  }

  /**
   * Update user preferences based on feedback patterns
   */
  private updateUserPreferences(sessionId: string, feedback: FeedbackEntry): void {
    // For demo purposes, using sessionId as userId
    const userId = sessionId;
    const preferences = this.getUserPreferences(userId);

    // Update preferred personas based on positive feedback
    if (feedback.rating >= 4) {
      if (!preferences.preferredPersonas.includes(feedback.aiPersona)) {
        preferences.preferredPersonas.push(feedback.aiPersona);
      }
    }

    // Update feedback patterns
    if (feedback.rating >= 4) {
      if (!preferences.feedbackPatterns.highRatedCategories.includes(feedback.category)) {
        preferences.feedbackPatterns.highRatedCategories.push(feedback.category);
      }
      if (feedback.comment) {
        preferences.feedbackPatterns.commonPraises.push(feedback.comment);
      }
    } else if (feedback.rating <= 2) {
      if (!preferences.feedbackPatterns.lowRatedCategories.includes(feedback.category)) {
        preferences.feedbackPatterns.lowRatedCategories.push(feedback.category);
      }
      if (feedback.comment) {
        preferences.feedbackPatterns.commonComplaints.push(feedback.comment);
      }
    }

    // Limit array sizes
    preferences.preferredPersonas = preferences.preferredPersonas.slice(-5);
    preferences.feedbackPatterns.highRatedCategories = preferences.feedbackPatterns.highRatedCategories.slice(-10);
    preferences.feedbackPatterns.lowRatedCategories = preferences.feedbackPatterns.lowRatedCategories.slice(-10);
    preferences.feedbackPatterns.commonPraises = preferences.feedbackPatterns.commonPraises.slice(-20);
    preferences.feedbackPatterns.commonComplaints = preferences.feedbackPatterns.commonComplaints.slice(-20);

    preferences.lastUpdated = new Date().toISOString();
  }

  /**
   * Generate AI adaptation suggestions based on feedback
   */
  generateAdaptationSuggestions(sessionId: string): string[] {
    const sessionFeedback = this.getFeedbackForSession(sessionId);
    const preferences = this.getUserPreferences(sessionId);
    const suggestions: string[] = [];

    if (sessionFeedback.length === 0) {
      return ['No feedback available for adaptation suggestions.'];
    }

    // Analyze recent feedback patterns
    const recentFeedback = sessionFeedback.slice(0, 10);
    const averageRating = recentFeedback.reduce((sum, f) => sum + f.rating, 0) / recentFeedback.length;

    if (averageRating < 3) {
      suggestions.push('Consider switching to a different AI persona based on recent feedback.');
      
      const lowCategories = recentFeedback
        .filter(f => f.rating <= 2)
        .map(f => f.category);
      
      if (lowCategories.includes('clarity')) {
        suggestions.push('Focus on providing clearer, more structured responses.');
      }
      if (lowCategories.includes('relevance')) {
        suggestions.push('Ensure responses are more directly relevant to the brainstorming context.');
      }
      if (lowCategories.includes('creativity')) {
        suggestions.push('Try more creative and innovative approaches to idea generation.');
      }
    }

    // Persona-specific suggestions
    const personaFeedback = recentFeedback.reduce((acc, f) => {
      if (!acc[f.aiPersona]) acc[f.aiPersona] = [];
      acc[f.aiPersona].push(f);
      return acc;
    }, {} as Record<string, FeedbackEntry[]>);

    Object.entries(personaFeedback).forEach(([persona, feedback]) => {
      const avgRating = feedback.reduce((sum, f) => sum + f.rating, 0) / feedback.length;
      if (avgRating < 2.5) {
        suggestions.push(`Consider avoiding the ${persona} persona based on recent performance.`);
      } else if (avgRating > 4) {
        suggestions.push(`The ${persona} persona is performing well - consider using it more often.`);
      }
    });

    return suggestions.length > 0 ? suggestions : ['Current AI performance is satisfactory based on feedback.'];
  }

  /**
   * Get comprehensive feedback analytics
   */
  getFeedbackAnalytics(): FeedbackAnalytics {
    if (this.analytics) {
      return this.analytics;
    }

    const allFeedback = Array.from(this.feedback.values());
    
    if (allFeedback.length === 0) {
      return {
        totalFeedback: 0,
        averageRating: 0,
        categoryBreakdown: {},
        personaPerformance: {},
        improvementSuggestions: ['No feedback data available yet.'],
        learningInsights: ['Start collecting feedback to generate insights.'],
      };
    }

    const totalFeedback = allFeedback.length;
    const averageRating = allFeedback.reduce((sum, f) => sum + f.rating, 0) / totalFeedback;

    // Category breakdown
    const categoryBreakdown: Record<string, any> = {};
    const categories = ['relevance', 'creativity', 'clarity', 'usefulness', 'accuracy'];
    
    categories.forEach(category => {
      const categoryFeedback = allFeedback.filter(f => f.category === category);
      if (categoryFeedback.length > 0) {
        const avgRating = categoryFeedback.reduce((sum, f) => sum + f.rating, 0) / categoryFeedback.length;
        categoryBreakdown[category] = {
          count: categoryFeedback.length,
          averageRating: avgRating,
          trend: this.calculateTrend(categoryFeedback),
        };
      }
    });

    // Persona performance
    const personaPerformance: Record<string, any> = {};
    const personas = [...new Set(allFeedback.map(f => f.aiPersona))];
    
    personas.forEach(persona => {
      const personaFeedback = allFeedback.filter(f => f.aiPersona === persona);
      const avgRating = personaFeedback.reduce((sum, f) => sum + f.rating, 0) / personaFeedback.length;
      
      const categoryRatings = categories.map(category => {
        const catFeedback = personaFeedback.filter(f => f.category === category);
        return {
          category,
          rating: catFeedback.length > 0 ? catFeedback.reduce((sum, f) => sum + f.rating, 0) / catFeedback.length : 0,
          count: catFeedback.length,
        };
      }).filter(c => c.count > 0);

      const strongCategories = categoryRatings.filter(c => c.rating >= 4).map(c => c.category);
      const weakCategories = categoryRatings.filter(c => c.rating <= 2).map(c => c.category);

      personaPerformance[persona] = {
        count: personaFeedback.length,
        averageRating: avgRating,
        strongCategories,
        weakCategories,
      };
    });

    // Generate improvement suggestions
    const improvementSuggestions = this.generateImprovementSuggestions(categoryBreakdown, personaPerformance);
    const learningInsights = this.generateLearningInsights(allFeedback);

    this.analytics = {
      totalFeedback,
      averageRating,
      categoryBreakdown,
      personaPerformance,
      improvementSuggestions,
      learningInsights,
    };

    return this.analytics;
  }

  /**
   * Export feedback data for analysis
   */
  exportFeedbackData(): any {
    return {
      feedback: Array.from(this.feedback.values()),
      preferences: Array.from(this.preferences.values()),
      analytics: this.getFeedbackAnalytics(),
      exportedAt: new Date().toISOString(),
    };
  }

  /**
   * Clear old feedback data for privacy
   */
  clearOldFeedback(daysOld: number = 90): number {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    
    let removedCount = 0;
    this.feedback.forEach((entry, id) => {
      if (new Date(entry.timestamp) < cutoffDate) {
        this.feedback.delete(id);
        removedCount++;
      }
    });

    this.invalidateAnalytics();
    return removedCount;
  }

  private createDefaultPreferences(userId: string): UserPreferences {
    return {
      id: this.generateId(),
      userId,
      preferredPersonas: [],
      responseStyle: 'balanced' as any,
      topicPreferences: [],
      feedbackPatterns: {
        highRatedCategories: [],
        lowRatedCategories: [],
        commonComplaints: [],
        commonPraises: [],
      },
      adaptationSettings: {
        enableLearning: true,
        learningRate: 0.5,
        confidenceThreshold: 0.7,
      },
      lastUpdated: new Date().toISOString(),
    };
  }

  private calculateTrend(feedback: FeedbackEntry[]): 'improving' | 'declining' | 'stable' {
    if (feedback.length < 4) return 'stable';
    
    const sorted = feedback.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    const firstHalf = sorted.slice(0, Math.floor(sorted.length / 2));
    const secondHalf = sorted.slice(Math.floor(sorted.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, f) => sum + f.rating, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, f) => sum + f.rating, 0) / secondHalf.length;
    
    const diff = secondAvg - firstAvg;
    if (diff > 0.3) return 'improving';
    if (diff < -0.3) return 'declining';
    return 'stable';
  }

  private generateImprovementSuggestions(categoryBreakdown: any, personaPerformance: any): string[] {
    const suggestions: string[] = [];
    
    // Category-based suggestions
    Object.entries(categoryBreakdown).forEach(([category, data]: [string, any]) => {
      if (data.averageRating < 3) {
        suggestions.push(`Focus on improving ${category} in AI responses.`);
      }
      if (data.trend === 'declining') {
        suggestions.push(`Address declining performance in ${category}.`);
      }
    });

    // Persona-based suggestions
    Object.entries(personaPerformance).forEach(([persona, data]: [string, any]) => {
      if (data.averageRating < 2.5) {
        suggestions.push(`Consider refining the ${persona} persona or using it less frequently.`);
      }
      if (data.weakCategories.length > 0) {
        suggestions.push(`Improve ${persona} persona in: ${data.weakCategories.join(', ')}.`);
      }
    });

    return suggestions.length > 0 ? suggestions : ['Overall performance is satisfactory.'];
  }

  private generateLearningInsights(feedback: FeedbackEntry[]): string[] {
    const insights: string[] = [];
    
    if (feedback.length < 10) {
      insights.push('Collect more feedback to generate meaningful insights.');
      return insights;
    }

    // Time-based patterns
    const recentFeedback = feedback.slice(-20);
    const avgRecent = recentFeedback.reduce((sum, f) => sum + f.rating, 0) / recentFeedback.length;
    const avgOverall = feedback.reduce((sum, f) => sum + f.rating, 0) / feedback.length;
    
    if (avgRecent > avgOverall + 0.3) {
      insights.push('AI performance has improved significantly over time.');
    } else if (avgRecent < avgOverall - 0.3) {
      insights.push('AI performance has declined recently - investigation needed.');
    }

    // Comment analysis
    const comments = feedback.filter(f => f.comment).map(f => f.comment!);
    if (comments.length > 5) {
      insights.push(`Analyzed ${comments.length} detailed comments for improvement opportunities.`);
    }

    return insights;
  }

  private invalidateAnalytics(): void {
    this.analytics = null;
  }

  private generateId(): string {
    return `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const feedbackLearningSystem = new FeedbackLearningSystem();