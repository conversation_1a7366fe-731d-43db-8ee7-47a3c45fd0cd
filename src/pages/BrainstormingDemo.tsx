/**
 * Brainstorming Demo Page
 * 
 * This page demonstrates the enhanced brainstorming system with idea extraction
 * and management capabilities.
 */

import React from "react";
import { EnhancedBrainstormingChat } from "@/components/brainstorming";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";

export const BrainstormingDemo: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col h-screen">
      <header className="border-b p-4">
        <div className="container mx-auto flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-xl font-semibold">Enhanced Brainstorming System</h1>
          </div>
          <div>
            <Button variant="outline" onClick={() => navigate("/")}>
              Back to Home
            </Button>
          </div>
        </div>
      </header>

      <main className="flex-1 container mx-auto p-4">
        <div className="bg-card border rounded-lg shadow-sm h-full">
          <EnhancedBrainstormingChat />
        </div>
      </main>

      <footer className="border-t p-4">
        <div className="container mx-auto text-center text-sm text-muted-foreground">
          Enhanced Brainstorming System - Claudia AI Assistant
        </div>
      </footer>
    </div>
  );
};

export default BrainstormingDemo;