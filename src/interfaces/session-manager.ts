/**
 * Core interfaces for Claude Session Management
 * 
 * These interfaces define the contracts for session management components
 * as specified in the design document.
 */

import type {
  SessionData,
  SessionState,
  ClaudeMessage,
  QueuedPrompt,
  Checkpoint,
  SessionEvent,
  SessionEventCallback,
  StateUpdateCallback,
  UnsubscribeFn,
  ModelType,
  SessionSettings,
  ExportFormat
} from '../types/session';

/**
 * Core session manager interface
 */
export interface IClaudeSessionManager {
  // Session lifecycle
  createSession(projectPath: string): Promise<SessionData>;
  resumeSession(sessionId: string): Promise<SessionData>;
  terminateSession(sessionId: string): Promise<void>;
  
  // Message handling
  sendPrompt(prompt: string, model: ModelType): Promise<void>;
  queuePrompt(prompt: string, model: ModelType): void;
  cancelExecution(): Promise<void>;
  
  // State management
  getSessionState(): SessionState;
  subscribeToUpdates(callback: StateUpdateCallback): UnsubscribeFn;
}

/**
 * Message stream handler interface
 */
export interface IMessageStreamHandler {
  startStream(sessionId: string): Promise<void>;
  stopStream(): void;
  reconnectStream(): Promise<void>;
  onMessage(callback: MessageCallback): void;
  onError(callback: ErrorCallback): void;
  onComplete(callback: CompleteCallback): void;
}

/**
 * Session persistence interface
 */
export interface ISessionPersistence {
  saveSession(session: SessionData): Promise<void>;
  loadSession(sessionId: string): Promise<SessionData>;
  listSessions(): Promise<SessionInfo[]>;
  deleteSession(sessionId: string): Promise<void>;
  exportSession(sessionId: string, format: ExportFormat): Promise<string>;
}

/**
 * Performance optimizer interface
 */
export interface IPerformanceOptimizer {
  enableVirtualScrolling(container: HTMLElement): VirtualScrollManager;
  optimizeMessageRendering(messages: ClaudeMessage[]): OptimizedMessages;
  manageMemoryUsage(): void;
  preloadContent(range: [number, number]): void;
}

/**
 * Checkpoint manager interface
 */
export interface ICheckpointManager {
  createCheckpoint(sessionId: string, name: string, description?: string): Promise<Checkpoint>;
  listCheckpoints(sessionId: string): Promise<Checkpoint[]>;
  restoreCheckpoint(checkpointId: string): Promise<void>;
  deleteCheckpoint(checkpointId: string): Promise<void>;
  forkFromCheckpoint(checkpointId: string, newSessionName: string): Promise<SessionData>;
}

/**
 * Session export interface
 */
export interface ISessionExporter {
  exportAsJsonl(sessionId: string): Promise<string>;
  exportAsMarkdown(sessionId: string): Promise<string>;
  copyToClipboard(content: string): Promise<void>;
  saveToFile(content: string, filename: string): Promise<void>;
}

/**
 * Error recovery interface
 */
export interface IErrorRecovery {
  // Automatic recovery
  autoReconnect(): Promise<boolean>;
  recoverFromCheckpoint(checkpointId: string): Promise<void>;
  validateAndRepairState(): Promise<SessionState>;
  
  // Manual recovery
  retryLastOperation(): Promise<void>;
  resetSession(): Promise<void>;
  exportBeforeReset(): Promise<string>;
}

// Supporting types for interfaces

export type MessageCallback = (message: ClaudeMessage) => void;
export type ErrorCallback = (error: string) => void;
export type CompleteCallback = (success: boolean) => void;

export interface SessionInfo {
  id: string;
  projectPath: string;
  projectId: string;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
  totalTokens: number;
}

export interface VirtualScrollManager {
  scrollToIndex(index: number): void;
  scrollToTop(): void;
  scrollToBottom(): void;
  getVisibleRange(): [number, number];
}

export interface OptimizedMessages {
  visibleMessages: ClaudeMessage[];
  totalCount: number;
  startIndex: number;
  endIndex: number;
}

/**
 * Session component props interfaces
 */
export interface SessionHeaderProps {
  session: SessionData;
  onBack: () => void;
  onSettings: () => void;
  onExport: () => void;
  isStreaming: boolean;
}

export interface MessageListProps {
  messages: ClaudeMessage[];
  isStreaming: boolean;
  streamingMessage?: Partial<ClaudeMessage>;
  onMessageAction?: (messageId: string, action: string) => void;
  virtualScrollEnabled?: boolean;
}

export interface PromptQueueProps {
  queuedPrompts: QueuedPrompt[];
  onReorder: (prompts: QueuedPrompt[]) => void;
  onRemove: (promptId: string) => void;
  onClear: () => void;
  collapsed?: boolean;
  onToggleCollapse: () => void;
}

export interface TimelineNavigatorProps {
  checkpoints: Checkpoint[];
  currentCheckpoint?: string;
  onCheckpointSelect: (checkpointId: string) => void;
  onCheckpointCreate: () => void;
  onCheckpointFork: (checkpointId: string) => void;
}

export interface SessionSettingsProps {
  settings: SessionSettings;
  onSettingsChange: (settings: Partial<SessionSettings>) => void;
  onClose: () => void;
}

/**
 * Hook interfaces
 */
export interface UseSessionReturn {
  // State
  sessionState: SessionState;
  currentSession: SessionData | null;
  isLoading: boolean;
  isStreaming: boolean;
  error: string | null;
  
  // Actions
  createSession: (projectPath: string) => Promise<SessionData>;
  resumeSession: (sessionId: string) => Promise<SessionData>;
  terminateSession: () => Promise<void>;
  sendPrompt: (prompt: string, model?: ModelType) => Promise<void>;
  queuePrompt: (prompt: string, model?: ModelType) => void;
  cancelExecution: () => Promise<void>;
  updateSettings: (settings: Partial<SessionSettings>) => void;
}

export interface UseMessageStreamReturn {
  messages: ClaudeMessage[];
  isStreaming: boolean;
  streamingMessage: Partial<ClaudeMessage> | null;
  error: string | null;
  connect: (sessionId: string) => Promise<void>;
  disconnect: () => void;
  reconnect: () => Promise<void>;
}

export interface UseCheckpointsReturn {
  checkpoints: Checkpoint[];
  isLoading: boolean;
  error: string | null;
  createCheckpoint: (name: string, description?: string) => Promise<void>;
  restoreCheckpoint: (checkpointId: string) => Promise<void>;
  deleteCheckpoint: (checkpointId: string) => Promise<void>;
  forkFromCheckpoint: (checkpointId: string, newSessionName: string) => Promise<void>;
  refreshCheckpoints: () => Promise<void>;
}