import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Send,
  Spark<PERSON>,
  Zap,
  Square,
  Brain,
  Mic,
  Paperclip,
  Code,
  TestTube,
  FileText
} from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { type FileEntry } from "@/lib/api";
import { PersonaSwitcher } from "@/components/brainstorming/PersonaSwitcher";
import { usePersonaStore } from "@/stores/personaStore";
import { BrainstormingPersona, PersonaContext } from "@/types/persona";

interface EnhancedFloatingPromptInputProps {
  /**
   * Callback when prompt is sent
   */
  onSend: (prompt: string, model: "sonnet" | "opus" | "haiku") => void;
  /**
   * Whether the input is loading
   */
  isLoading?: boolean;
  /**
   * Whether the input is disabled
   */
  disabled?: boolean;
  /**
   * Default model to select
   */
  defaultModel?: "sonnet" | "opus" | "haiku";
  /**
   * Project path for file picker
   */
  projectPath?: string;
  /**
   * Optional className for styling
   */
  className?: string;
  /**
   * Callback when cancel is clicked (only during loading)
   */
  onCancel?: () => void;
  /**
   * Whether to show persona switcher for brainstorming mode
   */
  showPersonaSwitcher?: boolean;
  /**
   * Persona context for recommendations
   */
  personaContext?: PersonaContext;
  /**
   * Callback when persona changes
   */
  onPersonaChange?: (persona: BrainstormingPersona) => void;
}

export interface EnhancedFloatingPromptInputRef {
  addImage: (imagePath: string) => void;
  focus: () => void;
}

const MODEL_OPTIONS = [
  {
    id: "haiku" as const,
    name: "Claude 3 Haiku",
    description: "Fast & efficient for simple tasks",
    icon: <Brain className="h-4 w-4" />,
    color: "green"
  },
  {
    id: "sonnet" as const,
    name: "Claude 3.5 Sonnet", 
    description: "Balanced performance for most tasks",
    icon: <Zap className="h-4 w-4" />,
    color: "blue"
  },
  {
    id: "opus" as const,
    name: "Claude 3 Opus",
    description: "Most capable for complex tasks",
    icon: <Sparkles className="h-4 w-4" />,
    color: "purple"
  }
];

/**
 * Enhanced Floating Prompt Input with modern UI/UX
 */
export const EnhancedFloatingPromptInput = React.forwardRef<
  EnhancedFloatingPromptInputRef,
  EnhancedFloatingPromptInputProps
>(({
  onSend,
  isLoading = false,
  disabled = false,
  defaultModel = "sonnet",
  className,
  onCancel,
  showPersonaSwitcher = false,
  personaContext,
  onPersonaChange
}, ref) => {
  const [prompt, setPrompt] = useState("");
  const [selectedModel, setSelectedModel] = useState(defaultModel);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [attachedFiles, setAttachedFiles] = useState<FileEntry[]>([]);
  const [isRecording, setIsRecording] = useState(false);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Persona integration
  const { activePersonaId, personas } = usePersonaStore();
  const activePersona = activePersonaId ? personas[activePersonaId] : null;

  // Smart suggestions based on context
  const generateSuggestions = (input: string) => {
    const contextSuggestions = [
      "Explain this code",
      "Review for best practices", 
      "Add error handling",
      "Write unit tests",
      "Optimize performance",
      "Add documentation",
      "Refactor this function",
      "Fix security issues"
    ];
    
    return contextSuggestions.filter(s => 
      s.toLowerCase().includes(input.toLowerCase())
    ).slice(0, 4);
  };

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [prompt]);

  // Expose methods via ref
  React.useImperativeHandle(ref, () => ({
    addImage: (imagePath: string) => {
      // Implementation for adding images
      console.log('Adding image:', imagePath);
    },
    focus: () => {
      textareaRef.current?.focus();
    }
  }));

  const handleSend = () => {
    if (!prompt.trim() || disabled) return;
    
    onSend(prompt, selectedModel);
    setPrompt("");
    setAttachedFiles([]);
    setShowSuggestions(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
    if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      // Open command palette
    }
  };

  const handlePromptChange = (value: string) => {
    setPrompt(value);
    const newSuggestions = generateSuggestions(value);
    setSuggestions(newSuggestions);
    setShowSuggestions(value.length > 2 && newSuggestions.length > 0);
  };

  return (
    <TooltipProvider>
      <motion.div
        ref={containerRef}
        layout
        className={cn(
          "fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50",
          "w-full max-w-4xl mx-auto px-4",
          className
        )}
      >
        {/* Smart Suggestions Overlay */}
        <AnimatePresence>
          {showSuggestions && suggestions.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              className="mb-3"
            >
              <div className="p-3 bg-background/95 backdrop-blur-xl border rounded-2xl shadow-xl">
                <div className="text-xs font-medium text-muted-foreground mb-2">
                  Suggestions
                </div>
                <div className="flex flex-wrap gap-2">
                  {suggestions.map((suggestion, index) => (
                    <motion.button
                      key={suggestion}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.05 }}
                      onClick={() => {
                        setPrompt(suggestion);
                        setShowSuggestions(false);
                        textareaRef.current?.focus();
                      }}
                      className="px-3 py-1.5 text-sm bg-muted hover:bg-muted/80 rounded-lg transition-colors"
                    >
                      {suggestion}
                    </motion.button>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Input Container */}
        <motion.div
          layout
          className={cn(
            "relative bg-background/95 backdrop-blur-xl rounded-2xl shadow-2xl border",
            "ring-1 ring-black/5 dark:ring-white/5",
            isExpanded && "shadow-3xl ring-2 ring-primary/20",
            "transition-all duration-300"
          )}
        >
          {/* Attached Files Preview */}
          <AnimatePresence>
            {attachedFiles.length > 0 && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="p-3 border-b bg-muted/30"
              >
                <div className="flex flex-wrap gap-2">
                  {attachedFiles.map((file, index) => (
                    <motion.div
                      key={file.path}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.05 }}
                      className="flex items-center gap-2 px-3 py-1.5 bg-background rounded-lg border text-sm"
                    >
                      <FileText className="w-4 h-4" />
                      <span className="truncate max-w-32">{file.name}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-destructive/10"
                        onClick={() => setAttachedFiles(prev => prev.filter((_, i) => i !== index))}
                      >
                        ×
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Input Area */}
          <div className="p-4">
            <div className="flex items-end gap-3">
              {/* Enhanced Model Selector */}
              <div className="flex-shrink-0">
                <Select value={selectedModel} onValueChange={(value: string) => setSelectedModel(value as "sonnet" | "opus" | "haiku")}>
                  <SelectTrigger className={cn(
                    "w-36 h-10 bg-muted/50 border-0 relative overflow-hidden",
                    "bg-gradient-to-r transition-all duration-200",
                    selectedModel === "opus" && "from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/20",
                    selectedModel === "sonnet" && "from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20",
                    selectedModel === "haiku" && "from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20"
                  )}>
                    <SelectValue>
                      <div className="flex items-center gap-2">
                        {selectedModel === 'opus' && <Sparkles className="w-4 h-4 text-purple-500" />}
                        {selectedModel === 'sonnet' && <Zap className="w-4 h-4 text-blue-500" />}
                        {selectedModel === 'haiku' && <Brain className="w-4 h-4 text-green-500" />}
                        <span className="capitalize">{selectedModel}</span>
                      </div>
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {MODEL_OPTIONS.map((model, index) => (
                      <SelectItem key={model.id} value={model.id}>
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className="flex items-center gap-2"
                        >
                          {model.icon}
                          <div>
                            <div className="font-medium">{model.name}</div>
                            <div className="text-xs text-muted-foreground">{model.description}</div>
                          </div>
                        </motion.div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Persona Switcher */}
              {showPersonaSwitcher && (
                <div className="flex-shrink-0">
                  <PersonaSwitcher
                    context={personaContext}
                    onPersonaChange={onPersonaChange}
                    compactMode={true}
                  />
                </div>
              )}

              {/* Enhanced Textarea */}
              <div className="flex-1 relative">
                <Textarea
                  ref={textareaRef}
                  value={prompt}
                  onChange={(e) => handlePromptChange(e.target.value)}
                  onFocus={() => setIsExpanded(true)}
                  onBlur={() => setTimeout(() => setIsExpanded(false), 200)}
                  onKeyDown={handleKeyDown}
                  placeholder={
                    activePersona
                      ? `Ask ${activePersona.name}... (⌘K for commands)`
                      : "Ask Claude anything... (⌘K for commands)"
                  }
                  className={cn(
                    "min-h-[2.5rem] max-h-32 resize-none border-0 bg-transparent",
                    "placeholder:text-muted-foreground/60 focus:ring-0 focus:outline-none",
                    "text-base leading-relaxed"
                  )}
                  disabled={disabled}
                />
                
                {/* Character count and typing indicator */}
                <div className="absolute bottom-1 right-1 text-xs text-muted-foreground">
                  {prompt.length > 0 && (
                    <motion.span
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                    >
                      {prompt.length}
                    </motion.span>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-2">
                {/* Voice Input */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.div whileTap={{ scale: 0.95 }}>
                      <Button
                        variant="ghost"
                        size="icon"
                        className={cn(
                          "h-10 w-10 rounded-full",
                          isRecording && "bg-red-500 text-white animate-pulse"
                        )}
                        onClick={() => setIsRecording(!isRecording)}
                      >
                        <Mic className="w-4 h-4" />
                      </Button>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent>Voice Input</TooltipContent>
                </Tooltip>

                {/* File Attachment */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-10 w-10 rounded-full">
                      <Paperclip className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Attach Files</TooltipContent>
                </Tooltip>

                {/* Enhanced Send Button */}
                <motion.div
                  whileTap={{ scale: 0.95 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <Button
                    onClick={isLoading ? onCancel : handleSend}
                    disabled={!prompt.trim() || disabled}
                    className={cn(
                      "h-10 w-10 rounded-full p-0 relative overflow-hidden",
                      "bg-gradient-to-r from-blue-500 to-purple-600",
                      "hover:from-blue-600 hover:to-purple-700",
                      "shadow-lg hover:shadow-xl transition-all duration-200",
                      "disabled:opacity-50 disabled:cursor-not-allowed"
                    )}
                  >
                    <AnimatePresence mode="wait">
                      {isLoading ? (
                        <motion.div
                          key="loading"
                          initial={{ opacity: 0, rotate: -90 }}
                          animate={{ opacity: 1, rotate: 0 }}
                          exit={{ opacity: 0, rotate: 90 }}
                          className="flex items-center justify-center"
                        >
                          <Square className="w-4 h-4" />
                        </motion.div>
                      ) : (
                        <motion.div
                          key="send"
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: 10 }}
                          className="flex items-center justify-center"
                        >
                          <Send className="w-4 h-4" />
                        </motion.div>
                      )}
                    </AnimatePresence>
                    
                    {/* Ripple effect on click */}
                    <motion.div
                      className="absolute inset-0 bg-white/20 rounded-full"
                      initial={{ scale: 0, opacity: 1 }}
                      animate={{ scale: 2, opacity: 0 }}
                      transition={{ duration: 0.6 }}
                    />
                  </Button>
                </motion.div>
              </div>
            </div>
          </div>

          {/* Quick Actions Bar */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="px-4 pb-3 border-t bg-muted/20"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm" className="h-7 text-xs">
                      <Code className="w-3 h-3 mr-1" />
                      Code Review
                    </Button>
                    <Button variant="ghost" size="sm" className="h-7 text-xs">
                      <TestTube className="w-3 h-3 mr-1" />
                      Write Tests
                    </Button>
                    <Button variant="ghost" size="sm" className="h-7 text-xs">
                      <FileText className="w-3 h-3 mr-1" />
                      Document
                    </Button>
                  </div>
                  
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">⌘</kbd>
                    <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Enter</kbd>
                    <span>to send</span>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </motion.div>
    </TooltipProvider>
  );
});

EnhancedFloatingPromptInput.displayName = "EnhancedFloatingPromptInput";