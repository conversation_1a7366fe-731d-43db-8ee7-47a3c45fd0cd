import React from "react";
import { Prism as Synta<PERSON><PERSON><PERSON>lighter } from "react-syntax-highlighter";
import { claudeSyntaxTheme } from "@/lib/claudeSyntaxTheme";
import { cn } from "@/lib/utils";

interface InlineCodeProps {
  children: React.ReactNode;
  className?: string;
}

// Language detection patterns
const languagePatterns: Array<{ pattern: RegExp; language: string }> = [
  // Function calls
  { pattern: /^\w+\s*\(.*\)$/, language: "javascript" },
  // Object/Array literals
  { pattern: /^(\{.*\}|\[.*\])$/, language: "javascript" },
  // CLI commands
  { pattern: /^(npm|yarn|pnpm|git|cd|ls|mkdir|rm|cp|mv|cat|echo|grep|find|curl|wget)\s+/, language: "bash" },
  // File paths
  { pattern: /^(\/|\.\/|\.\.\/|~\/|[A-Za-z]:[\\/]).*\.(js|ts|jsx|tsx|py|java|cpp|c|go|rs|rb|php)$/, language: "plaintext" },
  // Import/export statements
  { pattern: /^(import|export|from|require)\s+/, language: "javascript" },
  // Variable declarations
  { pattern: /^(const|let|var|def|int|float|double|string)\s+/, language: "javascript" },
  // HTML/JSX tags
  { pattern: /^<[^>]+>/, language: "html" },
  // CSS properties
  { pattern: /^[a-z-]+:\s*[^;]+;?$/, language: "css" },
  // SQL keywords
  { pattern: /^(SELECT|INSERT|UPDATE|DELETE|FROM|WHERE|JOIN|CREATE|DROP)\s+/i, language: "sql" },
  // Python specific
  { pattern: /^(def|class|if|elif|else|for|while|import|from|return)\s+/, language: "python" },
  // Type annotations
  { pattern: /^[A-Z]\w*(<.*>)?(\[\])?$/, language: "typescript" },
];

// Detect language from code content
const detectLanguage = (code: string): string | null => {
  const trimmedCode = code.trim();
  
  for (const { pattern, language } of languagePatterns) {
    if (pattern.test(trimmedCode)) {
      return language;
    }
  }
  
  // Check for common programming constructs
  if (trimmedCode.includes("=>") || trimmedCode.includes("function")) {
    return "javascript";
  }
  if (trimmedCode.includes("::") || trimmedCode.includes("->")) {
    return "cpp";
  }
  if (trimmedCode.includes(":=")) {
    return "go";
  }
  
  return null;
};

export const InlineCode: React.FC<InlineCodeProps> = ({ children, className }) => {
  const codeContent = String(children);
  const detectedLanguage = detectLanguage(codeContent);
  
  // If we can detect a language and it's a single line, apply syntax highlighting
  if (detectedLanguage && !codeContent.includes('\n')) {
    return (
      <span className={cn("inline-flex items-center", className)}>
        <SyntaxHighlighter
          style={claudeSyntaxTheme}
          language={detectedLanguage}
          PreTag="span"
          customStyle={{
            display: 'inline',
            padding: '0.125rem 0.375rem',
            margin: 0,
            borderRadius: '0.25rem',
            fontSize: '0.875em',
            background: 'rgba(139, 92, 246, 0.1)',
            border: '1px solid rgba(139, 92, 246, 0.2)',
          }}
          codeTagProps={{
            style: {
              fontSize: 'inherit',
              fontFamily: 'var(--font-mono)',
            }
          }}
        >
          {codeContent}
        </SyntaxHighlighter>
      </span>
    );
  }
  
  // Fallback to regular code styling
  return (
    <code 
      className={cn(
        "inline-flex items-center px-1.5 py-0.5 rounded text-sm",
        "bg-muted/50 border border-border/50",
        "font-mono",
        className
      )}
    >
      {children}
    </code>
  );
};

export default InlineCode;