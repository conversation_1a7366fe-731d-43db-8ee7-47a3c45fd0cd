import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Save,
  Clock,
  Trash2,
  FileText,
  RotateCcw,
  AlertCircle,
  CheckCircle,
  Loader2,
  Settings,
  History,
  Download,
  Upload,
  Search,
  Star,
  StarOff
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";


import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

import { ScrollArea } from "@/components/ui/scroll-area";

import { <PERSON><PERSON><PERSON>, Toolt<PERSON><PERSON>ontent, TooltipTrigger } from "@/components/ui/tooltip";

import { PremiumProgress } from "@/components/ui/premium-progress";
import { cn } from "@/lib/utils";

interface DraftMessage {
  id: string;
  content: string;
  timestamp: Date;
  sessionId: string;
  projectPath?: string;
  model?: string;
  attachments?: string[];
  tags?: string[];
  isStarred?: boolean;
  title?: string;
  wordCount: number;
  characterCount: number;
}

interface AutoSaveSettings {
  enabled: boolean;
  interval: number; // in seconds
  maxDrafts: number;
  autoCleanup: boolean;
  cleanupAfterDays: number;
  saveOnTyping: boolean;
  typingDelay: number; // in milliseconds
  backupToCloud: boolean;
  compressionEnabled: boolean;
}

interface AutoSaveDraftsProps {
  currentContent: string;
  sessionId: string;
  projectPath?: string;
  model?: string;
  onRestoreDraft: (content: string) => void;
  onSettingsChange?: (settings: AutoSaveSettings) => void;
  className?: string;
}

const DEFAULT_SETTINGS: AutoSaveSettings = {
  enabled: true,
  interval: 30,
  maxDrafts: 50,
  autoCleanup: true,
  cleanupAfterDays: 7,
  saveOnTyping: true,
  typingDelay: 2000,
  backupToCloud: false,
  compressionEnabled: true
};

const STORAGE_KEYS = {
  DRAFTS: 'claude-code-session-drafts',
  SETTINGS: 'claude-code-session-autosave-settings',
  LAST_SAVE: 'claude-code-session-last-save'
};

export const AutoSaveDrafts: React.FC<AutoSaveDraftsProps> = ({
  currentContent,
  sessionId,
  projectPath,
  model,
  onRestoreDraft,
  onSettingsChange,
  className
}) => {
  const [drafts, setDrafts] = useState<DraftMessage[]>([]);
  const [settings, setSettings] = useState<AutoSaveSettings>(DEFAULT_SETTINGS);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSaveTime, setLastSaveTime] = useState<Date | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<'timestamp' | 'title' | 'wordCount'>('timestamp');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showSettings, setShowSettings] = useState(false);
  const [showDraftsDialog, setShowDraftsDialog] = useState(false);


  // Load settings and drafts from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem(STORAGE_KEYS.SETTINGS);
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...DEFAULT_SETTINGS, ...parsed });
      } catch (error) {
        console.error('Failed to parse auto-save settings:', error);
      }
    }

    const savedDrafts = localStorage.getItem(STORAGE_KEYS.DRAFTS);
    if (savedDrafts) {
      try {
        const parsed = JSON.parse(savedDrafts).map((draft: any) => ({
          ...draft,
          timestamp: new Date(draft.timestamp)
        }));
        setDrafts(parsed);
      } catch (error) {
        console.error('Failed to parse drafts:', error);
      }
    }

    const lastSave = localStorage.getItem(STORAGE_KEYS.LAST_SAVE);
    if (lastSave) {
      setLastSaveTime(new Date(lastSave));
    }
  }, []);

  // Save settings to localStorage
  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(settings));
    onSettingsChange?.(settings);
  }, [settings, onSettingsChange]);

  // Save drafts to localStorage
  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(drafts));
  }, [drafts]);

  // Auto-save functionality
  const saveDraft = useCallback(async (content: string, isManual = false) => {
    if (!settings.enabled || (!isManual && content.trim().length < 10)) {
      return;
    }

    setIsAutoSaving(true);

    try {
      const now = new Date();
      const wordCount = content.trim().split(/\s+/).length;
      const characterCount = content.length;

      const newDraft: DraftMessage = {
        id: `draft-${now.getTime()}-${Math.random().toString(36).substr(2, 9)}`,
        content,
        timestamp: now,
        sessionId,
        projectPath,
        model,
        wordCount,
        characterCount,
        title: content.slice(0, 50).trim() + (content.length > 50 ? '...' : ''),
        tags: [],
        isStarred: false
      };

      setDrafts(prev => {
        const updated = [newDraft, ...prev];
        
        // Limit number of drafts
        if (updated.length > settings.maxDrafts) {
          return updated.slice(0, settings.maxDrafts);
        }
        
        return updated;
      });

      setLastSaveTime(now);
      localStorage.setItem(STORAGE_KEYS.LAST_SAVE, now.toISOString());

      // Auto-cleanup old drafts
      if (settings.autoCleanup) {
        const cutoffDate = new Date(now.getTime() - (settings.cleanupAfterDays * 24 * 60 * 60 * 1000));
        setDrafts(prev => prev.filter(draft => draft.timestamp > cutoffDate));
      }
    } catch (error) {
      console.error('Failed to save draft:', error);
    } finally {
      setIsAutoSaving(false);
    }
  }, [settings, sessionId, projectPath, model]);

  // Auto-save timer
  useEffect(() => {
    if (!settings.enabled || !currentContent.trim()) {
      return;
    }

    const timer = setTimeout(() => {
      saveDraft(currentContent);
    }, settings.interval * 1000);

    return () => clearTimeout(timer);
  }, [currentContent, settings.enabled, settings.interval, saveDraft]);

  // Typing delay auto-save
  useEffect(() => {
    if (!settings.enabled || !settings.saveOnTyping || !currentContent.trim()) {
      return;
    }

    const timer = setTimeout(() => {
      saveDraft(currentContent);
    }, settings.typingDelay);

    return () => clearTimeout(timer);
  }, [currentContent, settings.enabled, settings.saveOnTyping, settings.typingDelay, saveDraft]);

  // Filter and sort drafts
  const filteredAndSortedDrafts = useMemo(() => {
    let filtered = drafts;

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(draft => 
        draft.content.toLowerCase().includes(query) ||
        draft.title?.toLowerCase().includes(query) ||
        draft.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Filter by selected tags
    if (selectedTags.length > 0) {
      filtered = filtered.filter(draft => 
        draft.tags?.some(tag => selectedTags.includes(tag))
      );
    }

    // Sort
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'timestamp':
          comparison = a.timestamp.getTime() - b.timestamp.getTime();
          break;
        case 'title':
          comparison = (a.title || '').localeCompare(b.title || '');
          break;
        case 'wordCount':
          comparison = a.wordCount - b.wordCount;
          break;
      }
      
      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [drafts, searchQuery, selectedTags, sortBy, sortOrder]);

  // Get all unique tags
  const allTags = useMemo(() => {
    const tags = new Set<string>();
    drafts.forEach(draft => {
      draft.tags?.forEach(tag => tags.add(tag));
    });
    return Array.from(tags).sort();
  }, [drafts]);

  const updateSettings = <K extends keyof AutoSaveSettings>(
    key: K,
    value: AutoSaveSettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const deleteDraft = (draftId: string) => {
    setDrafts(prev => prev.filter(draft => draft.id !== draftId));
  };

  const toggleStarDraft = (draftId: string) => {
    setDrafts(prev => prev.map(draft => 
      draft.id === draftId 
        ? { ...draft, isStarred: !draft.isStarred }
        : draft
    ));
  };





  const exportDrafts = () => {
    const dataStr = JSON.stringify(drafts, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `claude-drafts-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const importDrafts = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string);
        const validDrafts = imported.filter((draft: any) => 
          draft.id && draft.content && draft.timestamp
        ).map((draft: any) => ({
          ...draft,
          timestamp: new Date(draft.timestamp)
        }));
        
        setDrafts(prev => [...validDrafts, ...prev]);
      } catch (error) {
        console.error('Failed to import drafts:', error);
      }
    };
    reader.readAsText(file);
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const getNextSaveTime = () => {
    if (!settings.enabled || !lastSaveTime) return null;
    return new Date(lastSaveTime.getTime() + (settings.interval * 1000));
  };

  const nextSaveTime = getNextSaveTime();
  const timeUntilNextSave = nextSaveTime ? Math.max(0, nextSaveTime.getTime() - Date.now()) : 0;
  const progressPercentage = nextSaveTime ? 
    Math.max(0, 100 - (timeUntilNextSave / (settings.interval * 1000)) * 100) : 0;

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {/* Auto-save Status */}
      <div className="flex items-center gap-2">
        {isAutoSaving ? (
          <div className="flex items-center gap-1 text-blue-600">
            <Loader2 className="h-3 w-3 animate-spin" />
            <span className="text-xs">Saving...</span>
          </div>
        ) : settings.enabled ? (
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-1 text-green-600">
                <CheckCircle className="h-3 w-3" />
                <span className="text-xs">Auto-save on</span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1">
                <p>Auto-save enabled</p>
                {lastSaveTime && (
                  <p className="text-xs text-muted-foreground">
                    Last saved: {formatTimeAgo(lastSaveTime)}
                  </p>
                )}
                {nextSaveTime && (
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">
                      Next save in: {Math.ceil(timeUntilNextSave / 1000)}s
                    </p>
                    <PremiumProgress value={progressPercentage} className="h-1" />
                  </div>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        ) : (
          <div className="flex items-center gap-1 text-muted-foreground">
            <AlertCircle className="h-3 w-3" />
            <span className="text-xs">Auto-save off</span>
          </div>
        )}
      </div>

      {/* Manual Save Button */}
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => saveDraft(currentContent, true)}
            disabled={!currentContent.trim() || isAutoSaving}
            className="h-7 w-7 p-0"
          >
            <Save className="h-3 w-3" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>Save draft manually</TooltipContent>
      </Tooltip>

      {/* Drafts Manager */}
      <Dialog open={showDraftsDialog} onOpenChange={setShowDraftsDialog}>
        <DialogTrigger asChild>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 relative">
                <History className="h-3 w-3" />
                {drafts.length > 0 && (
                  <Badge 
                    variant="secondary" 
                    className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs flex items-center justify-center"
                  >
                    {drafts.length}
                  </Badge>
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>View saved drafts ({drafts.length})</TooltipContent>
          </Tooltip>
        </DialogTrigger>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <History className="h-5 w-5" />
              Saved Drafts ({drafts.length})
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* Search and Filters */}
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
                <Input
                  placeholder="Search drafts..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-7 h-8"
                />
              </div>
              
              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-32 h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="timestamp">Date</SelectItem>
                  <SelectItem value="title">Title</SelectItem>
                  <SelectItem value="wordCount">Length</SelectItem>
                </SelectContent>
              </Select>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc')}
                className="h-8 w-8 p-0"
              >
                {sortOrder === 'desc' ? '↓' : '↑'}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={exportDrafts}
                className="h-8"
              >
                <Download className="h-3 w-3 mr-1" />
                Export
              </Button>
              
              <label className="cursor-pointer">
                <input
                  type="file"
                  accept=".json"
                  onChange={importDrafts}
                  className="hidden"
                />
                <Button variant="outline" size="sm" className="h-8" asChild>
                  <span>
                    <Upload className="h-3 w-3 mr-1" />
                    Import
                  </span>
                </Button>
              </label>
            </div>

            {/* Tags Filter */}
            {allTags.length > 0 && (
              <div className="flex items-center gap-2 flex-wrap">
                <Label className="text-xs text-muted-foreground">Tags:</Label>
                {allTags.map(tag => (
                  <Badge
                    key={tag}
                    variant={selectedTags.includes(tag) ? "default" : "secondary"}
                    className="cursor-pointer text-xs"
                    onClick={() => {
                      setSelectedTags(prev => 
                        prev.includes(tag)
                          ? prev.filter(t => t !== tag)
                          : [...prev, tag]
                      );
                    }}
                  >
                    {tag}
                  </Badge>
                ))}
                {selectedTags.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedTags([])}
                    className="h-6 text-xs"
                  >
                    Clear
                  </Button>
                )}
              </div>
            )}

            {/* Drafts List */}
            <ScrollArea className="h-96">
              <div className="space-y-2">
                <AnimatePresence>
                  {filteredAndSortedDrafts.map(draft => (
                    <motion.div
                      key={draft.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="group"
                    >
                      <Card className="hover:shadow-md transition-shadow">
                        <CardContent className="p-3">
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-sm truncate">
                                  {draft.title || 'Untitled Draft'}
                                </h4>
                                {draft.isStarred && (
                                  <Star className="h-3 w-3 text-yellow-500 fill-current" />
                                )}
                              </div>
                              
                              <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                                {draft.content}
                              </p>
                              
                              <div className="flex items-center gap-3 text-xs text-muted-foreground">
                                <span className="flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {formatTimeAgo(draft.timestamp)}
                                </span>
                                <span>{draft.wordCount} words</span>
                                {draft.model && (
                                  <Badge variant="outline" className="text-xs">
                                    {draft.model}
                                  </Badge>
                                )}
                              </div>
                              
                              {draft.tags && draft.tags.length > 0 && (
                                <div className="flex items-center gap-1 mt-2">
                                  {draft.tags.map(tag => (
                                    <Badge key={tag} variant="secondary" className="text-xs">
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                            </div>
                            
                            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => toggleStarDraft(draft.id)}
                                    className="h-6 w-6 p-0"
                                  >
                                    {draft.isStarred ? (
                                      <Star className="h-3 w-3 text-yellow-500 fill-current" />
                                    ) : (
                                      <StarOff className="h-3 w-3" />
                                    )}
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  {draft.isStarred ? 'Unstar' : 'Star'} draft
                                </TooltipContent>
                              </Tooltip>
                              
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      onRestoreDraft(draft.content);
                                      setShowDraftsDialog(false);
                                    }}
                                    className="h-6 w-6 p-0"
                                  >
                                    <RotateCcw className="h-3 w-3" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>Restore draft</TooltipContent>
                              </Tooltip>
                              
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => deleteDraft(draft.id)}
                                    className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>Delete draft</TooltipContent>
                              </Tooltip>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </AnimatePresence>
                
                {filteredAndSortedDrafts.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No drafts found</p>
                    {searchQuery && (
                      <p className="text-sm mt-1">Try adjusting your search or filters</p>
                    )}
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>

      {/* Settings */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogTrigger asChild>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                <Settings className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Auto-save settings</TooltipContent>
          </Tooltip>
        </DialogTrigger>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Auto-save Settings
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Enable Auto-save</Label>
              <Switch
                checked={settings.enabled}
                onCheckedChange={(checked) => updateSettings('enabled', checked)}
              />
            </div>
            
            {settings.enabled && (
              <>
                <div className="space-y-2">
                  <Label>Save Interval (seconds)</Label>
                  <Select
                    value={settings.interval.toString()}
                    onValueChange={(value) => updateSettings('interval', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="15">15 seconds</SelectItem>
                      <SelectItem value="30">30 seconds</SelectItem>
                      <SelectItem value="60">1 minute</SelectItem>
                      <SelectItem value="120">2 minutes</SelectItem>
                      <SelectItem value="300">5 minutes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Maximum Drafts</Label>
                  <Select
                    value={settings.maxDrafts.toString()}
                    onValueChange={(value) => updateSettings('maxDrafts', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="25">25 drafts</SelectItem>
                      <SelectItem value="50">50 drafts</SelectItem>
                      <SelectItem value="100">100 drafts</SelectItem>
                      <SelectItem value="200">200 drafts</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center justify-between">
                  <Label>Save on Typing</Label>
                  <Switch
                    checked={settings.saveOnTyping}
                    onCheckedChange={(checked) => updateSettings('saveOnTyping', checked)}
                  />
                </div>
                
                {settings.saveOnTyping && (
                  <div className="space-y-2">
                    <Label>Typing Delay (milliseconds)</Label>
                    <Select
                      value={settings.typingDelay.toString()}
                      onValueChange={(value) => updateSettings('typingDelay', parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1000">1 second</SelectItem>
                        <SelectItem value="2000">2 seconds</SelectItem>
                        <SelectItem value="3000">3 seconds</SelectItem>
                        <SelectItem value="5000">5 seconds</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
                
                <div className="flex items-center justify-between">
                  <Label>Auto Cleanup</Label>
                  <Switch
                    checked={settings.autoCleanup}
                    onCheckedChange={(checked) => updateSettings('autoCleanup', checked)}
                  />
                </div>
                
                {settings.autoCleanup && (
                  <div className="space-y-2">
                    <Label>Cleanup After (days)</Label>
                    <Select
                      value={settings.cleanupAfterDays.toString()}
                      onValueChange={(value) => updateSettings('cleanupAfterDays', parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="3">3 days</SelectItem>
                        <SelectItem value="7">1 week</SelectItem>
                        <SelectItem value="14">2 weeks</SelectItem>
                        <SelectItem value="30">1 month</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};