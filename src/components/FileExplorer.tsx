import React, { useState, useEffect, useCallback } from "react";
import {
  ChevronRight,
  ChevronDown,
  File,
  Folder,
  FolderOpen,
  FileText,
  FileCode,
  Image,
  FileJson,
  FileType,
  GitBranch,
  RefreshCw,
  Search,
  Filter,
  MoreVertical,
  Plus,
  Trash2,
  Edit,
  Copy,
  Scissors,
  Clipboard
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { api } from "@/lib/api";
import { useToast } from "@/contexts/ToastContext";
import { readDir, FileEntry } from "@tauri-apps/plugin-fs";

interface FileNode {
  name: string;
  path: string;
  type: "file" | "directory";
  children?: FileNode[];
  size?: number;
  modified?: string;
  extension?: string;
}

interface FileExplorerProps {
  projectPath: string;
  onFileSelect?: (path: string) => void;
  onFileOpen?: (path: string) => void;
  selectedPath?: string;
  className?: string;
  showHidden?: boolean;
  fileFilter?: (file: FileNode) => boolean;
}

// Get file icon based on extension
const getFileIcon = (file: FileNode) => {
  if (file.type === "directory") {
    return file.children ? FolderOpen : Folder;
  }

  const ext = file.extension?.toLowerCase();
  switch (ext) {
    case "ts":
    case "tsx":
    case "js":
    case "jsx":
    case "py":
    case "java":
    case "cpp":
    case "c":
    case "cs":
    case "go":
    case "rs":
      return FileCode;
    case "json":
    case "yaml":
    case "yml":
    case "toml":
      return FileJson;
    case "md":
    case "mdx":
    case "txt":
    case "doc":
    case "docx":
      return FileText;
    case "png":
    case "jpg":
    case "jpeg":
    case "gif":
    case "svg":
    case "webp":
      return Image;
    case "git":
    case "gitignore":
      return GitBranch;
    default:
      return File;
  }
};

const FileTreeNode: React.FC<{
  node: FileNode;
  level: number;
  selectedPath?: string;
  expandedPaths: Set<string>;
  onToggle: (path: string) => void;
  onSelect: (path: string) => void;
  onOpen: (path: string) => void;
  onContextMenu: (e: React.MouseEvent, node: FileNode) => void;
}> = ({
  node,
  level,
  selectedPath,
  expandedPaths,
  onToggle,
  onSelect,
  onOpen,
  onContextMenu
}) => {
  const isExpanded = expandedPaths.has(node.path);
  const isSelected = selectedPath === node.path;
  const Icon = getFileIcon(node);
  const hasChildren = node.type === "directory" && node.children && node.children.length > 0;

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect(node.path);
    if (node.type === "directory") {
      onToggle(node.path);
    }
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (node.type === "file") {
      onOpen(node.path);
    }
  };

  return (
    <div>
      <ContextMenuTrigger>
        <div
          className={cn(
            "flex items-center gap-1 px-2 py-1 cursor-pointer hover:bg-accent/50 rounded-sm",
            isSelected && "bg-accent"
          )}
          style={{ paddingLeft: `${level * 12 + 8}px` }}
          onClick={handleClick}
          onDoubleClick={handleDoubleClick}
          onContextMenu={(e) => onContextMenu(e, node)}
        >
          {node.type === "directory" && (
            <div className="w-4 h-4 flex items-center justify-center">
              {hasChildren && (
                isExpanded ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )
              )}
            </div>
          )}
          <Icon className="h-4 w-4 flex-shrink-0" />
          <span className="text-sm truncate flex-1">{node.name}</span>
        </div>
      </ContextMenuTrigger>

      {isExpanded && node.children && (
        <div>
          {node.children.map((child) => (
            <FileTreeNode
              key={child.path}
              node={child}
              level={level + 1}
              selectedPath={selectedPath}
              expandedPaths={expandedPaths}
              onToggle={onToggle}
              onSelect={onSelect}
              onOpen={onOpen}
              onContextMenu={onContextMenu}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const FileExplorer: React.FC<FileExplorerProps> = ({
  projectPath,
  onFileSelect,
  onFileOpen,
  selectedPath,
  className,
  showHidden = false,
  fileFilter
}) => {
  const [fileTree, setFileTree] = useState<FileNode | null>(null);
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(false);
  const { addToast } = useToast();

  // Load directory recursively
  const loadDirectoryRecursive = useCallback(async (dirPath: string, depth: number = 0, maxDepth: number = 3): Promise<FileNode> => {
    const name = dirPath.split('/').pop() || 'root';
    const node: FileNode = {
      name,
      path: dirPath,
      type: 'directory',
      children: []
    };

    // Don't go too deep to avoid performance issues
    if (depth >= maxDepth) {
      return node;
    }

    try {
      const entries = await readDir(dirPath);
      
      for (const entry of entries) {
        // Skip hidden files if not showing them
        if (!showHidden && entry.name.startsWith('.')) {
          continue;
        }

        // Skip common ignore patterns
        if (entry.name === 'node_modules' || entry.name === '.git' || entry.name === 'target' || entry.name === 'dist') {
          continue;
        }

        const childPath = `${dirPath}/${entry.name}`;
        
        if (entry.isDirectory) {
          // Recursively load subdirectories
          const childNode = await loadDirectoryRecursive(childPath, depth + 1, maxDepth);
          node.children?.push(childNode);
        } else {
          // Add file node
          const fileNode: FileNode = {
            name: entry.name,
            path: childPath,
            type: 'file',
            extension: entry.name.split('.').pop()
          };
          node.children?.push(fileNode);
        }
      }

      // Sort children: directories first, then files
      node.children?.sort((a, b) => {
        if (a.type === b.type) {
          return a.name.localeCompare(b.name);
        }
        return a.type === 'directory' ? -1 : 1;
      });
    } catch (error) {
      console.error(`Failed to read directory ${dirPath}:`, error);
    }

    return node;
  }, [showHidden]);

  // Load file tree
  const loadFileTree = useCallback(async () => {
    if (!projectPath) return;

    try {
      setLoading(true);
      const tree = await loadDirectoryRecursive(projectPath);
      setFileTree(tree);
      
      // Auto-expand root
      setExpandedPaths(new Set([projectPath]));
    } catch (error) {
      console.error("Failed to load file tree:", error);
      addToast({
        title: "Failed to load files",
        description: "Could not read the project directory",
        variant: "error"
      });
    } finally {
      setLoading(false);
    }
  }, [projectPath, loadDirectoryRecursive, addToast]);

  // Filter tree based on search
  const filterTree = useCallback((node: FileNode, query: string): FileNode | null => {
    if (!query) return node;

    const matches = node.name.toLowerCase().includes(query.toLowerCase());
    
    if (node.type === "file") {
      return matches && (!fileFilter || fileFilter(node)) ? node : null;
    }

    const filteredChildren = node.children
      ?.map(child => filterTree(child, query))
      .filter(Boolean) as FileNode[] | undefined;

    if (matches || (filteredChildren && filteredChildren.length > 0)) {
      return {
        ...node,
        children: filteredChildren
      };
    }

    return null;
  }, [fileFilter]);

  // Toggle expanded state
  const handleToggle = useCallback((path: string) => {
    setExpandedPaths(prev => {
      const next = new Set(prev);
      if (next.has(path)) {
        next.delete(path);
      } else {
        next.add(path);
      }
      return next;
    });
  }, []);

  // Handle file selection
  const handleSelect = useCallback((path: string) => {
    onFileSelect?.(path);
  }, [onFileSelect]);

  // Handle file open
  const handleOpen = useCallback((path: string) => {
    onFileOpen?.(path);
  }, [onFileOpen]);

  // Handle context menu
  const handleContextMenu = useCallback((e: React.MouseEvent, node: FileNode) => {
    e.preventDefault();
    // Context menu is handled by ContextMenu component
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    loadFileTree();
  }, [loadFileTree]);

  // Load initial tree
  useEffect(() => {
    loadFileTree();
  }, [loadFileTree]);

  const filteredTree = fileTree ? filterTree(fileTree, searchQuery) : null;

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-2 border-b">
        <h3 className="text-sm font-medium">Explorer</h3>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={cn("h-3 w-3", loading && "animate-spin")} />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <MoreVertical className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Plus className="h-4 w-4 mr-2" />
                New File
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Folder className="h-4 w-4 mr-2" />
                New Folder
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Filter className="h-4 w-4 mr-2" />
                Show Hidden Files
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Search */}
      <div className="p-2 border-b">
        <div className="relative">
          <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8 h-8"
          />
        </div>
      </div>

      {/* File Tree */}
      <ScrollArea className="flex-1">
        <div className="p-1">
          {loading ? (
            <div className="flex items-center justify-center p-4">
              <RefreshCw className="h-4 w-4 animate-spin" />
            </div>
          ) : filteredTree ? (
            <FileTreeNode
              node={filteredTree}
              level={0}
              selectedPath={selectedPath}
              expandedPaths={expandedPaths}
              onToggle={handleToggle}
              onSelect={handleSelect}
              onOpen={handleOpen}
              onContextMenu={handleContextMenu}
            />
          ) : (
            <div className="text-center text-sm text-muted-foreground p-4">
              No files found
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Context Menu */}
      <ContextMenu>
        <ContextMenuContent>
          <ContextMenuItem>
            <FileText className="h-4 w-4 mr-2" />
            Open
          </ContextMenuItem>
          <ContextMenuItem>
            <Edit className="h-4 w-4 mr-2" />
            Rename
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuItem>
            <Copy className="h-4 w-4 mr-2" />
            Copy
          </ContextMenuItem>
          <ContextMenuItem>
            <Scissors className="h-4 w-4 mr-2" />
            Cut
          </ContextMenuItem>
          <ContextMenuItem>
            <Clipboard className="h-4 w-4 mr-2" />
            Paste
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuItem className="text-destructive">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>
    </div>
  );
};

export default FileExplorer;