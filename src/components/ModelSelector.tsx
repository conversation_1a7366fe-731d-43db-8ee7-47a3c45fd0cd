import React, { useState } from "react";
import {
  Sparkles,
  Zap,
  Brain,
  Cpu,
  ChevronDown,
  Check,
  Info,
  Crown,
  Gauge,
  DollarSign
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import type { ModelType } from "@/types/session";

interface ModelInfo {
  id: ModelType;
  name: string;
  provider: "Anthropic" | "OpenAI" | "Google" | "Other";
  category: "flagship" | "fast" | "efficient" | "legacy";
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  capabilities: string[];
  contextWindow: number;
  costPerMillion: {
    input: number;
    output: number;
  };
  speed: "fast" | "medium" | "slow";
  recommended?: boolean;
}

const MODELS: ModelInfo[] = [
  {
    id: "claude-3-5-sonnet-20241022",
    name: "Claude 3.5 Sonnet",
    provider: "Anthropic",
    category: "flagship",
    description: "Most intelligent model, best for complex tasks",
    icon: Crown,
    capabilities: ["Advanced reasoning", "Code generation", "Creative writing", "Analysis"],
    contextWindow: 200000,
    costPerMillion: { input: 3, output: 15 },
    speed: "fast",
    recommended: true
  },
  {
    id: "claude-3-opus-20240229",
    name: "Claude 3 Opus",
    provider: "Anthropic",
    category: "flagship",
    description: "Powerful model for research and analysis",
    icon: Brain,
    capabilities: ["Deep analysis", "Research", "Complex reasoning"],
    contextWindow: 200000,
    costPerMillion: { input: 15, output: 75 },
    speed: "slow"
  },
  {
    id: "claude-3-sonnet-20240229",
    name: "Claude 3 Sonnet",
    provider: "Anthropic",
    category: "fast",
    description: "Balanced performance and cost",
    icon: Zap,
    capabilities: ["General tasks", "Code assistance", "Writing"],
    contextWindow: 200000,
    costPerMillion: { input: 3, output: 15 },
    speed: "medium"
  },
  {
    id: "claude-3-haiku-20240307",
    name: "Claude 3 Haiku",
    provider: "Anthropic",
    category: "efficient",
    description: "Fast and cost-effective for simple tasks",
    icon: Gauge,
    capabilities: ["Quick responses", "Simple tasks", "High volume"],
    contextWindow: 200000,
    costPerMillion: { input: 0.25, output: 1.25 },
    speed: "fast"
  }
];

interface ModelSelectorProps {
  value: ModelType;
  onChange: (model: ModelType) => void;
  disabled?: boolean;
  showDetails?: boolean;
  className?: string;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  value,
  onChange,
  disabled = false,
  showDetails = true,
  className
}) => {
  const [open, setOpen] = useState(false);
  const selectedModel = MODELS.find(m => m.id === value) || MODELS[0];
  const Icon = selectedModel.icon;

  const getCategoryColor = (category: ModelInfo['category']) => {
    switch (category) {
      case 'flagship': return 'text-purple-500';
      case 'fast': return 'text-blue-500';
      case 'efficient': return 'text-green-500';
      case 'legacy': return 'text-gray-500';
    }
  };

  const getSpeedIcon = (speed: ModelInfo['speed']) => {
    switch (speed) {
      case 'fast': return <Zap className="h-3 w-3 text-green-500" />;
      case 'medium': return <Gauge className="h-3 w-3 text-yellow-500" />;
      case 'slow': return <Brain className="h-3 w-3 text-blue-500" />;
    }
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <Button
          variant="outline"
          className={cn("justify-between", className)}
        >
          <div className="flex items-center gap-2">
            <Icon className="h-4 w-4" />
            <span>{selectedModel.name}</span>
            {selectedModel.recommended && (
              <Badge variant="secondary" className="ml-1 text-xs">
                Recommended
              </Badge>
            )}
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[400px]">
        <DropdownMenuLabel>Select AI Model</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {['flagship', 'fast', 'efficient'].map((category) => {
          const categoryModels = MODELS.filter(m => m.category === category);
          if (categoryModels.length === 0) return null;
          
          return (
            <div key={category}>
              <DropdownMenuLabel className="text-xs uppercase text-muted-foreground">
                {category} Models
              </DropdownMenuLabel>
              {categoryModels.map((model) => {
                const ModelIcon = model.icon;
                const isSelected = model.id === value;
                
                return (
                  <DropdownMenuItem
                    key={model.id}
                    onClick={() => {
                      onChange(model.id);
                      setOpen(false);
                    }}
                    className="p-3 cursor-pointer"
                  >
                    <div className="flex items-start gap-3 w-full">
                      <div className={cn(
                        "p-2 rounded-lg",
                        isSelected ? "bg-primary/10" : "bg-muted"
                      )}>
                        <ModelIcon className={cn(
                          "h-4 w-4",
                          getCategoryColor(model.category)
                        )} />
                      </div>
                      
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{model.name}</span>
                            {model.recommended && (
                              <Badge variant="secondary" className="text-xs">
                                Recommended
                              </Badge>
                            )}
                          </div>
                          {isSelected && (
                            <Check className="h-4 w-4 text-primary" />
                          )}
                        </div>
                        
                        <p className="text-xs text-muted-foreground">
                          {model.description}
                        </p>
                        
                        {showDetails && (
                          <div className="flex items-center gap-4 mt-2">
                            <div className="flex items-center gap-1">
                              {getSpeedIcon(model.speed)}
                              <span className="text-xs text-muted-foreground">
                                {model.speed}
                              </span>
                            </div>
                            
                            <div className="flex items-center gap-1">
                              <Info className="h-3 w-3 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground">
                                {(model.contextWindow / 1000).toFixed(0)}k context
                              </span>
                            </div>
                            
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-3 w-3 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground">
                                ${model.costPerMillion.input}/{model.costPerMillion.output}
                              </span>
                            </div>
                          </div>
                        )}
                        
                        {showDetails && model.capabilities.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {model.capabilities.map((cap, idx) => (
                              <Badge
                                key={idx}
                                variant="outline"
                                className="text-xs py-0"
                              >
                                {cap}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </DropdownMenuItem>
                );
              })}
              <DropdownMenuSeparator />
            </div>
          );
        })}
        
        <div className="p-2 text-xs text-muted-foreground">
          <p>Cost shown as $ per million tokens (input/output)</p>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Compact version for toolbar use
export const CompactModelSelector: React.FC<ModelSelectorProps> = ({
  value,
  onChange,
  disabled = false,
  className
}) => {
  const selectedModel = MODELS.find(m => m.id === value) || MODELS[0];
  const Icon = selectedModel.icon;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <Button
          variant="ghost"
          size="sm"
          className={cn("gap-2", className)}
        >
          <Icon className="h-4 w-4" />
          <span className="hidden sm:inline">{selectedModel.name}</span>
          <ChevronDown className="h-3 w-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {MODELS.map((model) => {
          const ModelIcon = model.icon;
          return (
            <DropdownMenuItem
              key={model.id}
              onClick={() => onChange(model.id)}
              className="gap-2"
            >
              <ModelIcon className="h-4 w-4" />
              {model.name}
              {model.id === value && <Check className="h-4 w-4 ml-auto" />}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ModelSelector;