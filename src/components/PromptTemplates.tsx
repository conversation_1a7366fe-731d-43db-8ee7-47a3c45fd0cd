import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  FileText,
  Plus,
  Search,
  Edit2,
  Trash2,
  Copy,
  Download,
  Upload,
  FolderOpen,
  Tag,
  Clock,
  Star,
  StarOff,
  ChevronRight,
  Code,
  MessageSquare,
  Sparkles,
  Settings,
  Filter,
  SortAsc,
  MoreVertical,
  Save,
  X,
  Check,
  AlertCircle,
  Loader2,
  Hash,
  Calendar,
  User,
  Zap,
  FileCode,
  Bug,
  GitBranch,
  Database,
  Shield,
  Palette,
  Globe,
  BookOpen
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { useToast } from "@/contexts/ToastContext";
import { save, open } from "@tauri-apps/plugin-dialog";
import { writeTextFile, readTextFile } from "@tauri-apps/plugin-fs";

export interface PromptTemplate {
  id: string;
  title: string;
  description: string;
  prompt: string;
  category: string;
  tags: string[];
  variables: TemplateVariable[];
  favorite: boolean;
  usageCount: number;
  createdAt: string;
  updatedAt: string;
  author?: string;
  icon?: string;
}

export interface TemplateVariable {
  name: string;
  description: string;
  defaultValue?: string;
  required: boolean;
  type: "text" | "number" | "boolean" | "select" | "multiline";
  options?: string[]; // For select type
}

interface PromptTemplatesProps {
  onUseTemplate: (prompt: string) => void;
  className?: string;
}

const DEFAULT_CATEGORIES = [
  { id: "code", label: "Code", icon: Code },
  { id: "debug", label: "Debug", icon: Bug },
  { id: "refactor", label: "Refactor", icon: GitBranch },
  { id: "documentation", label: "Documentation", icon: BookOpen },
  { id: "testing", label: "Testing", icon: Shield },
  { id: "database", label: "Database", icon: Database },
  { id: "ui", label: "UI/UX", icon: Palette },
  { id: "api", label: "API", icon: Globe },
  { id: "general", label: "General", icon: MessageSquare },
  { id: "custom", label: "Custom", icon: Sparkles }
];

const BUILTIN_TEMPLATES: PromptTemplate[] = [
  {
    id: "code-review",
    title: "Code Review",
    description: "Comprehensive code review with best practices",
    prompt: "Please review this {{language}} code for:\n- Code quality and readability\n- Performance issues\n- Security vulnerabilities\n- Best practices\n- Potential bugs\n\nCode:\n{{code}}\n\nProvide specific suggestions for improvements.",
    category: "code",
    tags: ["review", "quality", "best-practices"],
    variables: [
      { name: "language", description: "Programming language", defaultValue: "TypeScript", required: true, type: "text" },
      { name: "code", description: "Code to review", required: true, type: "multiline" }
    ],
    favorite: true,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: "System",
    icon: "🔍"
  },
  {
    id: "explain-code",
    title: "Explain Code",
    description: "Get detailed explanation of code functionality",
    prompt: "Please explain this {{language}} code in detail:\n\n{{code}}\n\nExplain:\n1. What this code does\n2. How it works step by step\n3. Any important concepts or patterns used\n4. Potential use cases",
    category: "code",
    tags: ["explain", "learning", "documentation"],
    variables: [
      { name: "language", description: "Programming language", defaultValue: "TypeScript", required: true, type: "text" },
      { name: "code", description: "Code to explain", required: true, type: "multiline" }
    ],
    favorite: false,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: "System",
    icon: "📚"
  },
  {
    id: "fix-bug",
    title: "Fix Bug",
    description: "Debug and fix code issues",
    prompt: "I'm experiencing this issue: {{issue}}\n\nHere's my code:\n{{code}}\n\nError message (if any):\n{{error}}\n\nPlease help me:\n1. Identify the root cause\n2. Provide a fix\n3. Explain why this happened\n4. Suggest how to prevent similar issues",
    category: "debug",
    tags: ["debug", "fix", "troubleshooting"],
    variables: [
      { name: "issue", description: "Describe the issue", required: true, type: "text" },
      { name: "code", description: "Problematic code", required: true, type: "multiline" },
      { name: "error", description: "Error message", required: false, type: "multiline" }
    ],
    favorite: true,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: "System",
    icon: "🐛"
  },
  {
    id: "optimize-performance",
    title: "Optimize Performance",
    description: "Improve code performance and efficiency",
    prompt: "Please optimize this {{language}} code for better performance:\n\n{{code}}\n\nFocus on:\n- Time complexity improvements\n- Memory usage optimization\n- {{specific_focus}}\n\nProvide the optimized code with explanations.",
    category: "refactor",
    tags: ["performance", "optimization", "refactor"],
    variables: [
      { name: "language", description: "Programming language", defaultValue: "TypeScript", required: true, type: "text" },
      { name: "code", description: "Code to optimize", required: true, type: "multiline" },
      { name: "specific_focus", description: "Specific optimization focus", defaultValue: "Overall efficiency", required: false, type: "text" }
    ],
    favorite: false,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: "System",
    icon: "⚡"
  },
  {
    id: "write-tests",
    title: "Write Unit Tests",
    description: "Generate comprehensive unit tests",
    prompt: "Write comprehensive unit tests for this {{language}} code using {{framework}}:\n\n{{code}}\n\nInclude:\n- Edge cases\n- Error scenarios\n- Happy path tests\n- Mock dependencies as needed\n\nMake the tests clear and maintainable.",
    category: "testing",
    tags: ["testing", "unit-tests", "quality"],
    variables: [
      { name: "language", description: "Programming language", defaultValue: "TypeScript", required: true, type: "text" },
      { name: "framework", description: "Testing framework", defaultValue: "Jest", required: true, type: "text" },
      { name: "code", description: "Code to test", required: true, type: "multiline" }
    ],
    favorite: true,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: "System",
    icon: "🧪"
  },
  {
    id: "add-documentation",
    title: "Add Documentation",
    description: "Generate code documentation",
    prompt: "Add comprehensive documentation to this {{language}} code:\n\n{{code}}\n\nInclude:\n- Function/class descriptions\n- Parameter documentation\n- Return value descriptions\n- Usage examples\n- Any important notes\n\nUse {{doc_format}} format.",
    category: "documentation",
    tags: ["documentation", "comments", "maintainability"],
    variables: [
      { name: "language", description: "Programming language", defaultValue: "TypeScript", required: true, type: "text" },
      { name: "code", description: "Code to document", required: true, type: "multiline" },
      { name: "doc_format", description: "Documentation format", defaultValue: "JSDoc", required: true, type: "select", options: ["JSDoc", "TSDoc", "Javadoc", "Docstring", "XML Comments"] }
    ],
    favorite: false,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: "System",
    icon: "📝"
  },
  {
    id: "sql-query-optimization",
    title: "SQL Query Optimization",
    description: "Optimize SQL queries for better performance",
    prompt: "Optimize this SQL query:\n\n{{query}}\n\nDatabase: {{database}}\nTable sizes: {{table_info}}\n\nProvide:\n1. Optimized query\n2. Explanation of changes\n3. Index recommendations\n4. Performance improvement estimate",
    category: "database",
    tags: ["sql", "database", "optimization"],
    variables: [
      { name: "query", description: "SQL query to optimize", required: true, type: "multiline" },
      { name: "database", description: "Database system", defaultValue: "PostgreSQL", required: true, type: "select", options: ["PostgreSQL", "MySQL", "SQLite", "SQL Server", "Oracle"] },
      { name: "table_info", description: "Table sizes and info", required: false, type: "text" }
    ],
    favorite: false,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: "System",
    icon: "🗄️"
  },
  {
    id: "convert-code",
    title: "Convert Code",
    description: "Convert code between languages or frameworks",
    prompt: "Convert this {{source_language}} code to {{target_language}}:\n\n{{code}}\n\nRequirements:\n- Maintain the same functionality\n- Follow {{target_language}} best practices\n- Include necessary imports/dependencies\n- Add comments for non-obvious conversions",
    category: "refactor",
    tags: ["conversion", "migration", "refactor"],
    variables: [
      { name: "source_language", description: "Source language/framework", required: true, type: "text" },
      { name: "target_language", description: "Target language/framework", required: true, type: "text" },
      { name: "code", description: "Code to convert", required: true, type: "multiline" }
    ],
    favorite: false,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: "System",
    icon: "🔄"
  },
  {
    id: "api-endpoint",
    title: "Create API Endpoint",
    description: "Generate REST API endpoint implementation",
    prompt: "Create a {{method}} endpoint for {{resource}} with the following:\n\nPath: {{path}}\nDescription: {{description}}\nRequest body: {{request_body}}\nResponse: {{response_format}}\n\nInclude:\n- Input validation\n- Error handling\n- {{additional_features}}\n\nUse {{framework}} framework.",
    category: "api",
    tags: ["api", "rest", "backend"],
    variables: [
      { name: "method", description: "HTTP method", defaultValue: "GET", required: true, type: "select", options: ["GET", "POST", "PUT", "PATCH", "DELETE"] },
      { name: "resource", description: "Resource name", required: true, type: "text" },
      { name: "path", description: "Endpoint path", required: true, type: "text" },
      { name: "description", description: "What the endpoint does", required: true, type: "text" },
      { name: "request_body", description: "Request body structure", required: false, type: "multiline" },
      { name: "response_format", description: "Response format", required: true, type: "multiline" },
      { name: "framework", description: "Backend framework", defaultValue: "Express", required: true, type: "text" },
      { name: "additional_features", description: "Additional features", defaultValue: "Authentication check", required: false, type: "text" }
    ],
    favorite: false,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: "System",
    icon: "🌐"
  },
  {
    id: "ui-component",
    title: "Create UI Component",
    description: "Generate a reusable UI component",
    prompt: "Create a {{component_type}} component called {{name}} with:\n\nDescription: {{description}}\nProps: {{props}}\nStyling: {{styling_approach}}\n\nRequirements:\n- Fully typed (TypeScript)\n- Responsive design\n- Accessibility compliant\n- {{additional_requirements}}\n\nUse {{framework}}.",
    category: "ui",
    tags: ["ui", "component", "frontend"],
    variables: [
      { name: "component_type", description: "Type of component", required: true, type: "text" },
      { name: "name", description: "Component name", required: true, type: "text" },
      { name: "description", description: "What the component does", required: true, type: "text" },
      { name: "props", description: "Component props", required: true, type: "multiline" },
      { name: "styling_approach", description: "Styling approach", defaultValue: "Tailwind CSS", required: true, type: "select", options: ["Tailwind CSS", "CSS Modules", "Styled Components", "Emotion", "Plain CSS"] },
      { name: "framework", description: "UI framework", defaultValue: "React", required: true, type: "select", options: ["React", "Vue", "Angular", "Svelte"] },
      { name: "additional_requirements", description: "Additional requirements", required: false, type: "text" }
    ],
    favorite: false,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: "System",
    icon: "🎨"
  }
];

export const PromptTemplates: React.FC<PromptTemplatesProps> = ({
  onUseTemplate,
  className
}) => {
  const [templates, setTemplates] = useState<PromptTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<PromptTemplate[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<"name" | "date" | "usage">("usage");
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null);
  const [creatingTemplate, setCreatingTemplate] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<PromptTemplate | null>(null);
  const [templateVariables, setTemplateVariables] = useState<Record<string, string>>({});
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  
  const { addToast } = useToast();

  // Load templates from localStorage
  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = () => {
    try {
      const savedTemplates = localStorage.getItem("prompt-templates");
      if (savedTemplates) {
        const parsed = JSON.parse(savedTemplates);
        setTemplates([...BUILTIN_TEMPLATES, ...parsed]);
      } else {
        setTemplates(BUILTIN_TEMPLATES);
      }
    } catch (error) {
      console.error("Failed to load templates:", error);
      setTemplates(BUILTIN_TEMPLATES);
    }
  };

  const saveTemplates = (newTemplates: PromptTemplate[]) => {
    // Filter out built-in templates before saving
    const customTemplates = newTemplates.filter(t => t.author !== "System");
    localStorage.setItem("prompt-templates", JSON.stringify(customTemplates));
    setTemplates(newTemplates);
  };

  // Filter templates based on search and filters
  useEffect(() => {
    let filtered = [...templates];

    // Category filter
    if (selectedCategory !== "all") {
      filtered = filtered.filter(t => t.category === selectedCategory);
    }

    // Tag filter
    if (selectedTags.length > 0) {
      filtered = filtered.filter(t => 
        selectedTags.every(tag => t.tags.includes(tag))
      );
    }

    // Favorites filter
    if (showFavoritesOnly) {
      filtered = filtered.filter(t => t.favorite);
    }

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(t => 
        t.title.toLowerCase().includes(query) ||
        t.description.toLowerCase().includes(query) ||
        t.prompt.toLowerCase().includes(query) ||
        t.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.title.localeCompare(b.title);
        case "date":
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        case "usage":
          return b.usageCount - a.usageCount;
        default:
          return 0;
      }
    });

    setFilteredTemplates(filtered);
  }, [templates, searchQuery, selectedCategory, selectedTags, showFavoritesOnly, sortBy]);

  // Get all unique tags
  const allTags = Array.from(new Set(templates.flatMap(t => t.tags)));

  // Handle template creation/editing
  const handleSaveTemplate = (template: PromptTemplate) => {
    if (editingTemplate) {
      // Update existing template
      const updated = templates.map(t => 
        t.id === template.id ? { ...template, updatedAt: new Date().toISOString() } : t
      );
      saveTemplates(updated);
      addToast({
        title: "Template updated",
        description: `"${template.title}" has been updated`,
        variant: "success"
      });
    } else {
      // Create new template
      const newTemplate: PromptTemplate = {
        ...template,
        id: `custom-${Date.now()}`,
        usageCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        author: "User"
      };
      saveTemplates([...templates, newTemplate]);
      addToast({
        title: "Template created",
        description: `"${template.title}" has been created`,
        variant: "success"
      });
    }
    setEditingTemplate(null);
    setCreatingTemplate(false);
  };

  // Handle template deletion
  const handleDeleteTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template?.author === "System") {
      addToast({
        title: "Cannot delete",
        description: "Built-in templates cannot be deleted",
        variant: "error"
      });
      return;
    }

    const updated = templates.filter(t => t.id !== templateId);
    saveTemplates(updated);
    addToast({
      title: "Template deleted",
      description: "The template has been removed",
      variant: "success"
    });
  };

  // Handle template duplication
  const handleDuplicateTemplate = (template: PromptTemplate) => {
    const duplicate: PromptTemplate = {
      ...template,
      id: `custom-${Date.now()}`,
      title: `${template.title} (Copy)`,
      usageCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      author: "User"
    };
    saveTemplates([...templates, duplicate]);
    addToast({
      title: "Template duplicated",
      description: `"${duplicate.title}" has been created`,
      variant: "success"
    });
  };

  // Toggle favorite
  const handleToggleFavorite = (templateId: string) => {
    const updated = templates.map(t => 
      t.id === templateId ? { ...t, favorite: !t.favorite } : t
    );
    saveTemplates(updated);
  };

  // Use template
  const handleUseTemplate = (template: PromptTemplate) => {
    // Increment usage count
    const updated = templates.map(t => 
      t.id === template.id ? { ...t, usageCount: t.usageCount + 1 } : t
    );
    saveTemplates(updated);

    // Replace variables in prompt
    let prompt = template.prompt;
    template.variables.forEach(variable => {
      const value = templateVariables[variable.name] || variable.defaultValue || "";
      const regex = new RegExp(`{{${variable.name}}}`, "g");
      prompt = prompt.replace(regex, value);
    });

    onUseTemplate(prompt);
    setSelectedTemplate(null);
    setTemplateVariables({});
    
    addToast({
      title: "Template applied",
      description: `Using "${template.title}"`,
      variant: "success"
    });
  };

  // Export templates
  const handleExportTemplates = async () => {
    try {
      setIsExporting(true);
      const customTemplates = templates.filter(t => t.author === "User");
      const exportData = {
        version: "1.0",
        exportDate: new Date().toISOString(),
        templates: customTemplates
      };

      const path = await save({
        defaultPath: `prompt-templates-${Date.now()}.json`,
        filters: [{
          name: 'JSON Files',
          extensions: ['json']
        }]
      });

      if (path) {
        await writeTextFile(path, JSON.stringify(exportData, null, 2));
        addToast({
          title: "Templates exported",
          description: `Exported ${customTemplates.length} templates`,
          variant: "success"
        });
      }
    } catch (error) {
      console.error("Failed to export templates:", error);
      addToast({
        title: "Export failed",
        description: "Could not export templates",
        variant: "error"
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Import templates
  const handleImportTemplates = async () => {
    try {
      setIsImporting(true);
      const path = await open({
        multiple: false,
        filters: [{
          name: 'JSON Files',
          extensions: ['json']
        }]
      });

      if (path && typeof path === 'string') {
        const content = await readTextFile(path);
        const importData = JSON.parse(content);
        
        if (importData.templates && Array.isArray(importData.templates)) {
          const imported = importData.templates.map((t: PromptTemplate) => ({
            ...t,
            id: `custom-${Date.now()}-${Math.random()}`,
            author: "User"
          }));
          
          saveTemplates([...templates, ...imported]);
          addToast({
            title: "Templates imported",
            description: `Imported ${imported.length} templates`,
            variant: "success"
          });
        }
      }
    } catch (error) {
      console.error("Failed to import templates:", error);
      addToast({
        title: "Import failed",
        description: "Could not import templates",
        variant: "error"
      });
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="p-4 border-b space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold">Prompt Templates</h2>
            <p className="text-sm text-muted-foreground">
              Save and reuse your favorite prompts
            </p>
          </div>
          <div className="flex items-center gap-2">
            <TooltipProvider delayDuration={0}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleImportTemplates}
                    disabled={isImporting}
                  >
                    {isImporting ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Import templates</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExportTemplates}
                    disabled={isExporting || templates.filter(t => t.author === "User").length === 0}
                  >
                    {isExporting ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Download className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Export templates</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <Button
              size="sm"
              onClick={() => setCreatingTemplate(true)}
            >
              <Plus className="h-4 w-4 mr-1" />
              New Template
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>

          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectSeparator />
              {DEFAULT_CATEGORIES.map(cat => (
                <SelectItem key={cat.id} value={cat.id}>
                  <div className="flex items-center gap-2">
                    <cat.icon className="h-4 w-4" />
                    {cat.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Filter Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem
                checked={showFavoritesOnly}
                onCheckedChange={setShowFavoritesOnly}
              >
                Show favorites only
              </DropdownMenuCheckboxItem>
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Sort by</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => setSortBy("usage")}>
                Most used
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("name")}>
                Name
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("date")}>
                Last updated
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Active Filters */}
        {(selectedTags.length > 0 || showFavoritesOnly) && (
          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-sm text-muted-foreground">Active filters:</span>
            {showFavoritesOnly && (
              <Badge
                variant="secondary"
                className="cursor-pointer"
                onClick={() => setShowFavoritesOnly(false)}
              >
                Favorites
                <X className="h-3 w-3 ml-1" />
              </Badge>
            )}
            {selectedTags.map(tag => (
              <Badge
                key={tag}
                variant="secondary"
                className="cursor-pointer"
                onClick={() => setSelectedTags(prev => prev.filter(t => t !== tag))}
              >
                {tag}
                <X className="h-3 w-3 ml-1" />
              </Badge>
            ))}
          </div>
        )}
      </div>

      {/* Templates Grid */}
      <ScrollArea className="flex-1 p-4">
        {filteredTemplates.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-[400px] text-center">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No templates found</h3>
            <p className="text-sm text-muted-foreground mb-4">
              {searchQuery ? "Try adjusting your search" : "Create your first template to get started"}
            </p>
            {!searchQuery && (
              <Button onClick={() => setCreatingTemplate(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            )}
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredTemplates.map(template => (
              <Card
                key={template.id}
                className="group hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => setSelectedTemplate(template)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-2">
                      <span className="text-2xl">{template.icon || "📄"}</span>
                      <div>
                        <CardTitle className="text-base">{template.title}</CardTitle>
                        <CardDescription className="text-xs mt-1">
                          {template.description}
                        </CardDescription>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleToggleFavorite(template.id);
                      }}
                    >
                      {template.favorite ? (
                        <Star className="h-4 w-4 fill-yellow-500 text-yellow-500" />
                      ) : (
                        <StarOff className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pb-3">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="outline" className="text-xs">
                      {DEFAULT_CATEGORIES.find(c => c.id === template.category)?.label || template.category}
                    </Badge>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Zap className="h-3 w-3" />
                      {template.usageCount} uses
                    </div>
                  </div>
                  
                  {template.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mb-2">
                      {template.tags.slice(0, 3).map(tag => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="text-xs cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedTags(prev => 
                              prev.includes(tag) ? prev : [...prev, tag]
                            );
                          }}
                        >
                          <Hash className="h-2.5 w-2.5 mr-0.5" />
                          {tag}
                        </Badge>
                      ))}
                      {template.tags.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{template.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}

                  {template.variables.length > 0 && (
                    <div className="text-xs text-muted-foreground">
                      {template.variables.length} variable{template.variables.length !== 1 ? 's' : ''}
                    </div>
                  )}
                </CardContent>
                <CardFooter className="pt-0 pb-3">
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <User className="h-3 w-3" />
                      {template.author}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          setSelectedTemplate(template);
                        }}>
                          <ChevronRight className="h-4 w-4 mr-2" />
                          Use Template
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          if (template.author !== "System") {
                            setEditingTemplate(template);
                          }
                        }} disabled={template.author === "System"}>
                          <Edit2 className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleDuplicateTemplate(template);
                        }}>
                          <Copy className="h-4 w-4 mr-2" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteTemplate(template.id);
                          }}
                          disabled={template.author === "System"}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </ScrollArea>

      {/* Template Editor Dialog */}
      <Dialog 
        open={creatingTemplate || editingTemplate !== null} 
        onOpenChange={(open) => {
          if (!open) {
            setCreatingTemplate(false);
            setEditingTemplate(null);
          }
        }}
      >
        <DialogContent className="max-w-3xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>
              {editingTemplate ? "Edit Template" : "Create New Template"}
            </DialogTitle>
            <DialogDescription>
              Create reusable prompts with variables for dynamic content
            </DialogDescription>
          </DialogHeader>
          <TemplateEditor
            template={editingTemplate}
            onSave={handleSaveTemplate}
            onCancel={() => {
              setCreatingTemplate(false);
              setEditingTemplate(null);
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Use Template Dialog */}
      <Dialog open={selectedTemplate !== null} onOpenChange={(open) => {
        if (!open) {
          setSelectedTemplate(null);
          setTemplateVariables({});
        }
      }}>
        <DialogContent className="max-w-2xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <span className="text-2xl">{selectedTemplate?.icon || "📄"}</span>
              {selectedTemplate?.title}
            </DialogTitle>
            <DialogDescription>
              {selectedTemplate?.description}
            </DialogDescription>
          </DialogHeader>
          
          {selectedTemplate && (
            <div className="space-y-4">
              {/* Variables */}
              {selectedTemplate.variables.length > 0 && (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">Fill in the variables</h4>
                  {selectedTemplate.variables.map(variable => (
                    <div key={variable.name} className="space-y-2">
                      <Label htmlFor={variable.name}>
                        {variable.name}
                        {variable.required && <span className="text-destructive ml-1">*</span>}
                      </Label>
                      {variable.description && (
                        <p className="text-xs text-muted-foreground">{variable.description}</p>
                      )}
                      {variable.type === "multiline" ? (
                        <Textarea
                          id={variable.name}
                          placeholder={variable.defaultValue}
                          value={templateVariables[variable.name] || ""}
                          onChange={(e) => setTemplateVariables(prev => ({
                            ...prev,
                            [variable.name]: e.target.value
                          }))}
                          rows={4}
                        />
                      ) : variable.type === "select" && variable.options ? (
                        <Select
                          value={templateVariables[variable.name] || variable.defaultValue}
                          onValueChange={(value) => setTemplateVariables(prev => ({
                            ...prev,
                            [variable.name]: value
                          }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {variable.options.map(option => (
                              <SelectItem key={option} value={option}>
                                {option}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : (
                        <Input
                          id={variable.name}
                          type={variable.type === "number" ? "number" : "text"}
                          placeholder={variable.defaultValue}
                          value={templateVariables[variable.name] || ""}
                          onChange={(e) => setTemplateVariables(prev => ({
                            ...prev,
                            [variable.name]: e.target.value
                          }))}
                        />
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Preview */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Preview</h4>
                <div className="p-3 rounded-md bg-muted text-sm whitespace-pre-wrap">
                  {(() => {
                    let preview = selectedTemplate.prompt;
                    selectedTemplate.variables.forEach(variable => {
                      const value = templateVariables[variable.name] || variable.defaultValue || `[${variable.name}]`;
                      const regex = new RegExp(`{{${variable.name}}}`, "g");
                      preview = preview.replace(regex, value);
                    });
                    return preview;
                  })()}
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setSelectedTemplate(null);
              setTemplateVariables({});
            }}>
              Cancel
            </Button>
            <Button
              onClick={() => selectedTemplate && handleUseTemplate(selectedTemplate)}
              disabled={selectedTemplate?.variables.some(v => 
                v.required && !templateVariables[v.name] && !v.defaultValue
              )}
            >
              <ChevronRight className="h-4 w-4 mr-1" />
              Use Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Template Editor Component
interface TemplateEditorProps {
  template: PromptTemplate | null;
  onSave: (template: PromptTemplate) => void;
  onCancel: () => void;
}

const TemplateEditor: React.FC<TemplateEditorProps> = ({
  template,
  onSave,
  onCancel
}) => {
  const [formData, setFormData] = useState<Partial<PromptTemplate>>({
    title: template?.title || "",
    description: template?.description || "",
    prompt: template?.prompt || "",
    category: template?.category || "general",
    tags: template?.tags || [],
    variables: template?.variables || [],
    icon: template?.icon || "📄"
  });
  
  const [newTag, setNewTag] = useState("");
  const [editingVariable, setEditingVariable] = useState<number | null>(null);

  // Extract variables from prompt
  const extractVariables = (prompt: string): string[] => {
    const regex = /{{(\w+)}}/g;
    const matches = [];
    let match;
    while ((match = regex.exec(prompt)) !== null) {
      if (!matches.includes(match[1])) {
        matches.push(match[1]);
      }
    }
    return matches;
  };

  // Update variables when prompt changes
  useEffect(() => {
    const extractedVars = extractVariables(formData.prompt || "");
    const currentVarNames = formData.variables?.map(v => v.name) || [];
    
    // Add new variables
    extractedVars.forEach(varName => {
      if (!currentVarNames.includes(varName)) {
        setFormData(prev => ({
          ...prev,
          variables: [...(prev.variables || []), {
            name: varName,
            description: "",
            required: true,
            type: "text"
          }]
        }));
      }
    });

    // Remove variables that are no longer in the prompt
    setFormData(prev => ({
      ...prev,
      variables: prev.variables?.filter(v => extractedVars.includes(v.name)) || []
    }));
  }, [formData.prompt]);

  const handleAddTag = () => {
    if (newTag && !formData.tags?.includes(newTag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag]
      }));
      setNewTag("");
    }
  };

  const handleRemoveTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(t => t !== tag) || []
    }));
  };

  const handleUpdateVariable = (index: number, variable: TemplateVariable) => {
    setFormData(prev => ({
      ...prev,
      variables: prev.variables?.map((v, i) => i === index ? variable : v) || []
    }));
  };

  const handleSave = () => {
    if (!formData.title || !formData.prompt) return;
    
    onSave({
      id: template?.id || "",
      title: formData.title,
      description: formData.description || "",
      prompt: formData.prompt,
      category: formData.category || "general",
      tags: formData.tags || [],
      variables: formData.variables || [],
      favorite: template?.favorite || false,
      usageCount: template?.usageCount || 0,
      createdAt: template?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      author: template?.author || "User",
      icon: formData.icon || "📄"
    });
  };

  return (
    <div className="space-y-4">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="variables">Variables</TabsTrigger>
        </TabsList>
        
        <TabsContent value="basic" className="space-y-4">
          <div className="grid gap-4 grid-cols-[auto,1fr]">
            <div className="space-y-2">
              <Label>Icon</Label>
              <Select value={formData.icon} onValueChange={(value) => setFormData({...formData, icon: value})}>
                <SelectTrigger className="w-[80px]">
                  <SelectValue>
                    <span className="text-2xl">{formData.icon}</span>
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {["📄", "🔍", "📚", "🐛", "⚡", "🧪", "📝", "🗄️", "🔄", "🌐", "🎨"].map(icon => (
                    <SelectItem key={icon} value={icon}>
                      <span className="text-2xl">{icon}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                placeholder="Template title"
                value={formData.title}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              placeholder="Brief description of this template"
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {DEFAULT_CATEGORIES.map(cat => (
                  <SelectItem key={cat.id} value={cat.id}>
                    <div className="flex items-center gap-2">
                      <cat.icon className="h-4 w-4" />
                      {cat.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex items-center gap-2">
              <Input
                placeholder="Add tag"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), handleAddTag())}
              />
              <Button type="button" size="sm" onClick={handleAddTag}>
                Add
              </Button>
            </div>
            {formData.tags && formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {formData.tags.map(tag => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => handleRemoveTag(tag)}
                  >
                    {tag}
                    <X className="h-3 w-3 ml-1" />
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="content" className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="prompt">Prompt Template *</Label>
            <p className="text-xs text-muted-foreground mb-2">
              Use {"{{variable_name}}"} to create variables that can be filled in when using the template
            </p>
            <Textarea
              id="prompt"
              placeholder="Enter your prompt template..."
              value={formData.prompt}
              onChange={(e) => setFormData({...formData, prompt: e.target.value})}
              rows={10}
              className="font-mono text-sm"
            />
          </div>
        </TabsContent>
        
        <TabsContent value="variables" className="space-y-4">
          {formData.variables && formData.variables.length > 0 ? (
            <ScrollArea className="h-[300px]">
              <div className="space-y-3 pr-4">
                {formData.variables.map((variable, index) => (
                  <Card key={variable.name} className="p-3">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{`{{${variable.name}}}`}</h4>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingVariable(editingVariable === index ? null : index)}
                        >
                          <Edit2 className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      {editingVariable === index ? (
                        <div className="space-y-3">
                          <div>
                            <Label>Description</Label>
                            <Input
                              placeholder="Variable description"
                              value={variable.description}
                              onChange={(e) => handleUpdateVariable(index, {...variable, description: e.target.value})}
                            />
                          </div>
                          
                          <div>
                            <Label>Type</Label>
                            <Select
                              value={variable.type}
                              onValueChange={(value: TemplateVariable['type']) => handleUpdateVariable(index, {...variable, type: value})}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="text">Text</SelectItem>
                                <SelectItem value="multiline">Multiline Text</SelectItem>
                                <SelectItem value="number">Number</SelectItem>
                                <SelectItem value="boolean">Boolean</SelectItem>
                                <SelectItem value="select">Select</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          
                          <div>
                            <Label>Default Value</Label>
                            <Input
                              placeholder="Default value (optional)"
                              value={variable.defaultValue || ""}
                              onChange={(e) => handleUpdateVariable(index, {...variable, defaultValue: e.target.value})}
                            />
                          </div>
                          
                          {variable.type === "select" && (
                            <div>
                              <Label>Options (comma-separated)</Label>
                              <Input
                                placeholder="Option 1, Option 2, Option 3"
                                value={variable.options?.join(", ") || ""}
                                onChange={(e) => handleUpdateVariable(index, {
                                  ...variable,
                                  options: e.target.value.split(",").map(o => o.trim()).filter(Boolean)
                                })}
                              />
                            </div>
                          )}
                          
                          <div className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              id={`required-${index}`}
                              checked={variable.required}
                              onChange={(e) => handleUpdateVariable(index, {...variable, required: e.target.checked})}
                            />
                            <Label htmlFor={`required-${index}`}>Required</Label>
                          </div>
                        </div>
                      ) : (
                        <div className="text-sm space-y-1">
                          {variable.description && (
                            <p className="text-muted-foreground">{variable.description}</p>
                          )}
                          <div className="flex items-center gap-2 text-xs">
                            <Badge variant="outline">{variable.type}</Badge>
                            {variable.required && <Badge variant="secondary">Required</Badge>}
                            {variable.defaultValue && (
                              <span className="text-muted-foreground">Default: {variable.defaultValue}</span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <AlertCircle className="h-8 w-8 mx-auto mb-2" />
              <p className="text-sm">No variables in your template yet</p>
              <p className="text-xs mt-1">Add {"{{variable_name}}"} to your prompt to create variables</p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      <DialogFooter>
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSave} disabled={!formData.title || !formData.prompt}>
          <Save className="h-4 w-4 mr-1" />
          Save Template
        </Button>
      </DialogFooter>
    </div>
  );
};

export default PromptTemplates;