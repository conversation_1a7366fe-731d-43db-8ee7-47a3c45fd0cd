/**
 * Interactive Idea Map Component
 * 
 * Provides drag-and-drop idea clustering, visual mind maps, and interactive
 * idea organization for enhanced brainstorming sessions.
 */

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence, useDragControls } from 'framer-motion';
import {
  Lightbulb,
  Plus,
  Minus,
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Move,
  Link,
  Unlink,
  Palette,
  Tag,
  Star,
  Trash2,
  Edit,
  Copy,
  Share2
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { Idea, IdeaStatus, Priority } from '@/types/brainstorm';
import { useUndoRedo } from '@/hooks/useUndoRedo';
import { UndoRedoToolbar } from './UndoRedoToolbar';

interface IdeaNode extends Idea {
  position: { x: number; y: number };
  connections: string[];
  clusterId?: string;
  size: 'small' | 'medium' | 'large';
}

interface IdeaCluster {
  id: string;
  title: string;
  color: string;
  ideaIds: string[];
  position: { x: number; y: number };
  size: { width: number; height: number };
}

interface Connection {
  id: string;
  fromIdeaId: string;
  toIdeaId: string;
  type: 'related' | 'depends' | 'conflicts' | 'builds-on';
  strength: number; // 0-1
}

interface InteractiveIdeaMapProps {
  sessionId: string;
  className?: string;
}

export const InteractiveIdeaMap: React.FC<InteractiveIdeaMapProps> = ({
  sessionId,
  className
}) => {
  const { theme } = useBrainstormingTheme();
  const { getIdeasBySession, addIdea, updateIdea, deleteIdea } = useBrainstormStore();
  const undoRedo = useUndoRedo();
  const containerRef = useRef<HTMLDivElement>(null);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [isPanning, setIsPanning] = useState(false);
  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });
  const [selectedIdeas, setSelectedIdeas] = useState<Set<string>>(new Set());
  const [dragMode, setDragMode] = useState<'select' | 'pan' | 'connect'>('select');
  const [showConnections, setShowConnections] = useState(true);
  const [editingIdea, setEditingIdea] = useState<string | null>(null);
  const [newIdeaPosition, setNewIdeaPosition] = useState<{ x: number; y: number } | null>(null);

  // Get ideas from store and convert to nodes with positions
  const ideas = getIdeasBySession(sessionId);
  const ideaNodes = useMemo<IdeaNode[]>(() =>
    ideas.map((idea, index) => ({
      ...idea,
      position: idea.position || {
        x: 100 + (index % 5) * 200,
        y: 100 + Math.floor(index / 5) * 150
      },
      connections: idea.connections || [],
      size: 'medium' as const
    })), [ideas]
  );

  const [clusters, setClusters] = useState<IdeaCluster[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);

  const handleZoomIn = () => setZoom(prev => Math.min(prev * 1.2, 3));
  const handleZoomOut = () => setZoom(prev => Math.max(prev / 1.2, 0.3));
  const handleResetView = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  };

  // Pan functionality
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (dragMode === 'pan') {
      setIsPanning(true);
      setLastPanPoint({ x: event.clientX, y: event.clientY });
    }
  }, [dragMode]);

  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (isPanning && dragMode === 'pan') {
      const deltaX = event.clientX - lastPanPoint.x;
      const deltaY = event.clientY - lastPanPoint.y;
      
      setPan(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }));
      
      setLastPanPoint({ x: event.clientX, y: event.clientY });
    }
  }, [isPanning, dragMode, lastPanPoint]);

  const handleMouseUp = useCallback(() => {
    setIsPanning(false);
  }, []);

  const handleIdeaSelect = (ideaId: string, multiSelect: boolean = false) => {
    setSelectedIdeas(prev => {
      const newSelection = new Set(prev);
      if (multiSelect) {
        if (newSelection.has(ideaId)) {
          newSelection.delete(ideaId);
        } else {
          newSelection.add(ideaId);
        }
      } else {
        newSelection.clear();
        newSelection.add(ideaId);
      }
      return newSelection;
    });
  };

  const handleIdeaDrag = useCallback((ideaId: string, newPosition: { x: number; y: number }) => {
    const idea = ideaNodes.find(n => n.id === ideaId);
    if (idea) {
      // Record the position change for undo/redo
      undoRedo.recordUpdate(sessionId, ideaId, 
        { position: idea.position }, 
        { position: newPosition }
      );
      // Update the idea in the store with the new position
      updateIdea(ideaId, { position: newPosition });
    }
  }, [updateIdea, ideaNodes, sessionId, undoRedo]);

  const handleCreateCluster = useCallback(() => {
    if (selectedIdeas.size < 2) return;

    const selectedNodes = ideaNodes.filter(node => selectedIdeas.has(node.id));
    const centerX = selectedNodes.reduce((sum, node) => sum + node.position.x, 0) / selectedNodes.length;
    const centerY = selectedNodes.reduce((sum, node) => sum + node.position.y, 0) / selectedNodes.length;

    const clusterId = `cluster_${Date.now()}`;
    const newCluster: IdeaCluster = {
      id: clusterId,
      title: 'New Cluster',
      color: theme.colors.primary,
      ideaIds: Array.from(selectedIdeas),
      position: { x: centerX - 100, y: centerY - 50 },
      size: { width: 200, height: 100 }
    };

    // Update each selected idea with the cluster reference
    selectedIdeas.forEach(ideaId => {
      updateIdea(ideaId, { cluster: clusterId });
    });

    setClusters(prev => [...prev, newCluster]);
    setSelectedIdeas(new Set());
  }, [selectedIdeas, ideaNodes, theme.colors.primary, updateIdea]);

  const handleDoubleClick = (event: React.MouseEvent) => {
    if (dragMode !== 'select') return;

    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = (event.clientX - rect.left - pan.x) / zoom;
    const y = (event.clientY - rect.top - pan.y) / zoom;

    setNewIdeaPosition({ x, y });
  };

  const handleCreateIdea = useCallback((content: string) => {
    if (newIdeaPosition) {
      const newIdea = addIdea(sessionId, content, { position: newIdeaPosition });
      // Record the creation for undo/redo
      if (newIdea) {
        undoRedo.recordCreate(sessionId, newIdea);
      }
      setNewIdeaPosition(null);
    }
  }, [newIdeaPosition, sessionId, addIdea, undoRedo]);

  const getIdeaColor = (idea: IdeaNode) => {
    switch (idea.status) {
      case 'active': return theme.colors.accent;
      case 'in-progress': return theme.colors.primary;
      case 'completed': return theme.colors.success;
      case 'archived': return theme.colors.textSecondary;
      default: return theme.colors.primary;
    }
  };

  const getIdeaSize = (size: IdeaNode['size']) => {
    switch (size) {
      case 'small': return { width: 120, height: 80 };
      case 'medium': return { width: 160, height: 100 };
      case 'large': return { width: 200, height: 120 };
    }
  };

  return (
    <div className={cn("relative w-full h-full overflow-hidden", className)}>
      {/* Undo/Redo Toolbar */}
      <UndoRedoToolbar className="absolute top-4 right-4 z-10" compact={true} />

      {/* Toolbar */}
      <div 
        className="absolute top-4 left-4 z-10 flex items-center gap-2 p-2 rounded-lg shadow-lg backdrop-blur-md"
        style={{ 
          backgroundColor: theme.colors.surface + 'CC',
          border: `1px solid ${theme.colors.border}`
        }}
      >
        <div className="flex items-center gap-1 border-r pr-2" style={{ borderColor: theme.colors.border }}>
          <Button
            variant={dragMode === 'select' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setDragMode('select')}
            aria-label="Selection mode - click and drag to select ideas"
            title="Selection mode (click and drag ideas)"
          >
            <Move className="w-4 h-4" aria-hidden="true" />
          </Button>
          <Button
            variant={dragMode === 'pan' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setDragMode('pan')}
            aria-label="Pan mode - click and drag to move the view"
            title="Pan mode (drag to move view)"
          >
            <Move className="w-4 h-4" aria-hidden="true" />
          </Button>
          <Button
            variant={dragMode === 'connect' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setDragMode('connect')}
            aria-label="Connect mode - create connections between ideas"
            title="Connect mode (create idea connections)"
          >
            <Link className="w-4 h-4" aria-hidden="true" />
          </Button>
        </div>

        <div className="flex items-center gap-1 border-r pr-2" style={{ borderColor: theme.colors.border }}>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleZoomOut}
            aria-label="Zoom out"
            title="Zoom out (make ideas smaller)"
          >
            <ZoomOut className="w-4 h-4" aria-hidden="true" />
          </Button>
          <span 
            className="text-xs px-2" 
            style={{ color: theme.colors.text }}
            aria-label={`Current zoom level: ${Math.round(zoom * 100)} percent`}
          >
            {Math.round(zoom * 100)}%
          </span>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleZoomIn}
            aria-label="Zoom in"
            title="Zoom in (make ideas larger)"
          >
            <ZoomIn className="w-4 h-4" aria-hidden="true" />
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleResetView}
            aria-label="Reset view"
            title="Reset zoom and position to default"
          >
            <RotateCcw className="w-4 h-4" aria-hidden="true" />
          </Button>
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowConnections(!showConnections)}
          >
            {showConnections ? <Link className="w-4 h-4" /> : <Unlink className="w-4 h-4" />}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleCreateCluster}
            disabled={selectedIdeas.size < 2}
          >
            <Plus className="w-4 h-4 mr-1" />
            Cluster ({selectedIdeas.size})
          </Button>
        </div>
      </div>

      {/* Canvas */}
      <div
        ref={containerRef}
        className={cn(
          "w-full h-full",
          dragMode === 'pan' ? 'cursor-grab' : 'cursor-crosshair',
          isPanning && 'cursor-grabbing'
        )}
        onDoubleClick={handleDoubleClick}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onKeyDown={(e) => {
          if (e.key === 'Delete' && selectedIdeas.size > 0) {
            if (confirm('Are you sure you want to delete the selected ideas?')) {
              const ideasToDelete = ideaNodes.filter(idea => selectedIdeas.has(idea.id));
              if (ideasToDelete.length === 1) {
                undoRedo.recordDelete(sessionId, ideasToDelete[0]);
                deleteIdea(ideasToDelete[0].id);
              } else if (ideasToDelete.length > 1) {
                undoRedo.recordBulkDelete(sessionId, ideasToDelete);
                ideasToDelete.forEach(idea => deleteIdea(idea.id));
              }
              setSelectedIdeas(new Set());
            }
          } else if (e.key === 'Escape') {
            setSelectedIdeas(new Set());
            setNewIdeaPosition(null);
          }
        }}
        style={{
          background: `radial-gradient(circle at 50% 50%, ${theme.colors.border}40 1px, transparent 1px)`,
          backgroundSize: '20px 20px',
          transform: `scale(${zoom}) translate(${pan.x}px, ${pan.y}px)`,
          transformOrigin: '0 0'
        }}
        role="application"
        aria-label="Interactive idea map - double-click to add ideas, drag to move them"
        tabIndex={0}
      >
        {/* Clusters */}
        {clusters.map(cluster => (
          <motion.div
            key={cluster.id}
            className="absolute border-2 border-dashed rounded-lg"
            style={{
              left: cluster.position.x,
              top: cluster.position.y,
              width: cluster.size.width,
              height: cluster.size.height,
              borderColor: cluster.color,
              backgroundColor: cluster.color + '10'
            }}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <div 
              className="absolute -top-6 left-2 px-2 py-1 rounded text-xs font-medium"
              style={{ 
                backgroundColor: cluster.color,
                color: 'white'
              }}
            >
              {cluster.title}
            </div>
          </motion.div>
        ))}

        {/* Connections */}
        {showConnections && connections.map(connection => {
          const fromNode = ideaNodes.find(n => n.id === connection.fromIdeaId);
          const toNode = ideaNodes.find(n => n.id === connection.toIdeaId);
          
          if (!fromNode || !toNode) return null;

          return (
            <svg
              key={connection.id}
              className="absolute pointer-events-none"
              style={{
                left: 0,
                top: 0,
                width: '100%',
                height: '100%'
              }}
            >
              <line
                x1={fromNode.position.x + 80}
                y1={fromNode.position.y + 50}
                x2={toNode.position.x + 80}
                y2={toNode.position.y + 50}
                stroke={theme.colors.primary}
                strokeWidth={2}
                strokeOpacity={connection.strength}
                strokeDasharray={connection.type === 'conflicts' ? '5,5' : 'none'}
              />
            </svg>
          );
        })}

        {/* Ideas */}
        {ideaNodes.map(idea => {
          const isSelected = selectedIdeas.has(idea.id);
          const size = getIdeaSize(idea.size);
          
          return (
            <motion.div
              key={idea.id}
              className={cn(
                "absolute cursor-pointer select-none focus:outline-none focus:ring-2 focus:ring-offset-2",
                isSelected && "ring-2 ring-offset-2"
              )}
              style={{
                left: idea.position.x,
                top: idea.position.y,
                width: size.width,
                height: size.height,
                ringColor: theme.colors.primary
              }}
              drag={dragMode === 'select'}
              onDrag={(_, info) => {
                handleIdeaDrag(idea.id, {
                  x: idea.position.x + info.delta.x / zoom,
                  y: idea.position.y + info.delta.y / zoom
                });
              }}
              onClick={(e) => {
                e.stopPropagation();
                handleIdeaSelect(idea.id, e.metaKey || e.ctrlKey);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleIdeaSelect(idea.id, e.metaKey || e.ctrlKey);
                } else if (e.key === 'Delete') {
                  e.stopPropagation();
                  if (confirm('Are you sure you want to delete this idea?')) {
                    undoRedo.recordDelete(sessionId, idea);
                    deleteIdea(idea.id);
                  }
                }
              }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              tabIndex={0}
              role="button"
              aria-label={`Idea: ${idea.content}. ${isSelected ? 'Selected' : 'Not selected'}. Press Enter to select, Delete to remove.`}
            >
              <Card 
                className="w-full h-full shadow-md hover:shadow-lg transition-shadow"
                style={{ borderColor: getIdeaColor(idea) }}
              >
                <CardContent className="p-3 h-full flex flex-col">
                  <div className="flex items-start justify-between mb-2">
                    <Lightbulb 
                      className="w-4 h-4 flex-shrink-0" 
                      style={{ color: getIdeaColor(idea) }}
                    />
                    <div className="flex gap-1">
                      {idea.priority === 'high' && (
                        <Star className="w-3 h-3" style={{ color: theme.colors.warning }} />
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-0 h-auto"
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditingIdea(idea.id);
                        }}
                      >
                        <Edit className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-0 h-auto"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (confirm('Are you sure you want to delete this idea?')) {
                            undoRedo.recordDelete(sessionId, idea);
                            deleteIdea(idea.id);
                          }
                        }}
                      >
                        <Trash2 className="w-3 h-3 text-destructive" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex-1 overflow-hidden">
                    {editingIdea === idea.id ? (
                      <Input
                        defaultValue={idea.content}
                        className="text-xs"
                        onBlur={(e) => {
                          if (e.target.value !== idea.content) {
                            undoRedo.recordUpdate(sessionId, idea.id, 
                              { content: idea.content }, 
                              { content: e.target.value }
                            );
                            updateIdea(idea.id, { content: e.target.value });
                          }
                          setEditingIdea(null);
                        }}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            if (e.currentTarget.value !== idea.content) {
                              undoRedo.recordUpdate(sessionId, idea.id, 
                                { content: idea.content }, 
                                { content: e.currentTarget.value }
                              );
                              updateIdea(idea.id, { content: e.currentTarget.value });
                            }
                            setEditingIdea(null);
                          }
                        }}
                        autoFocus
                      />
                    ) : (
                      <p className="text-xs leading-tight" style={{ color: theme.colors.text }}>
                        {idea.content}
                      </p>
                    )}
                  </div>
                  
                  {idea.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {idea.tags.slice(0, 2).map(tag => (
                        <Badge key={tag} variant="secondary" className="text-xs px-1 py-0">
                          {tag}
                        </Badge>
                      ))}
                      {idea.tags.length > 2 && (
                        <Badge variant="secondary" className="text-xs px-1 py-0">
                          +{idea.tags.length - 2}
                        </Badge>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          );
        })}

        {/* New Idea Creation */}
        <AnimatePresence>
          {newIdeaPosition && (
            <motion.div
              className="absolute"
              style={{
                left: newIdeaPosition.x,
                top: newIdeaPosition.y,
                width: 160,
                height: 100
              }}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
            >
              <Card className="w-full h-full shadow-lg">
                <CardContent className="p-3 h-full">
                  <Input
                    placeholder="Enter your idea..."
                    className="text-xs"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        handleCreateIdea(e.currentTarget.value);
                      } else if (e.key === 'Escape') {
                        setNewIdeaPosition(null);
                      }
                    }}
                    onBlur={(e) => {
                      if (e.target.value.trim()) {
                        handleCreateIdea(e.target.value);
                      } else {
                        setNewIdeaPosition(null);
                      }
                    }}
                    autoFocus
                  />
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Selection Info */}
      {selectedIdeas.size > 0 && (
        <div 
          className="absolute bottom-4 left-4 p-3 rounded-lg shadow-lg backdrop-blur-md"
          style={{ 
            backgroundColor: theme.colors.surface + 'CC',
            border: `1px solid ${theme.colors.border}`
          }}
        >
          <div className="text-sm" style={{ color: theme.colors.text }}>
            {selectedIdeas.size} idea{selectedIdeas.size > 1 ? 's' : ''} selected
          </div>
          <div className="flex gap-2 mt-2">
            <Button size="sm" onClick={handleCreateCluster} disabled={selectedIdeas.size < 2}>
              Create Cluster
            </Button>
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => setSelectedIdeas(new Set())}
            >
              Clear Selection
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
