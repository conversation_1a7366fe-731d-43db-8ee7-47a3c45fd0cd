import React, { useCallback, useEffect, useState } from 'react';
import React<PERSON><PERSON>, {
  Node,
  Edge,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  MarkerType,
  NodeTypes,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Lightbulb, 
  Plus, 
  Download, 
  Maximize2,
  Minimize2,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { IdeaStatus, Priority } from '@/types/brainstorm';

interface MindMapViewProps {
  sessionId: string;
  className?: string;
}

// Custom node component for ideas
const IdeaNode = ({ data }: { data: any }) => {
  const { updateIdea } = useBrainstormStore();
  const [isEditing, setIsEditing] = useState(false);
  const [content, setContent] = useState(data.content);

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleBlur = () => {
    setIsEditing(false);
    if (content !== data.content) {
      updateIdea(data.id, { content });
    }
  };

  const statusColors = {
    [IdeaStatus.TO_EXPLORE]: 'bg-gray-100 dark:bg-gray-800',
    [IdeaStatus.IN_PROGRESS]: 'bg-blue-100 dark:bg-blue-900',
    [IdeaStatus.VALIDATED]: 'bg-green-100 dark:bg-green-900',
    [IdeaStatus.ARCHIVED]: 'bg-gray-200 dark:bg-gray-700',
  };

  const priorityColors = {
    [Priority.LOW]: 'text-gray-500',
    [Priority.MEDIUM]: 'text-yellow-500',
    [Priority.HIGH]: 'text-orange-500',
    [Priority.CRITICAL]: 'text-red-500',
  };

  return (
    <Card 
      className={cn(
        'p-3 min-w-[200px] max-w-[300px] cursor-pointer transition-all',
        statusColors[data.status as IdeaStatus],
        'hover:shadow-lg'
      )}
      onDoubleClick={handleDoubleClick}
    >
      <div className="flex items-start justify-between gap-2 mb-2">
        <Lightbulb className={cn('h-4 w-4', priorityColors[(data.priority || Priority.MEDIUM) as Priority])} />
        <Badge variant="secondary" className="text-xs">
          {data.status}
        </Badge>
      </div>
      
      {isEditing ? (
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          onBlur={handleBlur}
          className="w-full bg-transparent border-0 outline-none resize-none"
          autoFocus
        />
      ) : (
        <p className="text-sm">{data.content}</p>
      )}
      
      {data.tags && data.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {data.tags.map((tag: string) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
      )}
    </Card>
  );
};

const nodeTypes: NodeTypes = {
  idea: IdeaNode,
};

export const MindMapView: React.FC<MindMapViewProps> = ({ sessionId, className }) => {
  const { ideas, clusters, addIdea, updateIdea, getIdeasBySession } = useBrainstormStore();
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Convert ideas to nodes
  useEffect(() => {
    const sessionIdeas = getIdeasBySession(sessionId);
    const newNodes: Node[] = [];
    const newEdges: Edge[] = [];
    
    // Create a central node for the session
    newNodes.push({
      id: 'session-center',
      type: 'default',
      position: { x: 400, y: 300 },
      data: { label: 'Brainstorming Session' },
      style: {
        background: '#6366f1',
        color: 'white',
        fontSize: '16px',
        fontWeight: 'bold',
      },
    });

    // Position ideas around the center
    sessionIdeas.forEach((idea, index) => {
      const angle = (index * 2 * Math.PI) / sessionIdeas.length;
      const radius = 250;
      const x = 400 + radius * Math.cos(angle);
      const y = 300 + radius * Math.sin(angle);

      newNodes.push({
        id: idea.id,
        type: 'idea',
        position: idea.position || { x, y },
        data: {
          ...idea,
          content: idea.content,
          status: idea.status,
          priority: idea.priority,
          tags: idea.tags,
        },
      });

      // Connect to center if no cluster
      if (!idea.cluster) {
        newEdges.push({
          id: `e-center-${idea.id}`,
          source: 'session-center',
          target: idea.id,
          type: 'smoothstep',
          animated: true,
          style: { stroke: '#6366f1' },
        });
      }
    });

    // Add cluster nodes and connections
    Object.values(clusters).forEach((cluster) => {
      if (cluster.ideaIds.some(id => sessionIdeas.find(idea => idea.id === id))) {
        const clusterNode: Node = {
          id: `cluster-${cluster.id}`,
          type: 'default',
          position: { x: 200, y: 200 }, // Will be auto-layouted
          data: { label: cluster.name },
          style: {
            background: cluster.color,
            color: 'white',
            fontSize: '14px',
            fontWeight: 'bold',
          },
        };
        newNodes.push(clusterNode);

        // Connect ideas to clusters
        cluster.ideaIds.forEach(ideaId => {
          if (sessionIdeas.find(idea => idea.id === ideaId)) {
            newEdges.push({
              id: `e-cluster-${cluster.id}-${ideaId}`,
              source: `cluster-${cluster.id}`,
              target: ideaId,
              type: 'smoothstep',
              style: { stroke: cluster.color },
            });
          }
        });
      }
    });

    // Add idea connections
    sessionIdeas.forEach((idea) => {
      idea.connections?.forEach(targetId => {
        newEdges.push({
          id: `e-${idea.id}-${targetId}`,
          source: idea.id,
          target: targetId,
          type: 'smoothstep',
          markerEnd: {
            type: MarkerType.ArrowClosed,
          },
        });
      });
    });

    setNodes(newNodes);
    setEdges(newEdges);
  }, [sessionId, ideas, clusters, getIdeasBySession]);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onNodeDragStop = useCallback(
    (_event: any, node: Node) => {
      if (node.id.startsWith('cluster-') || node.id === 'session-center') return;
      
      updateIdea(node.id, {
        position: { x: node.position.x, y: node.position.y }
      });
    },
    [updateIdea]
  );

  const addNewIdea = () => {
    const centerX = 400;
    const centerY = 300;
    const randomOffset = () => (Math.random() - 0.5) * 200;
    
    addIdea(sessionId, 'New Idea', {
      status: IdeaStatus.TO_EXPLORE,
      tags: [],
      connections: [],
      position: {
        x: centerX + randomOffset(),
        y: centerY + randomOffset(),
      }
    });
  };

  const exportMindMap = async () => {
    const mindMapElement = document.querySelector('.react-flow__viewport');
    if (!mindMapElement) return;

    try {
      const html2canvas = (await import('html2canvas')).default;
      const canvas = await html2canvas(mindMapElement as HTMLElement, {
        backgroundColor: '#ffffff',
        scale: 2,
        useCORS: true,
      });
      
      const link = document.createElement('a');
      link.download = `mindmap-${sessionId}-${Date.now()}.png`;
      link.href = canvas.toDataURL();
      link.click();
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <div className={cn(
      'flex flex-col h-full',
      isFullscreen && 'fixed inset-0 z-50 bg-background',
      className
    )}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur">
        <div className="flex items-center gap-2">
          <Button onClick={addNewIdea} size="sm">
            <Plus className="h-4 w-4 mr-1" />
            Add Idea
          </Button>
          <Button onClick={exportMindMap} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={toggleFullscreen}
            variant="outline"
            size="sm"
          >
            {isFullscreen ? (
              <Minimize2 className="h-4 w-4" />
            ) : (
              <Maximize2 className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Mind Map */}
      <div className="flex-1">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodeDragStop={onNodeDragStop}
          nodeTypes={nodeTypes}
          fitView
          className="bg-gray-50 dark:bg-gray-900"
        >
          <Background />
          <Controls />
          <MiniMap />
        </ReactFlow>
      </div>
    </div>
  );
};