/**
 * Real-Time Collaboration Component
 * 
 * Provides live collaboration features for brainstorming sessions including
 * participant presence, live cursors, real-time idea updates, and chat.
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  UserPlus,
  MessageCircle,
  Eye,
  EyeOff,
  Mic,
  MicOff,
  Video,
  VideoOff,
  Share2,
  Crown,
  Settings,
  Bell,
  BellOff,
  Cursor,
  Activity
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';

export interface Participant {
  id: string;
  name: string;
  avatar?: string;
  color: string;
  role: 'owner' | 'moderator' | 'participant';
  status: 'active' | 'idle' | 'away';
  lastSeen: string;
  cursor?: {
    x: number;
    y: number;
    visible: boolean;
  };
  permissions: {
    canEdit: boolean;
    canInvite: boolean;
    canModerate: boolean;
  };
}

export interface CollaborationActivity {
  id: string;
  type: 'join' | 'leave' | 'idea_added' | 'idea_edited' | 'idea_deleted' | 'chat_message';
  participantId: string;
  participantName: string;
  timestamp: string;
  data?: any;
}

interface RealTimeCollaborationProps {
  sessionId: string;
  currentUserId: string;
  participants: Participant[];
  activities: CollaborationActivity[];
  onInviteParticipant: () => void;
  onRemoveParticipant: (participantId: string) => void;
  onUpdatePermissions: (participantId: string, permissions: Participant['permissions']) => void;
  onSendChatMessage: (message: string) => void;
  className?: string;
}

export const RealTimeCollaboration: React.FC<RealTimeCollaborationProps> = ({
  sessionId,
  currentUserId,
  participants,
  activities,
  onInviteParticipant,
  onRemoveParticipant,
  onUpdatePermissions,
  onSendChatMessage,
  className
}) => {
  const { theme } = useBrainstormingTheme();
  const [showParticipants, setShowParticipants] = useState(true);
  const [showActivity, setShowActivity] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [chatMessage, setChatMessage] = useState('');
  const [notifications, setNotifications] = useState(true);

  const currentUser = participants.find(p => p.id === currentUserId);
  const activeParticipants = participants.filter(p => p.status === 'active');
  const recentActivities = activities.slice(-10);

  const handleSendMessage = () => {
    if (chatMessage.trim()) {
      onSendChatMessage(chatMessage);
      setChatMessage('');
    }
  };

  const getStatusColor = (status: Participant['status']) => {
    switch (status) {
      case 'active': return '#10B981';
      case 'idle': return '#F59E0B';
      case 'away': return '#6B7280';
      default: return '#6B7280';
    }
  };

  const getRoleIcon = (role: Participant['role']) => {
    switch (role) {
      case 'owner': return <Crown className="w-3 h-3" />;
      case 'moderator': return <Settings className="w-3 h-3" />;
      default: return null;
    }
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Collaboration Header */}
      <div 
        className="flex items-center justify-between p-4 border-b"
        style={{ borderColor: theme.colors.border }}
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5" style={{ color: theme.colors.primary }} />
            <span className="font-medium" style={{ color: theme.colors.text }}>
              Collaboration
            </span>
            <Badge 
              variant="secondary"
              style={{ 
                backgroundColor: theme.colors.primary + '20',
                color: theme.colors.primary 
              }}
            >
              {activeParticipants.length} active
            </Badge>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setNotifications(!notifications)}
          >
            {notifications ? <Bell className="w-4 h-4" /> : <BellOff className="w-4 h-4" />}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={onInviteParticipant}
          >
            <UserPlus className="w-4 h-4 mr-2" />
            Invite
          </Button>
        </div>
      </div>

      {/* Collaboration Tabs */}
      <div className="flex border-b" style={{ borderColor: theme.colors.border }}>
        <button
          onClick={() => {
            setShowParticipants(true);
            setShowActivity(false);
            setShowChat(false);
          }}
          className={cn(
            "flex-1 px-4 py-2 text-sm font-medium transition-colors",
            showParticipants && "border-b-2"
          )}
          style={{
            color: showParticipants ? theme.colors.primary : theme.colors.textSecondary,
            borderColor: showParticipants ? theme.colors.primary : 'transparent'
          }}
        >
          <Users className="w-4 h-4 mr-2 inline" />
          Participants ({participants.length})
        </button>
        
        <button
          onClick={() => {
            setShowParticipants(false);
            setShowActivity(true);
            setShowChat(false);
          }}
          className={cn(
            "flex-1 px-4 py-2 text-sm font-medium transition-colors",
            showActivity && "border-b-2"
          )}
          style={{
            color: showActivity ? theme.colors.primary : theme.colors.textSecondary,
            borderColor: showActivity ? theme.colors.primary : 'transparent'
          }}
        >
          <Activity className="w-4 h-4 mr-2 inline" />
          Activity
        </button>
        
        <button
          onClick={() => {
            setShowParticipants(false);
            setShowActivity(false);
            setShowChat(true);
          }}
          className={cn(
            "flex-1 px-4 py-2 text-sm font-medium transition-colors",
            showChat && "border-b-2"
          )}
          style={{
            color: showChat ? theme.colors.primary : theme.colors.textSecondary,
            borderColor: showChat ? theme.colors.primary : 'transparent'
          }}
        >
          <MessageCircle className="w-4 h-4 mr-2 inline" />
          Chat
        </button>
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-auto">
        {/* Participants View */}
        {showParticipants && (
          <div className="p-4 space-y-3">
            {participants.map(participant => (
              <motion.div
                key={participant.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center justify-between p-3 rounded-lg border"
                style={{ 
                  borderColor: theme.colors.border,
                  backgroundColor: participant.id === currentUserId ? theme.colors.primary + '10' : 'transparent'
                }}
              >
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={participant.avatar} />
                      <AvatarFallback style={{ backgroundColor: participant.color }}>
                        {participant.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div
                      className="absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white"
                      style={{ backgroundColor: getStatusColor(participant.status) }}
                    />
                  </div>
                  
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm" style={{ color: theme.colors.text }}>
                        {participant.name}
                        {participant.id === currentUserId && ' (You)'}
                      </span>
                      {getRoleIcon(participant.role)}
                    </div>
                    <span className="text-xs" style={{ color: theme.colors.textSecondary }}>
                      {participant.status} • {participant.role}
                    </span>
                  </div>
                </div>

                {participant.id !== currentUserId && currentUser?.permissions.canModerate && (
                  <div className="flex items-center gap-1">
                    <Button variant="ghost" size="sm">
                      <Settings className="w-3 h-3" />
                    </Button>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        )}

        {/* Activity View */}
        {showActivity && (
          <div className="p-4 space-y-2">
            {recentActivities.map(activity => (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                className="flex items-start gap-3 p-2 rounded text-sm"
              >
                <div 
                  className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                  style={{ backgroundColor: theme.colors.primary }}
                />
                <div>
                  <span style={{ color: theme.colors.text }}>
                    <strong>{activity.participantName}</strong> {activity.type.replace('_', ' ')}
                  </span>
                  <div className="text-xs mt-1" style={{ color: theme.colors.textSecondary }}>
                    {new Date(activity.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* Chat View */}
        {showChat && (
          <div className="flex flex-col h-full">
            <div className="flex-1 p-4 space-y-3 overflow-auto">
              {/* Chat messages would go here */}
              <div className="text-center text-sm" style={{ color: theme.colors.textSecondary }}>
                Chat feature coming soon...
              </div>
            </div>
            
            <div className="p-4 border-t" style={{ borderColor: theme.colors.border }}>
              <div className="flex gap-2">
                <Input
                  placeholder="Type a message..."
                  value={chatMessage}
                  onChange={(e) => setChatMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  className="flex-1"
                />
                <Button onClick={handleSendMessage} disabled={!chatMessage.trim()}>
                  Send
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
