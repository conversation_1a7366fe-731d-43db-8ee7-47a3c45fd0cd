import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { 
  TrendingUp, 
  TrendingDown, 
  Target,
  Zap,
  Clock,
  Lightbulb,
  Info
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { Idea, Priority } from '@/types/brainstorm';

interface MatrixViewProps {
  sessionId: string;
  className?: string;
}

interface Quadrant {
  id: string;
  label: string;
  description: string;
  color: string;
  bgColor: string;
  icon: React.ComponentType<any>;
  recommendation: string;
}

const quadrants: Quadrant[] = [
  {
    id: 'quick-wins',
    label: 'Quick Wins',
    description: 'High impact, low effort',
    color: 'text-green-600',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    icon: Zap,
    recommendation: 'Do these first!',
  },
  {
    id: 'strategic',
    label: 'Strategic',
    description: 'High impact, high effort',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    icon: Target,
    recommendation: 'Plan carefully',
  },
  {
    id: 'fill-ins',
    label: 'Fill-ins',
    description: 'Low impact, low effort',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
    icon: Clock,
    recommendation: 'Do when time permits',
  },
  {
    id: 'questionable',
    label: 'Questionable',
    description: 'Low impact, high effort',
    color: 'text-red-600',
    bgColor: 'bg-red-50 dark:bg-red-900/20',
    icon: TrendingDown,
    recommendation: 'Reconsider or defer',
  },
];

const IdeaDot: React.FC<{
  idea: Idea;
  x: number;
  y: number;
  onSelect: (idea: Idea) => void;
  isSelected: boolean;
}> = ({ idea, x, y, onSelect, isSelected }) => {
  const priorityColors = {
    [Priority.LOW]: 'bg-gray-400',
    [Priority.MEDIUM]: 'bg-yellow-400',
    [Priority.HIGH]: 'bg-orange-400',
    [Priority.CRITICAL]: 'bg-red-400',
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            className={cn(
              'absolute w-3 h-3 rounded-full transition-all hover:scale-150',
              priorityColors[idea.priority || Priority.MEDIUM],
              isSelected && 'ring-4 ring-primary scale-150'
            )}
            style={{
              left: `${x}%`,
              top: `${y}%`,
              transform: 'translate(-50%, -50%)',
            }}
            onClick={() => onSelect(idea)}
          />
        </TooltipTrigger>
        <TooltipContent>
          <div className="max-w-xs">
            <p className="font-medium">{idea.content}</p>
            <div className="flex gap-2 mt-1">
              <Badge variant="secondary" className="text-xs">
                Impact: {idea.impact || 5}
              </Badge>
              <Badge variant="secondary" className="text-xs">
                Effort: {idea.effort || 5}
              </Badge>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export const MatrixView: React.FC<MatrixViewProps> = ({ sessionId, className }) => {
  const { getIdeasBySession, updateIdea } = useBrainstormStore();
  const [selectedIdea, setSelectedIdea] = useState<Idea | null>(null);
  const [tempImpact, setTempImpact] = useState<number>(5);
  const [tempEffort, setTempEffort] = useState<number>(5);
  
  const ideas = getIdeasBySession(sessionId);

  const handleIdeaSelect = useCallback((idea: Idea) => {
    setSelectedIdea(idea);
    setTempImpact(idea.impact || 5);
    setTempEffort(idea.effort || 5);
  }, []);

  const handleUpdateIdea = useCallback(() => {
    if (selectedIdea) {
      updateIdea(selectedIdea.id, {
        impact: tempImpact,
        effort: tempEffort,
      });
    }
  }, [selectedIdea, tempImpact, tempEffort, updateIdea]);

  const getQuadrant = (impact: number, effort: number): string => {
    if (impact > 5 && effort <= 5) return 'quick-wins';
    if (impact > 5 && effort > 5) return 'strategic';
    if (impact <= 5 && effort <= 5) return 'fill-ins';
    return 'questionable';
  };

  const quadrantCounts = useMemo(() => {
    const counts: Record<string, number> = {
      'quick-wins': 0,
      'strategic': 0,
      'fill-ins': 0,
      'questionable': 0,
    };
    
    ideas.forEach(idea => {
      const quadrant = getQuadrant(idea.impact || 5, idea.effort || 5);
      counts[quadrant]++;
    });
    
    return counts;
  }, [ideas]);

  return (
    <div className={cn('flex flex-col h-full', className)}>
      <div className="flex gap-6 h-full p-6">
        {/* Matrix */}
        <div className="flex-1">
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Impact vs Effort Matrix
              </CardTitle>
            </CardHeader>
            <CardContent className="relative h-[calc(100%-5rem)]">
              {/* Axis Labels */}
              <div className="absolute -left-8 top-1/2 -translate-y-1/2 -rotate-90 text-sm font-medium">
                Impact →
              </div>
              <div className="absolute bottom-[-2rem] left-1/2 -translate-x-1/2 text-sm font-medium">
                Effort →
              </div>

              {/* Grid */}
              <div className="relative w-full h-full border-2 border-gray-200 dark:border-gray-700">
                {/* Quadrant backgrounds */}
                <div className="absolute inset-0 grid grid-cols-2 grid-rows-2">
                  <div className={cn('border-r-2 border-b-2 border-gray-200 dark:border-gray-700', quadrants[2].bgColor)} />
                  <div className={cn('border-b-2 border-gray-200 dark:border-gray-700', quadrants[0].bgColor)} />
                  <div className={cn('border-r-2 border-gray-200 dark:border-gray-700', quadrants[3].bgColor)} />
                  <div className={quadrants[1].bgColor} />
                </div>

                {/* Quadrant labels */}
                {quadrants.map((quadrant, index) => {
                  const Icon = quadrant.icon;
                  const x = index % 2 === 0 ? '25%' : '75%';
                  const y = index < 2 ? '25%' : '75%';
                  
                  return (
                    <div
                      key={quadrant.id}
                      className="absolute flex flex-col items-center gap-1"
                      style={{
                        left: x,
                        top: y,
                        transform: 'translate(-50%, -50%)',
                      }}
                    >
                      <Icon className={cn('h-8 w-8 opacity-20', quadrant.color)} />
                      <span className={cn('text-sm font-medium', quadrant.color)}>
                        {quadrant.label}
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {quadrantCounts[quadrant.id]} ideas
                      </Badge>
                    </div>
                  );
                })}

                {/* Ideas */}
                {ideas.map(idea => {
                  const impact = idea.impact || 5;
                  const effort = idea.effort || 5;
                  const x = (effort / 10) * 100;
                  const y = 100 - (impact / 10) * 100;
                  
                  return (
                    <IdeaDot
                      key={idea.id}
                      idea={idea}
                      x={x}
                      y={y}
                      onSelect={handleIdeaSelect}
                      isSelected={selectedIdea?.id === idea.id}
                    />
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="w-80 space-y-4">
          {/* Selected Idea */}
          {selectedIdea ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5" />
                  Selected Idea
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm">{selectedIdea.content}</p>
                </div>

                <div className="space-y-3">
                  <div>
                    <Label>Impact: {tempImpact}</Label>
                    <Slider
                      value={[tempImpact]}
                      onValueChange={(value) => setTempImpact(value[0])}
                      min={1}
                      max={10}
                      step={1}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label>Effort: {tempEffort}</Label>
                    <Slider
                      value={[tempEffort]}
                      onValueChange={(value) => setTempEffort(value[0])}
                      min={1}
                      max={10}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                </div>

                <Button 
                  onClick={handleUpdateIdea}
                  disabled={
                    tempImpact === selectedIdea.impact && 
                    tempEffort === selectedIdea.effort
                  }
                  className="w-full"
                >
                  Update Position
                </Button>

                <div className="pt-2 border-t">
                  <p className="text-sm text-muted-foreground">
                    Current quadrant: {' '}
                    <span className="font-medium">
                      {quadrants.find(q => q.id === getQuadrant(tempImpact, tempEffort))?.label}
                    </span>
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center h-32">
                <Info className="h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground text-center">
                  Click on an idea to adjust its impact and effort scores
                </p>
              </CardContent>
            </Card>
          )}

          {/* Legend */}
          <Card>
            <CardHeader>
              <CardTitle>Quadrant Guide</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {quadrants.map(quadrant => {
                const Icon = quadrant.icon;
                return (
                  <div key={quadrant.id} className="flex items-start gap-3">
                    <Icon className={cn('h-5 w-5 mt-0.5', quadrant.color)} />
                    <div className="flex-1">
                      <p className="font-medium text-sm">{quadrant.label}</p>
                      <p className="text-xs text-muted-foreground">
                        {quadrant.description}
                      </p>
                      <p className="text-xs font-medium mt-1">
                        {quadrant.recommendation}
                      </p>
                    </div>
                  </div>
                );
              })}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};