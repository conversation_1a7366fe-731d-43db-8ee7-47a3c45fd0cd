import React, { useState, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  ListTodo,
  Calendar,
  Clock,
  User,
  Tag,
  CheckCircle,
  Circle,
  AlertCircle,
  Sparkles,
  Download,
  Trash2,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { Idea, Priority, GeneratedTask } from '@/types/brainstorm';
import { useToast } from '@/hooks/useToast';

interface TaskGeneratorProps {
  sessionId: string;
  selectedIdeas?: string[];
  className?: string;
}

export const TaskGenerator: React.FC<TaskGeneratorProps> = ({
  sessionId,
  selectedIdeas = [],
  className,
}) => {
  const { getIdeasBySession, getIdeaById } = useBrainstormStore();
  const [tasks, setTasks] = useState<GeneratedTask[]>([]);
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [editingTask, setEditingTask] = useState<GeneratedTask | null>(null);
  
  const { toast } = useToast();
  
  const ideas = getIdeasBySession(sessionId);
  const selectedIdeaObjects = selectedIdeas.length > 0
    ? selectedIdeas.map(id => getIdeaById(id)).filter(Boolean) as Idea[]
    : ideas;

  const generateTasksFromIdeas = useCallback(async () => {
    setIsGenerating(true);
    
    try {
      // Simulate AI task generation
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const newTasks: GeneratedTask[] = selectedIdeaObjects.map((idea, index) => ({
        id: `task_${Date.now()}_${index}`,
        title: `Implement: ${idea.content.substring(0, 50)}...`,
        description: `Task generated from idea: ${idea.content}\n\nBreak this down into actionable steps and implement.`,
        ideaId: idea.id,
        priority: idea.priority || Priority.MEDIUM,
        estimatedEffort: Math.floor(Math.random() * 8) + 1,
        dependencies: [],
        tags: [...idea.tags, 'generated'],
        status: 'todo',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }));
      
      setTasks(prev => [...prev, ...newTasks]);
      toast({
        message: `Generated ${newTasks.length} tasks from ideas`,
        type: 'success',
      });
    } catch (error) {
      toast({
        message: 'Failed to generate tasks',
        type: 'error',
      });
    } finally {
      setIsGenerating(false);
    }
  }, [selectedIdeaObjects, toast]);

  const toggleTaskSelection = (taskId: string) => {
    setSelectedTasks(prev =>
      prev.includes(taskId)
        ? prev.filter(id => id !== taskId)
        : [...prev, taskId]
    );
  };

  const deleteTask = (taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId));
  };

  const updateTask = (taskId: string, updates: Partial<GeneratedTask>) => {
    setTasks(prev =>
      prev.map(task =>
        task.id === taskId
          ? { ...task, ...updates, updatedAt: new Date().toISOString() }
          : task
      )
    );
  };

  const exportTasks = () => {
    const tasksToExport = selectedTasks.length > 0
      ? tasks.filter(task => selectedTasks.includes(task.id))
      : tasks;

    const markdown = tasksToExport.map(task => {
      return `## ${task.title}

**Priority:** ${task.priority}
**Status:** ${task.status}
**Estimated Effort:** ${task.estimatedEffort} hours
**Tags:** ${task.tags.join(', ')}

### Description
${task.description}

${task.dependencies.length > 0 ? `### Dependencies\n${task.dependencies.map(d => `- ${d}`).join('\n')}` : ''}

---
`;
    }).join('\n');

    const blob = new Blob([markdown], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tasks-${new Date().toISOString().split('T')[0]}.md`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const TaskCard: React.FC<{ task: GeneratedTask }> = ({ task }) => {
    const StatusIcon = task.status === 'done' ? CheckCircle : task.status === 'in_progress' ? AlertCircle : Circle;
    const statusColor = task.status === 'done' ? 'text-green-600' : task.status === 'in_progress' ? 'text-blue-600' : 'text-gray-400';
    
    const priorityColors = {
      [Priority.LOW]: 'bg-gray-100 text-gray-700',
      [Priority.MEDIUM]: 'bg-yellow-100 text-yellow-700',
      [Priority.HIGH]: 'bg-orange-100 text-orange-700',
      [Priority.CRITICAL]: 'bg-red-100 text-red-700',
    };

    return (
      <Card className={cn(
        'transition-all',
        selectedTasks.includes(task.id) && 'ring-2 ring-primary'
      )}>
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Checkbox
              checked={selectedTasks.includes(task.id)}
              onCheckedChange={() => toggleTaskSelection(task.id)}
            />
            
            <div className="flex-1 space-y-2">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  <StatusIcon className={cn('h-4 w-4', statusColor)} />
                  <h4 className="font-medium">{task.title}</h4>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setEditingTask(task)}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteTask(task.id)}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
              
              <p className="text-sm text-muted-foreground line-clamp-2">
                {task.description}
              </p>
              
              <div className="flex items-center gap-2 flex-wrap">
                <Badge className={cn('text-xs', priorityColors[task.priority])}>
                  {task.priority}
                </Badge>
                
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  {task.estimatedEffort}h
                </div>
                
                {task.assignee && (
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <User className="h-3 w-3" />
                    {task.assignee}
                  </div>
                )}
                
                {task.dueDate && (
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Calendar className="h-3 w-3" />
                    {new Date(task.dueDate).toLocaleDateString()}
                  </div>
                )}
                
                {task.tags.map(tag => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    <Tag className="h-2 w-2 mr-1" />
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <ListTodo className="h-5 w-5" />
          <h3 className="font-semibold">Task Generator</h3>
          <Badge variant="secondary">{tasks.length} tasks</Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={generateTasksFromIdeas}
            disabled={isGenerating || selectedIdeaObjects.length === 0}
          >
            {isGenerating ? (
              <>
                <Sparkles className="h-4 w-4 mr-1 animate-pulse" />
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-1" />
                Generate Tasks
              </>
            )}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={exportTasks}
            disabled={tasks.length === 0}
          >
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
        </div>
      </div>

      {/* Task List */}
      <ScrollArea className="flex-1 p-4">
        {tasks.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <ListTodo className="h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">
              No tasks yet. Select ideas and click "Generate Tasks" to create actionable items.
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {tasks.map(task => (
              <TaskCard key={task.id} task={task} />
            ))}
          </div>
        )}
      </ScrollArea>

      {/* Edit Task Dialog */}
      {editingTask && (
        <Dialog open={!!editingTask} onOpenChange={() => setEditingTask(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Task</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label>Title</Label>
                <Input
                  value={editingTask.title}
                  onChange={(e) => setEditingTask({
                    ...editingTask,
                    title: e.target.value
                  })}
                />
              </div>
              
              <div>
                <Label>Description</Label>
                <Textarea
                  value={editingTask.description}
                  onChange={(e) => setEditingTask({
                    ...editingTask,
                    description: e.target.value
                  })}
                  rows={4}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Priority</Label>
                  <Select
                    value={editingTask.priority}
                    onValueChange={(value) => setEditingTask({
                      ...editingTask,
                      priority: value as Priority
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(Priority).map(priority => (
                        <SelectItem key={priority} value={priority}>
                          {priority}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label>Status</Label>
                  <Select
                    value={editingTask.status}
                    onValueChange={(value) => setEditingTask({
                      ...editingTask,
                      status: value as 'todo' | 'in_progress' | 'done'
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="todo">To Do</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="done">Done</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Estimated Effort (hours)</Label>
                  <Input
                    type="number"
                    value={editingTask.estimatedEffort}
                    onChange={(e) => setEditingTask({
                      ...editingTask,
                      estimatedEffort: parseInt(e.target.value) || 0
                    })}
                  />
                </div>
                
                <div>
                  <Label>Assignee</Label>
                  <Input
                    value={editingTask.assignee || ''}
                    onChange={(e) => setEditingTask({
                      ...editingTask,
                      assignee: e.target.value
                    })}
                    placeholder="Enter assignee name"
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setEditingTask(null)}>
                  Cancel
                </Button>
                <Button onClick={() => {
                  updateTask(editingTask.id, editingTask);
                  setEditingTask(null);
                }}>
                  Save Changes
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};