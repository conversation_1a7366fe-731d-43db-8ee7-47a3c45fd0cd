import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MessageSquare,
  Reply,
  Heart,
  ThumbsUp,
  ThumbsDown,
  Smile,
  MoreHorizontal,
  Edit3,
  Copy,
  Share,
  Bookmark,
  Flag,
  Search,
  Filter,
  Calendar,
  User,
  Bot,
  Mic,
  Image,
  Paperclip,
  Send,
  X,
  Check
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

// Import integrated components
import { MultiModalInput } from '../MultiModalInput';
import { VoiceRecorder } from '../VoiceRecorder';
import { PersonaSwitcher } from '../PersonaSwitcher';
import { MessageSearch } from '../../MessageSearch';

// Import advanced components
import { AdvancedIdeaVisualization } from './AdvancedIdeaVisualization';
import { CollaborativeEditingSystem } from './CollaborativeEditingSystem';

interface Reaction {
  emoji: string;
  count: number;
  users: string[];
  hasReacted: boolean;
}

interface MessageThread {
  id: string;
  parentId: string;
  replies: Message[];
}

interface Message {
  id: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    type: 'user' | 'assistant' | 'system';
  };
  timestamp: Date;
  reactions: Reaction[];
  thread?: MessageThread;
  isEdited?: boolean;
  editHistory?: Array<{ content: string; timestamp: Date }>;
  attachments?: Array<{ type: 'image' | 'file'; url: string; name: string }>;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  isBookmarked?: boolean;
}

interface EnhancedMessageInterfaceProps {
  messages: Message[];
  onSendMessage: (content: string, attachments?: File[]) => void;
  onEditMessage: (messageId: string, newContent: string) => void;
  onDeleteMessage: (messageId: string) => void;
  onReactToMessage: (messageId: string, emoji: string) => void;
  onReplyToMessage: (messageId: string, content: string) => void;
  onBookmarkMessage: (messageId: string) => void;
  currentUserId: string;
  className?: string;
}

/**
 * Enhanced Message Interface
 * Integrates: MessageSearch, MultiModalInput, VoiceRecorder, PersonaSwitcher
 * Implements: Task 5.1 (Enhanced message cards), Task 5.2 (Message search), Task 5.3 (Status displays)
 */
export const EnhancedMessageInterface: React.FC<EnhancedMessageInterfaceProps> = ({
  messages,
  onSendMessage,
  onEditMessage,
  onDeleteMessage,
  onReactToMessage,
  onReplyToMessage,
  onBookmarkMessage,
  currentUserId,
  className
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'user' | 'assistant' | 'system'>('all');
  const [dateRange, setDateRange] = useState<{ start?: Date; end?: Date }>({});
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [replyingToId, setReplyingToId] = useState<string | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState<string | null>(null);
  const [highlightedMessageId, setHighlightedMessageId] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Filter messages based on search and filters
  const filteredMessages = messages.filter(message => {
    // Filter by type
    if (selectedFilter !== 'all' && message.author.type !== selectedFilter) {
      return false;
    }

    // Filter by date range
    if (dateRange.start && message.timestamp < dateRange.start) {
      return false;
    }
    if (dateRange.end && message.timestamp > dateRange.end) {
      return false;
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        message.content.toLowerCase().includes(query) ||
        message.author.name.toLowerCase().includes(query)
      );
    }

    return true;
  });

  // Handle message search with highlighting
  const handleSearchResult = (messageId: string) => {
    setHighlightedMessageId(messageId);
    // Scroll to message
    const messageElement = document.getElementById(`message-${messageId}`);
    messageElement?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // Clear highlight after 3 seconds
    setTimeout(() => setHighlightedMessageId(null), 3000);
  };

  // Handle emoji reactions
  const handleReaction = (messageId: string, emoji: string) => {
    onReactToMessage(messageId, emoji);
    setShowEmojiPicker(null);
  };

  // Common emoji reactions
  const commonEmojis = ['👍', '❤️', '😂', '😮', '😢', '😡', '🎉', '🤔'];

  // Start editing message
  const startEditing = (message: Message) => {
    setEditingMessageId(message.id);
    setEditContent(message.content);
  };

  // Save edited message
  const saveEdit = () => {
    if (editingMessageId && editContent.trim()) {
      onEditMessage(editingMessageId, editContent.trim());
      setEditingMessageId(null);
      setEditContent('');
    }
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditingMessageId(null);
    setEditContent('');
  };

  // Start replying to message
  const startReply = (messageId: string) => {
    setReplyingToId(messageId);
  };

  // Get status icon for message
  const getStatusIcon = (status: Message['status']) => {
    switch (status) {
      case 'sending':
        return <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" />;
      case 'sent':
        return <Check className="w-3 h-3 text-gray-400" />;
      case 'delivered':
        return <div className="flex gap-0.5"><Check className="w-3 h-3 text-gray-400" /><Check className="w-3 h-3 text-gray-400 -ml-1" /></div>;
      case 'read':
        return <div className="flex gap-0.5"><Check className="w-3 h-3 text-blue-500" /><Check className="w-3 h-3 text-blue-500 -ml-1" /></div>;
      case 'failed':
        return <X className="w-3 h-3 text-red-500" />;
      default:
        return null;
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return timestamp.toLocaleDateString();
  };

  // Render message card
  const renderMessage = (message: Message) => {
    const isCurrentUser = message.author.id === currentUserId;
    const isEditing = editingMessageId === message.id;
    const isHighlighted = highlightedMessageId === message.id;

    return (
      <motion.div
        key={message.id}
        id={`message-${message.id}`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={cn(
          "group relative",
          isCurrentUser ? "ml-12" : "mr-12",
          isHighlighted && "ring-2 ring-primary ring-opacity-50"
        )}
      >
        <Card className={cn(
          "transition-all duration-200",
          isCurrentUser ? "bg-primary text-primary-foreground" : "bg-card",
          "hover:shadow-md"
        )}>
          <CardContent className="p-4">
            {/* Message Header */}
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Avatar className="w-6 h-6">
                  <AvatarImage src={message.author.avatar} />
                  <AvatarFallback>
                    {message.author.type === 'assistant' ? <Bot className="w-3 h-3" /> : 
                     message.author.type === 'system' ? '⚙️' : 
                     <User className="w-3 h-3" />}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm font-medium">{message.author.name}</span>
                {message.author.type === 'assistant' && (
                  <Badge variant="secondary" className="text-xs">AI</Badge>
                )}
                {message.isEdited && (
                  <Badge variant="outline" className="text-xs">Edited</Badge>
                )}
              </div>
              
              <div className="flex items-center gap-2 text-xs opacity-60">
                <span>{formatTimestamp(message.timestamp)}</span>
                {getStatusIcon(message.status)}
              </div>
            </div>

            {/* Message Content */}
            {isEditing ? (
              <div className="space-y-2">
                <Textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  className="min-h-[60px]"
                  autoFocus
                />
                <div className="flex gap-2">
                  <Button size="sm" onClick={saveEdit}>Save</Button>
                  <Button size="sm" variant="outline" onClick={cancelEdit}>Cancel</Button>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <p className="text-sm leading-relaxed whitespace-pre-wrap">
                  {message.content}
                </p>
                
                {/* Attachments */}
                {message.attachments && message.attachments.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {message.attachments.map((attachment, index) => (
                      <div key={index} className="flex items-center gap-1 text-xs bg-background/20 rounded px-2 py-1">
                        {attachment.type === 'image' ? <Image className="w-3 h-3" /> : <Paperclip className="w-3 h-3" />}
                        <span>{attachment.name}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Reactions */}
            {message.reactions.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {message.reactions.map((reaction, index) => (
                  <Button
                    key={index}
                    variant={reaction.hasReacted ? "default" : "outline"}
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={() => handleReaction(message.id, reaction.emoji)}
                  >
                    {reaction.emoji} {reaction.count}
                  </Button>
                ))}
              </div>
            )}

            {/* Thread Preview */}
            {message.thread && message.thread.replies.length > 0 && (
              <div className="mt-2 p-2 bg-background/20 rounded text-xs">
                <div className="flex items-center gap-1 text-muted-foreground">
                  <Reply className="w-3 h-3" />
                  <span>{message.thread.replies.length} replies</span>
                </div>
              </div>
            )}
          </CardContent>

          {/* Message Actions */}
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="flex items-center gap-1">
              {/* Emoji Reaction */}
              <Popover open={showEmojiPicker === message.id} onOpenChange={(open) => setShowEmojiPicker(open ? message.id : null)}>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Smile className="w-3 h-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-2">
                  <div className="flex gap-1">
                    {commonEmojis.map(emoji => (
                      <Button
                        key={emoji}
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => handleReaction(message.id, emoji)}
                      >
                        {emoji}
                      </Button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>

              {/* Reply */}
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => startReply(message.id)}
              >
                <Reply className="w-3 h-3" />
              </Button>

              {/* More Actions */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreHorizontal className="w-3 h-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-1">
                  <div className="space-y-1">
                    {isCurrentUser && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start"
                        onClick={() => startEditing(message)}
                      >
                        <Edit3 className="w-3 h-3 mr-2" />
                        Edit
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => navigator.clipboard.writeText(message.content)}
                    >
                      <Copy className="w-3 h-3 mr-2" />
                      Copy
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => onBookmarkMessage(message.id)}
                    >
                      <Bookmark className={cn("w-3 h-3 mr-2", message.isBookmarked && "fill-current")} />
                      {message.isBookmarked ? 'Remove Bookmark' : 'Bookmark'}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start"
                    >
                      <Share className="w-3 h-3 mr-2" />
                      Share
                    </Button>
                    <Separator />
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start text-red-600"
                    >
                      <Flag className="w-3 h-3 mr-2" />
                      Report
                    </Button>
                    {isCurrentUser && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start text-red-600"
                        onClick={() => onDeleteMessage(message.id)}
                      >
                        <X className="w-3 h-3 mr-2" />
                        Delete
                      </Button>
                    )}
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Search and Filter Header */}
      <div className="flex-shrink-0 p-4 border-b space-y-3">
        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              ref={searchInputRef}
              placeholder="Search messages..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
          
          <Select value={selectedFilter} onValueChange={(value: any) => setSelectedFilter(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="user">User</SelectItem>
              <SelectItem value="assistant">Assistant</SelectItem>
              <SelectItem value="system">System</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="sm">
            <Calendar className="w-4 h-4 mr-2" />
            Date Range
          </Button>
        </div>

        {/* Integrated Message Search */}
        <MessageSearch
          onSearchResult={handleSearchResult}
          messages={messages}
        />
      </div>

      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {filteredMessages.map(renderMessage)}
          
          {/* Typing Indicator */}
          <AnimatePresence>
            {(isTyping || typingUsers.length > 0) && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="flex items-center gap-2 text-sm text-muted-foreground"
              >
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
                <span>
                  {typingUsers.length > 0 
                    ? `${typingUsers.join(', ')} ${typingUsers.length === 1 ? 'is' : 'are'} typing...`
                    : 'AI is thinking...'
                  }
                </span>
              </motion.div>
            )}
          </AnimatePresence>
          
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Reply Context */}
      <AnimatePresence>
        {replyingToId && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="flex-shrink-0 p-3 bg-muted border-t"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm">
                <Reply className="w-4 h-4" />
                <span>Replying to message</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setReplyingToId(null)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Enhanced Input Area */}
      <div className="flex-shrink-0 p-4 border-t">
        <div className="space-y-3">
          {/* Persona Switcher */}
          <PersonaSwitcher />
          
          {/* Multi-Modal Input */}
          <MultiModalInput
            onSubmit={(content, attachments) => {
              if (replyingToId) {
                onReplyToMessage(replyingToId, content);
                setReplyingToId(null);
              } else {
                onSendMessage(content, attachments);
              }
            }}
            placeholder={replyingToId ? "Write a reply..." : "Type your message..."}
            showVoiceRecorder={true}
            showPersonaSwitcher={false} // Already shown above
          />
        </div>
      </div>
    </div>
  );
};

export default EnhancedMessageInterface;