import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  UserPlus,
  UserMinus,
  MessageCircle,
  Edit3,
  Eye,
  EyeOff,
  Share2,
  Lock,
  Unlock,
  Wifi,
  WifiOff,
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Settings,
  Crown,
  Shield,
  User
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

// Import integrated collaboration components
import { CollaborationPanel } from '../CollaborationPanel';
import { RealTimeCollaboration } from '../RealTimeCollaboration';
import { SyncManager } from '../SyncManager';

interface Collaborator {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'editor' | 'viewer';
  status: 'online' | 'offline' | 'away';
  lastSeen: Date;
  cursor?: {
    x: number;
    y: number;
    selection?: { start: number; end: number };
  };
  permissions: {
    canEdit: boolean;
    canComment: boolean;
    canShare: boolean;
    canManageUsers: boolean;
  };
}

interface EditOperation {
  id: string;
  type: 'insert' | 'delete' | 'replace';
  position: number;
  content: string;
  author: string;
  timestamp: Date;
  applied: boolean;
}

interface Comment {
  id: string;
  content: string;
  author: Collaborator;
  timestamp: Date;
  position: number;
  resolved: boolean;
  replies: Comment[];
}

interface CollaborativeEditingSystemProps {
  documentId: string;
  content: string;
  onContentChange: (content: string) => void;
  currentUser: Collaborator;
  collaborators: Collaborator[];
  onCollaboratorAdd: (email: string, role: Collaborator['role']) => void;
  onCollaboratorRemove: (userId: string) => void;
  onCollaboratorRoleChange: (userId: string, role: Collaborator['role']) => void;
  onCommentAdd: (comment: Omit<Comment, 'id' | 'timestamp' | 'replies'>) => void;
  onCommentResolve: (commentId: string) => void;
  className?: string;
}

/**
 * Collaborative Editing System
 * Integrates: CollaborationPanel, RealTimeCollaboration, SyncManager
 * Implements: Real-time collaborative editing with conflict resolution
 */
export const CollaborativeEditingSystem: React.FC<CollaborativeEditingSystemProps> = ({
  documentId,
  content,
  onContentChange,
  currentUser,
  collaborators,
  onCollaboratorAdd,
  onCollaboratorRemove,
  onCollaboratorRoleChange,
  onCommentAdd,
  onCommentResolve,
  className
}) => {
  const [localContent, setLocalContent] = useState(content);
  const [pendingOperations, setPendingOperations] = useState<EditOperation[]>([]);
  const [comments, setComments] = useState<Comment[]>([]);
  const [selectedText, setSelectedText] = useState<{ start: number; end: number } | null>(null);
  const [showCommentDialog, setShowCommentDialog] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connected');
  const [conflictResolution, setConflictResolution] = useState<'auto' | 'manual'>('auto');
  const [showCollaborators, setShowCollaborators] = useState(true);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<Collaborator['role']>('editor');

  const editorRef = useRef<HTMLTextAreaElement>(null);
  const operationQueueRef = useRef<EditOperation[]>([]);
  const lastSyncRef = useRef<Date>(new Date());

  // Simulate real-time collaboration updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate receiving operations from other users
      if (Math.random() > 0.8) {
        const randomCollaborator = collaborators[Math.floor(Math.random() * collaborators.length)];
        if (randomCollaborator && randomCollaborator.id !== currentUser.id) {
          simulateRemoteOperation(randomCollaborator);
        }
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [collaborators, currentUser.id]);

  // Handle text selection for comments
  const handleTextSelection = useCallback(() => {
    if (editorRef.current) {
      const start = editorRef.current.selectionStart;
      const end = editorRef.current.selectionEnd;
      
      if (start !== end) {
        setSelectedText({ start, end });
      } else {
        setSelectedText(null);
      }
    }
  }, []);

  // Apply operation to content
  const applyOperation = useCallback((operation: EditOperation, targetContent: string): string => {
    switch (operation.type) {
      case 'insert':
        return targetContent.slice(0, operation.position) + 
               operation.content + 
               targetContent.slice(operation.position);
      case 'delete':
        return targetContent.slice(0, operation.position) + 
               targetContent.slice(operation.position + operation.content.length);
      case 'replace':
        const endPos = operation.position + operation.content.length;
        return targetContent.slice(0, operation.position) + 
               operation.content + 
               targetContent.slice(endPos);
      default:
        return targetContent;
    }
  }, []);

  // Transform operation based on concurrent operations (Operational Transformation)
  const transformOperation = useCallback((op1: EditOperation, op2: EditOperation): EditOperation => {
    if (op1.position <= op2.position) {
      return op1;
    }
    
    // Adjust position based on the other operation
    let newPosition = op1.position;
    if (op2.type === 'insert') {
      newPosition += op2.content.length;
    } else if (op2.type === 'delete') {
      newPosition -= op2.content.length;
    }
    
    return { ...op1, position: Math.max(0, newPosition) };
  }, []);

  // Handle local content changes
  const handleContentChange = useCallback((newContent: string) => {
    const operation: EditOperation = {
      id: `op-${Date.now()}-${Math.random()}`,
      type: 'replace',
      position: 0,
      content: newContent,
      author: currentUser.id,
      timestamp: new Date(),
      applied: false
    };

    setLocalContent(newContent);
    setPendingOperations(prev => [...prev, operation]);
    
    // Simulate sending to server
    setTimeout(() => {
      setPendingOperations(prev => prev.filter(op => op.id !== operation.id));
      onContentChange(newContent);
    }, 100);
  }, [currentUser.id, onContentChange]);

  // Simulate remote operation
  const simulateRemoteOperation = useCallback((collaborator: Collaborator) => {
    const operations = [
      'added a comment',
      'edited a section',
      'highlighted text',
      'made a suggestion'
    ];
    
    const operation = operations[Math.floor(Math.random() * operations.length)];
    
    // Create a mock edit operation
    const mockOperation: EditOperation = {
      id: `remote-${Date.now()}-${Math.random()}`,
      type: 'insert',
      position: Math.floor(Math.random() * localContent.length),
      content: ` [${collaborator.name} ${operation}] `,
      author: collaborator.id,
      timestamp: new Date(),
      applied: true
    };

    // Apply transformation if there are pending operations
    let transformedOp = mockOperation;
    pendingOperations.forEach(pendingOp => {
      transformedOp = transformOperation(transformedOp, pendingOp);
    });

    const newContent = applyOperation(transformedOp, localContent);
    setLocalContent(newContent);
  }, [localContent, pendingOperations, transformOperation, applyOperation]);

  // Handle adding comment
  const handleAddComment = useCallback(() => {
    if (newComment.trim() && selectedText) {
      const comment: Comment = {
        id: `comment-${Date.now()}`,
        content: newComment.trim(),
        author: currentUser,
        timestamp: new Date(),
        position: selectedText.start,
        resolved: false,
        replies: []
      };

      setComments(prev => [...prev, comment]);
      onCommentAdd(comment);
      setNewComment('');
      setShowCommentDialog(false);
      setSelectedText(null);
    }
  }, [newComment, selectedText, currentUser, onCommentAdd]);

  // Handle inviting collaborator
  const handleInviteCollaborator = useCallback(() => {
    if (inviteEmail.trim()) {
      onCollaboratorAdd(inviteEmail.trim(), inviteRole);
      setInviteEmail('');
    }
  }, [inviteEmail, inviteRole, onCollaboratorAdd]);

  // Get role badge variant
  const getRoleBadgeVariant = (role: Collaborator['role']) => {
    switch (role) {
      case 'owner': return 'default';
      case 'editor': return 'secondary';
      case 'viewer': return 'outline';
      default: return 'outline';
    }
  };

  // Get role icon
  const getRoleIcon = (role: Collaborator['role']) => {
    switch (role) {
      case 'owner': return Crown;
      case 'editor': return Edit3;
      case 'viewer': return Eye;
      default: return User;
    }
  };

  // Get status color
  const getStatusColor = (status: Collaborator['status']) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      case 'offline': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  return (
    <div className={cn("flex h-full", className)}>
      {/* Main Editor Area */}
      <div className="flex-1 flex flex-col">
        {/* Editor Header */}
        <div className="flex-shrink-0 border-b bg-card p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h2 className="font-semibold">Collaborative Document</h2>
              <div className="flex items-center gap-2">
                {connectionStatus === 'connected' ? (
                  <Wifi className="w-4 h-4 text-green-500" />
                ) : connectionStatus === 'connecting' ? (
                  <RefreshCw className="w-4 h-4 text-yellow-500 animate-spin" />
                ) : (
                  <WifiOff className="w-4 h-4 text-red-500" />
                )}
                <span className="text-sm text-muted-foreground capitalize">
                  {connectionStatus}
                </span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* Collaborator Avatars */}
              <div className="flex -space-x-2">
                {collaborators.slice(0, 5).map(collaborator => (
                  <TooltipProvider key={collaborator.id}>
                    <Tooltip>
                      <TooltipTrigger>
                        <div className="relative">
                          <Avatar className="w-8 h-8 border-2 border-background">
                            <AvatarImage src={collaborator.avatar} />
                            <AvatarFallback className="text-xs">
                              {collaborator.name.slice(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className={cn(
                            "absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background",
                            getStatusColor(collaborator.status)
                          )} />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-center">
                          <p className="font-medium">{collaborator.name}</p>
                          <p className="text-xs text-muted-foreground capitalize">
                            {collaborator.status} • {collaborator.role}
                          </p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ))}
                {collaborators.length > 5 && (
                  <div className="w-8 h-8 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs font-medium">
                    +{collaborators.length - 5}
                  </div>
                )}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCollaborators(!showCollaborators)}
              >
                <Users className="w-4 h-4 mr-2" />
                {showCollaborators ? 'Hide' : 'Show'} Panel
              </Button>

              <Button variant="outline" size="sm">
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </div>

        {/* Editor Content */}
        <div className="flex-1 relative">
          <Textarea
            ref={editorRef}
            value={localContent}
            onChange={(e) => handleContentChange(e.target.value)}
            onSelect={handleTextSelection}
            className="w-full h-full resize-none border-0 focus:ring-0 text-sm leading-relaxed p-6"
            placeholder="Start typing to collaborate..."
          />

          {/* Comment Indicators */}
          {comments.map(comment => (
            <div
              key={comment.id}
              className="absolute right-2 w-2 h-4 bg-yellow-400 rounded cursor-pointer"
              style={{
                top: `${(comment.position / localContent.length) * 100}%`
              }}
              onClick={() => {
                // Scroll to comment position
                if (editorRef.current) {
                  editorRef.current.setSelectionRange(comment.position, comment.position);
                  editorRef.current.focus();
                }
              }}
            />
          ))}

          {/* Selection Comment Button */}
          <AnimatePresence>
            {selectedText && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                className="absolute z-10 bg-popover border rounded-lg shadow-lg p-2"
                style={{
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)'
                }}
              >
                <Button
                  size="sm"
                  onClick={() => setShowCommentDialog(true)}
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Add Comment
                </Button>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Pending Operations Indicator */}
          <AnimatePresence>
            {pendingOperations.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                className="absolute bottom-4 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs flex items-center gap-2"
              >
                <RefreshCw className="w-3 h-3 animate-spin" />
                Syncing changes...
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Status Bar */}
        <div className="flex-shrink-0 border-t bg-card px-4 py-2">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>Last saved: {lastSyncRef.current.toLocaleTimeString()}</span>
              <span>{localContent.length} characters</span>
              <span>{comments.length} comments</span>
            </div>
            
            <div className="flex items-center gap-2">
              <Label htmlFor="conflict-resolution" className="text-xs">
                Conflict Resolution:
              </Label>
              <Select value={conflictResolution} onValueChange={(value: any) => setConflictResolution(value)}>
                <SelectTrigger className="h-6 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">Auto</SelectItem>
                  <SelectItem value="manual">Manual</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* Collaboration Sidebar */}
      <AnimatePresence>
        {showCollaborators && (
          <motion.div
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 320, opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
            className="border-l bg-card overflow-hidden"
          >
            <div className="h-full flex flex-col">
              {/* Sidebar Header */}
              <div className="flex-shrink-0 p-4 border-b">
                <h3 className="font-semibold mb-3">Collaboration</h3>
                
                {/* Invite Collaborator */}
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Email address"
                      value={inviteEmail}
                      onChange={(e) => setInviteEmail(e.target.value)}
                      className="text-sm"
                    />
                    <Select value={inviteRole} onValueChange={(value: any) => setInviteRole(value)}>
                      <SelectTrigger className="w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="viewer">Viewer</SelectItem>
                        <SelectItem value="editor">Editor</SelectItem>
                        <SelectItem value="owner">Owner</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button
                    size="sm"
                    onClick={handleInviteCollaborator}
                    className="w-full"
                    disabled={!inviteEmail.trim()}
                  >
                    <UserPlus className="w-4 h-4 mr-2" />
                    Invite
                  </Button>
                </div>
              </div>

              {/* Collaborators List */}
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-muted-foreground">
                    Active Collaborators ({collaborators.length})
                  </h4>
                  
                  {collaborators.map(collaborator => {
                    const RoleIcon = getRoleIcon(collaborator.role);
                    
                    return (
                      <div key={collaborator.id} className="flex items-center justify-between p-2 rounded-lg hover:bg-muted/50">
                        <div className="flex items-center gap-3">
                          <div className="relative">
                            <Avatar className="w-8 h-8">
                              <AvatarImage src={collaborator.avatar} />
                              <AvatarFallback className="text-xs">
                                {collaborator.name.slice(0, 2).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div className={cn(
                              "absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background",
                              getStatusColor(collaborator.status)
                            )} />
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <p className="text-sm font-medium truncate">
                                {collaborator.name}
                                {collaborator.id === currentUser.id && ' (You)'}
                              </p>
                              <RoleIcon className="w-3 h-3 text-muted-foreground" />
                            </div>
                            <p className="text-xs text-muted-foreground">
                              {collaborator.status === 'online' ? 'Active now' : 
                               `Last seen ${collaborator.lastSeen.toLocaleTimeString()}`}
                            </p>
                          </div>
                        </div>

                        {currentUser.permissions.canManageUsers && collaborator.id !== currentUser.id && (
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <Settings className="w-4 h-4" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-48 p-2">
                              <div className="space-y-1">
                                <Select
                                  value={collaborator.role}
                                  onValueChange={(value: any) => onCollaboratorRoleChange(collaborator.id, value)}
                                >
                                  <SelectTrigger className="h-8">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="viewer">Viewer</SelectItem>
                                    <SelectItem value="editor">Editor</SelectItem>
                                    <SelectItem value="owner">Owner</SelectItem>
                                  </SelectContent>
                                </Select>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="w-full justify-start text-red-600"
                                  onClick={() => onCollaboratorRemove(collaborator.id)}
                                >
                                  <UserMinus className="w-4 h-4 mr-2" />
                                  Remove
                                </Button>
                              </div>
                            </PopoverContent>
                          </Popover>
                        )}
                      </div>
                    );
                  })}
                </div>
              </ScrollArea>

              {/* Integrated Components */}
              <div className="flex-shrink-0 border-t">
                <CollaborationPanel />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Comment Dialog */}
      <AnimatePresence>
        {showCommentDialog && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
            onClick={() => setShowCommentDialog(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-background border rounded-lg p-6 w-96 max-w-[90vw]"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="font-semibold mb-4">Add Comment</h3>
              <Textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Write your comment..."
                className="mb-4"
                autoFocus
              />
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCommentDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddComment} disabled={!newComment.trim()}>
                  Add Comment
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Integrated Real-time Collaboration */}
      <RealTimeCollaboration
        documentId={documentId}
        collaborators={collaborators}
        currentUser={currentUser}
      />

      {/* Integrated Sync Manager */}
      <SyncManager
        documentId={documentId}
        content={localContent}
        onSyncComplete={() => {
          lastSyncRef.current = new Date();
        }}
      />
    </div>
  );
};

export default CollaborativeEditingSystem;