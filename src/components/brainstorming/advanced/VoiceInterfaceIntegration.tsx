import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Play,
  Pause,
  Square,
  SkipBack,
  SkipForward,
  Settings,
  Languages,
  Waveform,
  FileAudio,
  Download,
  Upload,
  Trash2,
  Edit3,
  Save,
  X,
  Check,
  AlertCircle,
  Loader2
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/scroll-area';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

// Import integrated voice components
import { VoiceInterface } from '../VoiceInterface';
import { VoiceRecorder } from '../VoiceRecorder';
import { MultiModalInput } from '../MultiModalInput';

interface VoiceRecording {
  id: string;
  name: string;
  duration: number;
  timestamp: Date;
  transcription?: string;
  audioUrl: string;
  waveform: number[];
  isProcessing: boolean;
}

interface VoiceCommand {
  command: string;
  action: string;
  parameters?: Record<string, any>;
  confidence: number;
}

interface VoiceSettings {
  language: string;
  speechRate: number;
  speechPitch: number;
  speechVolume: number;
  autoTranscribe: boolean;
  voiceActivation: boolean;
  noiseReduction: boolean;
  echoCancellation: boolean;
}

interface VoiceInterfaceIntegrationProps {
  onVoiceCommand: (command: VoiceCommand) => void;
  onTranscriptionComplete: (text: string, audioUrl: string) => void;
  onRecordingSave: (recording: VoiceRecording) => void;
  className?: string;
}

/**
 * Voice Interface Integration
 * Integrates: VoiceInterface, VoiceRecorder, MultiModalInput
 * Implements: Complete voice interaction system with recording, transcription, and commands
 */
export const VoiceInterfaceIntegration: React.FC<VoiceInterfaceIntegrationProps> = ({
  onVoiceCommand,
  onTranscriptionComplete,
  onRecordingSave,
  className
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentRecording, setCurrentRecording] = useState<VoiceRecording | null>(null);
  const [recordings, setRecordings] = useState<VoiceRecording[]>([]);
  const [isListening, setIsListening] = useState(false);
  const [transcriptionText, setTranscriptionText] = useState('');
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [voiceSettings, setVoiceSettings] = useState<VoiceSettings>({
    language: 'en-US',
    speechRate: 1,
    speechPitch: 1,
    speechVolume: 0.8,
    autoTranscribe: true,
    voiceActivation: false,
    noiseReduction: true,
    echoCancellation: true
  });

  const [waveformData, setWaveformData] = useState<number[]>([]);
  const [audioLevel, setAudioLevel] = useState(0);
  const [editingRecording, setEditingRecording] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationFrameRef = useRef<number>();

  // Initialize audio context and analyzer
  useEffect(() => {
    const initializeAudio = async () => {
      try {
        audioContextRef.current = new AudioContext();
        analyserRef.current = audioContextRef.current.createAnalyser();
        analyserRef.current.fftSize = 256;
      } catch (error) {
        console.error('Failed to initialize audio context:', error);
      }
    };

    initializeAudio();

    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  // Audio level monitoring
  const monitorAudioLevel = () => {
    if (!analyserRef.current) return;

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    analyserRef.current.getByteFrequencyData(dataArray);
    
    const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
    setAudioLevel(average / 255);

    // Update waveform data
    setWaveformData(prev => [...prev.slice(-50), average / 255]);

    animationFrameRef.current = requestAnimationFrame(monitorAudioLevel);
  };

  // Start recording
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: voiceSettings.echoCancellation,
          noiseSuppression: voiceSettings.noiseReduction,
          autoGainControl: true
        }
      });

      // Connect to analyzer
      if (audioContextRef.current && analyserRef.current) {
        const source = audioContextRef.current.createMediaStreamSource(stream);
        source.connect(analyserRef.current);
        monitorAudioLevel();
      }

      mediaRecorderRef.current = new MediaRecorder(stream);
      const chunks: Blob[] = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        chunks.push(event.data);
      };

      mediaRecorderRef.current.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(blob);
        
        const recording: VoiceRecording = {
          id: `recording-${Date.now()}`,
          name: `Recording ${recordings.length + 1}`,
          duration: Date.now() - recordingStartTime,
          timestamp: new Date(),
          audioUrl,
          waveform: [...waveformData],
          isProcessing: voiceSettings.autoTranscribe
        };

        setCurrentRecording(recording);
        setRecordings(prev => [...prev, recording]);

        if (voiceSettings.autoTranscribe) {
          transcribeAudio(recording);
        }

        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      };

      const recordingStartTime = Date.now();
      mediaRecorderRef.current.start();
      setIsRecording(true);
      setWaveformData([]);
    } catch (error) {
      console.error('Failed to start recording:', error);
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    }
  };

  // Transcribe audio
  const transcribeAudio = async (recording: VoiceRecording) => {
    setIsTranscribing(true);
    
    try {
      // Simulate transcription API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockTranscription = "This is a mock transcription of the audio recording. In a real implementation, this would be the actual transcribed text from the audio.";
      
      setRecordings(prev => prev.map(r => 
        r.id === recording.id 
          ? { ...r, transcription: mockTranscription, isProcessing: false }
          : r
      ));

      setTranscriptionText(mockTranscription);
      onTranscriptionComplete(mockTranscription, recording.audioUrl);
    } catch (error) {
      console.error('Transcription failed:', error);
      setRecordings(prev => prev.map(r => 
        r.id === recording.id 
          ? { ...r, isProcessing: false }
          : r
      ));
    } finally {
      setIsTranscribing(false);
    }
  };

  // Play recording
  const playRecording = (recording: VoiceRecording) => {
    const audio = new Audio(recording.audioUrl);
    audio.play();
    setIsPlaying(true);
    
    audio.onended = () => {
      setIsPlaying(false);
    };
  };

  // Delete recording
  const deleteRecording = (recordingId: string) => {
    setRecordings(prev => prev.filter(r => r.id !== recordingId));
    if (currentRecording?.id === recordingId) {
      setCurrentRecording(null);
    }
  };

  // Save recording name
  const saveRecordingName = (recordingId: string) => {
    if (editingName.trim()) {
      setRecordings(prev => prev.map(r => 
        r.id === recordingId 
          ? { ...r, name: editingName.trim() }
          : r
      ));
    }
    setEditingRecording(null);
    setEditingName('');
  };

  // Format duration
  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
  };

  // Render waveform
  const renderWaveform = (data: number[], height = 40) => (
    <div className="flex items-end justify-center gap-0.5" style={{ height }}>
      {data.map((value, index) => (
        <div
          key={index}
          className="bg-primary rounded-sm transition-all duration-100"
          style={{
            width: '2px',
            height: `${Math.max(2, value * height)}px`
          }}
        />
      ))}
    </div>
  );

  return (
    <div className={cn("flex flex-col h-full", className)}>
      <Tabs defaultValue="recorder" className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="recorder">Recorder</TabsTrigger>
          <TabsTrigger value="transcription">Transcription</TabsTrigger>
          <TabsTrigger value="commands">Commands</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="recorder" className="flex-1 flex flex-col space-y-4">
          {/* Recording Controls */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Voice Recorder</span>
                <div className="flex items-center gap-2">
                  {isRecording && (
                    <Badge variant="destructive" className="animate-pulse">
                      Recording
                    </Badge>
                  )}
                  {isListening && (
                    <Badge variant="default">
                      Listening
                    </Badge>
                  )}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Waveform Visualization */}
              <div className="bg-muted rounded-lg p-4">
                {isRecording ? (
                  <div className="space-y-2">
                    <div className="text-center text-sm text-muted-foreground">
                      Recording in progress...
                    </div>
                    {renderWaveform(waveformData.slice(-50), 60)}
                    <div className="text-center text-xs text-muted-foreground">
                      Audio Level: {Math.round(audioLevel * 100)}%
                    </div>
                  </div>
                ) : currentRecording ? (
                  <div className="space-y-2">
                    <div className="text-center text-sm font-medium">
                      {currentRecording.name}
                    </div>
                    {renderWaveform(currentRecording.waveform, 60)}
                    <div className="text-center text-xs text-muted-foreground">
                      Duration: {formatDuration(currentRecording.duration)}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Mic className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>Click record to start</p>
                  </div>
                )}
              </div>

              {/* Control Buttons */}
              <div className="flex items-center justify-center gap-2">
                <Button
                  variant={isRecording ? "destructive" : "default"}
                  size="lg"
                  onClick={isRecording ? stopRecording : startRecording}
                  className="rounded-full w-16 h-16"
                >
                  {isRecording ? (
                    <Square className="w-6 h-6" />
                  ) : (
                    <Mic className="w-6 h-6" />
                  )}
                </Button>

                {currentRecording && (
                  <>
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => playRecording(currentRecording)}
                      disabled={isPlaying}
                      className="rounded-full w-12 h-12"
                    >
                      {isPlaying ? (
                        <Pause className="w-4 h-4" />
                      ) : (
                        <Play className="w-4 h-4" />
                      )}
                    </Button>

                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => onRecordingSave(currentRecording)}
                      className="rounded-full w-12 h-12"
                    >
                      <Save className="w-4 h-4" />
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Recordings List */}
          <Card className="flex-1">
            <CardHeader>
              <CardTitle>Recordings ({recordings.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {recordings.map(recording => (
                    <div
                      key={recording.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
                    >
                      <div className="flex items-center gap-3 flex-1">
                        <div className="w-12 h-8">
                          {renderWaveform(recording.waveform.slice(0, 20), 32)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          {editingRecording === recording.id ? (
                            <div className="flex items-center gap-2">
                              <Input
                                value={editingName}
                                onChange={(e) => setEditingName(e.target.value)}
                                className="h-8"
                                autoFocus
                              />
                              <Button
                                size="sm"
                                onClick={() => saveRecordingName(recording.id)}
                              >
                                <Check className="w-3 h-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => setEditingRecording(null)}
                              >
                                <X className="w-3 h-3" />
                              </Button>
                            </div>
                          ) : (
                            <div>
                              <p className="font-medium text-sm truncate">
                                {recording.name}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {formatDuration(recording.duration)} • {recording.timestamp.toLocaleTimeString()}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-1">
                        {recording.isProcessing && (
                          <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                        )}
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => playRecording(recording)}
                        >
                          <Play className="w-3 h-3" />
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setEditingRecording(recording.id);
                            setEditingName(recording.name);
                          }}
                        >
                          <Edit3 className="w-3 h-3" />
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteRecording(recording.id)}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                  
                  {recordings.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <FileAudio className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p>No recordings yet</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transcription" className="flex-1 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Transcription</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {isTranscribing ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="w-6 h-6 animate-spin mr-2" />
                  <span>Transcribing audio...</span>
                </div>
              ) : transcriptionText ? (
                <div className="space-y-3">
                  <Textarea
                    value={transcriptionText}
                    onChange={(e) => setTranscriptionText(e.target.value)}
                    className="min-h-32"
                    placeholder="Transcription will appear here..."
                  />
                  <div className="flex gap-2">
                    <Button size="sm">
                      <Save className="w-4 h-4 mr-2" />
                      Save
                    </Button>
                    <Button size="sm" variant="outline">
                      <Download className="w-4 h-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Languages className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>Record audio to see transcription</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="commands" className="flex-1">
          <VoiceInterface
            onCommand={onVoiceCommand}
            isListening={isListening}
            onListeningChange={setIsListening}
          />
        </TabsContent>

        <TabsContent value="settings" className="flex-1 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Voice Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Language</Label>
                  <Select
                    value={voiceSettings.language}
                    onValueChange={(value) => setVoiceSettings(prev => ({ ...prev, language: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en-US">English (US)</SelectItem>
                      <SelectItem value="en-GB">English (UK)</SelectItem>
                      <SelectItem value="es-ES">Spanish</SelectItem>
                      <SelectItem value="fr-FR">French</SelectItem>
                      <SelectItem value="de-DE">German</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Speech Rate: {voiceSettings.speechRate}</Label>
                  <Slider
                    value={[voiceSettings.speechRate]}
                    onValueChange={([value]) => setVoiceSettings(prev => ({ ...prev, speechRate: value }))}
                    min={0.5}
                    max={2}
                    step={0.1}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Speech Pitch: {voiceSettings.speechPitch}</Label>
                  <Slider
                    value={[voiceSettings.speechPitch]}
                    onValueChange={([value]) => setVoiceSettings(prev => ({ ...prev, speechPitch: value }))}
                    min={0.5}
                    max={2}
                    step={0.1}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Speech Volume: {Math.round(voiceSettings.speechVolume * 100)}%</Label>
                  <Slider
                    value={[voiceSettings.speechVolume]}
                    onValueChange={([value]) => setVoiceSettings(prev => ({ ...prev, speechVolume: value }))}
                    min={0}
                    max={1}
                    step={0.1}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-transcribe">Auto Transcribe</Label>
                  <Switch
                    id="auto-transcribe"
                    checked={voiceSettings.autoTranscribe}
                    onCheckedChange={(checked) => setVoiceSettings(prev => ({ ...prev, autoTranscribe: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="voice-activation">Voice Activation</Label>
                  <Switch
                    id="voice-activation"
                    checked={voiceSettings.voiceActivation}
                    onCheckedChange={(checked) => setVoiceSettings(prev => ({ ...prev, voiceActivation: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="noise-reduction">Noise Reduction</Label>
                  <Switch
                    id="noise-reduction"
                    checked={voiceSettings.noiseReduction}
                    onCheckedChange={(checked) => setVoiceSettings(prev => ({ ...prev, noiseReduction: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="echo-cancellation">Echo Cancellation</Label>
                  <Switch
                    id="echo-cancellation"
                    checked={voiceSettings.echoCancellation}
                    onCheckedChange={(checked) => setVoiceSettings(prev => ({ ...prev, echoCancellation: checked }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Integrated Multi-Modal Input */}
      <div className="flex-shrink-0 border-t p-4">
        <MultiModalInput
          onSubmit={(content, attachments) => {
            console.log('Multi-modal input:', content, attachments);
          }}
          showVoiceRecorder={false} // Already integrated above
          placeholder="Type or speak your message..."
        />
      </div>
    </div>
  );
};

export default VoiceInterfaceIntegration;