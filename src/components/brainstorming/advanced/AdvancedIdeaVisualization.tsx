import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Grid3x3,
  List,
  Network,
  Calendar,
  BarChart3,
  Zap,
  Target,
  Lightbulb,
  Users,
  Clock,
  Star,
  Filter,
  Search,
  Plus,
  Minus,
  RotateCcw,
  Download,
  Share,
  Settings,
  Eye,
  EyeOff,
  Maximize2,
  Minimize2
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

// Import integrated visualization components
import { MindMapView } from '../MindMapView';
import { KanbanView } from '../KanbanView';
import { MatrixView } from '../MatrixView';
import { InteractiveIdeaMap } from '../InteractiveIdeaMap';
import { ClusterManager } from '../ClusterManager';

// Import visualization components from visualizations directory
import { RelationshipGraph } from '../visualizations/RelationshipGraph';
import { TimelineView } from '../visualizations/TimelineView';
import { MindMapVisualization } from '../visualizations/MindMapVisualization';
import { KanbanBoard } from '../visualizations/KanbanBoard';

import { useBrainstormStore } from '@/stores/brainstormStore';
import { Idea, IdeaStatus, Priority } from '@/types/brainstorm';

type ViewMode = 'grid' | 'list' | 'mindmap' | 'kanban' | 'matrix' | 'timeline' | 'network' | 'cluster';

interface ViewConfig {
  id: ViewMode;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  component: React.ComponentType<any>;
}

interface AdvancedIdeaVisualizationProps {
  ideas: Idea[];
  onIdeaUpdate: (idea: Idea) => void;
  onIdeaCreate: (idea: Partial<Idea>) => void;
  onIdeaDelete: (ideaId: string) => void;
  className?: string;
}

/**
 * Advanced Idea Visualization System
 * Integrates: MindMapView, KanbanView, MatrixView, InteractiveIdeaMap, ClusterManager
 * Implements: Task 6.2 (Multiple view modes), Task 6.3 (Search and filtering), Task 6.4 (Drag-and-drop)
 */
export const AdvancedIdeaVisualization: React.FC<AdvancedIdeaVisualizationProps> = ({
  ideas,
  onIdeaUpdate,
  onIdeaCreate,
  onIdeaDelete,
  className
}) => {
  const [currentView, setCurrentView] = useState<ViewMode>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState<{
    status: IdeaStatus | 'all';
    priority: Priority | 'all';
    category: string | 'all';
    dateRange: { start?: Date; end?: Date };
  }>({
    status: 'all',
    priority: 'all',
    category: 'all',
    dateRange: {}
  });
  
  const [viewSettings, setViewSettings] = useState({
    showConnections: true,
    showMetadata: true,
    groupByCategory: false,
    sortBy: 'created',
    sortOrder: 'desc' as 'asc' | 'desc',
    zoomLevel: 100,
    showMinimap: false,
    autoLayout: true
  });

  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedIdeas, setSelectedIdeas] = useState<string[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // Available view configurations
  const viewConfigs: ViewConfig[] = [
    {
      id: 'grid',
      name: 'Grid View',
      icon: Grid3x3,
      description: 'Card-based grid layout',
      component: ({ ideas, onUpdate, onDelete }: any) => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4">
          {ideas.map((idea: Idea) => (
            <IdeaCard
              key={idea.id}
              idea={idea}
              onUpdate={onUpdate}
              onDelete={onDelete}
              onSelect={(id) => setSelectedIdeas(prev => 
                prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
              )}
              isSelected={selectedIdeas.includes(idea.id)}
            />
          ))}
        </div>
      )
    },
    {
      id: 'list',
      name: 'List View',
      icon: List,
      description: 'Detailed list layout',
      component: ({ ideas, onUpdate, onDelete }: any) => (
        <div className="space-y-2 p-4">
          {ideas.map((idea: Idea) => (
            <IdeaListItem
              key={idea.id}
              idea={idea}
              onUpdate={onUpdate}
              onDelete={onDelete}
              onSelect={(id) => setSelectedIdeas(prev => 
                prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]
              )}
              isSelected={selectedIdeas.includes(idea.id)}
            />
          ))}
        </div>
      )
    },
    {
      id: 'mindmap',
      name: 'Mind Map',
      icon: Network,
      description: 'Hierarchical mind map',
      component: MindMapVisualization
    },
    {
      id: 'kanban',
      name: 'Kanban Board',
      icon: BarChart3,
      description: 'Status-based kanban',
      component: KanbanBoard
    },
    {
      id: 'matrix',
      name: 'Matrix View',
      icon: Target,
      description: 'Priority vs effort matrix',
      component: MatrixView
    },
    {
      id: 'timeline',
      name: 'Timeline',
      icon: Calendar,
      description: 'Chronological timeline',
      component: TimelineView
    },
    {
      id: 'network',
      name: 'Network Graph',
      icon: Network,
      description: 'Relationship network',
      component: RelationshipGraph
    },
    {
      id: 'cluster',
      name: 'Cluster View',
      icon: Zap,
      description: 'AI-powered clustering',
      component: ({ ideas }: any) => (
        <div className="h-full">
          <ClusterManager ideas={ideas} />
        </div>
      )
    }
  ];

  // Filter ideas based on search and filters
  const filteredIdeas = ideas.filter(idea => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!idea.title.toLowerCase().includes(query) && 
          !idea.description.toLowerCase().includes(query) &&
          !idea.tags.some(tag => tag.toLowerCase().includes(query))) {
        return false;
      }
    }

    // Status filter
    if (selectedFilters.status !== 'all' && idea.status !== selectedFilters.status) {
      return false;
    }

    // Priority filter
    if (selectedFilters.priority !== 'all' && idea.priority !== selectedFilters.priority) {
      return false;
    }

    // Category filter
    if (selectedFilters.category !== 'all' && idea.category !== selectedFilters.category) {
      return false;
    }

    // Date range filter
    if (selectedFilters.dateRange.start && new Date(idea.createdAt) < selectedFilters.dateRange.start) {
      return false;
    }
    if (selectedFilters.dateRange.end && new Date(idea.createdAt) > selectedFilters.dateRange.end) {
      return false;
    }

    return true;
  });

  // Sort filtered ideas
  const sortedIdeas = [...filteredIdeas].sort((a, b) => {
    let comparison = 0;
    
    switch (viewSettings.sortBy) {
      case 'created':
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        break;
      case 'updated':
        comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime();
        break;
      case 'title':
        comparison = a.title.localeCompare(b.title);
        break;
      case 'priority':
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        comparison = priorityOrder[a.priority] - priorityOrder[b.priority];
        break;
      default:
        comparison = 0;
    }
    
    return viewSettings.sortOrder === 'desc' ? -comparison : comparison;
  });

  // Get unique categories for filter
  const categories = Array.from(new Set(ideas.map(idea => idea.category))).filter(Boolean);

  // Handle fullscreen toggle
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Handle bulk operations
  const handleBulkOperation = (operation: string) => {
    switch (operation) {
      case 'delete':
        selectedIdeas.forEach(id => onIdeaDelete(id));
        setSelectedIdeas([]);
        break;
      case 'archive':
        selectedIdeas.forEach(id => {
          const idea = ideas.find(i => i.id === id);
          if (idea) {
            onIdeaUpdate({ ...idea, status: 'archived' as IdeaStatus });
          }
        });
        setSelectedIdeas([]);
        break;
      case 'favorite':
        selectedIdeas.forEach(id => {
          const idea = ideas.find(i => i.id === id);
          if (idea) {
            onIdeaUpdate({ ...idea, isFavorite: !idea.isFavorite });
          }
        });
        setSelectedIdeas([]);
        break;
    }
  };

  const currentViewConfig = viewConfigs.find(v => v.id === currentView)!;
  const CurrentViewComponent = currentViewConfig.component;

  return (
    <div ref={containerRef} className={cn("flex flex-col h-full bg-background", className)}>
      {/* Header with View Selector and Controls */}
      <div className="flex-shrink-0 border-b bg-card">
        <div className="p-4 space-y-4">
          {/* View Mode Tabs */}
          <Tabs value={currentView} onValueChange={(value) => setCurrentView(value as ViewMode)}>
            <div className="flex items-center justify-between">
              <TabsList className="grid grid-cols-4 lg:grid-cols-8 w-full max-w-4xl">
                {viewConfigs.map(config => (
                  <TabsTrigger key={config.id} value={config.id} className="flex items-center gap-2">
                    <config.icon className="w-4 h-4" />
                    <span className="hidden sm:inline">{config.name}</span>
                  </TabsTrigger>
                ))}
              </TabsList>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleFullscreen}
                >
                  {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
                </Button>
                
                <Button variant="outline" size="sm">
                  <Settings className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </Tabs>

          {/* Search and Filters */}
          <div className="flex flex-wrap items-center gap-2">
            <div className="relative flex-1 min-w-64">
              <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search ideas..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
            
            <Select 
              value={selectedFilters.status} 
              onValueChange={(value: any) => setSelectedFilters(prev => ({ ...prev, status: value }))}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>

            <Select 
              value={selectedFilters.priority} 
              onValueChange={(value: any) => setSelectedFilters(prev => ({ ...prev, priority: value }))}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>

            <Select 
              value={selectedFilters.category} 
              onValueChange={(value: any) => setSelectedFilters(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              More Filters
            </Button>
          </div>

          {/* Bulk Actions */}
          <AnimatePresence>
            {selectedIdeas.length > 0 && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="flex items-center justify-between p-3 bg-primary/10 rounded-lg"
              >
                <span className="text-sm font-medium">
                  {selectedIdeas.length} idea{selectedIdeas.length !== 1 ? 's' : ''} selected
                </span>
                <div className="flex items-center gap-2">
                  <Button size="sm" variant="outline" onClick={() => handleBulkOperation('favorite')}>
                    <Star className="w-4 h-4 mr-2" />
                    Toggle Favorite
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => handleBulkOperation('archive')}>
                    Archive
                  </Button>
                  <Button size="sm" variant="destructive" onClick={() => handleBulkOperation('delete')}>
                    Delete
                  </Button>
                  <Button size="sm" variant="ghost" onClick={() => setSelectedIdeas([])}>
                    Cancel
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* View Content */}
      <div className="flex-1 overflow-hidden">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentView}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.2 }}
            className="h-full"
          >
            <CurrentViewComponent
              ideas={sortedIdeas}
              onUpdate={onIdeaUpdate}
              onCreate={onIdeaCreate}
              onDelete={onIdeaDelete}
              settings={viewSettings}
              selectedIdeas={selectedIdeas}
              onSelectionChange={setSelectedIdeas}
            />
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Status Bar */}
      <div className="flex-shrink-0 border-t bg-card px-4 py-2">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>{sortedIdeas.length} of {ideas.length} ideas</span>
            <span>View: {currentViewConfig.name}</span>
            {viewSettings.zoomLevel !== 100 && (
              <span>Zoom: {viewSettings.zoomLevel}%</span>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button variant="ghost" size="sm">
              <Share className="w-4 h-4 mr-2" />
              Share
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper components for grid and list views
const IdeaCard: React.FC<{
  idea: Idea;
  onUpdate: (idea: Idea) => void;
  onDelete: (id: string) => void;
  onSelect: (id: string) => void;
  isSelected: boolean;
}> = ({ idea, onUpdate, onDelete, onSelect, isSelected }) => (
  <motion.div
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    className={cn(
      "cursor-pointer",
      isSelected && "ring-2 ring-primary"
    )}
    onClick={() => onSelect(idea.id)}
  >
    <Card className="h-full">
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <CardTitle className="text-sm line-clamp-2">{idea.title}</CardTitle>
          <Badge variant={idea.priority === 'high' ? 'destructive' : idea.priority === 'medium' ? 'default' : 'secondary'}>
            {idea.priority}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-xs text-muted-foreground line-clamp-3 mb-3">
          {idea.description}
        </p>
        <div className="flex items-center justify-between">
          <div className="flex flex-wrap gap-1">
            {idea.tags.slice(0, 2).map(tag => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {idea.tags.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{idea.tags.length - 2}
              </Badge>
            )}
          </div>
          {idea.isFavorite && <Star className="w-4 h-4 fill-current text-yellow-500" />}
        </div>
      </CardContent>
    </Card>
  </motion.div>
);

const IdeaListItem: React.FC<{
  idea: Idea;
  onUpdate: (idea: Idea) => void;
  onDelete: (id: string) => void;
  onSelect: (id: string) => void;
  isSelected: boolean;
}> = ({ idea, onUpdate, onDelete, onSelect, isSelected }) => (
  <motion.div
    whileHover={{ backgroundColor: 'rgba(0,0,0,0.02)' }}
    className={cn(
      "p-3 rounded-lg border cursor-pointer transition-colors",
      isSelected && "ring-2 ring-primary bg-primary/5"
    )}
    onClick={() => onSelect(idea.id)}
  >
    <div className="flex items-center justify-between">
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h3 className="font-medium truncate">{idea.title}</h3>
          <Badge variant={idea.priority === 'high' ? 'destructive' : idea.priority === 'medium' ? 'default' : 'secondary'} className="text-xs">
            {idea.priority}
          </Badge>
          {idea.isFavorite && <Star className="w-4 h-4 fill-current text-yellow-500" />}
        </div>
        <p className="text-sm text-muted-foreground line-clamp-1 mb-2">
          {idea.description}
        </p>
        <div className="flex items-center gap-2">
          <div className="flex flex-wrap gap-1">
            {idea.tags.slice(0, 3).map(tag => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {idea.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{idea.tags.length - 3}
              </Badge>
            )}
          </div>
        </div>
      </div>
      
      <div className="flex items-center gap-2 text-xs text-muted-foreground">
        <Clock className="w-3 h-3" />
        <span>{new Date(idea.updatedAt).toLocaleDateString()}</span>
      </div>
    </div>
  </motion.div>
);

export default AdvancedIdeaVisualization;