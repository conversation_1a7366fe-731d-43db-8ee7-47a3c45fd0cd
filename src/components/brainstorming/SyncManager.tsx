/**
 * Sync Manager UI Component
 * 
 * Manages bi-directional synchronization with external tools
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  RefreshCw, 
  AlertCircle, 
  CheckCircle2, 
  XCircle,
  GitBranch,
  Clock,
  Cloud,
  CloudOff,
  GitMerge,
  FileWarning
} from 'lucide-react';
import { syncManager, type SyncStatus, type SyncConflict } from '@/lib/sync-manager';
import { projectManagementIntegration } from '@/lib/project-management-integration';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { format, formatDistanceToNow } from 'date-fns';

interface SyncManagerProps {
  sessionId: string;
}

export function SyncManagerUI({ sessionId }: SyncManagerProps) {
  const [syncStatus, setSyncStatus] = useState<SyncStatus | undefined>();
  const [autoSync, setAutoSync] = useState(false);
  const [syncInterval, setSyncInterval] = useState(300000); // 5 minutes
  const [conflictResolution, setConflictResolution] = useState<'local' | 'remote' | 'merge' | 'manual'>('merge');
  const [isSyncing, setIsSyncing] = useState(false);
  const [selectedConflict, setSelectedConflict] = useState<SyncConflict | null>(null);

  const activeProvider = projectManagementIntegration.getActiveProvider();
  const session = useBrainstormStore(state => state.sessions[sessionId]);

  // Initialize sync manager
  useEffect(() => {
    if (activeProvider && activeProvider.isConnected) {
      syncManager.registerSync(sessionId, {
        provider: activeProvider,
        sessionId,
        syncInterval,
        autoSync,
        conflictResolution
      });
      
      // Get initial status
      setSyncStatus(syncManager.getStatus(sessionId));
    }
    
    return () => {
      syncManager.clearSync(sessionId);
    };
  }, [sessionId, activeProvider, syncInterval, autoSync, conflictResolution]);

  // Poll for status updates
  useEffect(() => {
    const interval = setInterval(() => {
      setSyncStatus(syncManager.getStatus(sessionId));
    }, 1000);
    
    return () => clearInterval(interval);
  }, [sessionId]);

  // Handle manual sync
  const handleManualSync = useCallback(async () => {
    setIsSyncing(true);
    try {
      const status = await syncManager.sync(sessionId);
      setSyncStatus(status);
    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      setIsSyncing(false);
    }
  }, [sessionId]);

  // Handle auto-sync toggle
  const handleAutoSyncToggle = useCallback((enabled: boolean) => {
    setAutoSync(enabled);
    if (enabled) {
      syncManager.startAutoSync(sessionId);
    } else {
      syncManager.stopAutoSync(sessionId);
    }
  }, [sessionId]);

  // Handle conflict resolution
  const handleConflictResolution = useCallback(async (
    conflictId: string,
    resolution: 'local' | 'remote' | 'merged'
  ) => {
    await syncManager.resolveConflict(sessionId, conflictId, resolution);
    setSyncStatus(syncManager.getStatus(sessionId));
    setSelectedConflict(null);
  }, [sessionId]);

  if (!activeProvider || !activeProvider.isConnected) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CloudOff className="h-5 w-5" />
            Sync Manager
          </CardTitle>
          <CardDescription>
            Connect to a project management tool to enable synchronization
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cloud className="h-5 w-5" />
            Sync Manager
          </CardTitle>
          <CardDescription>
            Synchronize with {activeProvider.name}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Sync Status */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="text-sm font-medium">Sync Status</div>
              <div className="flex items-center gap-2">
                {syncStatus?.isRunning ? (
                  <Badge variant="default" className="gap-1">
                    <RefreshCw className="h-3 w-3 animate-spin" />
                    Syncing...
                  </Badge>
                ) : syncStatus?.lastSync ? (
                  <Badge variant="secondary" className="gap-1">
                    <CheckCircle2 className="h-3 w-3" />
                    Last synced {formatDistanceToNow(syncStatus.lastSync, { addSuffix: true })}
                  </Badge>
                ) : (
                  <Badge variant="outline">Never synced</Badge>
                )}
                {syncStatus && syncStatus.pendingChanges > 0 && (
                  <Badge variant="warning">
                    {syncStatus.pendingChanges} pending changes
                  </Badge>
                )}
              </div>
            </div>
            <Button
              onClick={handleManualSync}
              disabled={isSyncing || syncStatus?.isRunning}
              size="sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
              Sync Now
            </Button>
          </div>

          {/* Auto-sync Settings */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Switch
                id="auto-sync"
                checked={autoSync}
                onCheckedChange={handleAutoSyncToggle}
              />
              <Label htmlFor="auto-sync">Enable automatic sync</Label>
            </div>
            
            {autoSync && (
              <div className="ml-6 space-y-2">
                <Label htmlFor="sync-interval">Sync interval</Label>
                <Select
                  value={syncInterval.toString()}
                  onValueChange={(value) => setSyncInterval(parseInt(value))}
                >
                  <SelectTrigger id="sync-interval">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="60000">Every minute</SelectItem>
                    <SelectItem value="300000">Every 5 minutes</SelectItem>
                    <SelectItem value="600000">Every 10 minutes</SelectItem>
                    <SelectItem value="1800000">Every 30 minutes</SelectItem>
                    <SelectItem value="3600000">Every hour</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          {/* Conflict Resolution */}
          <div className="space-y-2">
            <Label htmlFor="conflict-resolution">Conflict resolution strategy</Label>
            <Select
              value={conflictResolution}
              onValueChange={(value: any) => setConflictResolution(value)}
            >
              <SelectTrigger id="conflict-resolution">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="local">Local wins (keep your changes)</SelectItem>
                <SelectItem value="remote">Remote wins (use external changes)</SelectItem>
                <SelectItem value="merge">Automatic merge (combine changes)</SelectItem>
                <SelectItem value="manual">Manual resolution (review each conflict)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Conflicts Tab */}
      {syncStatus && (syncStatus.conflicts.length > 0 || syncStatus.errors.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle>Sync Issues</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="conflicts">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="conflicts" className="gap-2">
                  <GitBranch className="h-4 w-4" />
                  Conflicts ({syncStatus.conflicts.length})
                </TabsTrigger>
                <TabsTrigger value="errors" className="gap-2">
                  <AlertCircle className="h-4 w-4" />
                  Errors ({syncStatus.errors.length})
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="conflicts" className="space-y-2">
                {syncStatus.conflicts.map((conflict) => (
                  <Alert key={conflict.id} className="cursor-pointer" onClick={() => setSelectedConflict(conflict)}>
                    <FileWarning className="h-4 w-4" />
                    <AlertTitle>Conflict in {conflict.type}</AlertTitle>
                    <AlertDescription className="space-y-1">
                      <div>Local: {conflict.localVersion.title || conflict.localVersion.content}</div>
                      <div>Remote: {conflict.remoteVersion.summary || conflict.remoteVersion.name}</div>
                      <div className="text-xs text-muted-foreground">
                        Detected {formatDistanceToNow(conflict.detectedAt, { addSuffix: true })}
                      </div>
                    </AlertDescription>
                  </Alert>
                ))}
                
                {syncStatus.conflicts.length === 0 && (
                  <div className="text-center py-4 text-muted-foreground">
                    No conflicts
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="errors" className="space-y-2">
                {syncStatus.errors.map((error, index) => (
                  <Alert key={index} variant="destructive">
                    <XCircle className="h-4 w-4" />
                    <AlertTitle>Sync Error</AlertTitle>
                    <AlertDescription>
                      <div>{error.message}</div>
                      <div className="text-xs">
                        {format(error.timestamp, 'PPp')}
                      </div>
                    </AlertDescription>
                  </Alert>
                ))}
                
                {syncStatus.errors.length === 0 && (
                  <div className="text-center py-4 text-muted-foreground">
                    No errors
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Conflict Resolution Dialog */}
      {selectedConflict && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <GitMerge className="h-5 w-5" />
              Resolve Conflict
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Local Version</h4>
                <div className="p-3 border rounded-md bg-muted/50">
                  <div className="font-medium">
                    {selectedConflict.localVersion.title || selectedConflict.localVersion.content}
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    Updated: {format(new Date(selectedConflict.localVersion.updatedAt), 'PPp')}
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">Remote Version</h4>
                <div className="p-3 border rounded-md bg-muted/50">
                  <div className="font-medium">
                    {selectedConflict.remoteVersion.summary || selectedConflict.remoteVersion.name}
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    Updated: {format(new Date(selectedConflict.remoteVersion.updated), 'PPp')}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => handleConflictResolution(selectedConflict.id, 'local')}
              >
                Keep Local
              </Button>
              <Button
                variant="outline"
                onClick={() => handleConflictResolution(selectedConflict.id, 'remote')}
              >
                Use Remote
              </Button>
              <Button
                onClick={() => handleConflictResolution(selectedConflict.id, 'merged')}
              >
                Auto-merge
              </Button>
              <Button
                variant="ghost"
                onClick={() => setSelectedConflict(null)}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sync Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Sync Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            {syncStatus?.lastSync && (
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Last sync</span>
                <span>{format(syncStatus.lastSync, 'PPp')}</span>
              </div>
            )}
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Pending changes</span>
              <Badge variant={syncStatus?.pendingChanges ? 'warning' : 'secondary'}>
                {syncStatus?.pendingChanges || 0}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Active conflicts</span>
              <Badge variant={syncStatus?.conflicts.length ? 'destructive' : 'secondary'}>
                {syncStatus?.conflicts.length || 0}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Sync errors</span>
              <Badge variant={syncStatus?.errors.length ? 'destructive' : 'secondary'}>
                {syncStatus?.errors.length || 0}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}