import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { BrainstormingNavigation } from '../BrainstormingNavigation';
import { EnhancedFloatingPromptInput } from '../../EnhancedFloatingPromptInput';
import { KeyboardShortcuts } from '../../KeyboardShortcuts';
import { useBrainstormingTheme } from '../BrainstormingThemeProvider';

interface BrainstormingLayoutSystemProps {
  children: React.ReactNode;
  sidebarOpen?: boolean;
  onSidebarToggle?: () => void;
  currentView?: string;
  onViewChange?: (view: string) => void;
  className?: string;
}

/**
 * Core layout system for brainstorming interface
 * Integrates: BrainstormingNavigation, EnhancedFloatingPromptInput, KeyboardShortcuts
 * Implements: Task 2.1 ResponsiveLayout component structure
 */
export const BrainstormingLayoutSystem: React.FC<BrainstormingLayoutSystemProps> = ({
  children,
  sidebarOpen = true,
  onSidebarToggle,
  currentView = 'dashboard',
  onViewChange,
  className
}) => {
  const { theme } = useBrainstormingTheme();
  const [isMobile, setIsMobile] = useState(false);
  const [showFloatingInput, setShowFloatingInput] = useState(false);

  // Responsive breakpoint detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Keyboard shortcuts integration
  const handleKeyboardShortcut = useCallback((action: string) => {
    switch (action) {
      case 'toggle-sidebar':
        onSidebarToggle?.();
        break;
      case 'toggle-floating-input':
        setShowFloatingInput(prev => !prev);
        break;
      case 'focus-search':
        // Focus search input
        const searchInput = document.querySelector('[data-search-input]') as HTMLInputElement;
        searchInput?.focus();
        break;
      default:
        break;
    }
  }, [onSidebarToggle]);

  // Layout variants for different screen sizes
  const layoutVariants = {
    desktop: {
      gridTemplateColumns: sidebarOpen ? '280px 1fr 320px' : '280px 1fr',
      gridTemplateAreas: sidebarOpen 
        ? '"navigation main sidebar"'
        : '"navigation main main"'
    },
    tablet: {
      gridTemplateColumns: sidebarOpen ? '240px 1fr' : '1fr',
      gridTemplateAreas: sidebarOpen 
        ? '"navigation main"'
        : '"main main"'
    },
    mobile: {
      gridTemplateColumns: '1fr',
      gridTemplateAreas: '"main"'
    }
  };

  const currentLayout = isMobile ? 'mobile' : (window.innerWidth < 1024 ? 'tablet' : 'desktop');

  return (
    <div className={cn(
      "h-screen bg-background overflow-hidden",
      "grid transition-all duration-300 ease-in-out",
      className
    )}
    style={{
      ...layoutVariants[currentLayout],
      gap: '1px',
      backgroundColor: theme.colors.border
    }}>
      
      {/* Keyboard Shortcuts Handler */}
      <KeyboardShortcuts
        shortcuts={[
          { key: 'b', modifiers: ['cmd'], action: 'toggle-sidebar' },
          { key: 'k', modifiers: ['cmd'], action: 'focus-search' },
          { key: 'n', modifiers: ['cmd'], action: 'toggle-floating-input' },
        ]}
        onShortcut={handleKeyboardShortcut}
      />

      {/* Navigation Sidebar */}
      <AnimatePresence>
        {(!isMobile || sidebarOpen) && (
          <motion.div
            initial={{ x: isMobile ? -280 : 0, opacity: isMobile ? 0 : 1 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: isMobile ? -280 : 0, opacity: isMobile ? 0 : 1 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className={cn(
              "bg-card border-r",
              "flex flex-col",
              isMobile && "absolute inset-y-0 left-0 z-50 shadow-xl"
            )}
            style={{ gridArea: 'navigation' }}
          >
            <BrainstormingNavigation
              currentView={currentView}
              onViewChange={onViewChange}
              onSidebarToggle={onSidebarToggle}
              isMobile={isMobile}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content Area */}
      <motion.div
        className="bg-background overflow-hidden relative"
        style={{ gridArea: 'main' }}
        layout
        transition={{ duration: 0.3, ease: 'easeInOut' }}
      >
        {children}
        
        {/* Enhanced Floating Prompt Input */}
        <AnimatePresence>
          {showFloatingInput && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ duration: 0.2, ease: 'easeOut' }}
              className="absolute bottom-6 left-6 right-6 z-40"
            >
              <EnhancedFloatingPromptInput
                onSubmit={(message) => {
                  // Handle message submission
                  console.log('Floating input message:', message);
                  setShowFloatingInput(false);
                }}
                onClose={() => setShowFloatingInput(false)}
                placeholder="Quick brainstorming input..."
                showPersonaSwitcher={true}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Sidebar Panel (Desktop only) */}
      {!isMobile && sidebarOpen && (
        <motion.div
          initial={{ width: 0, opacity: 0 }}
          animate={{ width: 320, opacity: 1 }}
          exit={{ width: 0, opacity: 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="bg-card border-l overflow-hidden"
          style={{ gridArea: 'sidebar' }}
        >
          {/* Sidebar content will be populated by parent components */}
          <div className="h-full p-4">
            <div className="text-sm text-muted-foreground">
              Ideas Panel
            </div>
          </div>
        </motion.div>
      )}

      {/* Mobile Overlay */}
      {isMobile && sidebarOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-40"
          onClick={onSidebarToggle}
        />
      )}
    </div>
  );
};

export default BrainstormingLayoutSystem;