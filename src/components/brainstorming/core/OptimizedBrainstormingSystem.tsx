import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

// Import Phase 3 components
import { AnimationOptimizer } from '../performance/AnimationOptimizer';
import { AccessibilityEnhancer } from '../accessibility/AccessibilityEnhancer';
import { BundleOptimizer } from '../performance/BundleOptimizer';

// Import the comprehensive hub
import { ComprehensiveBrainstormingHub } from './ComprehensiveBrainstormingHub';

// Import enhanced UI effects
import { IntegratedUIEffects } from '../enhanced-ui/IntegratedUIEffects';

interface OptimizedBrainstormingSystemProps {
  onClose?: () => void;
  initialView?: string;
  enablePerformanceMonitoring?: boolean;
  enableAccessibilityPanel?: boolean;
  enableBundleOptimization?: boolean;
  enableUIEffects?: boolean;
  effectsLevel?: 'minimal' | 'moderate' | 'full';
  className?: string;
}

/**
 * Optimized Brainstorming System
 * Integrates: AnimationOptimizer, AccessibilityEnhancer, BundleOptimizer, IntegratedUIEffects
 * Implements: Complete Phase 3 - Performance & Polish
 */
export const OptimizedBrainstormingSystem: React.FC<OptimizedBrainstormingSystemProps> = ({
  onClose,
  initialView = 'dashboard',
  enablePerformanceMonitoring = true,
  enableAccessibilityPanel = true,
  enableBundleOptimization = true,
  enableUIEffects = true,
  effectsLevel = 'moderate',
  className
}) => {
  const [systemReady, setSystemReady] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [performanceMetrics, setPerformanceMetrics] = useState({
    initTime: 0,
    renderTime: 0,
    memoryUsage: 0
  });

  // Initialize system with performance tracking
  useEffect(() => {
    const initStart = performance.now();
    
    const initializeSystem = async () => {
      // Simulate system initialization with progress updates
      const steps = [
        { name: 'Loading core components', duration: 200 },
        { name: 'Initializing accessibility features', duration: 150 },
        { name: 'Setting up performance monitoring', duration: 100 },
        { name: 'Optimizing bundle loading', duration: 100 },
        { name: 'Preparing UI effects', duration: 50 }
      ];

      let progress = 0;
      for (const step of steps) {
        await new Promise(resolve => setTimeout(resolve, step.duration));
        progress += 100 / steps.length;
        setLoadingProgress(progress);
      }

      const initEnd = performance.now();
      setPerformanceMetrics(prev => ({
        ...prev,
        initTime: initEnd - initStart
      }));

      setSystemReady(true);
    };

    initializeSystem();
  }, []);

  // Track render performance
  useEffect(() => {
    if (systemReady) {
      const renderStart = performance.now();
      
      // Use requestAnimationFrame to measure actual render time
      requestAnimationFrame(() => {
        const renderEnd = performance.now();
        setPerformanceMetrics(prev => ({
          ...prev,
          renderTime: renderEnd - renderStart
        }));
      });
    }
  }, [systemReady]);

  // Monitor memory usage
  useEffect(() => {
    if (!systemReady || !enablePerformanceMonitoring) return;

    const monitorMemory = () => {
      if ('memory' in performance) {
        const memoryInfo = (performance as any).memory;
        setPerformanceMetrics(prev => ({
          ...prev,
          memoryUsage: Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024)
        }));
      }
    };

    monitorMemory();
    const interval = setInterval(monitorMemory, 5000);
    return () => clearInterval(interval);
  }, [systemReady, enablePerformanceMonitoring]);

  // Loading screen component
  const LoadingScreen: React.FC = () => (
    <div className="fixed inset-0 bg-background flex items-center justify-center z-50">
      <div className="text-center space-y-6 max-w-md">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-16 h-16 mx-auto"
        >
          <div className="w-full h-full border-4 border-primary border-t-transparent rounded-full" />
        </motion.div>
        
        <div className="space-y-3">
          <h2 className="text-xl font-semibold">Initializing Brainstorming System</h2>
          <p className="text-sm text-muted-foreground">
            Setting up performance optimizations and accessibility features...
          </p>
          
          <div className="w-full bg-muted rounded-full h-2">
            <motion.div
              className="bg-primary h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${loadingProgress}%` }}
              transition={{ duration: 0.3 }}
            />
          </div>
          
          <p className="text-xs text-muted-foreground">
            {Math.round(loadingProgress)}% complete
          </p>
        </div>

        {enablePerformanceMonitoring && (
          <div className="text-xs text-muted-foreground space-y-1">
            <div>Init Time: {performanceMetrics.initTime.toFixed(0)}ms</div>
            {performanceMetrics.memoryUsage > 0 && (
              <div>Memory: {performanceMetrics.memoryUsage}MB</div>
            )}
          </div>
        )}
      </div>
    </div>
  );

  // Error boundary for the system
  const SystemErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [hasError, setHasError] = useState(false);
    const [error, setError] = useState<Error | null>(null);

    useEffect(() => {
      const handleError = (event: ErrorEvent) => {
        setHasError(true);
        setError(new Error(event.message));
      };

      const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
        setHasError(true);
        setError(new Error(event.reason));
      };

      window.addEventListener('error', handleError);
      window.addEventListener('unhandledrejection', handleUnhandledRejection);

      return () => {
        window.removeEventListener('error', handleError);
        window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      };
    }, []);

    if (hasError) {
      return (
        <div className="fixed inset-0 bg-background flex items-center justify-center z-50">
          <div className="text-center space-y-4 max-w-md p-6">
            <div className="w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center">
              <span className="text-2xl">⚠️</span>
            </div>
            <h2 className="text-xl font-semibold text-red-600">System Error</h2>
            <p className="text-sm text-muted-foreground">
              The brainstorming system encountered an error and needs to be reloaded.
            </p>
            {error && (
              <details className="text-xs text-left bg-muted p-3 rounded">
                <summary className="cursor-pointer font-medium">Error Details</summary>
                <pre className="mt-2 whitespace-pre-wrap">{error.message}</pre>
              </details>
            )}
            <div className="flex gap-2 justify-center">
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
              >
                Reload System
              </button>
              <button
                onClick={() => {
                  setHasError(false);
                  setError(null);
                }}
                className="px-4 py-2 border rounded hover:bg-muted"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      );
    }

    return <>{children}</>;
  };

  // Performance metrics display
  const PerformanceMetrics: React.FC = () => {
    if (!enablePerformanceMonitoring) return null;

    return (
      <div className="fixed top-4 left-4 bg-black/80 text-white p-2 rounded text-xs font-mono z-40">
        <div className="space-y-1">
          <div>Init: {performanceMetrics.initTime.toFixed(0)}ms</div>
          <div>Render: {performanceMetrics.renderTime.toFixed(0)}ms</div>
          {performanceMetrics.memoryUsage > 0 && (
            <div>Memory: {performanceMetrics.memoryUsage}MB</div>
          )}
        </div>
      </div>
    );
  };

  if (!systemReady) {
    return <LoadingScreen />;
  }

  return (
    <SystemErrorBoundary>
      <div className={cn("h-screen overflow-hidden", className)}>
        {/* Bundle Optimizer - Outermost wrapper for bundle management */}
        <BundleOptimizer
          enableMonitoring={enableBundleOptimization}
          showMetrics={enablePerformanceMonitoring}
        >
          {/* Animation Optimizer - Manages all animations and performance */}
          <AnimationOptimizer
            maxConcurrentAnimations={5}
            targetFPS={60}
            enablePerformanceMonitoring={enablePerformanceMonitoring}
          >
            {/* Accessibility Enhancer - Provides accessibility features */}
            <AccessibilityEnhancer
              enableA11yPanel={enableAccessibilityPanel}
              autoDetectPreferences={true}
            >
              {/* UI Effects - Visual enhancements */}
              {enableUIEffects ? (
                <IntegratedUIEffects
                  enableEffects={true}
                  effectsLevel={effectsLevel}
                >
                  {/* Main Brainstorming System */}
                  <ComprehensiveBrainstormingHub
                    onClose={onClose}
                    initialView={initialView as any}
                  />
                </IntegratedUIEffects>
              ) : (
                <ComprehensiveBrainstormingHub
                  onClose={onClose}
                  initialView={initialView as any}
                />
              )}
            </AccessibilityEnhancer>
          </AnimationOptimizer>
        </BundleOptimizer>

        {/* Performance Metrics Overlay */}
        <PerformanceMetrics />

        {/* System Status Indicator */}
        <div className="fixed bottom-4 right-4 z-40">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="w-3 h-3 bg-green-500 rounded-full shadow-lg"
            title="System Ready"
          />
        </div>
      </div>
    </SystemErrorBoundary>
  );
};

export default OptimizedBrainstormingSystem;