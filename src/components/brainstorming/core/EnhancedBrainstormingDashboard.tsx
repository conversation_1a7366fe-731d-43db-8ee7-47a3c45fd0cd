import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Lightbulb, 
  Clock, 
  Target,
  Brain,
  Zap,
  Star,
  Calendar,
  Filter,
  Search,
  Grid,
  List,
  Plus,
  Download,
  Settings
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';

// Import unused components to integrate them
import { AnalyticsPanel } from '../../AnalyticsPanel';
import { SessionAnalytics } from '../../SessionAnalytics';
import { BookmarkManager } from '../../BookmarkManager';
import { StorageTab } from '../../StorageTab';
import { useBrainstormingTheme } from '../BrainstormingThemeProvider';

interface DashboardStats {
  totalSessions: number;
  activeIdeas: number;
  collaborators: number;
  completedTasks: number;
  weeklyGrowth: number;
  averageSessionTime: string;
}

interface EnhancedBrainstormingDashboardProps {
  onNavigate?: (view: string) => void;
  onCreateSession?: () => void;
  className?: string;
}

/**
 * Enhanced Brainstorming Dashboard
 * Integrates: AnalyticsPanel, SessionAnalytics, BookmarkManager, StorageTab
 * Implements: Task 3.1 (Enhanced stats cards), Task 3.2 (Dashboard grid layout)
 */
export const EnhancedBrainstormingDashboard: React.FC<EnhancedBrainstormingDashboardProps> = ({
  onNavigate,
  onCreateSession,
  className
}) => {
  const { theme } = useBrainstormingTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [stats, setStats] = useState<DashboardStats>({
    totalSessions: 24,
    activeIdeas: 156,
    collaborators: 8,
    completedTasks: 42,
    weeklyGrowth: 12.5,
    averageSessionTime: '2h 34m'
  });

  // Mock data for recent sessions
  const [recentSessions] = useState([
    {
      id: '1',
      title: 'Product Roadmap Q2',
      type: 'Design Thinking',
      participants: 5,
      ideas: 23,
      lastActive: '2 hours ago',
      status: 'active'
    },
    {
      id: '2', 
      title: 'Marketing Campaign Ideas',
      type: 'Brainstorming',
      participants: 3,
      ideas: 18,
      lastActive: '1 day ago',
      status: 'completed'
    },
    {
      id: '3',
      title: 'UX Improvements',
      type: 'SWOT Analysis',
      participants: 4,
      ideas: 31,
      lastActive: '3 days ago',
      status: 'paused'
    }
  ]);

  // Stats cards configuration
  const statsCards = [
    {
      title: 'Total Sessions',
      value: stats.totalSessions,
      icon: Brain,
      trend: '+12%',
      color: theme.colors.primary
    },
    {
      title: 'Active Ideas',
      value: stats.activeIdeas,
      icon: Lightbulb,
      trend: '+8%',
      color: '#f59e0b'
    },
    {
      title: 'Collaborators',
      value: stats.collaborators,
      icon: Users,
      trend: '+3%',
      color: '#10b981'
    },
    {
      title: 'Completed Tasks',
      value: stats.completedTasks,
      icon: Target,
      trend: '+15%',
      color: '#8b5cf6'
    }
  ];

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'new-session':
        onCreateSession?.();
        break;
      case 'analytics':
        onNavigate?.('analytics');
        break;
      case 'bookmarks':
        onNavigate?.('bookmarks');
        break;
      case 'storage':
        onNavigate?.('storage');
        break;
      default:
        console.log('Quick action:', action);
    }
  };

  return (
    <div className={cn("h-full overflow-auto", className)}>
      <div className="p-6 space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold" style={{ color: theme.colors.text }}>
              Brainstorming Dashboard
            </h1>
            <p className="text-muted-foreground mt-1">
              Welcome back! Here's what's happening with your creative sessions.
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              onClick={() => handleQuickAction('new-session')}
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              New Session
            </Button>
            <Button variant="outline" size="icon">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Stats Cards Grid - Task 3.1 Implementation */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {statsCards.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="relative overflow-hidden group hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        {stat.title}
                      </p>
                      <p className="text-2xl font-bold mt-1" style={{ color: stat.color }}>
                        {stat.value}
                      </p>
                      <div className="flex items-center gap-1 mt-2">
                        <TrendingUp className="h-3 w-3 text-green-500" />
                        <span className="text-xs text-green-500 font-medium">
                          {stat.trend}
                        </span>
                        <span className="text-xs text-muted-foreground">vs last week</span>
                      </div>
                    </div>
                    <div 
                      className="p-3 rounded-full opacity-20 group-hover:opacity-30 transition-opacity"
                      style={{ backgroundColor: stat.color }}
                    >
                      <stat.icon className="h-6 w-6" style={{ color: stat.color }} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Main Dashboard Grid - Task 3.2 Implementation */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* Left Column - Analytics Integration */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Search and Filter - Task 3.3 Implementation */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Recent Sessions</span>
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search sessions..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-8 w-64"
                        data-search-input
                      />
                    </div>
                    <Select value={selectedFilter} onValueChange={setSelectedFilter}>
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="paused">Paused</SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="flex border rounded-md">
                      <Button
                        variant={viewMode === 'grid' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setViewMode('grid')}
                        className="rounded-r-none"
                      >
                        <Grid className="h-4 w-4" />
                      </Button>
                      <Button
                        variant={viewMode === 'list' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setViewMode('list')}
                        className="rounded-l-none"
                      >
                        <List className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className={cn(
                  "gap-4",
                  viewMode === 'grid' ? "grid grid-cols-1 md:grid-cols-2" : "space-y-3"
                )}>
                  {recentSessions
                    .filter(session => 
                      selectedFilter === 'all' || session.status === selectedFilter
                    )
                    .filter(session =>
                      searchQuery === '' || 
                      session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      session.type.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map((session, index) => (
                      <motion.div
                        key={session.id}
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.05 }}
                        className="p-4 border rounded-lg hover:shadow-md transition-all duration-200 cursor-pointer group"
                        onClick={() => onNavigate?.(`session-${session.id}`)}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <h3 className="font-medium group-hover:text-primary transition-colors">
                            {session.title}
                          </h3>
                          <Badge 
                            variant={session.status === 'active' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {session.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          {session.type}
                        </p>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <div className="flex items-center gap-4">
                            <span className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              {session.participants}
                            </span>
                            <span className="flex items-center gap-1">
                              <Lightbulb className="h-3 w-3" />
                              {session.ideas}
                            </span>
                          </div>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {session.lastActive}
                          </span>
                        </div>
                      </motion.div>
                    ))}
                </div>
              </CardContent>
            </Card>

            {/* Integrated Analytics Panel */}
            <Card>
              <CardHeader>
                <CardTitle>Session Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <AnalyticsPanel />
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Sidebar Components */}
          <div className="space-y-6">
            
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  variant="outline" 
                  className="w-full justify-start gap-2"
                  onClick={() => handleQuickAction('analytics')}
                >
                  <BarChart3 className="h-4 w-4" />
                  View Analytics
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start gap-2"
                  onClick={() => handleQuickAction('bookmarks')}
                >
                  <Star className="h-4 w-4" />
                  Manage Bookmarks
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start gap-2"
                  onClick={() => handleQuickAction('storage')}
                >
                  <Download className="h-4 w-4" />
                  Storage Settings
                </Button>
              </CardContent>
            </Card>

            {/* Integrated Bookmark Manager */}
            <Card>
              <CardHeader>
                <CardTitle>Bookmarked Items</CardTitle>
              </CardHeader>
              <CardContent>
                <BookmarkManager />
              </CardContent>
            </Card>

            {/* Session Analytics Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <SessionAnalytics />
              </CardContent>
            </Card>

            {/* Storage Management */}
            <Card>
              <CardHeader>
                <CardTitle>Storage Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <StorageTab />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedBrainstormingDashboard;