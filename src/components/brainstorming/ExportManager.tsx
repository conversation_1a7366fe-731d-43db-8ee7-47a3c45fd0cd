/**
 * Export Manager Component
 * 
 * Handles exporting brainstorming sessions to various formats including
 * Markdown, JSON, PDF, CSV, and integration with external tools.
 */

import React, { useState } from 'react';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { exportBrainstormSession, ExportFormat } from '@/lib/brainstorm-export';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { PremiumProgress } from '@/components/ui/premium-progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Download,
  FileText,
  FileJson,
  FileImage,
  Table,
  ExternalLink,
  Loader2,
  Check,
  AlertCircle,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/useToast';

interface ExportManagerProps {
  sessionId: string;
  className?: string;
}

const exportFormats = [
  {
    id: 'markdown' as ExportFormat,
    name: 'Markdown',
    description: 'Human-readable format with formatting',
    icon: FileText,
    extension: '.md',
  },
  {
    id: 'json' as ExportFormat,
    name: 'JSON',
    description: 'Structured data format for developers',
    icon: FileJson,
    extension: '.json',
  },
  {
    id: 'csv' as ExportFormat,
    name: 'CSV',
    description: 'Spreadsheet-compatible format',
    icon: Table,
    extension: '.csv',
  },
  {
    id: 'pdf' as ExportFormat,
    name: 'PDF',
    description: 'Printable document format',
    icon: FileImage,
    extension: '.pdf',
  },
  {
    id: 'mermaid' as ExportFormat,
    name: 'Mermaid Diagram',
    description: 'Visual diagram markup',
    icon: FileText,
    extension: '.mmd',
  },
];

const integrationTargets = [
  {
    id: 'jira',
    name: 'Jira',
    description: 'Export as Jira tickets',
    icon: ExternalLink,
  },
  {
    id: 'asana',
    name: 'Asana',
    description: 'Export as Asana tasks',
    icon: ExternalLink,
  },
  {
    id: 'trello',
    name: 'Trello',
    description: 'Export as Trello cards',
    icon: ExternalLink,
  },
  {
    id: 'github',
    name: 'GitHub Issues',
    description: 'Export as GitHub issues',
    icon: ExternalLink,
  },
];

export const ExportManager: React.FC<ExportManagerProps> = ({
  sessionId,
  className,
}) => {
  const { toast } = useToast();
  const {
    sessions,
    ideas,
    clusters,
    relationships,
    getIdeasBySession,
    getIdeaRelationships,
  } = useBrainstormStore();

  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>('markdown');
  const [exportOptions, setExportOptions] = useState({
    includeMetadata: true,
    includeTimestamps: true,
    includeTasks: false,
    includeRelationships: true,
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [showIntegrations, setShowIntegrations] = useState(false);

  const session = sessions[sessionId];
  const sessionIdeas = getIdeasBySession(sessionId);
  const sessionClusters = Object.values(clusters).filter(cluster =>
    cluster.ideaIds.some(ideaId => sessionIdeas.find(idea => idea.id === ideaId))
  );
  const sessionRelationships = getIdeaRelationships(sessionId);

  const handleExport = async () => {
    if (!session) {
      toast({
        title: 'Export Failed',
        description: 'Session not found',
        variant: 'destructive',
      });
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      const exportData = await exportBrainstormSession(
        session,
        sessionIdeas,
        sessionClusters,
        sessionRelationships,
        [], // tasks - would be implemented
        {
          format: selectedFormat,
          ...exportOptions,
        }
      );

      clearInterval(progressInterval);
      setExportProgress(100);

      // Create download
      const blob = new Blob([exportData], {
        type: getContentType(selectedFormat),
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${session.title.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}${getFileExtension(selectedFormat)}`;
      link.click();
      URL.revokeObjectURL(url);

      toast({
        title: 'Export Successful',
        description: `Session exported as ${selectedFormat.toUpperCase()}`,
      });
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: 'Export Failed',
        description: 'Could not export session',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  const handleIntegrationExport = (target: string) => {
    // This would integrate with external APIs
    toast({
      title: 'Integration Export',
      description: `Exporting to ${target} (feature coming soon)`,
    });
  };

  const getContentType = (format: ExportFormat): string => {
    switch (format) {
      case 'json': return 'application/json';
      case 'csv': return 'text/csv';
      case 'pdf': return 'application/pdf';
      case 'mermaid': return 'text/plain';
      default: return 'text/markdown';
    }
  };

  const getFileExtension = (format: ExportFormat): string => {
    const formatConfig = exportFormats.find(f => f.id === format);
    return formatConfig?.extension || '.txt';
  };

  const updateExportOption = (key: keyof typeof exportOptions, value: boolean) => {
    setExportOptions(prev => ({ ...prev, [key]: value }));
  };

  if (!session) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-muted-foreground">Session not found</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("export-manager space-y-4", className)}>
      {/* Export Formats */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Session
          </CardTitle>
          <CardDescription>
            Export "{session.title}" with {sessionIdeas.length} ideas and {sessionClusters.length} clusters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Format Selection */}
          <div>
            <Label className="text-sm font-medium mb-3 block">Export Format</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {exportFormats.map(format => {
                const IconComponent = format.icon;
                const isSelected = selectedFormat === format.id;
                
                return (
                  <Card
                    key={format.id}
                    className={cn(
                      "cursor-pointer transition-colors hover:border-primary/50",
                      isSelected && "border-primary bg-primary/5"
                    )}
                    onClick={() => setSelectedFormat(format.id)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        <IconComponent className="h-5 w-5" />
                        <div className="flex-1">
                          <div className="font-medium text-sm">{format.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {format.description}
                          </div>
                        </div>
                        {isSelected && <Check className="h-4 w-4 text-primary" />}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Export Options */}
          <div>
            <Label className="text-sm font-medium mb-3 block">Export Options</Label>
            <div className="space-y-3">
              {Object.entries(exportOptions).map(([key, value]) => (
                <div key={key} className="flex items-center space-x-2">
                  <Checkbox
                    id={key}
                    checked={value}
                    onCheckedChange={(checked) => 
                      updateExportOption(key as keyof typeof exportOptions, checked as boolean)
                    }
                  />
                  <Label htmlFor={key} className="text-sm">
                    {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Export Progress */}
          {isExporting && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Exporting...</span>
                <span>{exportProgress}%</span>
              </div>
              <PremiumProgress value={exportProgress} className="h-2" />
            </div>
          )}

          {/* Export Button */}
          <Button
            onClick={handleExport}
            disabled={isExporting}
            className="w-full"
          >
            {isExporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export as {selectedFormat.toUpperCase()}
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Integration Exports */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm flex items-center gap-2">
            <ExternalLink className="h-4 w-4" />
            Export to External Tools
          </CardTitle>
          <CardDescription className="text-xs">
            Send ideas and tasks directly to project management tools
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            {integrationTargets.map(target => {
              const IconComponent = target.icon;
              
              return (
                <Button
                  key={target.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleIntegrationExport(target.name)}
                  className="justify-start"
                >
                  <IconComponent className="h-4 w-4 mr-2" />
                  {target.name}
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Export History */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Recent Exports</CardTitle>
        </CardHeader>
        <CardContent>
          {session.metadata.exportHistory.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-4">
              No exports yet
            </p>
          ) : (
            <div className="space-y-2">
              {session.metadata.exportHistory.slice(0, 5).map(record => (
                <div key={record.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {record.format}
                    </Badge>
                    <span>{record.itemCount} items</span>
                  </div>
                  <span className="text-muted-foreground text-xs">
                    {new Date(record.timestamp).toLocaleDateString()}
                  </span>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};