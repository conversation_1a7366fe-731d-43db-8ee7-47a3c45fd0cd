/**
 * Resource Allocation Component
 * 
 * Drag-and-drop interface for assigning resources to tasks
 */

import React, { useState, useCallback } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { PremiumProgress } from '@/components/ui/premium-progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Calendar,
  Clock,
  User,
  Users,
  AlertCircle,
  CheckCircle2,
  Plus,
  X,
  Briefcase,
  Star,
  TrendingUp
} from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useBrainstormStore } from '@/stores/brainstormStore';
import type { GeneratedTask } from '@/types/brainstorm';
import { format, addDays, differenceInDays } from 'date-fns';

export interface Resource {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: string;
  skills: string[];
  availability: number; // hours per week
  currentLoad: number; // current hours allocated
  tasks: string[]; // task IDs
}

interface ResourceAllocationProps {
  sessionId: string;
  tasks: GeneratedTask[];
  onAllocationChange?: (taskId: string, resourceId: string | null) => void;
}

const defaultResources: Resource[] = [
  {
    id: 'res_1',
    name: 'Alice Johnson',
    email: '<EMAIL>',
    role: 'Senior Developer',
    skills: ['React', 'TypeScript', 'Node.js'],
    availability: 40,
    currentLoad: 0,
    tasks: []
  },
  {
    id: 'res_2',
    name: 'Bob Smith',
    email: '<EMAIL>',
    role: 'UI/UX Designer',
    skills: ['Figma', 'Design Systems', 'User Research'],
    availability: 40,
    currentLoad: 0,
    tasks: []
  },
  {
    id: 'res_3',
    name: 'Carol Davis',
    email: '<EMAIL>',
    role: 'Backend Developer',
    skills: ['Python', 'PostgreSQL', 'AWS'],
    availability: 32,
    currentLoad: 0,
    tasks: []
  },
  {
    id: 'res_4',
    name: 'David Wilson',
    email: '<EMAIL>',
    role: 'Product Manager',
    skills: ['Agile', 'Strategy', 'Analytics'],
    availability: 40,
    currentLoad: 0,
    tasks: []
  }
];

export function ResourceAllocation({ sessionId, tasks, onAllocationChange }: ResourceAllocationProps) {
  const [resources, setResources] = useState<Resource[]>(defaultResources);
  const [unassignedTasks, setUnassignedTasks] = useState<GeneratedTask[]>(tasks);
  const [showAddResource, setShowAddResource] = useState(false);
  const [newResource, setNewResource] = useState({
    name: '',
    email: '',
    role: '',
    skills: '',
    availability: 40
  });

  // Calculate resource metrics
  const calculateResourceMetrics = useCallback((resource: Resource) => {
    const assignedTasks = tasks.filter(task => resource.tasks.includes(task.id));
    const totalEffort = assignedTasks.reduce((sum, task) => sum + (task.estimatedEffort || 0), 0);
    const utilization = resource.availability > 0 ? (totalEffort / resource.availability) * 100 : 0;
    
    return {
      assignedTasks: assignedTasks.length,
      totalEffort,
      utilization,
      isOverloaded: utilization > 100
    };
  }, [tasks]);

  // Handle drag end
  const handleDragEnd = useCallback((result: DropResult) => {
    if (!result.destination) return;

    const { source, destination, draggableId } = result;
    const taskId = draggableId;

    // Remove task from source
    if (source.droppableId === 'unassigned') {
      setUnassignedTasks(prev => prev.filter(t => t.id !== taskId));
    } else {
      setResources(prev => prev.map(resource => {
        if (resource.id === source.droppableId) {
          return {
            ...resource,
            tasks: resource.tasks.filter(id => id !== taskId)
          };
        }
        return resource;
      }));
    }

    // Add task to destination
    if (destination.droppableId === 'unassigned') {
      const task = tasks.find(t => t.id === taskId);
      if (task) {
        setUnassignedTasks(prev => [...prev, task]);
        onAllocationChange?.(taskId, null);
      }
    } else {
      setResources(prev => prev.map(resource => {
        if (resource.id === destination.droppableId) {
          return {
            ...resource,
            tasks: [...resource.tasks, taskId]
          };
        }
        return resource;
      }));
      onAllocationChange?.(taskId, destination.droppableId);
    }
  }, [tasks, onAllocationChange]);

  // Add new resource
  const handleAddResource = useCallback(() => {
    const resource: Resource = {
      id: `res_${Date.now()}`,
      name: newResource.name,
      email: newResource.email,
      role: newResource.role,
      skills: newResource.skills.split(',').map(s => s.trim()).filter(Boolean),
      availability: newResource.availability,
      currentLoad: 0,
      tasks: []
    };
    
    setResources(prev => [...prev, resource]);
    setNewResource({ name: '', email: '', role: '', skills: '', availability: 40 });
    setShowAddResource(false);
  }, [newResource]);

  // Remove resource
  const handleRemoveResource = useCallback((resourceId: string) => {
    const resource = resources.find(r => r.id === resourceId);
    if (resource) {
      // Move all tasks back to unassigned
      const resourceTasks = tasks.filter(t => resource.tasks.includes(t.id));
      setUnassignedTasks(prev => [...prev, ...resourceTasks]);
      
      // Remove resource
      setResources(prev => prev.filter(r => r.id !== resourceId));
      
      // Update allocations
      resource.tasks.forEach(taskId => {
        onAllocationChange?.(taskId, null);
      });
    }
  }, [resources, tasks, onAllocationChange]);

  // Auto-allocate based on skills
  const handleAutoAllocate = useCallback(() => {
    const tasksCopy = [...unassignedTasks];
    const updatedResources = [...resources];
    
    tasksCopy.forEach(task => {
      // Find best matching resource based on skills and availability
      let bestResource: Resource | null = null;
      let bestScore = -1;
      
      updatedResources.forEach(resource => {
        const metrics = calculateResourceMetrics(resource);
        if (metrics.utilization >= 100) return; // Skip overloaded resources
        
        // Calculate skill match score
        const taskKeywords = task.tags;
        const skillMatch = resource.skills.filter(skill => 
          taskKeywords.some(keyword => 
            skill.toLowerCase().includes(keyword.toLowerCase())
          )
        ).length;
        
        // Calculate availability score
        const availabilityScore = (resource.availability - metrics.totalEffort) / resource.availability;
        
        // Combined score
        const score = skillMatch * 0.7 + availabilityScore * 0.3;
        
        if (score > bestScore) {
          bestScore = score;
          bestResource = resource;
        }
      });
      
      if (bestResource && bestScore > 0) {
        bestResource.tasks.push(task.id);
        setUnassignedTasks(prev => prev.filter(t => t.id !== task.id));
        onAllocationChange?.(task.id, bestResource.id);
      }
    });
    
    setResources(updatedResources);
  }, [unassignedTasks, resources, calculateResourceMetrics, onAllocationChange]);

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <div className="space-y-4">
        {/* Header Actions */}
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold">Resource Allocation</h3>
            <p className="text-sm text-muted-foreground">
              Drag and drop tasks to assign them to team members
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={handleAutoAllocate} variant="outline" size="sm">
              <TrendingUp className="h-4 w-4 mr-2" />
              Auto-allocate
            </Button>
            <Dialog open={showAddResource} onOpenChange={setShowAddResource}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Resource
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Team Member</DialogTitle>
                  <DialogDescription>
                    Add a new team member to allocate tasks to
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={newResource.name}
                      onChange={e => setNewResource(prev => ({ ...prev, name: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newResource.email}
                      onChange={e => setNewResource(prev => ({ ...prev, email: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="role">Role</Label>
                    <Input
                      id="role"
                      value={newResource.role}
                      onChange={e => setNewResource(prev => ({ ...prev, role: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="skills">Skills (comma-separated)</Label>
                    <Input
                      id="skills"
                      placeholder="React, TypeScript, Node.js"
                      value={newResource.skills}
                      onChange={e => setNewResource(prev => ({ ...prev, skills: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="availability">Availability (hours/week)</Label>
                    <Input
                      id="availability"
                      type="number"
                      value={newResource.availability}
                      onChange={e => setNewResource(prev => ({ ...prev, availability: parseInt(e.target.value) || 40 }))}
                    />
                  </div>
                  <Button onClick={handleAddResource} className="w-full">
                    Add Resource
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
          {/* Unassigned Tasks */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Unassigned Tasks</CardTitle>
                <CardDescription>{unassignedTasks.length} tasks</CardDescription>
              </CardHeader>
              <CardContent>
                <Droppable droppableId="unassigned">
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={`space-y-2 min-h-[200px] ${
                        snapshot.isDraggingOver ? 'bg-accent/50 rounded-md' : ''
                      }`}
                    >
                      {unassignedTasks.map((task, index) => (
                        <Draggable key={task.id} draggableId={task.id} index={index}>
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className={`${snapshot.isDragging ? 'opacity-50' : ''}`}
                            >
                              <TaskCard task={task} compact />
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </CardContent>
            </Card>
          </div>

          {/* Resources */}
          <div className="lg:col-span-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            {resources.map(resource => {
              const metrics = calculateResourceMetrics(resource);
              const assignedTasks = tasks.filter(t => resource.tasks.includes(t.id));
              
              return (
                <Card key={resource.id} className={metrics.isOverloaded ? 'border-destructive' : ''}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={resource.avatar} />
                          <AvatarFallback>
                            {resource.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <CardTitle className="text-base">{resource.name}</CardTitle>
                          <CardDescription>{resource.role}</CardDescription>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveResource(resource.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    {/* Skills */}
                    <div className="flex flex-wrap gap-1 mt-2">
                      {resource.skills.map(skill => (
                        <Badge key={skill} variant="secondary" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                    
                    {/* Utilization */}
                    <div className="mt-3 space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Utilization</span>
                        <span className={metrics.isOverloaded ? 'text-destructive' : ''}>
                          {Math.round(metrics.utilization)}%
                        </span>
                      </div>
                      <PremiumProgress 
                        value={Math.min(metrics.utilization, 100)} 
                        className={metrics.isOverloaded ? 'bg-destructive/20' : ''}
                      />
                      <div className="text-xs text-muted-foreground">
                        {metrics.totalEffort}h / {resource.availability}h per week
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Droppable droppableId={resource.id}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.droppableProps}
                          className={`space-y-2 min-h-[100px] ${
                            snapshot.isDraggingOver ? 'bg-accent/50 rounded-md' : ''
                          }`}
                        >
                          {assignedTasks.map((task, index) => (
                            <Draggable key={task.id} draggableId={task.id} index={index}>
                              {(provided, snapshot) => (
                                <div
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                  className={`${snapshot.isDragging ? 'opacity-50' : ''}`}
                                >
                                  <TaskCard task={task} compact />
                                </div>
                              )}
                            </Draggable>
                          ))}
                          {provided.placeholder}
                          {assignedTasks.length === 0 && (
                            <div className="text-center py-4 text-muted-foreground text-sm">
                              Drop tasks here
                            </div>
                          )}
                        </div>
                      )}
                    </Droppable>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Summary Stats */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Allocation Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">{tasks.length}</div>
                <div className="text-sm text-muted-foreground">Total Tasks</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{tasks.length - unassignedTasks.length}</div>
                <div className="text-sm text-muted-foreground">Assigned</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{unassignedTasks.length}</div>
                <div className="text-sm text-muted-foreground">Unassigned</div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {resources.filter(r => calculateResourceMetrics(r).isOverloaded).length}
                </div>
                <div className="text-sm text-muted-foreground">Overloaded</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DragDropContext>
  );
}

// Task Card Component
function TaskCard({ task, compact = false }: { task: GeneratedTask; compact?: boolean }) {
  const priorityColors = {
    low: 'text-green-600',
    medium: 'text-yellow-600',
    high: 'text-orange-600',
    critical: 'text-red-600'
  };

  const statusIcons = {
    pending: Clock,
    'in-progress': TrendingUp,
    completed: CheckCircle2
  };

  const StatusIcon = statusIcons[task.status] || Clock;

  return (
    <Card className="cursor-move hover:shadow-md transition-shadow">
      <CardContent className={compact ? 'p-3' : 'p-4'}>
        <div className="space-y-2">
          <div className="flex items-start justify-between gap-2">
            <h4 className="font-medium text-sm line-clamp-2">{task.title}</h4>
            <StatusIcon className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
          </div>
          
          {!compact && task.description && (
            <p className="text-xs text-muted-foreground line-clamp-2">
              {task.description}
            </p>
          )}
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={`text-xs ${priorityColors[task.priority]}`}>
                {task.priority}
              </Badge>
              {task.estimatedEffort && (
                <span className="text-xs text-muted-foreground flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {task.estimatedEffort}h
                </span>
              )}
            </div>
          </div>
          
          {task.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {task.tags.slice(0, compact ? 2 : 3).map(tag => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {task.tags.length > (compact ? 2 : 3) && (
                <Badge variant="secondary" className="text-xs">
                  +{task.tags.length - (compact ? 2 : 3)}
                </Badge>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}