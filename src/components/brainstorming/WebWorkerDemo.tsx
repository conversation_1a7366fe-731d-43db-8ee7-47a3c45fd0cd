/**
 * Web Worker Demo Component
 * 
 * Demonstrates the performance improvements from using web workers
 */

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PremiumProgress } from '@/components/ui/premium-progress';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Activity, Zap, Clock, Brain } from 'lucide-react';
import { enhancedIdeaExtractor } from '@/services/EnhancedIdeaExtractor';
import { clusteringService } from '@/lib/clustering-service';
import { workerManager } from '@/lib/worker-manager';
import type { Idea, Message } from '@/types/brainstorm';

// Generate mock data
function generateMockMessages(count: number): Message[] {
  const topics = [
    'implement user authentication with OAuth2',
    'create a dashboard for analytics',
    'optimize database queries for performance',
    'add real-time notifications',
    'build a mobile app version',
    'integrate payment processing',
    'improve search functionality',
    'add data export features',
    'implement caching strategy',
    'create API documentation'
  ];
  
  const messages: Message[] = [];
  
  for (let i = 0; i < count; i++) {
    messages.push({
      id: `msg_${i}`,
      role: 'user',
      content: `We should ${topics[i % topics.length]} to improve the application. This would help users ${
        i % 2 === 0 ? 'save time' : 'work more efficiently'
      } and provide better ${i % 3 === 0 ? 'performance' : 'user experience'}.`,
      timestamp: new Date().toISOString()
    });
  }
  
  return messages;
}

function generateMockIdeas(count: number): Idea[] {
  const ideas: Idea[] = [];
  const tags = ['feature', 'optimization', 'ui', 'backend', 'security', 'performance'];
  const priorities = ['low', 'medium', 'high', 'critical'];
  
  for (let i = 0; i < count; i++) {
    ideas.push({
      id: `idea_${i}`,
      sessionId: 'demo_session',
      content: `Idea ${i}: ${generateMockMessages(1)[0].content}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'active',
      priority: priorities[i % priorities.length] as any,
      tags: tags.slice(0, (i % 3) + 1)
    });
  }
  
  return ideas;
}

export function WebWorkerDemo() {
  const [useWebWorker, setUseWebWorker] = useState(true);
  const [extractionProgress, setExtractionProgress] = useState(0);
  const [clusteringProgress, setClusteringProgress] = useState(0);
  const [isExtracting, setIsExtracting] = useState(false);
  const [isClustering, setIsClustering] = useState(false);
  const [results, setResults] = useState<{
    extraction?: {
      ideas: number;
      time: number;
      withWorker: boolean;
    };
    clustering?: {
      clusters: number;
      time: number;
      withWorker: boolean;
    };
  }>({});
  
  // Run extraction benchmark
  const runExtractionBenchmark = useCallback(async () => {
    setIsExtracting(true);
    setExtractionProgress(0);
    
    const messages = generateMockMessages(100);
    enhancedIdeaExtractor.setWebWorkerEnabled(useWebWorker);
    
    const startTime = performance.now();
    
    try {
      const ideas = await enhancedIdeaExtractor.batchExtractIdeas(
        messages,
        'demo_session',
        {
          minConfidence: 0.6,
          maxIdeasPerMessage: 3,
          includeMetadata: true
        },
        (progress) => {
          setExtractionProgress((progress.processed / progress.total) * 100);
        }
      );
      
      const endTime = performance.now();
      
      setResults(prev => ({
        ...prev,
        extraction: {
          ideas: ideas.length,
          time: endTime - startTime,
          withWorker: useWebWorker
        }
      }));
    } catch (error) {
      console.error('Extraction failed:', error);
    } finally {
      setIsExtracting(false);
      setExtractionProgress(100);
    }
  }, [useWebWorker]);
  
  // Run clustering benchmark
  const runClusteringBenchmark = useCallback(async () => {
    setIsClustering(true);
    setClusteringProgress(0);
    
    const ideas = generateMockIdeas(200);
    clusteringService.setWebWorkerEnabled(useWebWorker);
    
    const startTime = performance.now();
    
    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setClusteringProgress(prev => Math.min(prev + 10, 90));
      }, 100);
      
      const result = await clusteringService.optimizeClusters(ideas);
      
      clearInterval(progressInterval);
      setClusteringProgress(100);
      
      const endTime = performance.now();
      
      setResults(prev => ({
        ...prev,
        clustering: {
          clusters: result.clusters.length,
          time: endTime - startTime,
          withWorker: useWebWorker
        }
      }));
    } catch (error) {
      console.error('Clustering failed:', error);
    } finally {
      setIsClustering(false);
    }
  }, [useWebWorker]);
  
  // Get worker status
  const workerStatus = workerManager.getStatus();
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Web Worker Performance Demo
          </CardTitle>
          <CardDescription>
            See how web workers improve performance for computationally intensive operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="use-worker"
                checked={useWebWorker}
                onCheckedChange={setUseWebWorker}
              />
              <Label htmlFor="use-worker">
                Use Web Workers (currently {useWebWorker ? 'enabled' : 'disabled'})
              </Label>
            </div>
            
            {!workerStatus.initialized && useWebWorker && (
              <Alert>
                <AlertDescription>
                  Web Workers are initializing... This may take a moment on first use.
                </AlertDescription>
              </Alert>
            )}
            
            <div className="grid grid-cols-4 gap-4 text-sm">
              <div className="space-y-1">
                <div className="text-muted-foreground">Worker Status</div>
                <div className="font-medium">
                  {workerStatus.initialized ? 'Ready' : 'Not Initialized'}
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-muted-foreground">Clustering Worker</div>
                <div className="font-medium">
                  {workerStatus.workers.clustering ? 'Active' : 'Inactive'}
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-muted-foreground">Extraction Worker</div>
                <div className="font-medium">
                  {workerStatus.workers.extraction ? 'Active' : 'Inactive'}
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-muted-foreground">Pending Tasks</div>
                <div className="font-medium">{workerStatus.pendingTasks}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Tabs defaultValue="extraction" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="extraction">Idea Extraction</TabsTrigger>
          <TabsTrigger value="clustering">Idea Clustering</TabsTrigger>
        </TabsList>
        
        <TabsContent value="extraction" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Idea Extraction Benchmark
              </CardTitle>
              <CardDescription>
                Extract ideas from 100 messages
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={runExtractionBenchmark}
                disabled={isExtracting}
                className="w-full"
              >
                {isExtracting ? 'Extracting...' : 'Run Extraction Benchmark'}
              </Button>
              
              {isExtracting && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(extractionProgress)}%</span>
                  </div>
                  <PremiumProgress value={extractionProgress} />
                </div>
              )}
              
              {results.extraction && (
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Ideas Extracted</div>
                    <div className="text-2xl font-bold">{results.extraction.ideas}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Time Taken</div>
                    <div className="text-2xl font-bold">
                      {(results.extraction.time / 1000).toFixed(2)}s
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Method</div>
                    <div className="text-lg font-medium">
                      {results.extraction.withWorker ? 'Web Worker' : 'Main Thread'}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="clustering" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Clustering Optimization Benchmark
              </CardTitle>
              <CardDescription>
                Find optimal clusters for 200 ideas
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={runClusteringBenchmark}
                disabled={isClustering}
                className="w-full"
              >
                {isClustering ? 'Clustering...' : 'Run Clustering Benchmark'}
              </Button>
              
              {isClustering && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(clusteringProgress)}%</span>
                  </div>
                  <PremiumProgress value={clusteringProgress} />
                </div>
              )}
              
              {results.clustering && (
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Optimal Clusters</div>
                    <div className="text-2xl font-bold">{results.clustering.clusters}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Time Taken</div>
                    <div className="text-2xl font-bold">
                      {(results.clustering.time / 1000).toFixed(2)}s
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm text-muted-foreground">Method</div>
                    <div className="text-lg font-medium">
                      {results.clustering.withWorker ? 'Web Worker' : 'Main Thread'}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {(results.extraction || results.clustering) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Performance Comparison
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              {results.extraction && (
                <div>
                  <strong>Extraction:</strong> Processed 100 messages in{' '}
                  {(results.extraction.time / 1000).toFixed(2)}s using{' '}
                  {results.extraction.withWorker ? 'Web Worker' : 'Main Thread'}
                </div>
              )}
              {results.clustering && (
                <div>
                  <strong>Clustering:</strong> Optimized 200 ideas in{' '}
                  {(results.clustering.time / 1000).toFixed(2)}s using{' '}
                  {results.clustering.withWorker ? 'Web Worker' : 'Main Thread'}
                </div>
              )}
              <div className="mt-4 p-3 bg-muted rounded-md">
                <strong>💡 Pro Tip:</strong> Web Workers prevent UI freezing during heavy computations.
                Try running both benchmarks with and without workers to see the difference!
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}