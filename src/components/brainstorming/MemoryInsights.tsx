import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Brain,
  Lightbulb,
  TrendingUp,
  BookOpen,
  Link,
  ChevronDown,
  ChevronRight,
  Sparkles,
  Clock,
  Eye,
} from 'lucide-react';
import { brainstormMemory, MemoryEntry } from '@/lib/brainstorm-memory';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface MemoryInsightsProps {
  sessionId: string;
  className?: string;
}

const memoryTypeIcons = {
  insight: Lightbulb,
  pattern: TrendingUp,
  concept: BookO<PERSON>,
  lesson: Brain,
  connection: Link,
};

const memoryTypeColors = {
  insight: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300',
  pattern: 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300',
  concept: 'bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300',
  lesson: 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300',
  connection: 'bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300',
};

export const MemoryInsights: React.FC<MemoryInsightsProps> = ({ sessionId, className }) => {
  const { sessions, getIdeasBySession } = useBrainstormStore();
  const [relevantMemories, setRelevantMemories] = useState<MemoryEntry[]>([]);
  const [suggestions, setSuggestions] = useState<{ content: string; reason: string; confidence: number }[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedMemory, setSelectedMemory] = useState<MemoryEntry | null>(null);

  const session = sessions[sessionId];
  const ideas = getIdeasBySession(sessionId);

  useEffect(() => {
    if (!session) return;

    // Extract session context
    const sessionTags = Array.from(new Set(ideas.flatMap(idea => idea.tags)));
    const sessionContext = session.title;

    // Get relevant memories
    const memories = brainstormMemory.getRelevantMemories(sessionTags, sessionContext, 8);
    setRelevantMemories(memories);

    // Generate suggestions
    const memorySuggestions = brainstormMemory.generateMemoryBasedSuggestions(sessionTags, ideas);
    setSuggestions(memorySuggestions);
  }, [session, ideas, sessionId]);

  const handleApplySuggestion = (suggestion: { content: string; reason: string; confidence: number }) => {
    const { addIdea } = useBrainstormStore.getState();
    addIdea(sessionId, suggestion.content, {
      tags: ['memory-suggested'],
      priority: suggestion.confidence > 0.7 ? 'high' : 'medium',
      metadata: {
        source: 'memory-suggestion',
        reason: suggestion.reason,
        confidence: suggestion.confidence
      }
    });
  };

  if (relevantMemories.length === 0 && suggestions.length === 0) {
    return null;
  }

  return (
    <Card className={cn('w-full', className)}>
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-purple-600" />
                <CardTitle className="text-base">Memory Insights</CardTitle>
                <Badge variant="secondary" className="text-xs">
                  {relevantMemories.length + suggestions.length}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                {suggestions.length > 0 && (
                  <Badge variant="outline" className="text-xs">
                    <Sparkles className="h-3 w-3 mr-1" />
                    {suggestions.length} suggestions
                  </Badge>
                )}
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>
            </div>
            <CardDescription>
              Insights from previous brainstorming sessions
            </CardDescription>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="pt-0">
            <div className="space-y-4">
              {/* Memory-based Suggestions */}
              {suggestions.length > 0 && (
                <div>
                  <h4 className="text-sm font-semibold mb-2 flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    Suggested Ideas
                  </h4>
                  <div className="space-y-2">
                    {suggestions.map((suggestion, index) => (
                      <Card key={index} className="bg-muted/30">
                        <CardContent className="p-3">
                          <div className="flex items-start justify-between gap-2">
                            <div className="flex-1">
                              <p className="text-sm font-medium">{suggestion.content}</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                {suggestion.reason}
                              </p>
                              <div className="flex items-center gap-2 mt-2">
                                <Badge variant="outline" className="text-xs">
                                  {Math.round(suggestion.confidence * 100)}% confidence
                                </Badge>
                              </div>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleApplySuggestion(suggestion)}
                            >
                              Add Idea
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {/* Relevant Memories */}
              {relevantMemories.length > 0 && (
                <div>
                  <h4 className="text-sm font-semibold mb-2 flex items-center gap-2">
                    <BookOpen className="h-4 w-4" />
                    Relevant Memories
                  </h4>
                  <ScrollArea className="h-[300px]">
                    <div className="space-y-2 pr-4">
                      {relevantMemories.map((memory) => {
                        const Icon = memoryTypeIcons[memory.type];
                        const isSelected = selectedMemory?.id === memory.id;

                        return (
                          <Card
                            key={memory.id}
                            className={cn(
                              'cursor-pointer transition-all hover:shadow-sm',
                              isSelected && 'ring-2 ring-primary'
                            )}
                            onClick={() => setSelectedMemory(isSelected ? null : memory)}
                          >
                            <CardContent className="p-3">
                              <div className="flex items-start gap-3">
                                <Icon className="h-4 w-4 mt-0.5 text-muted-foreground flex-shrink-0" />
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2 mb-1">
                                    <Badge
                                      variant="secondary"
                                      className={cn('text-xs', memoryTypeColors[memory.type])}
                                    >
                                      {memory.type}
                                    </Badge>
                                    <Badge variant="outline" className="text-xs">
                                      {Math.round(memory.confidence * 100)}%
                                    </Badge>
                                  </div>
                                  <p className="text-sm">{memory.content}</p>
                                  
                                  {memory.tags.length > 0 && (
                                    <div className="flex flex-wrap gap-1 mt-2">
                                      {memory.tags.slice(0, 3).map((tag) => (
                                        <Badge key={tag} variant="outline" className="text-xs">
                                          {tag}
                                        </Badge>
                                      ))}
                                      {memory.tags.length > 3 && (
                                        <span className="text-xs text-muted-foreground">
                                          +{memory.tags.length - 3} more
                                        </span>
                                      )}
                                    </div>
                                  )}
                                  
                                  <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                                    <div className="flex items-center gap-1">
                                      <Clock className="h-3 w-3" />
                                      {format(new Date(memory.createdAt), 'MMM d, yyyy')}
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <Eye className="h-3 w-3" />
                                      {memory.accessCount} uses
                                    </div>
                                  </div>
                                </div>
                              </div>
                              
                              {isSelected && memory.metadata && (
                                <div className="mt-3 pt-3 border-t">
                                  <div className="text-xs">
                                    <p className="font-medium mb-1">Additional Context:</p>
                                    <div className="space-y-1">
                                      {Object.entries(memory.metadata).map(([key, value]) => (
                                        <div key={key} className="flex justify-between">
                                          <span className="text-muted-foreground capitalize">
                                            {key.replace(/([A-Z])/g, ' $1').toLowerCase()}:
                                          </span>
                                          <span>{String(value)}</span>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </ScrollArea>
                </div>
              )}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};