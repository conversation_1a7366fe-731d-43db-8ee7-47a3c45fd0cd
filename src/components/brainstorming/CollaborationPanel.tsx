import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  Wifi, 
  WifiOff, 
  Server, 
  Link, 
  Copy, 
  Check,
  MessageSquare,
  Send
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { collaborationClient, CollaborationUser } from '@/lib/brainstorm-collaboration-client';

interface CollaborationPanelProps {
  sessionId: string;
  onUserClick?: (userId: string) => void;
}

export const CollaborationPanel: React.FC<CollaborationPanelProps> = ({
  sessionId,
  onUserClick,
}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isHost, setIsHost] = useState(false);
  const [users, setUsers] = useState<CollaborationUser[]>([]);
  const [userName, setUserName] = useState('');
  const [serverUrl, setServerUrl] = useState('ws://localhost:8765');
  const [chatMessages, setChatMessages] = useState<{ userId: string; userName: string; message: string; timestamp: Date }[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    // Set up event listeners
    const handleUserJoined = (user: CollaborationUser) => {
      setUsers(prev => [...prev, user]);
      toast({
        message: `${user.name} joined the session`,
        type: 'success',
      });
    };

    const handleUserLeft = (userId: string) => {
      const user = users.find(u => u.id === userId);
      setUsers(prev => prev.filter(u => u.id !== userId));
      if (user) {
        toast({
          message: `${user.name} left the session`,
          type: 'info',
        });
      }
    };

    const handleConnectionStateChanged = (connected: boolean) => {
      setIsConnected(connected);
    };

    const handleChatMessage = (userId: string, message: string) => {
      const user = collaborationClient.getUser(userId);
      setChatMessages(prev => [...prev, {
        userId,
        userName: user?.name || 'Unknown',
        message,
        timestamp: new Date(),
      }]);
    };

    collaborationClient.on('userJoined', handleUserJoined);
    collaborationClient.on('userLeft', handleUserLeft);
    collaborationClient.on('connectionStateChanged', handleConnectionStateChanged);
    collaborationClient.on('chatMessage', handleChatMessage);

    return () => {
      collaborationClient.off('userJoined', handleUserJoined);
      collaborationClient.off('userLeft', handleUserLeft);
      collaborationClient.off('connectionStateChanged', handleConnectionStateChanged);
      collaborationClient.off('chatMessage', handleChatMessage);
    };
  }, [users, toast]);

  const handleStartServer = async () => {
    try {
      await collaborationClient.startServer();
      setIsHost(true);
      toast({
        message: 'Collaboration server started',
        type: 'success',
      });
      // Auto-connect to own server
      await handleConnect();
    } catch (error) {
      toast({
        message: 'Failed to start server',
        type: 'error',
      });
    }
  };

  const handleStopServer = async () => {
    try {
      await collaborationClient.stopServer();
      setIsHost(false);
      toast({
        message: 'Collaboration server stopped',
        type: 'info',
      });
    } catch (error) {
      toast({
        message: 'Failed to stop server',
        type: 'error',
      });
    }
  };

  const handleConnect = async () => {
    if (!userName.trim()) {
      toast({
        message: 'Please enter your name',
        type: 'error',
      });
      return;
    }

    try {
      await collaborationClient.connect(sessionId, userName, serverUrl);
      toast({
        message: 'Connected to collaboration session',
        type: 'success',
      });
    } catch (error) {
      toast({
        message: 'Failed to connect',
        type: 'error',
      });
    }
  };

  const handleDisconnect = () => {
    collaborationClient.disconnect();
    setUsers([]);
    setChatMessages([]);
  };

  const handleSendChat = () => {
    if (!chatInput.trim()) return;
    
    collaborationClient.sendChatMessage(chatInput);
    
    // Add own message to chat
    setChatMessages(prev => [...prev, {
      userId: collaborationClient.getState().localUserId,
      userName: userName,
      message: chatInput,
      timestamp: new Date(),
    }]);
    
    setChatInput('');
  };

  const copyInviteLink = () => {
    const inviteData = {
      serverUrl,
      sessionId,
    };
    navigator.clipboard.writeText(JSON.stringify(inviteData));
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
    toast({
      message: 'Invite link copied to clipboard',
      type: 'success',
    });
  };

  return (
    <Card className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Collaboration</h3>
          {isConnected ? (
            <Badge variant="default" className="gap-1">
              <Wifi className="h-3 w-3" />
              Connected
            </Badge>
          ) : (
            <Badge variant="secondary" className="gap-1">
              <WifiOff className="h-3 w-3" />
              Disconnected
            </Badge>
          )}
        </div>
        {isHost && (
          <Badge variant="outline" className="gap-1">
            <Server className="h-3 w-3" />
            Host
          </Badge>
        )}
      </div>

      {!isConnected ? (
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Your Name</label>
            <Input
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
              placeholder="Enter your name"
            />
          </div>

          <Tabs defaultValue="host" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="host">Host Session</TabsTrigger>
              <TabsTrigger value="join">Join Session</TabsTrigger>
            </TabsList>
            
            <TabsContent value="host" className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Start a server to host this brainstorming session
              </p>
              <Button onClick={handleStartServer} className="w-full">
                <Server className="h-4 w-4 mr-2" />
                Start Server
              </Button>
            </TabsContent>
            
            <TabsContent value="join" className="space-y-2">
              <div className="space-y-2">
                <label className="text-sm font-medium">Server URL</label>
                <Input
                  value={serverUrl}
                  onChange={(e) => setServerUrl(e.target.value)}
                  placeholder="ws://localhost:8765"
                />
              </div>
              <Button onClick={handleConnect} className="w-full">
                <Link className="h-4 w-4 mr-2" />
                Connect
              </Button>
            </TabsContent>
          </Tabs>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm font-medium">Session Users ({users.length + 1})</p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" style={{ borderColor: collaborationClient.getState().localUserId }}>
                  {userName} (You)
                </Badge>
                {users.map(user => (
                  <Badge
                    key={user.id}
                    variant="outline"
                    style={{ borderColor: user.color }}
                    className="cursor-pointer"
                    onClick={() => onUserClick?.(user.id)}
                  >
                    {user.name}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {isHost && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={copyInviteLink}
                className="flex-1"
              >
                {copied ? (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Invite
                  </>
                )}
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleStopServer}
              >
                Stop Server
              </Button>
            </div>
          )}

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              <p className="text-sm font-medium">Chat</p>
            </div>
            <div className="border rounded-lg p-2 h-32 overflow-y-auto space-y-1">
              {chatMessages.map((msg, idx) => (
                <div key={idx} className="text-sm">
                  <span className="font-medium">{msg.userName}:</span> {msg.message}
                </div>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendChat()}
                placeholder="Type a message..."
                className="flex-1"
              />
              <Button size="sm" onClick={handleSendChat}>
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Button
            variant="outline"
            onClick={handleDisconnect}
            className="w-full"
          >
            Disconnect
          </Button>
        </div>
      )}
    </Card>
  );
};