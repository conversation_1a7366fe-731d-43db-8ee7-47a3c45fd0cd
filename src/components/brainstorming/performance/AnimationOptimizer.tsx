import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence, useReducedMotion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface AnimationConfig {
  duration: number;
  easing: string;
  stagger: number;
  priority: 'low' | 'medium' | 'high';
  canInterrupt: boolean;
}

interface QueuedAnimation {
  id: string;
  element: HTMLElement;
  animation: () => Promise<void>;
  config: AnimationConfig;
  timestamp: number;
  status: 'pending' | 'running' | 'completed' | 'cancelled';
}

interface PerformanceMetrics {
  fps: number;
  frameDrops: number;
  animationCount: number;
  memoryUsage: number;
  cpuUsage: number;
}

interface AnimationOptimizerProps {
  children: React.ReactNode;
  maxConcurrentAnimations?: number;
  targetFPS?: number;
  enablePerformanceMonitoring?: boolean;
  className?: string;
}

/**
 * Animation Optimizer
 * Implements: Task 7.1 (60fps animations), Task 7.2 (Animation queuing), Task 7.3 (Reduced motion)
 */
export const AnimationOptimizer: React.FC<AnimationOptimizerProps> = ({
  children,
  maxConcurrentAnimations = 5,
  targetFPS = 60,
  enablePerformanceMonitoring = true,
  className
}) => {
  const [animationQueue, setAnimationQueue] = useState<QueuedAnimation[]>([]);
  const [runningAnimations, setRunningAnimations] = useState<Set<string>>(new Set());
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    frameDrops: 0,
    animationCount: 0,
    memoryUsage: 0,
    cpuUsage: 0
  });

  const shouldReduceMotion = useReducedMotion();
  const frameTimeRef = useRef<number[]>([]);
  const lastFrameTimeRef = useRef<number>(performance.now());
  const animationFrameRef = useRef<number>();
  const performanceObserverRef = useRef<PerformanceObserver | null>(null);

  // Performance monitoring
  useEffect(() => {
    if (!enablePerformanceMonitoring) return;

    const monitorPerformance = () => {
      const now = performance.now();
      const deltaTime = now - lastFrameTimeRef.current;
      
      // Track frame times for FPS calculation
      frameTimeRef.current.push(deltaTime);
      if (frameTimeRef.current.length > 60) {
        frameTimeRef.current.shift();
      }
      
      // Calculate FPS
      const avgFrameTime = frameTimeRef.current.reduce((a, b) => a + b, 0) / frameTimeRef.current.length;
      const fps = Math.round(1000 / avgFrameTime);
      
      // Count frame drops (frames taking longer than 16.67ms for 60fps)
      const frameDrops = frameTimeRef.current.filter(time => time > 16.67).length;
      
      setPerformanceMetrics(prev => ({
        ...prev,
        fps: Math.min(fps, 60),
        frameDrops,
        animationCount: runningAnimations.size
      }));
      
      lastFrameTimeRef.current = now;
      animationFrameRef.current = requestAnimationFrame(monitorPerformance);
    };

    animationFrameRef.current = requestAnimationFrame(monitorPerformance);

    // Memory usage monitoring
    if ('memory' in performance) {
      const memoryInfo = (performance as any).memory;
      setPerformanceMetrics(prev => ({
        ...prev,
        memoryUsage: Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024)
      }));
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [enablePerformanceMonitoring, runningAnimations.size]);

  // Animation queue processor
  useEffect(() => {
    const processQueue = async () => {
      if (runningAnimations.size >= maxConcurrentAnimations) return;
      
      const nextAnimation = animationQueue
        .filter(anim => anim.status === 'pending')
        .sort((a, b) => {
          // Sort by priority, then by timestamp
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          const priorityDiff = priorityOrder[b.config.priority] - priorityOrder[a.config.priority];
          return priorityDiff !== 0 ? priorityDiff : a.timestamp - b.timestamp;
        })[0];

      if (!nextAnimation) return;

      // Check if we should reduce animation complexity based on performance
      if (performanceMetrics.fps < targetFPS * 0.8) {
        // Skip low priority animations when performance is poor
        if (nextAnimation.config.priority === 'low') {
          setAnimationQueue(prev => prev.map(anim => 
            anim.id === nextAnimation.id 
              ? { ...anim, status: 'cancelled' }
              : anim
          ));
          return;
        }
      }

      // Start animation
      setAnimationQueue(prev => prev.map(anim => 
        anim.id === nextAnimation.id 
          ? { ...anim, status: 'running' }
          : anim
      ));
      
      setRunningAnimations(prev => new Set([...prev, nextAnimation.id]));

      try {
        await nextAnimation.animation();
        
        setAnimationQueue(prev => prev.map(anim => 
          anim.id === nextAnimation.id 
            ? { ...anim, status: 'completed' }
            : anim
        ));
      } catch (error) {
        console.error('Animation failed:', error);
        
        setAnimationQueue(prev => prev.map(anim => 
          anim.id === nextAnimation.id 
            ? { ...anim, status: 'cancelled' }
            : anim
        ));
      } finally {
        setRunningAnimations(prev => {
          const newSet = new Set(prev);
          newSet.delete(nextAnimation.id);
          return newSet;
        });
      }
    };

    processQueue();
  }, [animationQueue, runningAnimations.size, maxConcurrentAnimations, performanceMetrics.fps, targetFPS]);

  // Clean up completed animations
  useEffect(() => {
    const cleanup = () => {
      setAnimationQueue(prev => prev.filter(anim => 
        anim.status !== 'completed' && anim.status !== 'cancelled'
      ));
    };

    const interval = setInterval(cleanup, 5000);
    return () => clearInterval(interval);
  }, []);

  // Queue animation function
  const queueAnimation = useCallback((
    element: HTMLElement,
    animation: () => Promise<void>,
    config: Partial<AnimationConfig> = {}
  ) => {
    const fullConfig: AnimationConfig = {
      duration: 300,
      easing: 'ease-out',
      stagger: 0,
      priority: 'medium',
      canInterrupt: true,
      ...config
    };

    const queuedAnimation: QueuedAnimation = {
      id: `anim-${Date.now()}-${Math.random()}`,
      element,
      animation,
      config: fullConfig,
      timestamp: Date.now(),
      status: 'pending'
    };

    setAnimationQueue(prev => [...prev, queuedAnimation]);
    return queuedAnimation.id;
  }, []);

  // Cancel animation function
  const cancelAnimation = useCallback((animationId: string) => {
    setAnimationQueue(prev => prev.map(anim => 
      anim.id === animationId && anim.config.canInterrupt
        ? { ...anim, status: 'cancelled' }
        : anim
    ));
  }, []);

  // Optimized motion variants
  const getOptimizedVariants = useCallback((baseVariants: any) => {
    if (shouldReduceMotion) {
      // Return instant transitions for reduced motion
      return Object.keys(baseVariants).reduce((acc, key) => {
        acc[key] = {
          ...baseVariants[key],
          transition: { duration: 0 }
        };
        return acc;
      }, {} as any);
    }

    // Optimize based on performance
    const performanceMultiplier = Math.max(0.5, performanceMetrics.fps / 60);
    
    return Object.keys(baseVariants).reduce((acc, key) => {
      const variant = baseVariants[key];
      acc[key] = {
        ...variant,
        transition: {
          ...variant.transition,
          duration: (variant.transition?.duration || 0.3) * performanceMultiplier,
          ease: performanceMetrics.fps < 45 ? 'linear' : (variant.transition?.ease || 'easeOut')
        }
      };
      return acc;
    }, {} as any);
  }, [shouldReduceMotion, performanceMetrics.fps]);

  // Performance-aware animation component
  const OptimizedMotion: React.FC<any> = ({ children, variants, ...props }) => {
    const optimizedVariants = getOptimizedVariants(variants || {});
    
    if (shouldReduceMotion) {
      return <div {...props}>{children}</div>;
    }

    return (
      <motion.div
        variants={optimizedVariants}
        {...props}
        style={{
          ...props.style,
          willChange: 'transform, opacity',
          backfaceVisibility: 'hidden',
          perspective: 1000
        }}
      >
        {children}
      </motion.div>
    );
  };

  // Performance dashboard component
  const PerformanceDashboard: React.FC = () => {
    if (!enablePerformanceMonitoring) return null;

    return (
      <div className="fixed bottom-4 right-4 bg-black/80 text-white p-3 rounded-lg text-xs font-mono z-50">
        <div className="space-y-1">
          <div className="flex justify-between gap-4">
            <span>FPS:</span>
            <span className={cn(
              performanceMetrics.fps >= 55 ? 'text-green-400' :
              performanceMetrics.fps >= 30 ? 'text-yellow-400' : 'text-red-400'
            )}>
              {performanceMetrics.fps}
            </span>
          </div>
          <div className="flex justify-between gap-4">
            <span>Drops:</span>
            <span className={cn(
              performanceMetrics.frameDrops === 0 ? 'text-green-400' :
              performanceMetrics.frameDrops < 5 ? 'text-yellow-400' : 'text-red-400'
            )}>
              {performanceMetrics.frameDrops}
            </span>
          </div>
          <div className="flex justify-between gap-4">
            <span>Anims:</span>
            <span>{performanceMetrics.animationCount}</span>
          </div>
          <div className="flex justify-between gap-4">
            <span>Queue:</span>
            <span>{animationQueue.filter(a => a.status === 'pending').length}</span>
          </div>
          {performanceMetrics.memoryUsage > 0 && (
            <div className="flex justify-between gap-4">
              <span>Memory:</span>
              <span>{performanceMetrics.memoryUsage}MB</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Context provider for animation utilities
  const AnimationContext = React.createContext({
    queueAnimation,
    cancelAnimation,
    performanceMetrics,
    OptimizedMotion
  });

  return (
    <AnimationContext.Provider value={{
      queueAnimation,
      cancelAnimation,
      performanceMetrics,
      OptimizedMotion
    }}>
      <div className={cn("relative", className)}>
        {children}
        <PerformanceDashboard />
      </div>
    </AnimationContext.Provider>
  );
};

// Hook to use animation utilities
export const useAnimationOptimizer = () => {
  const context = React.useContext(React.createContext({
    queueAnimation: () => '',
    cancelAnimation: () => {},
    performanceMetrics: {
      fps: 60,
      frameDrops: 0,
      animationCount: 0,
      memoryUsage: 0,
      cpuUsage: 0
    },
    OptimizedMotion: motion.div
  }));
  
  if (!context) {
    throw new Error('useAnimationOptimizer must be used within AnimationOptimizer');
  }
  
  return context;
};

// Optimized animation presets
export const animationPresets = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.2 }
  },
  
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.3, ease: 'easeOut' }
  },
  
  scaleIn: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
    transition: { duration: 0.2, ease: 'easeOut' }
  },
  
  slideInFromRight: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
    transition: { duration: 0.3, ease: 'easeOut' }
  },
  
  staggerChildren: {
    animate: {
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  }
};

export default AnimationOptimizer;