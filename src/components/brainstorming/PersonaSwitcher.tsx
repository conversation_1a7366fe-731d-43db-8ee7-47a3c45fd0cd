/**
 * Persona Switcher Component
 * 
 * Quick persona selection interface for active brainstorming sessions
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronDown,
  User,
  Zap,
  Clock,
  Star,
  TrendingUp,
  Brain,
  Lightbulb,
  Shield,
  Target,
  Eye,
  Users,
  Search,
  Heart,
  BarChart3,
  Shuffle,
  Settings
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import { cn } from '@/lib/utils';
import { usePersonaStore } from '@/stores/personaStore';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';
import { BrainstormingPersona, PersonaRole, PersonaContext } from '@/types/persona';

interface PersonaSwitcherProps {
  context?: PersonaContext;
  onPersonaChange?: (persona: BrainstormingPersona) => void;
  showRecommendations?: boolean;
  compactMode?: boolean;
  className?: string;
}

const ROLE_ICONS: Record<PersonaRole, React.ComponentType<any>> = {
  [PersonaRole.CREATIVE_THINKER]: Lightbulb,
  [PersonaRole.DEVILS_ADVOCATE]: Shield,
  [PersonaRole.PRACTICAL_ANALYST]: BarChart3,
  [PersonaRole.VISIONARY]: Eye,
  [PersonaRole.FACILITATOR]: Users,
  [PersonaRole.RESEARCHER]: Search,
  [PersonaRole.IMPLEMENTER]: Target,
  [PersonaRole.CUSTOMER_ADVOCATE]: Heart,
  [PersonaRole.RISK_ASSESSOR]: Shield,
  [PersonaRole.INNOVATOR]: Zap,
  [PersonaRole.SYNTHESIZER]: TrendingUp,
  [PersonaRole.QUESTIONER]: Brain
};

export const PersonaSwitcher: React.FC<PersonaSwitcherProps> = ({
  context,
  onPersonaChange,
  showRecommendations = true,
  compactMode = false,
  className
}) => {
  const { theme } = useBrainstormingTheme();
  const {
    personas,
    activePersonaId,
    setActivePersona,
    getPersonaRecommendations,
    getTopPerformingPersonas
  } = usePersonaStore();

  const [isOpen, setIsOpen] = useState(false);

  const activePersona = activePersonaId ? personas[activePersonaId] : null;
  const availablePersonas = Object.values(personas).filter(p => p.isActive);
  const recommendations = context && showRecommendations ? getPersonaRecommendations(context) : [];
  const topPerformers = getTopPerformingPersonas(3);

  const handlePersonaSelect = (persona: BrainstormingPersona) => {
    setActivePersona(persona.id);
    onPersonaChange?.(persona);
    setIsOpen(false);
  };

  const handleRandomPersona = () => {
    const availableIds = availablePersonas.filter(p => p.id !== activePersonaId);
    if (availableIds.length > 0) {
      const randomPersona = availableIds[Math.floor(Math.random() * availableIds.length)];
      handlePersonaSelect(randomPersona);
    }
  };

  const PersonaItem: React.FC<{ 
    persona: BrainstormingPersona; 
    isRecommended?: boolean;
    confidence?: number;
  }> = ({ persona, isRecommended, confidence }) => {
    const RoleIcon = ROLE_ICONS[persona.role];
    const isActive = persona.id === activePersonaId;

    return (
      <DropdownMenuItem
        onClick={() => handlePersonaSelect(persona)}
        className={cn(
          "flex items-center gap-3 p-3 cursor-pointer",
          isActive && "bg-primary/10"
        )}
      >
        <Avatar className="w-8 h-8">
          <AvatarFallback style={{ backgroundColor: persona.color + '20' }}>
            <RoleIcon className="w-4 h-4" style={{ color: persona.color }} />
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="font-medium truncate">{persona.name}</span>
            {isActive && (
              <Badge variant="default" className="text-xs">
                Active
              </Badge>
            )}
            {isRecommended && (
              <Badge variant="secondary" className="text-xs">
                Recommended {confidence && `(${Math.round(confidence * 100)}%)`}
              </Badge>
            )}
          </div>
          <p className="text-xs text-muted-foreground truncate">
            {persona.description}
          </p>
        </div>

        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Star className="w-3 h-3" />
          <span>{persona.metrics.averageRating.toFixed(1)}</span>
        </div>
      </DropdownMenuItem>
    );
  };

  if (compactMode) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsOpen(!isOpen)}
              className={cn("relative", className)}
            >
              {activePersona ? (
                <Avatar className="w-6 h-6">
                  <AvatarFallback style={{ backgroundColor: activePersona.color + '20' }}>
                    {React.createElement(ROLE_ICONS[activePersona.role], {
                      className: "w-3 h-3",
                      style: { color: activePersona.color }
                    })}
                  </AvatarFallback>
                </Avatar>
              ) : (
                <User className="w-4 h-4" />
              )}
              <ChevronDown className="w-3 h-3 ml-1" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{activePersona ? activePersona.name : 'Select Persona'}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <div className={cn("", className)}>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-between"
            style={{ borderColor: theme.colors.border }}
          >
            <div className="flex items-center gap-3">
              {activePersona ? (
                <>
                  <Avatar className="w-8 h-8">
                    <AvatarFallback style={{ backgroundColor: activePersona.color + '20' }}>
                      {React.createElement(ROLE_ICONS[activePersona.role], {
                        className: "w-4 h-4",
                        style: { color: activePersona.color }
                      })}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-left">
                    <div className="font-medium">{activePersona.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {activePersona.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <User className="w-8 h-8 p-2 bg-gray-100 rounded-full" />
                  <div className="text-left">
                    <div className="font-medium">Select Persona</div>
                    <div className="text-xs text-muted-foreground">Choose your perspective</div>
                  </div>
                </>
              )}
            </div>
            <ChevronDown className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent className="w-80" align="start">
          {/* Quick Actions */}
          <div className="p-2 border-b">
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRandomPersona}
                className="flex-1"
              >
                <Shuffle className="w-4 h-4 mr-2" />
                Random
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="flex-1"
              >
                <Settings className="w-4 h-4 mr-2" />
                Manage
              </Button>
            </div>
          </div>

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <>
              <DropdownMenuLabel className="flex items-center gap-2">
                <Zap className="w-4 h-4" style={{ color: theme.colors.accent }} />
                Recommended for this session
              </DropdownMenuLabel>
              {recommendations.slice(0, 3).map(rec => {
                const persona = personas[rec.personaId];
                return persona ? (
                  <PersonaItem
                    key={persona.id}
                    persona={persona}
                    isRecommended
                    confidence={rec.confidence}
                  />
                ) : null;
              })}
              <DropdownMenuSeparator />
            </>
          )}

          {/* Top Performers */}
          {topPerformers.length > 0 && (
            <>
              <DropdownMenuLabel className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" style={{ color: theme.colors.success }} />
                Top Performers
              </DropdownMenuLabel>
              {topPerformers.map(persona => (
                <PersonaItem key={persona.id} persona={persona} />
              ))}
              <DropdownMenuSeparator />
            </>
          )}

          {/* All Personas */}
          <DropdownMenuLabel className="flex items-center gap-2">
            <Users className="w-4 h-4" style={{ color: theme.colors.primary }} />
            All Personas ({availablePersonas.length})
          </DropdownMenuLabel>
          
          <div className="max-h-64 overflow-y-auto">
            {availablePersonas.map(persona => (
              <PersonaItem key={persona.id} persona={persona} />
            ))}
          </div>

          {availablePersonas.length === 0 && (
            <div className="p-4 text-center text-muted-foreground">
              <User className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No personas available</p>
              <p className="text-xs">Create your first persona to get started</p>
            </div>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Active Persona Info */}
      {activePersona && !compactMode && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-3"
        >
          <Card>
            <CardContent className="p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <div 
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: theme.colors.success }}
                  />
                  <span className="text-sm font-medium">Active Persona</span>
                </div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Clock className="w-3 h-3" />
                  <span>Last used: {new Date(activePersona.metrics.lastUsed).toLocaleDateString()}</span>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-2 text-center">
                <div>
                  <div className="text-sm font-medium" style={{ color: theme.colors.primary }}>
                    {activePersona.metrics.ideasGenerated}
                  </div>
                  <div className="text-xs text-muted-foreground">Ideas</div>
                </div>
                <div>
                  <div className="text-sm font-medium" style={{ color: theme.colors.primary }}>
                    {activePersona.metrics.sessionParticipation}
                  </div>
                  <div className="text-xs text-muted-foreground">Sessions</div>
                </div>
                <div>
                  <div className="text-sm font-medium" style={{ color: theme.colors.primary }}>
                    {activePersona.metrics.averageRating.toFixed(1)}
                  </div>
                  <div className="text-xs text-muted-foreground">Rating</div>
                </div>
              </div>

              {activePersona.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {activePersona.tags.slice(0, 3).map(tag => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {activePersona.tags.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{activePersona.tags.length - 3}
                    </Badge>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
};
