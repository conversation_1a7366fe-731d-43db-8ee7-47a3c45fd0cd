/**
 * Templates View Component
 * 
 * Provides a comprehensive interface for browsing, applying, and managing 
 * brainstorming templates to kickstart structured ideation sessions.
 */

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  Filter,
  Star,
  Clock,
  Users,
  Play,
  Plus,
  Edit,
  Trash2,
  Download,
  Upload,
  FileText,
  Lightbulb,
  Target,
  Zap,
  Briefcase,
  Puzzle,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  brainstormTemplates, 
  type BrainstormTemplate,
  getTemplatesByCategory,
  applyTemplate,
  createTemplateFromSession 
} from '@/lib/brainstorm-templates';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { useToast } from '@/hooks/useToast';
import { cn } from '@/lib/utils';

interface TemplatesViewProps {
  onTemplateApply?: (templateId: string) => void;
  className?: string;
}

const categoryIcons = {
  product: Lightbulb,
  technical: Target,
  creative: Star,
  strategy: Briefcase,
  'problem-solving': Puzzle,
};

const difficultyColors = {
  beginner: 'bg-green-100 text-green-800',
  intermediate: 'bg-yellow-100 text-yellow-800',
  advanced: 'bg-red-100 text-red-800',
};

export const TemplatesView: React.FC<TemplatesViewProps> = ({
  onTemplateApply,
  className,
}) => {
  const { 
    createSession, 
    currentSessionId, 
    getCurrentSession,
    getIdeasBySession,
    addTemplate,
    templates,
    setCurrentSession,
  } = useBrainstormStore();
  
  const { toast } = useToast();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showTemplateDialog, setShowTemplateDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<BrainstormTemplate | null>(null);
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    description: '',
    category: 'product' as BrainstormTemplate['category'],
    prompts: [''],
    suggestedTags: '',
  });

  // Combine built-in and custom templates
  const allTemplates = useMemo(() => {
    const customTemplates = Object.values(templates).map(template => ({
      ...template,
      category: template.category as BrainstormTemplate['category'],
      prompts: template.prompts || [],
      suggestedTags: template.suggestedTags || [],
      metadata: template.metadata || {},
    }));
    return [...brainstormTemplates, ...customTemplates];
  }, [templates]);

  // Filter templates based on search and filters
  const filteredTemplates = useMemo(() => {
    return allTemplates.filter(template => {
      const matchesSearch = !searchQuery || 
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.suggestedTags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
      
      const matchesDifficulty = selectedDifficulty === 'all' || 
        template.metadata?.difficulty === selectedDifficulty;
      
      return matchesSearch && matchesCategory && matchesDifficulty;
    });
  }, [allTemplates, searchQuery, selectedCategory, selectedDifficulty]);

  const handleApplyTemplate = async (templateId: string) => {
    try {
      const template = allTemplates.find(t => t.id === templateId);
      if (!template) return;

      // Create new session with template
      const sessionId = createSession(template.name, template.category);
      
      // Apply template to session
      const { initialMessage, suggestedIdeas } = applyTemplate(templateId, sessionId);
      
      // Add the initial message
      // Note: This would normally be handled by the chat component
      
      // Add suggested ideas
      suggestedIdeas.forEach(ideaData => {
        // This would be implemented in the store
      });

      setCurrentSession(sessionId);
      onTemplateApply?.(templateId);
      
      toast({
        title: 'Template Applied',
        description: `Started new session with ${template.name} template`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to apply template',
        variant: 'destructive',
      });
    }
  };

  const handleCreateTemplate = () => {
    if (!newTemplate.name || !newTemplate.description) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    const templateId = addTemplate({
      name: newTemplate.name,
      description: newTemplate.description,
      icon: '✨',
      category: newTemplate.category,
      prompts: newTemplate.prompts.filter(p => p.trim()),
      suggestedTags: newTemplate.suggestedTags.split(',').map(t => t.trim()).filter(Boolean),
      metadata: {
        difficulty: 'intermediate',
        estimatedDuration: '60 minutes',
      },
    });

    setShowCreateDialog(false);
    setNewTemplate({
      name: '',
      description: '',
      category: 'product',
      prompts: [''],
      suggestedTags: '',
    });

    toast({
      title: 'Template Created',
      description: 'Your custom template has been saved',
    });
  };

  const handleCreateFromSession = () => {
    const currentSession = getCurrentSession();
    if (!currentSession) {
      toast({
        title: 'Error',
        description: 'No active session to create template from',
        variant: 'destructive',
      });
      return;
    }

    const ideas = getIdeasBySession(currentSession.id);
    const templateData = createTemplateFromSession({
      title: currentSession.title,
      messages: currentSession.messages,
      ideas,
      tags: currentSession.tags,
    });

    const templateId = addTemplate({
      ...templateData,
      id: `custom_${Date.now()}`,
    });

    toast({
      title: 'Template Created',
      description: `Created template "${templateData.name}" from current session`,
    });
  };

  const addPrompt = () => {
    setNewTemplate(prev => ({
      ...prev,
      prompts: [...prev.prompts, ''],
    }));
  };

  const updatePrompt = (index: number, value: string) => {
    setNewTemplate(prev => ({
      ...prev,
      prompts: prev.prompts.map((p, i) => i === index ? value : p),
    }));
  };

  const removePrompt = (index: number) => {
    setNewTemplate(prev => ({
      ...prev,
      prompts: prev.prompts.filter((_, i) => i !== index),
    }));
  };

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Header */}
      <div className="flex flex-col gap-4 p-6 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Brainstorming Templates</h2>
            <p className="text-muted-foreground">
              Choose a template to structure your brainstorming session
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCreateFromSession}
              disabled={!currentSessionId}
            >
              <Plus className="w-4 h-4 mr-2" />
              From Session
            </Button>
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Template
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Create Custom Template</DialogTitle>
                  <DialogDescription>
                    Design a reusable template for your brainstorming sessions
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Template Name</Label>
                    <Input
                      id="name"
                      value={newTemplate.name}
                      onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Product Feature Brainstorm"
                    />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={newTemplate.description}
                      onChange={(e) => setNewTemplate(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe what this template is used for..."
                    />
                  </div>
                  <div>
                    <Label htmlFor="category">Category</Label>
                    <Select
                      value={newTemplate.category}
                      onValueChange={(value: BrainstormTemplate['category']) => 
                        setNewTemplate(prev => ({ ...prev, category: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="product">Product</SelectItem>
                        <SelectItem value="technical">Technical</SelectItem>
                        <SelectItem value="creative">Creative</SelectItem>
                        <SelectItem value="strategy">Strategy</SelectItem>
                        <SelectItem value="problem-solving">Problem Solving</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <Label>Guiding Prompts</Label>
                      <Button variant="outline" size="sm" onClick={addPrompt}>
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="space-y-2">
                      {newTemplate.prompts.map((prompt, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={prompt}
                            onChange={(e) => updatePrompt(index, e.target.value)}
                            placeholder="Enter a guiding question or prompt..."
                          />
                          {newTemplate.prompts.length > 1 && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => removePrompt(index)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="tags">Suggested Tags (comma-separated)</Label>
                    <Input
                      id="tags"
                      value={newTemplate.suggestedTags}
                      onChange={(e) => setNewTemplate(prev => ({ ...prev, suggestedTags: e.target.value }))}
                      placeholder="feature, innovation, user-experience"
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateTemplate}>
                      Create Template
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="product">Product</SelectItem>
              <SelectItem value="technical">Technical</SelectItem>
              <SelectItem value="creative">Creative</SelectItem>
              <SelectItem value="strategy">Strategy</SelectItem>
              <SelectItem value="problem-solving">Problem Solving</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Difficulty" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Levels</SelectItem>
              <SelectItem value="beginner">Beginner</SelectItem>
              <SelectItem value="intermediate">Intermediate</SelectItem>
              <SelectItem value="advanced">Advanced</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Templates Grid */}
      <ScrollArea className="flex-1">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <AnimatePresence>
              {filteredTemplates.map((template) => {
                const IconComponent = categoryIcons[template.category] || FileText;
                
                return (
                  <motion.div
                    key={template.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    whileHover={{ y: -2 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-2">
                            <div className="p-2 rounded-lg bg-primary/10">
                              <IconComponent className="w-4 h-4 text-primary" />
                            </div>
                            <div>
                              <CardTitle className="text-sm">{template.name}</CardTitle>
                              <Badge variant="secondary" className="text-xs mt-1">
                                {template.category}
                              </Badge>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedTemplate(template);
                              setShowTemplateDialog(true);
                            }}
                          >
                            <FileText className="w-4 h-4" />
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <CardDescription className="text-xs mb-3 line-clamp-2">
                          {template.description}
                        </CardDescription>
                        
                        {template.metadata && (
                          <div className="flex items-center gap-2 text-xs text-muted-foreground mb-3">
                            {template.metadata.estimatedDuration && (
                              <div className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {template.metadata.estimatedDuration}
                              </div>
                            )}
                            {template.metadata.participantCount && (
                              <div className="flex items-center gap-1">
                                <Users className="w-3 h-3" />
                                {template.metadata.participantCount}
                              </div>
                            )}
                            {template.metadata.difficulty && (
                              <Badge 
                                className={cn('text-xs', difficultyColors[template.metadata.difficulty])}
                              >
                                {template.metadata.difficulty}
                              </Badge>
                            )}
                          </div>
                        )}

                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            className="flex-1"
                            onClick={() => handleApplyTemplate(template.id)}
                          >
                            <Play className="w-3 h-3 mr-1" />
                            Use Template
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>
          
          {filteredTemplates.length === 0 && (
            <div className="text-center py-12">
              <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-medium mb-2">No templates found</h3>
              <p className="text-muted-foreground text-sm">
                Try adjusting your search or filters, or create a new template.
              </p>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Template Preview Dialog */}
      <Dialog open={showTemplateDialog} onOpenChange={setShowTemplateDialog}>
        <DialogContent className="max-w-2xl">
          {selectedTemplate && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <span>{selectedTemplate.icon}</span>
                  {selectedTemplate.name}
                </DialogTitle>
                <DialogDescription>
                  {selectedTemplate.description}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Guiding Prompts</h4>
                  <ul className="space-y-1">
                    {selectedTemplate.prompts.map((prompt, index) => (
                      <li key={index} className="text-sm text-muted-foreground">
                        {index + 1}. {prompt}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Suggested Tags</h4>
                  <div className="flex flex-wrap gap-1">
                    {selectedTemplate.suggestedTags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowTemplateDialog(false)}>
                    Close
                  </Button>
                  <Button onClick={() => {
                    handleApplyTemplate(selectedTemplate.id);
                    setShowTemplateDialog(false);
                  }}>
                    <Play className="w-4 h-4 mr-2" />
                    Use Template
                  </Button>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};