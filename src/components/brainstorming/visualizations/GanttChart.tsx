/**
 * Gantt Chart Visualization Component
 * 
 * Displays project timeline with task dependencies and critical path
 */

import React, { useEffect, useRef, useState } from 'react';
import { TimelineTask, ProjectTimeline } from '@/lib/timeline-estimation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  CalendarDays,
  Clock,
  Users,
  AlertTriangle,
  Download,
  ZoomIn,
  ZoomOut,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { format, differenceInDays, addDays } from 'date-fns';

interface GanttChartProps {
  timeline: ProjectTimeline;
  onTaskClick?: (task: TimelineTask) => void;
  className?: string;
}

interface GanttBar {
  task: TimelineTask;
  x: number;
  y: number;
  width: number;
  height: number;
}

export const GanttChart: React.FC<GanttChartProps> = ({
  timeline,
  onTaskClick,
  className,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState(1);
  const [offset, setOffset] = useState({ x: 0, y: 0 });
  const [hoveredTask, setHoveredTask] = useState<TimelineTask | null>(null);
  const [selectedTask, setSelectedTask] = useState<TimelineTask | null>(null);

  const dayWidth = 30 * scale;
  const taskHeight = 40;
  const headerHeight = 60;
  const leftPanelWidth = 250;

  useEffect(() => {
    drawGantt();
  }, [timeline, scale, offset]);

  const drawGantt = () => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx) return;

    // Set canvas size
    const totalDays = differenceInDays(
      new Date(timeline.endDate),
      new Date(timeline.startDate)
    );
    canvas.width = leftPanelWidth + totalDays * dayWidth + 100;
    canvas.height = headerHeight + timeline.tasks.length * taskHeight + 100;

    // Clear canvas
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw grid
    drawGrid(ctx, totalDays);

    // Draw tasks
    drawTasks(ctx);

    // Draw dependencies
    drawDependencies(ctx);

    // Draw critical path
    drawCriticalPath(ctx);
  };

  const drawGrid = (ctx: CanvasRenderingContext2D, totalDays: number) => {
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 1;

    // Draw vertical lines (days)
    for (let i = 0; i <= totalDays; i++) {
      const x = leftPanelWidth + i * dayWidth;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, ctx.canvas.height);
      ctx.stroke();

      // Draw day labels
      if (i % 7 === 0) {
        ctx.fillStyle = '#6b7280';
        ctx.font = '12px sans-serif';
        const date = addDays(new Date(timeline.startDate), i);
        ctx.fillText(format(date, 'MMM d'), x + 2, 20);
      }
    }

    // Draw horizontal lines (tasks)
    for (let i = 0; i <= timeline.tasks.length; i++) {
      const y = headerHeight + i * taskHeight;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(ctx.canvas.width, y);
      ctx.stroke();
    }

    // Draw header separator
    ctx.strokeStyle = '#374151';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(0, headerHeight);
    ctx.lineTo(ctx.canvas.width, headerHeight);
    ctx.stroke();
  };

  const drawTasks = (ctx: CanvasRenderingContext2D) => {
    timeline.tasks.forEach((task, index) => {
      const y = headerHeight + index * taskHeight + 5;
      const startX =
        leftPanelWidth +
        differenceInDays(new Date(task.startDate), new Date(timeline.startDate)) *
          dayWidth;
      const width = task.duration * dayWidth;

      // Draw task name in left panel
      ctx.fillStyle = '#111827';
      ctx.font = '14px sans-serif';
      ctx.fillText(
        task.title.length > 25 ? task.title.substring(0, 25) + '...' : task.title,
        10,
        y + taskHeight / 2
      );

      // Draw task bar
      ctx.fillStyle = task.criticalPath ? '#ef4444' : '#3b82f6';
      ctx.fillRect(startX, y, width, taskHeight - 10);

      // Draw progress
      if (task.status === 'in-progress' || task.status === 'completed') {
        const progress = task.status === 'completed' ? 1 : 0.5;
        ctx.fillStyle = task.criticalPath ? '#dc2626' : '#2563eb';
        ctx.fillRect(startX, y, width * progress, taskHeight - 10);
      }

      // Draw task label on bar
      ctx.fillStyle = '#ffffff';
      ctx.font = '12px sans-serif';
      const label = `${task.duration}d`;
      const labelWidth = ctx.measureText(label).width;
      if (width > labelWidth + 10) {
        ctx.fillText(label, startX + width / 2 - labelWidth / 2, y + taskHeight / 2);
      }
    });
  };

  const drawDependencies = (ctx: CanvasRenderingContext2D) => {
    ctx.strokeStyle = '#9ca3af';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);

    timeline.tasks.forEach((task, index) => {
      task.predecessors.forEach(predId => {
        const predIndex = timeline.tasks.findIndex(t => t.id === predId);
        if (predIndex !== -1) {
          const predTask = timeline.tasks[predIndex];
          const startY = headerHeight + predIndex * taskHeight + taskHeight / 2;
          const endY = headerHeight + index * taskHeight + taskHeight / 2;
          
          const startX =
            leftPanelWidth +
            differenceInDays(new Date(predTask.endDate), new Date(timeline.startDate)) *
              dayWidth;
          const endX =
            leftPanelWidth +
            differenceInDays(new Date(task.startDate), new Date(timeline.startDate)) *
              dayWidth;

          // Draw arrow
          ctx.beginPath();
          ctx.moveTo(startX, startY);
          ctx.lineTo(endX - 10, endY);
          ctx.stroke();

          // Draw arrowhead
          ctx.setLineDash([]);
          ctx.beginPath();
          ctx.moveTo(endX - 10, endY);
          ctx.lineTo(endX - 15, endY - 5);
          ctx.lineTo(endX - 15, endY + 5);
          ctx.closePath();
          ctx.fillStyle = '#9ca3af';
          ctx.fill();
          ctx.setLineDash([5, 5]);
        }
      });
    });

    ctx.setLineDash([]);
  };

  const drawCriticalPath = (ctx: CanvasRenderingContext2D) => {
    const criticalTasks = timeline.tasks.filter(t => t.criticalPath);
    if (criticalTasks.length === 0) return;

    ctx.strokeStyle = '#ef4444';
    ctx.lineWidth = 3;
    ctx.globalAlpha = 0.5;

    // Highlight critical path
    criticalTasks.forEach((task, index) => {
      const y = headerHeight + timeline.tasks.indexOf(task) * taskHeight;
      ctx.fillStyle = '#fee2e2';
      ctx.fillRect(0, y, ctx.canvas.width, taskHeight);
    });

    ctx.globalAlpha = 1;
  };

  const handleExport = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    link.download = `gantt-chart-${timeline.name}.png`;
    link.href = canvas.toDataURL();
    link.click();
  };

  const handleZoomIn = () => setScale(Math.min(scale * 1.2, 3));
  const handleZoomOut = () => setScale(Math.max(scale / 1.2, 0.5));

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{timeline.name} - Project Timeline</CardTitle>
            <CardDescription>
              {format(new Date(timeline.startDate), 'MMM d, yyyy')} -{' '}
              {format(new Date(timeline.endDate), 'MMM d, yyyy')} ({timeline.duration} days)
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleZoomOut}>
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleZoomIn}>
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Summary Stats */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          <div className="flex items-center gap-2">
            <CalendarDays className="h-5 w-5 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">{timeline.tasks.length} Tasks</p>
              <p className="text-xs text-muted-foreground">Total tasks</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">{timeline.duration} Days</p>
              <p className="text-xs text-muted-foreground">Total duration</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">{timeline.resources.length} Resources</p>
              <p className="text-xs text-muted-foreground">Team members</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <div>
              <p className="text-sm font-medium">
                {timeline.criticalPath.length} Critical
              </p>
              <p className="text-xs text-muted-foreground">Critical path tasks</p>
            </div>
          </div>
        </div>

        {/* Legend */}
        <div className="flex items-center gap-4 mb-4">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-blue-500 rounded" />
            <span className="text-sm">Regular Task</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-red-500 rounded" />
            <span className="text-sm">Critical Path</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-blue-600 rounded" />
            <span className="text-sm">In Progress</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-gray-300 rounded" />
            <span className="text-sm">Dependency</span>
          </div>
        </div>

        {/* Gantt Chart */}
        <div
          ref={containerRef}
          className="relative overflow-auto border rounded-lg"
          style={{ maxHeight: '600px' }}
        >
          <canvas
            ref={canvasRef}
            className="cursor-pointer"
            onClick={(e) => {
              // Handle task click
              const rect = canvasRef.current?.getBoundingClientRect();
              if (!rect) return;
              const x = e.clientX - rect.left;
              const y = e.clientY - rect.top;
              
              // Find clicked task
              const taskIndex = Math.floor((y - headerHeight) / taskHeight);
              if (taskIndex >= 0 && taskIndex < timeline.tasks.length) {
                const task = timeline.tasks[taskIndex];
                setSelectedTask(task);
                onTaskClick?.(task);
              }
            }}
          />
        </div>

        {/* Selected Task Details */}
        {selectedTask && (
          <div className="mt-4 p-4 border rounded-lg bg-muted/50">
            <h4 className="font-medium mb-2">{selectedTask.title}</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">Start:</span>{' '}
                {format(new Date(selectedTask.startDate), 'MMM d, yyyy')}
              </div>
              <div>
                <span className="text-muted-foreground">End:</span>{' '}
                {format(new Date(selectedTask.endDate), 'MMM d, yyyy')}
              </div>
              <div>
                <span className="text-muted-foreground">Duration:</span>{' '}
                {selectedTask.duration} days
              </div>
              <div>
                <span className="text-muted-foreground">Status:</span>{' '}
                <Badge variant={selectedTask.status === 'completed' ? 'default' : 'secondary'}>
                  {selectedTask.status}
                </Badge>
              </div>
            </div>
            {selectedTask.description && (
              <p className="mt-2 text-sm text-muted-foreground">
                {selectedTask.description}
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};