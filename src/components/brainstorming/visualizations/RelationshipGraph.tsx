/**
 * Relationship Graph Component
 * 
 * This component visualizes the connections between ideas as a force-directed graph.
 * It helps identify clusters and relationships between different ideas.
 */

import React, { useState, useEffect, useRef } from 'react';
import { Idea, IdeaStatus } from '@/types/brainstorm';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { formatIdeaStatus, getStatusVariant } from '@/components/brainstorming/utils/idea-utils';
import { motion } from 'framer-motion';

interface RelationshipGraphProps {
  ideas: Idea[];
  selectedIdeaIds: string[];
  onIdeaSelect: (ideaId: string) => void;
  className?: string;
}

interface GraphNode {
  id: string;
  idea: Idea;
  x: number;
  y: number;
  vx: number;
  vy: number;
  radius: number;
}

interface GraphLink {
  source: string;
  target: string;
  strength: number;
}

export const RelationshipGraph: React.FC<RelationshipGraphProps> = ({
  ideas,
  selectedIdeaIds,
  onIdeaSelect,
  className,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [nodes, setNodes] = useState<GraphNode[]>([]);
  const [links, setLinks] = useState<GraphLink[]>([]);
  const [scale, setScale] = useState(1);
  const [offset, setOffset] = useState({ x: 0, y: 0 });
  const [dragging, setDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [hoveredNodeId, setHoveredNodeId] = useState<string | null>(null);
  const [simulationRunning, setSimulationRunning] = useState(false);
  const animationFrameRef = useRef<number | null>(null);

  // Initialize graph data
  useEffect(() => {
    if (ideas.length === 0) return;

    const width = containerRef.current?.clientWidth || 800;
    const height = containerRef.current?.clientHeight || 600;
    
    // Create nodes
    const newNodes: GraphNode[] = ideas.map((idea) => {
      return {
        id: idea.id,
        idea,
        x: Math.random() * width * 0.8 + width * 0.1,
        y: Math.random() * height * 0.8 + height * 0.1,
        vx: 0,
        vy: 0,
        radius: 30 + (idea.connections?.length || 0) * 3, // Size based on connections
      };
    });

    // Create links based on idea connections
    const newLinks: GraphLink[] = [];
    ideas.forEach(idea => {
      if (idea.connections && idea.connections.length > 0) {
        idea.connections.forEach(connectionId => {
          // Avoid duplicate links
          const existingLink = newLinks.find(
            link => (link.source === idea.id && link.target === connectionId) ||
                   (link.source === connectionId && link.target === idea.id)
          );
          
          if (!existingLink) {
            newLinks.push({
              source: idea.id,
              target: connectionId,
              strength: 1,
            });
          }
        });
      }
    });

    // Create additional links based on shared tags
    ideas.forEach((ideaA, indexA) => {
      ideas.forEach((ideaB, indexB) => {
        if (indexA < indexB && ideaA.tags && ideaB.tags) {
          const sharedTags = ideaA.tags.filter(tag => ideaB.tags?.includes(tag));
          if (sharedTags.length > 0) {
            // Avoid duplicate links
            const existingLink = newLinks.find(
              link => (link.source === ideaA.id && link.target === ideaB.id) ||
                     (link.source === ideaB.id && link.target === ideaA.id)
            );
            
            if (!existingLink) {
              newLinks.push({
                source: ideaA.id,
                target: ideaB.id,
                strength: sharedTags.length * 0.5, // Weaker than direct connections
              });
            }
          }
        }
      });
    });

    setNodes(newNodes);
    setLinks(newLinks);
    setSimulationRunning(true);
  }, [ideas]);

  // Force simulation
  useEffect(() => {
    if (!simulationRunning) return;

    const width = containerRef.current?.clientWidth || 800;
    const height = containerRef.current?.clientHeight || 600;
    
    // Simple force-directed layout simulation
    const simulate = () => {
      setNodes(prevNodes => {
        const newNodes = [...prevNodes];
        
        // Apply forces
        for (let i = 0; i < newNodes.length; i++) {
          const node = newNodes[i];
          
          // Repulsive force between nodes
          for (let j = 0; j < newNodes.length; j++) {
            if (i !== j) {
              const otherNode = newNodes[j];
              const dx = node.x - otherNode.x;
              const dy = node.y - otherNode.y;
              const distance = Math.sqrt(dx * dx + dy * dy) || 1;
              const force = 200 / (distance * distance);
              
              node.vx += (dx / distance) * force;
              node.vy += (dy / distance) * force;
            }
          }
          
          // Attractive force for links
          links.forEach(link => {
            if (link.source === node.id || link.target === node.id) {
              const otherNodeId = link.source === node.id ? link.target : link.source;
              const otherNode = newNodes.find(n => n.id === otherNodeId);
              
              if (otherNode) {
                const dx = otherNode.x - node.x;
                const dy = otherNode.y - node.y;
                const distance = Math.sqrt(dx * dx + dy * dy) || 1;
                const force = distance * 0.01 * link.strength;
                
                node.vx += (dx / distance) * force;
                node.vy += (dy / distance) * force;
              }
            }
          });
          
          // Center gravity
          node.vx += (width / 2 - node.x) * 0.01;
          node.vy += (height / 2 - node.y) * 0.01;
          
          // Damping
          node.vx *= 0.9;
          node.vy *= 0.9;
          
          // Update position
          node.x += node.vx;
          node.y += node.vy;
          
          // Boundary constraints
          node.x = Math.max(node.radius, Math.min(width - node.radius, node.x));
          node.y = Math.max(node.radius, Math.min(height - node.radius, node.y));
        }
        
        return newNodes;
      });
      
      // Check if simulation should continue
      const isStable = nodes.every(node => Math.abs(node.vx) < 0.1 && Math.abs(node.vy) < 0.1);
      if (isStable) {
        setSimulationRunning(false);
      } else {
        animationFrameRef.current = requestAnimationFrame(simulate);
      }
    };
    
    animationFrameRef.current = requestAnimationFrame(simulate);
    
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [simulationRunning, nodes, links]);

  // Handle zoom with mouse wheel
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setScale(prevScale => Math.min(Math.max(prevScale * delta, 0.5), 2));
  };

  // Handle pan with mouse drag
  const handleMouseDown = (e: React.MouseEvent) => {
    setDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!dragging) return;
    
    const dx = e.clientX - dragStart.x;
    const dy = e.clientY - dragStart.y;
    
    setOffset(prev => ({ x: prev.x + dx, y: prev.y + dy }));
    setDragStart({ x: e.clientX, y: e.clientY });
  };

  const handleMouseUp = () => {
    setDragging(false);
  };

  // Handle node click
  const handleNodeClick = (ideaId: string) => {
    onIdeaSelect(ideaId);
  };

  // Reset simulation
  const handleResetSimulation = () => {
    setSimulationRunning(true);
  };

  return (
    <div className={cn("h-full relative", className)}>
      <div className="absolute top-4 right-4 z-10">
        <button
          className="bg-primary/10 hover:bg-primary/20 text-primary text-xs px-2 py-1 rounded-md"
          onClick={handleResetSimulation}
        >
          Reset Layout
        </button>
      </div>
      
      <div 
        ref={containerRef}
        className="w-full h-full overflow-hidden"
        onWheel={handleWheel}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <svg className="w-full h-full">
          <g
            transform={`translate(${offset.x}, ${offset.y}) scale(${scale})`}
          >
            {/* Render links */}
            {links.map((link) => {
              const sourceNode = nodes.find(node => node.id === link.source);
              const targetNode = nodes.find(node => node.id === link.target);
              
              if (!sourceNode || !targetNode) return null;
              
              const isHighlighted = 
                selectedIdeaIds.includes(link.source) || 
                selectedIdeaIds.includes(link.target) ||
                hoveredNodeId === link.source ||
                hoveredNodeId === link.target;
              
              return (
                <line
                  key={`${link.source}-${link.target}`}
                  x1={sourceNode.x}
                  y1={sourceNode.y}
                  x2={targetNode.x}
                  y2={targetNode.y}
                  stroke="currentColor"
                  strokeWidth={1 + link.strength}
                  className={cn(
                    "stroke-muted-foreground/30",
                    isHighlighted && "stroke-primary/50"
                  )}
                />
              );
            })}
            
            {/* Render nodes */}
            {nodes.map((node) => {
              const isSelected = selectedIdeaIds.includes(node.id);
              const isHovered = hoveredNodeId === node.id;
              const status = node.idea.status || IdeaStatus.TO_EXPLORE;
              const statusVariant = getStatusVariant(status);
              
              return (
                <g 
                  key={node.id} 
                  transform={`translate(${node.x}, ${node.y})`}
                  onClick={() => handleNodeClick(node.id)}
                  onMouseEnter={() => setHoveredNodeId(node.id)}
                  onMouseLeave={() => setHoveredNodeId(null)}
                  className="cursor-pointer"
                >
                  <motion.circle
                    r={node.radius}
                    className={cn(
                      "fill-card stroke-2",
                      isSelected ? "stroke-primary" : "stroke-border",
                      isHovered && "stroke-primary/50"
                    )}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  />
                  
                  <foreignObject
                    x={-node.radius + 5}
                    y={-node.radius + 5}
                    width={node.radius * 2 - 10}
                    height={node.radius * 2 - 10}
                    className="overflow-hidden text-center flex flex-col items-center justify-center"
                  >
                    <div className="text-xs font-medium truncate">
                      {node.idea.content.length > 30
                        ? `${node.idea.content.substring(0, 30)}...`
                        : node.idea.content}
                    </div>
                    <Badge variant={statusVariant} className="mt-1 text-[0.6rem] h-4">
                      {formatIdeaStatus(status)}
                    </Badge>
                    {node.idea.tags && node.idea.tags.length > 0 && (
                      <div className="mt-1 text-[0.6rem] text-muted-foreground">
                        {node.idea.tags.slice(0, 2).join(', ')}
                        {node.idea.tags.length > 2 && '...'}
                      </div>
                    )}
                  </foreignObject>
                </g>
              );
            })}
          </g>
        </svg>
      </div>
    </div>
  );
};