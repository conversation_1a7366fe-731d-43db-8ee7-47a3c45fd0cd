/**
 * Matrix View Component
 * 
 * This component visualizes ideas in a 2x2 matrix for evaluation based on
 * impact and effort. It helps prioritize ideas based on their potential value.
 */

import React, { useState, useEffect } from 'react';
import { Idea, IdeaStatus } from '@/types/brainstorm';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatIdeaStatus, getStatusVariant } from '@/components/brainstorming/utils/idea-utils';
import { motion } from 'framer-motion';

interface MatrixViewProps {
  ideas: Idea[];
  selectedIdeaIds: string[];
  onIdeaSelect: (ideaId: string) => void;
  className?: string;
}

interface MatrixQuadrant {
  id: string;
  title: string;
  description: string;
  className: string;
  ideas: Idea[];
}

export const MatrixView: React.FC<MatrixViewProps> = ({
  ideas,
  selectedIdeaIds,
  onIdeaSelect,
  className,
}) => {
  const [quadrants, setQuadrants] = useState<MatrixQuadrant[]>([]);
  const [draggingIdeaId, setDraggingIdeaId] = useState<string | null>(null);
  const [dragOverQuadrantId, setDragOverQuadrantId] = useState<string | null>(null);

  // Initialize quadrants
  useEffect(() => {
    // Define the quadrants
    const matrixQuadrants: MatrixQuadrant[] = [
      {
        id: 'high-impact-low-effort',
        title: 'Quick Wins',
        description: 'High Impact, Low Effort',
        className: 'bg-green-50 dark:bg-green-950/30',
        ideas: ideas.filter(idea => 
          (idea.impact || 0) >= 0.5 && (idea.effort || 0) < 0.5
        ),
      },
      {
        id: 'high-impact-high-effort',
        title: 'Major Projects',
        description: 'High Impact, High Effort',
        className: 'bg-blue-50 dark:bg-blue-950/30',
        ideas: ideas.filter(idea => 
          (idea.impact || 0) >= 0.5 && (idea.effort || 0) >= 0.5
        ),
      },
      {
        id: 'low-impact-low-effort',
        title: 'Fill-ins',
        description: 'Low Impact, Low Effort',
        className: 'bg-yellow-50 dark:bg-yellow-950/30',
        ideas: ideas.filter(idea => 
          (idea.impact || 0) < 0.5 && (idea.effort || 0) < 0.5
        ),
      },
      {
        id: 'low-impact-high-effort',
        title: 'Thankless Tasks',
        description: 'Low Impact, High Effort',
        className: 'bg-red-50 dark:bg-red-950/30',
        ideas: ideas.filter(idea => 
          (idea.impact || 0) < 0.5 && (idea.effort || 0) >= 0.5
        ),
      },
    ];

    // Handle ideas without impact/effort scores
    const unratedIdeas = ideas.filter(idea => 
      idea.impact === undefined || idea.effort === undefined
    );

    if (unratedIdeas.length > 0) {
      // Add a fifth "unrated" quadrant
      matrixQuadrants.push({
        id: 'unrated',
        title: 'Unrated Ideas',
        description: 'Impact/Effort not set',
        className: 'bg-gray-50 dark:bg-gray-800/30',
        ideas: unratedIdeas,
      });
    }

    setQuadrants(matrixQuadrants);
  }, [ideas]);

  // Handle drag start
  const handleDragStart = (ideaId: string) => {
    setDraggingIdeaId(ideaId);
  };

  // Handle drag over quadrant
  const handleDragOver = (e: React.DragEvent, quadrantId: string) => {
    e.preventDefault();
    setDragOverQuadrantId(quadrantId);
  };

  // Handle drag end
  const handleDragEnd = () => {
    setDraggingIdeaId(null);
    setDragOverQuadrantId(null);
  };

  // Handle drop on quadrant
  const handleDrop = (e: React.DragEvent, quadrantId: string) => {
    e.preventDefault();
    
    if (draggingIdeaId) {
      // In a real implementation, this would update the idea impact/effort in the store
      // based on which quadrant it was dropped in
      console.log(`Move idea ${draggingIdeaId} to quadrant ${quadrantId}`);
    }
    
    setDraggingIdeaId(null);
    setDragOverQuadrantId(null);
  };

  // Handle idea click
  const handleIdeaClick = (ideaId: string) => {
    onIdeaSelect(ideaId);
  };

  return (
    <div className={cn("h-full", className)}>
      <div className="flex justify-between mb-2">
        <div className="text-sm text-muted-foreground">Low Effort</div>
        <div className="text-sm text-muted-foreground">High Effort</div>
      </div>
      
      <div className="grid grid-cols-2 gap-4 h-[calc(100%-2rem)]">
        {/* Top row: High Impact */}
        <div 
          className={cn(
            "border rounded-lg p-3 overflow-auto",
            quadrants.find(q => q.id === 'high-impact-low-effort')?.className,
            dragOverQuadrantId === 'high-impact-low-effort' && "ring-2 ring-primary"
          )}
          onDragOver={(e) => handleDragOver(e, 'high-impact-low-effort')}
          onDrop={(e) => handleDrop(e, 'high-impact-low-effort')}
        >
          <h3 className="font-medium mb-1">
            {quadrants.find(q => q.id === 'high-impact-low-effort')?.title}
          </h3>
          <p className="text-xs text-muted-foreground mb-3">
            {quadrants.find(q => q.id === 'high-impact-low-effort')?.description}
          </p>
          
          {quadrants
            .find(q => q.id === 'high-impact-low-effort')
            ?.ideas.map((idea) => renderIdeaCard(idea))}
        </div>
        
        <div 
          className={cn(
            "border rounded-lg p-3 overflow-auto",
            quadrants.find(q => q.id === 'high-impact-high-effort')?.className,
            dragOverQuadrantId === 'high-impact-high-effort' && "ring-2 ring-primary"
          )}
          onDragOver={(e) => handleDragOver(e, 'high-impact-high-effort')}
          onDrop={(e) => handleDrop(e, 'high-impact-high-effort')}
        >
          <h3 className="font-medium mb-1">
            {quadrants.find(q => q.id === 'high-impact-high-effort')?.title}
          </h3>
          <p className="text-xs text-muted-foreground mb-3">
            {quadrants.find(q => q.id === 'high-impact-high-effort')?.description}
          </p>
          
          {quadrants
            .find(q => q.id === 'high-impact-high-effort')
            ?.ideas.map((idea) => renderIdeaCard(idea))}
        </div>
        
        {/* Bottom row: Low Impact */}
        <div 
          className={cn(
            "border rounded-lg p-3 overflow-auto",
            quadrants.find(q => q.id === 'low-impact-low-effort')?.className,
            dragOverQuadrantId === 'low-impact-low-effort' && "ring-2 ring-primary"
          )}
          onDragOver={(e) => handleDragOver(e, 'low-impact-low-effort')}
          onDrop={(e) => handleDrop(e, 'low-impact-low-effort')}
        >
          <h3 className="font-medium mb-1">
            {quadrants.find(q => q.id === 'low-impact-low-effort')?.title}
          </h3>
          <p className="text-xs text-muted-foreground mb-3">
            {quadrants.find(q => q.id === 'low-impact-low-effort')?.description}
          </p>
          
          {quadrants
            .find(q => q.id === 'low-impact-low-effort')
            ?.ideas.map((idea) => renderIdeaCard(idea))}
        </div>
        
        <div 
          className={cn(
            "border rounded-lg p-3 overflow-auto",
            quadrants.find(q => q.id === 'low-impact-high-effort')?.className,
            dragOverQuadrantId === 'low-impact-high-effort' && "ring-2 ring-primary"
          )}
          onDragOver={(e) => handleDragOver(e, 'low-impact-high-effort')}
          onDrop={(e) => handleDrop(e, 'low-impact-high-effort')}
        >
          <h3 className="font-medium mb-1">
            {quadrants.find(q => q.id === 'low-impact-high-effort')?.title}
          </h3>
          <p className="text-xs text-muted-foreground mb-3">
            {quadrants.find(q => q.id === 'low-impact-high-effort')?.description}
          </p>
          
          {quadrants
            .find(q => q.id === 'low-impact-high-effort')
            ?.ideas.map((idea) => renderIdeaCard(idea))}
        </div>
      </div>
      
      {/* Unrated ideas section (if any) */}
      {quadrants.find(q => q.id === 'unrated') && (
        <div 
          className={cn(
            "border rounded-lg p-3 mt-4 overflow-auto",
            quadrants.find(q => q.id === 'unrated')?.className,
            dragOverQuadrantId === 'unrated' && "ring-2 ring-primary"
          )}
          onDragOver={(e) => handleDragOver(e, 'unrated')}
          onDrop={(e) => handleDrop(e, 'unrated')}
        >
          <h3 className="font-medium mb-1">
            {quadrants.find(q => q.id === 'unrated')?.title}
          </h3>
          <p className="text-xs text-muted-foreground mb-3">
            {quadrants.find(q => q.id === 'unrated')?.description}
          </p>
          
          <div className="flex flex-wrap gap-2">
            {quadrants
              .find(q => q.id === 'unrated')
              ?.ideas.map((idea) => renderIdeaCard(idea))}
          </div>
        </div>
      )}
    </div>
  );

  // Helper function to render idea cards
  function renderIdeaCard(idea: Idea) {
    const isSelected = selectedIdeaIds.includes(idea.id);
    const status = idea.status || IdeaStatus.TO_EXPLORE;
    const statusVariant = getStatusVariant(status);
    
    return (
      <motion.div
        key={idea.id}
        layoutId={`matrix-${idea.id}`}
        draggable
        onDragStart={() => handleDragStart(idea.id)}
        onDragEnd={handleDragEnd}
        onClick={() => handleIdeaClick(idea.id)}
        className="mb-2 last:mb-0"
      >
        <Card
          className={cn(
            "cursor-pointer hover:border-primary/50 transition-colors",
            isSelected && "border-primary"
          )}
        >
          <CardContent className="p-3">
            <p className="text-sm">{idea.content}</p>
            <div className="flex justify-between items-center mt-2">
              <Badge variant={statusVariant} className="text-xs">
                {formatIdeaStatus(status)}
              </Badge>
              
              {idea.tags && idea.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 justify-end">
                  {idea.tags.slice(0, 2).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {idea.tags.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{idea.tags.length - 2}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }
};