/**
 * Visualization Container Component
 * 
 * This component serves as a container for different visualization types
 * in the brainstorming system. It handles switching between visualization types
 * and provides a consistent interface for all visualizations.
 */

import React, { useState } from 'react';
import { VisualizationType, Idea } from '@/types/brainstorm';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import { GitBranch, Columns3, Grid3x3, Calendar, Network } from 'lucide-react';

// Import visualization components
import { MindMapVisualization } from './MindMapVisualization';
import { KanbanBoard } from './KanbanBoard';
import { MatrixView } from './MatrixView';
import { TimelineView } from './TimelineView';
import { RelationshipGraph } from './RelationshipGraph';

interface VisualizationContainerProps {
  sessionId: string | null;
  ideas: Idea[];
  selectedIdeaIds: string[];
  onIdeaSelect: (ideaId: string) => void;
  className?: string;
}

export const VisualizationContainer: React.FC<VisualizationContainerProps> = ({
  sessionId,
  ideas,
  selectedIdeaIds,
  onIdeaSelect,
  className,
}) => {
  const [visualizationType, setVisualizationType] = useState<VisualizationType>(VisualizationType.MIND_MAP);

  // Render the appropriate visualization component
  const renderVisualization = () => {
    if (!sessionId || ideas.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
          <p>No ideas to visualize</p>
          <p className="text-sm mt-2">Start brainstorming to generate ideas</p>
        </div>
      );
    }

    switch (visualizationType) {
      case VisualizationType.MIND_MAP:
        return (
          <MindMapVisualization 
            ideas={ideas}
            selectedIdeaIds={selectedIdeaIds}
            onIdeaSelect={onIdeaSelect}
            className="h-[400px]"
          />
        );
      case VisualizationType.KANBAN:
        return (
          <KanbanBoard 
            ideas={ideas}
            selectedIdeaIds={selectedIdeaIds}
            onIdeaSelect={onIdeaSelect}
            className="h-[400px]"
          />
        );
      case VisualizationType.MATRIX:
        return (
          <MatrixView 
            ideas={ideas}
            selectedIdeaIds={selectedIdeaIds}
            onIdeaSelect={onIdeaSelect}
            className="h-[400px]"
          />
        );
      case VisualizationType.TIMELINE:
        return (
          <TimelineView 
            ideas={ideas}
            selectedIdeaIds={selectedIdeaIds}
            onIdeaSelect={onIdeaSelect}
            className="h-[400px]"
          />
        );
      case VisualizationType.RELATIONSHIP:
        return (
          <RelationshipGraph 
            ideas={ideas}
            selectedIdeaIds={selectedIdeaIds}
            onIdeaSelect={onIdeaSelect}
            className="h-[400px]"
          />
        );
      default:
        return (
          <div className="flex items-center justify-center h-64 text-muted-foreground">
            <p>Select a visualization type</p>
          </div>
        );
    }
  };

  return (
    <Card className={className}>
      <Tabs
        defaultValue={visualizationType}
        onValueChange={(value) => setVisualizationType(value as VisualizationType)}
        className="w-full"
      >
        <div className="flex items-center justify-between px-4 pt-4">
          <h3 className="text-lg font-medium">Visualizations</h3>
          <TabsList>
            <TabsTrigger value={VisualizationType.MIND_MAP}>
              <GitBranch className="h-4 w-4" />
            </TabsTrigger>
            <TabsTrigger value={VisualizationType.KANBAN}>
              <Columns3 className="h-4 w-4" />
            </TabsTrigger>
            <TabsTrigger value={VisualizationType.MATRIX}>
              <Grid3x3 className="h-4 w-4" />
            </TabsTrigger>
            <TabsTrigger value={VisualizationType.TIMELINE}>
              <Calendar className="h-4 w-4" />
            </TabsTrigger>
            <TabsTrigger value={VisualizationType.RELATIONSHIP}>
              <Network className="h-4 w-4" />
            </TabsTrigger>
          </TabsList>
        </div>
        <div className="p-4">
          {renderVisualization()}
        </div>
      </Tabs>
    </Card>
  );
};