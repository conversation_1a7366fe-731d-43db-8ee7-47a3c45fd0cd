/**
 * Timeline View Component
 * 
 * This component visualizes ideas as a timeline for project planning.
 * It organizes ideas chronologically and shows dependencies between them.
 */

import React, { useState, useEffect } from 'react';
import { Idea, IdeaStatus } from '@/types/brainstorm';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatIdeaStatus, getStatusVariant } from '@/components/brainstorming/utils/idea-utils';
import { motion } from 'framer-motion';

interface TimelineViewProps {
  ideas: Idea[];
  selectedIdeaIds: string[];
  onIdeaSelect: (ideaId: string) => void;
  className?: string;
}

interface TimelineItem {
  idea: Idea;
  x: number;
  y: number;
  width: number;
  dependencies: string[];
}

export const TimelineView: React.FC<TimelineViewProps> = ({
  ideas,
  selectedIdeaIds,
  onIdeaSelect,
  className,
}) => {
  const [timelineItems, setTimelineItems] = useState<TimelineItem[]>([]);
  const [timeScale] = useState<number>(100); // pixels per day
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [hoveredIdeaId, setHoveredIdeaId] = useState<string | null>(null);

  // Calculate timeline layout
  useEffect(() => {
    if (ideas.length === 0) return;

    // Find ideas with timestamps
    const ideasWithDates = ideas.filter(idea => idea.createdAt);
    
    if (ideasWithDates.length === 0) {
      // If no ideas have timestamps, use creation dates as fallback
      const sortedIdeas = [...ideas].sort((a, b) => {
        const dateA = a.createdAt ? new Date(a.createdAt) : new Date();
        const dateB = b.createdAt ? new Date(b.createdAt) : new Date();
        return dateA.getTime() - dateB.getTime();
      });

      // Set start date to earliest creation date
      const earliestDate = sortedIdeas[0].createdAt 
        ? new Date(sortedIdeas[0].createdAt) 
        : new Date();
      
      // Set end date to latest creation date + 7 days
      const latestDate = sortedIdeas[sortedIdeas.length - 1].createdAt 
        ? new Date(sortedIdeas[sortedIdeas.length - 1].createdAt) 
        : new Date();
      latestDate.setDate(latestDate.getDate() + 7);

      setStartDate(earliestDate);
      setEndDate(latestDate);

      // Create timeline items with evenly spaced positions
      const items: TimelineItem[] = sortedIdeas.map((idea, index) => {
        // Calculate position based on creation date
        const creationDate = idea.createdAt ? new Date(idea.createdAt) : new Date();
        const daysSinceStart = Math.floor(
          (creationDate.getTime() - earliestDate.getTime()) / (1000 * 60 * 60 * 24)
        );
        
        return {
          idea,
          x: daysSinceStart * timeScale,
          y: 50 + (index % 3) * 100, // Stagger vertically in 3 rows
          width: 200,
          dependencies: idea.connections || [],
        };
      });

      setTimelineItems(items);
    } else {
      // Use actual timestamps for timeline positioning
      const sortedIdeas = [...ideasWithDates].sort((a, b) => {
        const dateA = a.createdAt ? new Date(a.createdAt) : new Date();
        const dateB = b.createdAt ? new Date(b.createdAt) : new Date();
        return dateA.getTime() - dateB.getTime();
      });

      // Set start date to earliest timestamp
      const earliestDate = sortedIdeas[0].createdAt 
        ? new Date(sortedIdeas[0].createdAt) 
        : new Date();
      earliestDate.setDate(earliestDate.getDate() - 1); // Add 1 day buffer
      
      // Set end date to latest timestamp + 7 days
      const latestDate = sortedIdeas[sortedIdeas.length - 1].createdAt 
        ? new Date(sortedIdeas[sortedIdeas.length - 1].createdAt) 
        : new Date();
      latestDate.setDate(latestDate.getDate() + 7);

      setStartDate(earliestDate);
      setEndDate(latestDate);

      // Create timeline items with positions based on timestamps
      const items: TimelineItem[] = sortedIdeas.map((idea, index) => {
        // Calculate position based on timestamp
        const timestamp = idea.createdAt ? new Date(idea.createdAt) : new Date();
        const daysSinceStart = Math.floor(
          (timestamp.getTime() - earliestDate.getTime()) / (1000 * 60 * 60 * 24)
        );
        
        return {
          idea,
          x: daysSinceStart * timeScale,
          y: 50 + (index % 3) * 100, // Stagger vertically in 3 rows
          width: 200,
          dependencies: idea.connections || [],
        };
      });

      setTimelineItems(items);
    }
  }, [ideas, timeScale]);

  // Handle idea click
  const handleIdeaClick = (ideaId: string) => {
    onIdeaSelect(ideaId);
  };

  // Format date for display
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  // Generate month markers
  const generateMonthMarkers = () => {
    const markers = [];
    const currentDate = new Date(startDate);
    currentDate.setDate(1); // Start at the beginning of the month
    
    while (currentDate <= endDate) {
      const daysSinceStart = Math.floor(
        (currentDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      markers.push(
        <div 
          key={currentDate.toISOString()}
          className="absolute top-0 h-full border-l border-dashed border-muted-foreground/30"
          style={{ left: `${daysSinceStart * timeScale}px` }}
        >
          <div className="absolute -left-12 -top-8 text-xs text-muted-foreground">
            {currentDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
          </div>
        </div>
      );
      
      // Move to next month
      currentDate.setMonth(currentDate.getMonth() + 1);
    }
    
    return markers;
  };

  return (
    <div className={cn("h-full relative", className)}>
      <div className="flex justify-between mb-4">
        <div className="text-sm">
          <span className="text-muted-foreground">Start: </span>
          {formatDate(startDate)}
        </div>
        <div className="text-sm">
          <span className="text-muted-foreground">End: </span>
          {formatDate(endDate)}
        </div>
      </div>
      
      <div className="border rounded-lg p-4 h-[calc(100%-3rem)] overflow-auto">
        <div 
          className="relative min-h-[400px]"
          style={{ width: `${Math.max(800, (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24) * timeScale)}px` }}
        >
          {/* Month markers */}
          {generateMonthMarkers()}
          
          {/* Timeline items */}
          {timelineItems.map((item) => {
            const isSelected = selectedIdeaIds.includes(item.idea.id);
            const isHovered = hoveredIdeaId === item.idea.id;
            const status = item.idea.status || IdeaStatus.TO_EXPLORE;
            const statusVariant = getStatusVariant(status);
            
            return (
              <React.Fragment key={item.idea.id}>
                {/* Dependency lines */}
                {item.dependencies.map(depId => {
                  const dependencyItem = timelineItems.find(ti => ti.idea.id === depId);
                  if (!dependencyItem) return null;
                  
                  return (
                    <line
                      key={`${item.idea.id}-${depId}`}
                      x1={dependencyItem.x + dependencyItem.width}
                      y1={dependencyItem.y + 30}
                      x2={item.x}
                      y2={item.y + 30}
                      stroke="currentColor"
                      strokeWidth={2}
                      className={cn(
                        "stroke-muted-foreground/30",
                        (isSelected || selectedIdeaIds.includes(depId) || 
                         isHovered || hoveredIdeaId === depId) && "stroke-primary/50"
                      )}
                    />
                  );
                })}
                
                {/* Item card */}
                <motion.div
                  layoutId={`timeline-${item.idea.id}`}
                  className="absolute"
                  style={{ left: `${item.x}px`, top: `${item.y}px`, width: `${item.width}px` }}
                  onClick={() => handleIdeaClick(item.idea.id)}
                  onMouseEnter={() => setHoveredIdeaId(item.idea.id)}
                  onMouseLeave={() => setHoveredIdeaId(null)}
                  whileHover={{ y: -5 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <Card
                    className={cn(
                      "cursor-pointer hover:border-primary/50 transition-colors",
                      isSelected && "border-primary"
                    )}
                  >
                    <CardContent className="p-3">
                      <p className="text-sm">{item.idea.content}</p>
                      <div className="flex justify-between items-center mt-2">
                        <Badge variant={statusVariant} className="text-xs">
                          {formatIdeaStatus(status)}
                        </Badge>
                        
                        {item.idea.createdAt && (
                          <span className="text-xs text-muted-foreground">
                            {new Date(item.idea.createdAt).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </React.Fragment>
            );
          })}
          
          {/* Today marker */}
          <div 
            className="absolute top-0 h-full border-l-2 border-primary"
            style={{
              left: `${Math.floor((new Date().getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) * timeScale}px`,
            }}
          >
            <div className="absolute -left-8 -top-8 text-xs text-primary font-medium">
              Today
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};