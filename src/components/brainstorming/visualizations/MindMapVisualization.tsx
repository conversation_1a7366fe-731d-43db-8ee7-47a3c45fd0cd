/**
 * Mind Map Visualization Component
 * 
 * This component visualizes ideas as a mind map with nodes and connections.
 * It uses SVG for rendering and supports interactive features like zooming,
 * panning, and selecting nodes.
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Idea, IdeaStatus } from '@/types/brainstorm';
import { cn } from '@/lib/utils';
import { formatIdeaStatus, getStatusVariant } from '@/components/brainstorming/utils/idea-utils';
import { Badge } from '@/components/ui/badge';

interface MindMapVisualizationProps {
  ideas: Idea[];
  selectedIdeaIds: string[];
  onIdeaSelect: (ideaId: string) => void;
  className?: string;
}

interface NodePosition {
  x: number;
  y: number;
}

interface MindMapNode extends NodePosition {
  id: string;
  idea: Idea;
  radius: number;
}

interface MindMapEdge {
  source: string;
  target: string;
  sourcePosition: NodePosition;
  targetPosition: NodePosition;
}

export const MindMapVisualization: React.FC<MindMapVisualizationProps> = ({
  ideas,
  selectedIdeaIds,
  onIdeaSelect,
  className,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [nodes, setNodes] = useState<MindMapNode[]>([]);
  const [edges, setEdges] = useState<MindMapEdge[]>([]);
  const [scale, setScale] = useState(1);
  const [offset, setOffset] = useState({ x: 0, y: 0 });
  const [dragging, setDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  // Calculate node positions using a force-directed layout algorithm
  useEffect(() => {
    if (ideas.length === 0) return;

    // Simple circular layout for demonstration
    const centerX = 400;
    const centerY = 300;
    const radius = 200;
    
    // Create nodes
    const newNodes: MindMapNode[] = ideas.map((idea, index) => {
      const angle = (index / ideas.length) * 2 * Math.PI;
      return {
        id: idea.id,
        idea,
        x: centerX + radius * Math.cos(angle),
        y: centerY + radius * Math.sin(angle),
        radius: 40, // Base node size
      };
    });

    // Create edges based on idea connections
    const newEdges: MindMapEdge[] = [];
    ideas.forEach(idea => {
      if (idea.connections && idea.connections.length > 0) {
        idea.connections.forEach(connectionId => {
          const sourceNode = newNodes.find(node => node.id === idea.id);
          const targetNode = newNodes.find(node => node.id === connectionId);
          
          if (sourceNode && targetNode) {
            newEdges.push({
              source: sourceNode.id,
              target: targetNode.id,
              sourcePosition: { x: sourceNode.x, y: sourceNode.y },
              targetPosition: { x: targetNode.x, y: targetNode.y },
            });
          }
        });
      }
    });

    setNodes(newNodes);
    setEdges(newEdges);
  }, [ideas]);

  // Handle zoom with mouse wheel
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setScale(prevScale => Math.min(Math.max(prevScale * delta, 0.5), 2));
  };

  // Handle pan with mouse drag
  const handleMouseDown = (e: React.MouseEvent) => {
    setDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!dragging) return;
    
    const dx = e.clientX - dragStart.x;
    const dy = e.clientY - dragStart.y;
    
    setOffset(prev => ({ x: prev.x + dx, y: prev.y + dy }));
    setDragStart({ x: e.clientX, y: e.clientY });
  };

  const handleMouseUp = () => {
    setDragging(false);
  };

  // Handle node click
  const handleNodeClick = (ideaId: string) => {
    onIdeaSelect(ideaId);
  };

  return (
    <div 
      ref={containerRef}
      className={cn("relative overflow-hidden h-full w-full", className)}
      onWheel={handleWheel}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
    >
      <svg className="w-full h-full">
        <g
          transform={`translate(${offset.x}, ${offset.y}) scale(${scale})`}
        >
          {/* Render edges */}
          {edges.map((edge) => (
            <line
              key={`${edge.source}-${edge.target}`}
              x1={edge.sourcePosition.x}
              y1={edge.sourcePosition.y}
              x2={edge.targetPosition.x}
              y2={edge.targetPosition.y}
              stroke="currentColor"
              strokeWidth={2}
              className="stroke-muted-foreground/50"
            />
          ))}
          
          {/* Render nodes */}
          {nodes.map((node) => {
            const isSelected = selectedIdeaIds.includes(node.id);
            const status = node.idea.status || IdeaStatus.TO_EXPLORE;
            const statusVariant = getStatusVariant(status);
            
            return (
              <g 
                key={node.id} 
                transform={`translate(${node.x}, ${node.y})`}
                onClick={() => handleNodeClick(node.id)}
                className="cursor-pointer"
              >
                <motion.circle
                  r={node.radius}
                  className={cn(
                    "fill-card stroke-2",
                    isSelected ? "stroke-primary" : "stroke-border"
                  )}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                />
                
                <foreignObject
                  x={-node.radius + 5}
                  y={-node.radius + 5}
                  width={node.radius * 2 - 10}
                  height={node.radius * 2 - 10}
                  className="overflow-hidden text-center flex flex-col items-center justify-center"
                >
                  <div className="text-xs font-medium truncate">
                    {node.idea.content.length > 20
                      ? `${node.idea.content.substring(0, 20)}...`
                      : node.idea.content}
                  </div>
                  <Badge variant={statusVariant} className="mt-1 text-[0.6rem] h-4">
                    {formatIdeaStatus(status)}
                  </Badge>
                </foreignObject>
              </g>
            );
          })}
        </g>
      </svg>
    </div>
  );
};