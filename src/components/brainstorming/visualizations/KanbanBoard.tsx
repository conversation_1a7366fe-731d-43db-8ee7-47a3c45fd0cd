/**
 * Kanban Board Component
 * 
 * This component visualizes ideas as a kanban board with columns for different
 * idea statuses. It supports drag-and-drop functionality for moving ideas
 * between statuses.
 */

import React, { useState, useEffect } from 'react';
import { Idea, IdeaStatus } from '@/types/brainstorm';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatIdeaStatus, getStatusVariant } from '@/components/brainstorming/utils/idea-utils';
import { motion } from 'framer-motion';

interface KanbanBoardProps {
  ideas: Idea[];
  selectedIdeaIds: string[];
  onIdeaSelect: (ideaId: string) => void;
  className?: string;
}

interface KanbanColumn {
  status: IdeaStatus;
  title: string;
  variant: "default" | "destructive" | "outline" | "secondary";
  ideas: Idea[];
}

export const KanbanBoard: React.FC<KanbanBoardProps> = ({
  ideas,
  selectedIdeaIds,
  onIdeaSelect,
  className,
}) => {
  const [columns, setColumns] = useState<KanbanColumn[]>([]);
  const [draggingIdeaId, setDraggingIdeaId] = useState<string | null>(null);
  const [dragOverColumnStatus, setDragOverColumnStatus] = useState<IdeaStatus | null>(null);

  // Initialize columns based on idea statuses
  useEffect(() => {
    const statusColumns: KanbanColumn[] = [
      {
        status: IdeaStatus.TO_EXPLORE,
        title: formatIdeaStatus(IdeaStatus.TO_EXPLORE),
        variant: getStatusVariant(IdeaStatus.TO_EXPLORE),
        ideas: ideas.filter(idea => idea.status === IdeaStatus.TO_EXPLORE),
      },
      {
        status: IdeaStatus.IN_PROGRESS,
        title: formatIdeaStatus(IdeaStatus.IN_PROGRESS),
        variant: getStatusVariant(IdeaStatus.IN_PROGRESS),
        ideas: ideas.filter(idea => idea.status === IdeaStatus.IN_PROGRESS),
      },
      {
        status: IdeaStatus.VALIDATED,
        title: formatIdeaStatus(IdeaStatus.VALIDATED),
        variant: getStatusVariant(IdeaStatus.VALIDATED),
        ideas: ideas.filter(idea => idea.status === IdeaStatus.VALIDATED),
      },
      {
        status: IdeaStatus.ARCHIVED,
        title: formatIdeaStatus(IdeaStatus.ARCHIVED),
        variant: getStatusVariant(IdeaStatus.ARCHIVED),
        ideas: ideas.filter(idea => idea.status === IdeaStatus.ARCHIVED),
      },
    ];

    setColumns(statusColumns);
  }, [ideas]);

  // Handle drag start
  const handleDragStart = (ideaId: string) => {
    setDraggingIdeaId(ideaId);
  };

  // Handle drag over column
  const handleDragOver = (e: React.DragEvent, status: IdeaStatus) => {
    e.preventDefault();
    setDragOverColumnStatus(status);
  };

  // Handle drag end
  const handleDragEnd = () => {
    setDraggingIdeaId(null);
    setDragOverColumnStatus(null);
  };

  // Handle drop on column
  const handleDrop = (e: React.DragEvent, status: IdeaStatus) => {
    e.preventDefault();
    
    if (draggingIdeaId) {
      // In a real implementation, this would update the idea status in the store
      console.log(`Move idea ${draggingIdeaId} to status ${status}`);
    }
    
    setDraggingIdeaId(null);
    setDragOverColumnStatus(null);
  };

  // Handle idea click
  const handleIdeaClick = (ideaId: string) => {
    onIdeaSelect(ideaId);
  };

  return (
    <div className={cn("flex gap-4 overflow-x-auto pb-4 h-full", className)}>
      {columns.map((column) => (
        <div
          key={column.status}
          className={cn(
            "flex-shrink-0 w-72 flex flex-col h-full",
            dragOverColumnStatus === column.status && "bg-muted/50 rounded-lg"
          )}
          onDragOver={(e) => handleDragOver(e, column.status)}
          onDrop={(e) => handleDrop(e, column.status)}
        >
          <div className="flex items-center justify-between mb-2">
            <Badge variant={column.variant} className="px-2 py-1">
              {column.title}
            </Badge>
            <span className="text-xs text-muted-foreground">
              {column.ideas.length}
            </span>
          </div>
          
          <div className="flex-1 bg-muted/30 rounded-lg p-2 overflow-y-auto">
            {column.ideas.map((idea) => {
              const isSelected = selectedIdeaIds.includes(idea.id);
              
              return (
                <motion.div
                  key={idea.id}
                  layoutId={idea.id}
                  draggable
                  onDragStart={() => handleDragStart(idea.id)}
                  onDragEnd={handleDragEnd}
                  onClick={() => handleIdeaClick(idea.id)}
                  className="mb-2 last:mb-0"
                >
                  <Card
                    className={cn(
                      "cursor-pointer hover:border-primary/50 transition-colors",
                      isSelected && "border-primary"
                    )}
                  >
                    <CardContent className="p-3">
                      <p className="text-sm">{idea.content}</p>
                      {idea.tags && idea.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {idea.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
            {column.ideas.length === 0 && (
              <div className="flex items-center justify-center h-20 text-muted-foreground text-sm">
                No ideas
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};