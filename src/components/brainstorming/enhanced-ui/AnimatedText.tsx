import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface AnimatedGradientTextProps {
  children: React.ReactNode;
  className?: string;
  colors?: string[];
  animationSpeed?: number;
  showAnimation?: boolean;
}

export const AnimatedGradientText: React.FC<AnimatedGradientTextProps> = ({
  children,
  className,
  colors = ['#8b5cf6', '#06b6d4', '#10b981', '#f59e0b'],
  animationSpeed = 8,
  showAnimation = true
}) => {
  const gradientStyle = {
    background: `linear-gradient(-45deg, ${colors.join(', ')})`,
    backgroundSize: '400% 400%',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    animation: showAnimation ? `gradient ${animationSpeed}s ease infinite` : 'none'
  };

  return (
    <>
      <span
        className={cn('font-bold', className)}
        style={gradientStyle}
      >
        {children}
      </span>
      <style jsx>{`
        @keyframes gradient {
          0% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
          100% {
            background-position: 0% 50%;
          }
        }
      `}</style>
    </>
  );
};

interface TypingAnimationProps {
  text: string;
  className?: string;
  duration?: number;
  delay?: number;
  onComplete?: () => void;
}

export const TypingAnimation: React.FC<TypingAnimationProps> = ({
  text,
  className,
  duration = 2000,
  delay = 0,
  onComplete
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayedText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, duration / text.length + delay);

      return () => clearTimeout(timeout);
    } else if (onComplete) {
      onComplete();
    }
  }, [currentIndex, text, duration, delay, onComplete]);

  return (
    <span className={className}>
      {displayedText}
      <motion.span
        animate={{ opacity: [1, 0] }}
        transition={{ duration: 0.8, repeat: Infinity, repeatType: 'reverse' }}
        className="inline-block w-0.5 h-5 bg-current ml-1"
      />
    </span>
  );
};

interface TextRevealProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  duration?: number;
}

export const TextReveal: React.FC<TextRevealProps> = ({
  children,
  className,
  delay = 0,
  duration = 0.8
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration, delay }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

interface WordRotateProps {
  words: string[];
  className?: string;
  duration?: number;
}

export const WordRotate: React.FC<WordRotateProps> = ({
  words,
  className,
  duration = 2500
}) => {
  const [currentWordIndex, setCurrentWordIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentWordIndex((prev) => (prev + 1) % words.length);
    }, duration);

    return () => clearInterval(interval);
  }, [words.length, duration]);

  return (
    <div className={cn('relative inline-block', className)}>
      <AnimatePresence mode="wait">
        <motion.span
          key={currentWordIndex}
          initial={{ opacity: 0, y: 20, rotateX: 90 }}
          animate={{ opacity: 1, y: 0, rotateX: 0 }}
          exit={{ opacity: 0, y: -20, rotateX: -90 }}
          transition={{ duration: 0.3 }}
          className="inline-block"
        >
          {words[currentWordIndex]}
        </motion.span>
      </AnimatePresence>
    </div>
  );
};

interface FlipTextProps {
  children: React.ReactNode;
  className?: string;
  duration?: number;
  delay?: number;
}

export const FlipText: React.FC<FlipTextProps> = ({
  children,
  className,
  duration = 0.5,
  delay = 0
}) => {
  const text = children?.toString() || '';
  const letters = text.split('');

  return (
    <motion.div className={cn('inline-block', className)}>
      {letters.map((letter, index) => (
        <motion.span
          key={index}
          initial={{ rotateY: 90, opacity: 0 }}
          animate={{ rotateY: 0, opacity: 1 }}
          transition={{
            duration,
            delay: delay + index * 0.1,
            type: 'spring',
            stiffness: 100
          }}
          className="inline-block"
          style={{ transformOrigin: 'center' }}
        >
          {letter === ' ' ? '\u00A0' : letter}
        </motion.span>
      ))}
    </motion.div>
  );
};

interface SparklesTextProps {
  children: React.ReactNode;
  className?: string;
  sparklesCount?: number;
}

export const SparklesText: React.FC<SparklesTextProps> = ({
  children,
  className,
  sparklesCount = 3
}) => {
  const sparkles = Array.from({ length: sparklesCount }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    delay: Math.random() * 2
  }));

  return (
    <div className={cn('relative inline-block', className)}>
      {children}
      {sparkles.map((sparkle) => (
        <motion.div
          key={sparkle.id}
          className="absolute w-1 h-1 bg-yellow-400 rounded-full"
          style={{
            left: `${sparkle.x}%`,
            top: `${sparkle.y}%`
          }}
          animate={{
            scale: [0, 1, 0],
            opacity: [0, 1, 0]
          }}
          transition={{
            duration: 1.5,
            delay: sparkle.delay,
            repeat: Infinity,
            repeatDelay: 2
          }}
        />
      ))}
    </div>
  );
};

interface MorphingTextProps {
  texts: string[];
  className?: string;
  duration?: number;
}

export const MorphingText: React.FC<MorphingTextProps> = ({
  texts,
  className,
  duration = 3000
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % texts.length);
    }, duration);

    return () => clearInterval(interval);
  }, [texts.length, duration]);

  return (
    <div className={cn('relative', className)}>
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          initial={{ 
            opacity: 0,
            filter: 'blur(10px)',
            scale: 0.8
          }}
          animate={{ 
            opacity: 1,
            filter: 'blur(0px)',
            scale: 1
          }}
          exit={{ 
            opacity: 0,
            filter: 'blur(10px)',
            scale: 1.2
          }}
          transition={{ duration: 0.5 }}
        >
          {texts[currentIndex]}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

// Specialized text components for brainstorming
export const BrainstormingTitle: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <AnimatedGradientText
      className={cn('text-4xl font-bold', className)}
      colors={['#8b5cf6', '#06b6d4', '#10b981']}
    >
      {children}
    </AnimatedGradientText>
  );
};

export const IdeaLabel: React.FC<{
  children: React.ReactNode;
  className?: string;
  animated?: boolean;
}> = ({ children, className, animated = true }) => {
  if (animated) {
    return (
      <TextReveal className={cn('text-lg font-semibold', className)}>
        {children}
      </TextReveal>
    );
  }
  
  return (
    <span className={cn('text-lg font-semibold', className)}>
      {children}
    </span>
  );
};

export const StatusText: React.FC<{
  status: 'active' | 'completed' | 'pending';
  className?: string;
}> = ({ status, className }) => {
  const statusConfig = {
    active: { text: 'Active', colors: ['#10b981', '#34d399'] },
    completed: { text: 'Completed', colors: ['#8b5cf6', '#a78bfa'] },
    pending: { text: 'Pending', colors: ['#f59e0b', '#fbbf24'] }
  };

  const config = statusConfig[status];

  return (
    <AnimatedGradientText
      className={cn('text-sm font-medium', className)}
      colors={config.colors}
    >
      {config.text}
    </AnimatedGradientText>
  );
};