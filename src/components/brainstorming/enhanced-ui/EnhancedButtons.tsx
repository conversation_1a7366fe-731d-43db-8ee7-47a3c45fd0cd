import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Button, ButtonProps } from '@/components/ui/button';

interface ShimmerButtonProps extends ButtonProps {
  shimmerColor?: string;
  shimmerSize?: string;
  borderRadius?: string;
  shimmerDuration?: string;
  background?: string;
  className?: string;
  children: React.ReactNode;
}

export const ShimmerButton: React.FC<ShimmerButtonProps> = ({
  shimmerColor = '#ffffff',
  shimmerSize = '100px',
  borderRadius = '8px',
  shimmerDuration = '3s',
  background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  className,
  children,
  ...props
}) => {
  return (
    <Button
      className={cn(
        'relative overflow-hidden transition-all duration-300 hover:scale-105 active:scale-95',
        className
      )}
      style={{
        background,
        borderRadius,
      }}
      {...props}
    >
      <span className="relative z-10">{children}</span>
      <div
        className="absolute inset-0 -top-1/2 -left-1/2 w-full h-full opacity-30"
        style={{
          background: `linear-gradient(45deg, transparent 30%, ${shimmerColor} 50%, transparent 70%)`,
          width: shimmerSize,
          height: shimmerSize,
          animation: `shimmer ${shimmerDuration} infinite linear`,
        }}
      />
      <style jsx>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%) translateY(-100%) rotate(45deg);
          }
          100% {
            transform: translateX(200%) translateY(200%) rotate(45deg);
          }
        }
      `}</style>
    </Button>
  );
};

interface RainbowButtonProps extends ButtonProps {
  className?: string;
  children: React.ReactNode;
}

export const RainbowButton: React.FC<RainbowButtonProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <Button
      className={cn(
        'relative overflow-hidden bg-gradient-to-r from-purple-500 via-blue-500 via-green-500 via-yellow-500 via-orange-500 to-red-500',
        'bg-[length:200%_100%] animate-gradient-x',
        'hover:scale-105 active:scale-95 transition-all duration-300',
        'text-white font-semibold',
        className
      )}
      {...props}
    >
      <span className="relative z-10">{children}</span>
    </Button>
  );
};

interface InteractiveHoverButtonProps extends ButtonProps {
  className?: string;
  children: React.ReactNode;
  hoverEffect?: 'glow' | 'lift' | 'ripple' | 'magnetic';
}

export const InteractiveHoverButton: React.FC<InteractiveHoverButtonProps> = ({
  className,
  children,
  hoverEffect = 'glow',
  ...props
}) => {
  const [isHovered, setIsHovered] = React.useState(false);
  const [mousePosition, setMousePosition] = React.useState({ x: 0, y: 0 });

  const handleMouseMove = (e: React.MouseEvent<HTMLButtonElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    setMousePosition({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    });
  };

  const hoverEffects = {
    glow: 'hover:shadow-lg hover:shadow-purple-500/25',
    lift: 'hover:-translate-y-1 hover:shadow-xl',
    ripple: 'relative overflow-hidden',
    magnetic: 'transition-transform duration-300'
  };

  return (
    <motion.div
      whileHover={{ scale: hoverEffect === 'magnetic' ? 1.05 : 1 }}
      whileTap={{ scale: 0.95 }}
    >
      <Button
        className={cn(
          'relative transition-all duration-300',
          hoverEffects[hoverEffect],
          className
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onMouseMove={handleMouseMove}
        {...props}
      >
        {hoverEffect === 'ripple' && isHovered && (
          <motion.div
            className="absolute inset-0 bg-white/20 rounded-full"
            initial={{ scale: 0, opacity: 1 }}
            animate={{ scale: 4, opacity: 0 }}
            transition={{ duration: 0.6 }}
            style={{
              left: mousePosition.x - 10,
              top: mousePosition.y - 10,
              width: 20,
              height: 20,
            }}
          />
        )}
        <span className="relative z-10">{children}</span>
      </Button>
    </motion.div>
  );
};

interface PulsatingButtonProps extends ButtonProps {
  className?: string;
  children: React.ReactNode;
  pulseColor?: string;
  pulseDuration?: number;
}

export const PulsatingButton: React.FC<PulsatingButtonProps> = ({
  className,
  children,
  pulseColor = 'rgb(139, 92, 246)',
  pulseDuration = 2,
  ...props
}) => {
  return (
    <div className="relative">
      <div
        className="absolute inset-0 rounded-lg animate-pulse"
        style={{
          background: pulseColor,
          opacity: 0.3,
          animation: `pulse ${pulseDuration}s cubic-bezier(0.4, 0, 0.6, 1) infinite`,
        }}
      />
      <Button
        className={cn(
          'relative z-10 transition-all duration-300 hover:scale-105',
          className
        )}
        {...props}
      >
        {children}
      </Button>
    </div>
  );
};

// Specialized brainstorming buttons
export const CreateSessionButton: React.FC<{
  onClick: () => void;
  className?: string;
}> = ({ onClick, className }) => {
  return (
    <ShimmerButton
      onClick={onClick}
      className={cn('px-6 py-3 text-white font-semibold', className)}
      background="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
    >
      Create New Session
    </ShimmerButton>
  );
};

export const IdeaActionButton: React.FC<{
  children: React.ReactNode;
  onClick: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  className?: string;
}> = ({ children, onClick, variant = 'primary', className }) => {
  const variants = {
    primary: 'bg-gradient-to-r from-purple-500 to-blue-500',
    secondary: 'bg-gradient-to-r from-gray-500 to-gray-600',
    danger: 'bg-gradient-to-r from-red-500 to-pink-500'
  };

  return (
    <InteractiveHoverButton
      onClick={onClick}
      hoverEffect="glow"
      className={cn(
        'px-4 py-2 text-white font-medium rounded-lg',
        variants[variant],
        className
      )}
    >
      {children}
    </InteractiveHoverButton>
  );
};

export const FloatingActionButton: React.FC<{
  children: React.ReactNode;
  onClick: () => void;
  className?: string;
}> = ({ children, onClick, className }) => {
  return (
    <motion.div
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      className={cn(
        'fixed bottom-6 right-6 z-50',
        className
      )}
    >
      <Button
        onClick={onClick}
        className="w-14 h-14 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 shadow-lg hover:shadow-xl transition-all duration-300"
      >
        {children}
      </Button>
    </motion.div>
  );
};