import React from 'react';
import { cn } from '@/lib/utils';

interface WarpBackgroundProps {
  children: React.ReactNode;
  className?: string;
  intensity?: 'low' | 'medium' | 'high';
  color?: 'purple' | 'blue' | 'green' | 'orange' | 'pink';
}

export const WarpBackground: React.FC<WarpBackgroundProps> = ({
  children,
  className,
  intensity = 'medium',
  color = 'purple'
}) => {
  const intensityClasses = {
    low: 'opacity-20',
    medium: 'opacity-30',
    high: 'opacity-40'
  };

  const colorClasses = {
    purple: 'from-purple-500/20 via-purple-600/10 to-purple-700/20',
    blue: 'from-blue-500/20 via-blue-600/10 to-blue-700/20',
    green: 'from-green-500/20 via-green-600/10 to-green-700/20',
    orange: 'from-orange-500/20 via-orange-600/10 to-orange-700/20',
    pink: 'from-pink-500/20 via-pink-600/10 to-pink-700/20'
  };

  return (
    <div className={cn('relative overflow-hidden', className)}>
      {/* Animated Background Grid */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]" />
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse" />
      </div>
      
      {/* Warp Effect Beams */}
      <div className={cn('absolute inset-0', intensityClasses[intensity])}>
        {/* Diagonal Beams */}
        <div className={cn(
          'absolute top-0 left-0 w-full h-full bg-gradient-to-br',
          colorClasses[color],
          'animate-pulse'
        )} />
        
        {/* Moving Light Beams */}
        <div className="absolute inset-0">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className={cn(
                'absolute h-px w-full bg-gradient-to-r from-transparent via-white/20 to-transparent',
                'animate-pulse'
              )}
              style={{
                top: `${(i + 1) * 16.66}%`,
                animationDelay: `${i * 0.5}s`,
                animationDuration: '3s'
              }}
            />
          ))}
        </div>
        
        {/* Vertical Beams */}
        <div className="absolute inset-0">
          {[...Array(4)].map((_, i) => (
            <div
              key={i}
              className={cn(
                'absolute w-px h-full bg-gradient-to-b from-transparent via-white/20 to-transparent',
                'animate-pulse'
              )}
              style={{
                left: `${(i + 1) * 25}%`,
                animationDelay: `${i * 0.7}s`,
                animationDuration: '4s'
              }}
            />
          ))}
        </div>
      </div>
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};