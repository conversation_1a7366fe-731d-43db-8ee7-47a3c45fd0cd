import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface GlassmorphismCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'elevated' | 'floating' | 'glow';
  blur?: 'sm' | 'md' | 'lg' | 'xl';
  border?: boolean;
  glow?: boolean;
  interactive?: boolean;
  onClick?: () => void;
}

export const GlassmorphismCard: React.FC<GlassmorphismCardProps> = ({
  children,
  className,
  variant = 'default',
  blur = 'md',
  border = true,
  glow = false,
  interactive = false,
  onClick
}) => {
  const blurClasses = {
    sm: 'backdrop-blur-sm',
    md: 'backdrop-blur-md',
    lg: 'backdrop-blur-lg',
    xl: 'backdrop-blur-xl'
  };

  const variantClasses = {
    default: 'bg-white/10 shadow-lg',
    elevated: 'bg-white/15 shadow-xl',
    floating: 'bg-white/20 shadow-2xl',
    glow: 'bg-white/10 shadow-lg shadow-purple-500/20'
  };

  const borderClasses = border ? 'border border-white/20' : '';
  const glowClasses = glow ? 'shadow-lg shadow-purple-500/25' : '';
  
  const interactiveClasses = interactive ? [
    'cursor-pointer',
    'transition-all duration-300',
    'hover:bg-white/20',
    'hover:shadow-xl',
    'hover:scale-[1.02]',
    'active:scale-[0.98]'
  ].join(' ') : '';

  const Component = interactive ? motion.div : 'div';
  const motionProps = interactive ? {
    whileHover: { scale: 1.02 },
    whileTap: { scale: 0.98 },
    transition: { type: 'spring', stiffness: 300, damping: 20 }
  } : {};

  return (
    <Component
      className={cn(
        'rounded-xl',
        blurClasses[blur],
        variantClasses[variant],
        borderClasses,
        glowClasses,
        interactiveClasses,
        className
      )}
      onClick={onClick}
      {...motionProps}
    >
      {/* Inner glow effect */}
      {glow && (
        <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500/10 via-blue-500/10 to-pink-500/10 opacity-50" />
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </Component>
  );
};

// Specialized variants
export const GlassStatsCard: React.FC<{
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: 'up' | 'down' | 'neutral';
  className?: string;
}> = ({ title, value, icon, trend, className }) => {
  const trendColors = {
    up: 'text-green-400',
    down: 'text-red-400',
    neutral: 'text-gray-400'
  };

  return (
    <GlassmorphismCard 
      variant="elevated" 
      glow 
      interactive
      className={cn('p-6', className)}
    >
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <p className="text-white/70 text-sm font-medium">{title}</p>
          <p className="text-2xl font-bold text-white">{value}</p>
        </div>
        <div className={cn('p-3 rounded-lg bg-white/10', trend && trendColors[trend])}>
          {icon}
        </div>
      </div>
    </GlassmorphismCard>
  );
};

export const GlassActionCard: React.FC<{
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick: () => void;
  className?: string;
}> = ({ title, description, icon, onClick, className }) => {
  return (
    <GlassmorphismCard 
      variant="floating" 
      interactive
      onClick={onClick}
      className={cn('p-6 group', className)}
    >
      <div className="flex items-start space-x-4">
        <div className="p-3 rounded-lg bg-gradient-to-r from-purple-500/20 to-blue-500/20 group-hover:from-purple-500/30 group-hover:to-blue-500/30 transition-all duration-300">
          {icon}
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-white mb-2">{title}</h3>
          <p className="text-white/70 text-sm">{description}</p>
        </div>
      </div>
    </GlassmorphismCard>
  );
};