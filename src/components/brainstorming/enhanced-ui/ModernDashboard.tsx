import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  Lightbulb,
  Users,
  TrendingUp,
  Plus,
  Search,
  Filter,
  Grid3x3,
  List,
  Calendar,
  Clock,
  Star,
  Target,
  Zap,
  Activity,
  BarChart3
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { WarpBackground } from './WarpBackground';
import { GlassmorphismCard, GlassStatsCard, GlassActionCard } from './GlassmorphismCard';
import { AnimatedGradientText, BrainstormingTitle, TextReveal } from './AnimatedText';
import { CreateSessionButton, InteractiveHoverButton } from './EnhancedButtons';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BrainstormSession, TemplateType } from '@/types/brainstorm';

interface ModeStats {
  totalSessions: number;
  totalIdeas: number;
  activeCollaborations: number;
  recentActivity: string;
}

interface ModernDashboardProps {
  stats: ModeStats;
  sessions: Record<string, BrainstormSession>;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filterTemplate: TemplateType | 'all';
  setFilterTemplate: (template: TemplateType | 'all') => void;
  viewMode: 'grid' | 'list';
  setViewMode: (mode: 'grid' | 'list') => void;
  onCreateSession: () => void;
  onSessionSelect: (session: BrainstormSession) => void;
  className?: string;
}

const templateColors = {
  'creative-thinking': 'from-purple-500 to-pink-500',
  'problem-solving': 'from-blue-500 to-cyan-500',
  'product-development': 'from-green-500 to-teal-500',
  'strategic-planning': 'from-orange-500 to-red-500',
  'team-building': 'from-indigo-500 to-purple-500',
  'innovation': 'from-yellow-500 to-orange-500'
};

const quickActionCards = [
  {
    id: 'creative-session',
    title: 'Creative Thinking',
    description: 'Generate innovative ideas with AI-powered prompts',
    icon: Lightbulb,
    gradient: 'from-purple-500 to-pink-500'
  },
  {
    id: 'problem-solving',
    title: 'Problem Solving',
    description: 'Tackle complex challenges systematically',
    icon: Target,
    gradient: 'from-blue-500 to-cyan-500'
  },
  {
    id: 'collaboration',
    title: 'Team Collaboration',
    description: 'Brainstorm together in real-time',
    icon: Users,
    gradient: 'from-green-500 to-teal-500'
  },
  {
    id: 'innovation',
    title: 'Innovation Lab',
    description: 'Explore breakthrough concepts',
    icon: Zap,
    gradient: 'from-yellow-500 to-orange-500'
  }
];

export const ModernDashboard: React.FC<ModernDashboardProps> = ({
  stats,
  sessions,
  searchQuery,
  setSearchQuery,
  filterTemplate,
  setFilterTemplate,
  viewMode,
  setViewMode,
  onCreateSession,
  onSessionSelect,
  className
}) => {
  const [hoveredSession, setHoveredSession] = useState<string | null>(null);
  const filteredSessions = Object.values(sessions).filter(session => {
    const matchesSearch = !searchQuery ||
      session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesTemplate = filterTemplate === 'all' || session.template === filterTemplate;

    return matchesSearch && matchesTemplate;
  });

  return (
    <WarpBackground className={cn('min-h-screen', className)} intensity="medium" color="purple">
      <div className="p-6 space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-4"
        >
          <BrainstormingTitle className="text-5xl">
            Brainstorming Dashboard
          </BrainstormingTitle>
          <TextReveal delay={0.2}>
            <p className="text-xl text-white/70 max-w-2xl mx-auto">
              Transform your ideas into reality with AI-powered brainstorming sessions
            </p>
          </TextReveal>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          <GlassStatsCard
            title="Total Sessions"
            value={stats.totalSessions}
            icon={<Brain className="w-6 h-6" />}
            trend="up"
          />
          <GlassStatsCard
            title="Ideas Generated"
            value={stats.totalIdeas}
            icon={<Lightbulb className="w-6 h-6" />}
            trend="up"
          />
          <GlassStatsCard
            title="Active Sessions"
            value={stats.activeCollaborations}
            icon={<Users className="w-6 h-6" />}
            trend="neutral"
          />
          <GlassStatsCard
            title="Productivity"
            value={`${Math.round((stats.totalIdeas / Math.max(stats.totalSessions, 1)) * 10) / 10}/session`}
            icon={<TrendingUp className="w-6 h-6" />}
            trend="up"
          />
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="space-y-6"
        >
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white">Quick Start</h2>
            <CreateSessionButton onClick={onCreateSession} />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActionCards.map((card, index) => {
              const Icon = card.icon;
              return (
                <motion.div
                  key={card.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 + index * 0.1 }}
                >
                  <GlassActionCard
                    title={card.title}
                    description={card.description}
                    icon={<Icon className="w-6 h-6 text-white" />}
                    onClick={onCreateSession}
                  />
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Sessions Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-6"
        >
          {/* Section Header */}
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white">Recent Sessions</h2>
            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/50" />
                <Input
                  placeholder="Search sessions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/50 backdrop-blur-md"
                />
              </div>
              
              {/* Filter */}
              <select
                value={filterTemplate}
                onChange={(e) => setFilterTemplate(e.target.value as TemplateType | 'all')}
                className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white backdrop-blur-md"
              >
                <option value="all">All Templates</option>
                <option value="creative-thinking">Creative Thinking</option>
                <option value="problem-solving">Problem Solving</option>
                <option value="product-development">Product Development</option>
                <option value="strategic-planning">Strategic Planning</option>
                <option value="team-building">Team Building</option>
                <option value="innovation">Innovation</option>
              </select>
              
              {/* View Mode */}
              <div className="flex bg-white/10 rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className={cn(
                    'px-3 py-1',
                    viewMode === 'grid' && 'bg-white/20'
                  )}
                >
                  <Grid3x3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className={cn(
                    'px-3 py-1',
                    viewMode === 'list' && 'bg-white/20'
                  )}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Sessions Grid/List */}
          <AnimatePresence mode="wait">
            {filteredSessions.length === 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="text-center py-12"
              >
                <GlassmorphismCard className="p-8 max-w-md mx-auto">
                  <Brain className="w-12 h-12 mx-auto mb-4 text-white/50" />
                  <h3 className="text-lg font-semibold text-white mb-2">
                    No sessions found
                  </h3>
                  <p className="text-white/70 mb-4">
                    {searchQuery || filterTemplate !== 'all'
                      ? 'Try adjusting your search or filter criteria'
                      : 'Create your first brainstorming session to get started'}
                  </p>
                  <CreateSessionButton onClick={onCreateSession} />
                </GlassmorphismCard>
              </motion.div>
            ) : (
              <motion.div
                key={viewMode}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={cn(
                  'grid gap-6',
                  viewMode === 'grid'
                    ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                    : 'grid-cols-1'
                )}
              >
                {filteredSessions.map((session, index) => (
                  <motion.div
                    key={session.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    onMouseEnter={() => setHoveredSession(session.id)}
                    onMouseLeave={() => setHoveredSession(null)}
                  >
                    <GlassmorphismCard
                      variant="elevated"
                      interactive
                      onClick={() => onSessionSelect(session)}
                      className={cn(
                        'p-6 cursor-pointer transition-all duration-300',
                        hoveredSession === session.id && 'scale-105'
                      )}
                    >
                      <div className="space-y-4">
                        {/* Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-white mb-1">
                              {session.title}
                            </h3>
                            <p className="text-white/70 text-sm line-clamp-2">
                              {session.description}
                            </p>
                          </div>
                          <Badge
                            className={cn(
                              'ml-2 bg-gradient-to-r text-white border-0',
                              templateColors[session.template as keyof typeof templateColors] || 'from-gray-500 to-gray-600'
                            )}
                          >
                            {session.template}
                          </Badge>
                        </div>

                        {/* Stats */}
                        <div className="flex items-center space-x-4 text-sm text-white/60">
                          <div className="flex items-center space-x-1">
                            <Lightbulb className="w-4 h-4" />
                            <span>{session.ideas?.length || 0} ideas</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="w-4 h-4" />
                            <span>{new Date(session.updatedAt).toLocaleDateString()}</span>
                          </div>
                          {session.isActive && (
                            <div className="flex items-center space-x-1">
                              <Activity className="w-4 h-4 text-green-400" />
                              <span className="text-green-400">Active</span>
                            </div>
                          )}
                        </div>

                        {/* Tags */}
                        {session.tags.length > 0 && (
                          <div className="flex flex-wrap gap-2">
                            {session.tags.slice(0, 3).map((tag) => (
                              <Badge
                                key={tag}
                                variant="secondary"
                                className="text-xs bg-white/10 text-white/80 border-white/20"
                              >
                                {tag}
                              </Badge>
                            ))}
                            {session.tags.length > 3 && (
                              <Badge
                                variant="secondary"
                                className="text-xs bg-white/10 text-white/80 border-white/20"
                              >
                                +{session.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </GlassmorphismCard>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
    </WarpBackground>
  );
};