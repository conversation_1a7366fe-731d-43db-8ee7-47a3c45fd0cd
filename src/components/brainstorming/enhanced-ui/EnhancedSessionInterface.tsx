import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MessageSquare,
  Brain,
  Map,
  Kanban,
  Grid3x3,
  FileText,
  Users,
  Settings,
  Maximize2,
  Minimize2,
  Share2,
  Download,
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Sparkles,
  Zap,
  Target,
  Lightbulb,
  Plus,
  Filter,
  Search,
  Clock,
  Star,
  Bookmark
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { WarpBackground } from './WarpBackground';
import { GlassmorphismCard } from './GlassmorphismCard';
import { AnimatedBeam, IdeaConnectionBeam } from './AnimatedBeam';
import { InteractiveHoverButton, IdeaActionButton, FloatingActionButton } from './EnhancedButtons';
import { AnimatedGradientText, TypingAnimation, TextReveal } from './AnimatedText';
import { ModernNavigation } from './ModernNavigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { BrainstormSession, Idea, ViewType } from '@/types/brainstorm';

interface EnhancedSessionInterfaceProps {
  session: BrainstormSession;
  currentView: ViewType;
  onViewChange: (view: ViewType) => void;
  onIdeaAdd: (idea: Partial<Idea>) => void;
  onIdeaUpdate: (ideaId: string, updates: Partial<Idea>) => void;
  onIdeaDelete: (ideaId: string) => void;
  onSessionUpdate: (updates: Partial<BrainstormSession>) => void;
  isVoiceEnabled: boolean;
  onVoiceToggle: () => void;
  isSpeaking: boolean;
  onSpeakToggle: () => void;
  isFullscreen: boolean;
  onFullscreenToggle: () => void;
  className?: string;
}

const viewIcons = {
  chat: MessageSquare,
  'mind-map': Brain,
  kanban: Kanban,
  matrix: Grid3x3,
  templates: FileText
};

const viewLabels = {
  chat: 'Chat',
  'mind-map': 'Mind Map',
  kanban: 'Kanban',
  matrix: 'Matrix',
  templates: 'Templates'
};

const quickActions = [
  {
    id: 'add-idea',
    label: 'Add Idea',
    icon: Plus,
    color: 'from-blue-500 to-cyan-500'
  },
  {
    id: 'ai-suggest',
    label: 'AI Suggest',
    icon: Sparkles,
    color: 'from-purple-500 to-pink-500'
  },
  {
    id: 'categorize',
    label: 'Categorize',
    icon: Target,
    color: 'from-green-500 to-teal-500'
  },
  {
    id: 'connect',
    label: 'Connect Ideas',
    icon: Zap,
    color: 'from-yellow-500 to-orange-500'
  }
];

export const EnhancedSessionInterface: React.FC<EnhancedSessionInterfaceProps> = ({
  session,
  currentView,
  onViewChange,
  onIdeaAdd,
  onIdeaUpdate,
  onIdeaDelete,
  onSessionUpdate,
  isVoiceEnabled,
  onVoiceToggle,
  isSpeaking,
  onSpeakToggle,
  isFullscreen,
  onFullscreenToggle,
  className
}) => {
  const [newIdeaText, setNewIdeaText] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isAddingIdea, setIsAddingIdea] = useState(false);
  const [hoveredIdea, setHoveredIdea] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const filteredIdeas = session.ideas?.filter(idea => {
    const matchesSearch = !searchQuery ||
      idea.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      idea.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || idea.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  }) || [];

  const categories = Array.from(new Set(session.ideas?.map(idea => idea.category).filter(Boolean) || []));

  const handleAddIdea = () => {
    if (newIdeaText.trim()) {
      onIdeaAdd({
        content: newIdeaText,
        category: selectedCategory !== 'all' ? selectedCategory : undefined,
        tags: [],
        priority: 'medium'
      });
      setNewIdeaText('');
      setIsAddingIdea(false);
    }
  };

  const handleQuickAction = (actionId: string) => {
    switch (actionId) {
      case 'add-idea':
        setIsAddingIdea(true);
        break;
      case 'ai-suggest':
        // Trigger AI suggestion
        break;
      case 'categorize':
        // Auto-categorize ideas
        break;
      case 'connect':
        // Show connection mode
        break;
    }
  };

  return (
    <WarpBackground 
      className={cn('min-h-screen', className)} 
      intensity="low" 
      color="blue"
    >
      <div ref={containerRef} className="flex h-screen">
        {/* Navigation Sidebar */}
        <ModernNavigation
          currentView={currentView}
          onViewChange={onViewChange}
          session={session}
          className="w-64 flex-shrink-0"
        />

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <motion.header
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/5 backdrop-blur-md border-b border-white/10 p-4"
          >
            <div className="flex items-center justify-between">
              {/* Session Info */}
              <div className="flex items-center space-x-4">
                <div>
                  <AnimatedGradientText className="text-xl font-bold">
                    {session.title}
                  </AnimatedGradientText>
                  <p className="text-white/70 text-sm">
                    {session.description}
                  </p>
                </div>
                <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0">
                  {session.template}
                </Badge>
              </div>

              {/* Controls */}
              <div className="flex items-center space-x-2">
                {/* Voice Controls */}
                <InteractiveHoverButton
                  variant={isVoiceEnabled ? 'default' : 'outline'}
                  size="sm"
                  onClick={onVoiceToggle}
                  effect="glow"
                >
                  {isVoiceEnabled ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
                </InteractiveHoverButton>
                
                <InteractiveHoverButton
                  variant={isSpeaking ? 'default' : 'outline'}
                  size="sm"
                  onClick={onSpeakToggle}
                  effect="glow"
                >
                  {isSpeaking ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
                </InteractiveHoverButton>

                {/* Share */}
                <InteractiveHoverButton
                  variant="outline"
                  size="sm"
                  effect="lift"
                >
                  <Share2 className="w-4 h-4" />
                </InteractiveHoverButton>

                {/* Export */}
                <InteractiveHoverButton
                  variant="outline"
                  size="sm"
                  effect="lift"
                >
                  <Download className="w-4 h-4" />
                </InteractiveHoverButton>

                {/* Fullscreen */}
                <InteractiveHoverButton
                  variant="outline"
                  size="sm"
                  onClick={onFullscreenToggle}
                  effect="lift"
                >
                  {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
                </InteractiveHoverButton>
              </div>
            </div>

            {/* View Tabs */}
            <div className="flex items-center space-x-1 mt-4">
              {Object.entries(viewIcons).map(([view, Icon]) => (
                <Button
                  key={view}
                  variant={currentView === view ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => onViewChange(view as ViewType)}
                  className={cn(
                    'flex items-center space-x-2 px-4 py-2 transition-all duration-200',
                    currentView === view
                      ? 'bg-white/20 text-white'
                      : 'text-white/70 hover:text-white hover:bg-white/10'
                  )}
                >
                  <Icon className="w-4 h-4" />
                  <span>{viewLabels[view as keyof typeof viewLabels]}</span>
                </Button>
              ))}
            </div>
          </motion.header>

          {/* Content Area */}
          <div className="flex-1 flex overflow-hidden">
            {/* Main View */}
            <div className="flex-1 p-6 overflow-auto">
              <AnimatePresence mode="wait">
                {currentView === 'chat' && (
                  <motion.div
                    key="chat"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    className="space-y-6"
                  >
                    {/* Chat Interface */}
                    <GlassmorphismCard className="p-6">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-semibold text-white">AI Brainstorming Chat</h3>
                          <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                            Active
                          </Badge>
                        </div>
                        
                        {/* Chat Messages */}
                        <div className="h-96 bg-black/20 rounded-lg p-4 overflow-y-auto">
                          <div className="space-y-4">
                            <div className="flex items-start space-x-3">
                              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                                <Brain className="w-4 h-4 text-white" />
                              </div>
                              <div className="flex-1">
                                <TypingAnimation
                                  text="Welcome to your brainstorming session! I'm here to help you generate and develop ideas. What would you like to explore today?"
                                  className="text-white/90"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        {/* Chat Input */}
                        <div className="flex space-x-2">
                          <Input
                            placeholder="Type your message or idea..."
                            className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-white/50"
                          />
                          <InteractiveHoverButton effect="glow">
                            <MessageSquare className="w-4 h-4" />
                          </InteractiveHoverButton>
                        </div>
                      </div>
                    </GlassmorphismCard>
                  </motion.div>
                )}

                {currentView === 'mind-map' && (
                  <motion.div
                    key="mind-map"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    className="h-full"
                  >
                    {/* Mind Map View */}
                    <GlassmorphismCard className="h-full p-6">
                      <div className="flex items-center justify-between mb-6">
                        <h3 className="text-lg font-semibold text-white">Mind Map</h3>
                        <div className="flex space-x-2">
                          <InteractiveHoverButton size="sm" effect="lift">
                            <Plus className="w-4 h-4" />
                          </InteractiveHoverButton>
                          <InteractiveHoverButton size="sm" effect="lift">
                            <Zap className="w-4 h-4" />
                          </InteractiveHoverButton>
                        </div>
                      </div>
                      
                      {/* Mind Map Canvas */}
                      <div className="relative h-full bg-black/20 rounded-lg overflow-hidden">
                        <div className="absolute inset-0 flex items-center justify-center">
                          <TextReveal>
                            <p className="text-white/50 text-center">
                              Mind map visualization will be rendered here
                            </p>
                          </TextReveal>
                        </div>
                        
                        {/* Connection Beams */}
                        {filteredIdeas.map((idea, index) => (
                          <IdeaConnectionBeam
                            key={idea.id}
                            fromId={`idea-${index}`}
                            toId={`idea-${(index + 1) % filteredIdeas.length}`}
                            strength={0.8}
                          />
                        ))}
                      </div>
                    </GlassmorphismCard>
                  </motion.div>
                )}

                {currentView === 'kanban' && (
                  <motion.div
                    key="kanban"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    className="space-y-6"
                  >
                    {/* Kanban Board */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {['Ideas', 'In Progress', 'Completed'].map((column) => (
                        <GlassmorphismCard key={column} className="p-4">
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <h3 className="font-semibold text-white">{column}</h3>
                              <Badge className="bg-white/10 text-white/70 border-white/20">
                                {filteredIdeas.length}
                              </Badge>
                            </div>
                            
                            <div className="space-y-3">
                              {filteredIdeas.slice(0, 3).map((idea) => (
                                <motion.div
                                  key={idea.id}
                                  whileHover={{ scale: 1.02 }}
                                  className="p-3 bg-white/5 rounded-lg border border-white/10 cursor-pointer"
                                >
                                  <p className="text-white text-sm">{idea.content}</p>
                                  <div className="flex items-center justify-between mt-2">
                                    <div className="flex space-x-1">
                                      {idea.tags?.slice(0, 2).map((tag) => (
                                        <Badge
                                          key={tag}
                                          className="text-xs bg-white/10 text-white/70 border-white/20"
                                        >
                                          {tag}
                                        </Badge>
                                      ))}
                                    </div>
                                    <div className="flex space-x-1">
                                      <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                                        <Star className="w-3 h-3" />
                                      </Button>
                                      <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                                        <Bookmark className="w-3 h-3" />
                                      </Button>
                                    </div>
                                  </div>
                                </motion.div>
                              ))}
                            </div>
                            
                            <IdeaActionButton
                              onClick={() => setIsAddingIdea(true)}
                              className="w-full"
                            >
                              <Plus className="w-4 h-4 mr-2" />
                              Add Idea
                            </IdeaActionButton>
                          </div>
                        </GlassmorphismCard>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Sidebar */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="w-80 p-6 space-y-6 border-l border-white/10 bg-white/5 backdrop-blur-md"
            >
              {/* Quick Actions */}
              <GlassmorphismCard className="p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
                <div className="grid grid-cols-2 gap-3">
                  {quickActions.map((action) => {
                    const Icon = action.icon;
                    return (
                      <InteractiveHoverButton
                        key={action.id}
                        onClick={() => handleQuickAction(action.id)}
                        className="flex flex-col items-center space-y-2 p-3 h-auto"
                        effect="lift"
                      >
                        <Icon className="w-5 h-5" />
                        <span className="text-xs">{action.label}</span>
                      </InteractiveHoverButton>
                    );
                  })}
                </div>
              </GlassmorphismCard>

              {/* Ideas List */}
              <GlassmorphismCard className="p-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-white">Ideas</h3>
                    <Badge className="bg-white/10 text-white/70 border-white/20">
                      {filteredIdeas.length}
                    </Badge>
                  </div>
                  
                  {/* Search and Filter */}
                  <div className="space-y-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/50" />
                      <Input
                        placeholder="Search ideas..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/50"
                      />
                    </div>
                    
                    <select
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white"
                    >
                      <option value="all">All Categories</option>
                      {categories.map((category) => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  {/* Ideas List */}
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {filteredIdeas.map((idea) => (
                      <motion.div
                        key={idea.id}
                        whileHover={{ scale: 1.02 }}
                        className="p-3 bg-white/5 rounded-lg border border-white/10 cursor-pointer"
                        onMouseEnter={() => setHoveredIdea(idea.id)}
                        onMouseLeave={() => setHoveredIdea(null)}
                      >
                        <p className="text-white text-sm">{idea.content}</p>
                        <div className="flex items-center justify-between mt-2">
                          <div className="flex items-center space-x-1 text-xs text-white/60">
                            <Clock className="w-3 h-3" />
                            <span>{new Date(idea.createdAt).toLocaleDateString()}</span>
                          </div>
                          <div className="flex space-x-1">
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                              <Star className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </GlassmorphismCard>

              {/* Session Stats */}
              <GlassmorphismCard className="p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Session Stats</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Ideas Generated</span>
                    <span className="text-white font-semibold">{session.ideas?.length || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Duration</span>
                    <span className="text-white font-semibold">2h 15m</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">Participants</span>
                    <span className="text-white font-semibold">{session.participants?.length || 1}</span>
                  </div>
                </div>
              </GlassmorphismCard>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Floating Action Buttons */}
      <div className="fixed bottom-6 right-6 space-y-3">
        <FloatingActionButton
          onClick={() => setIsAddingIdea(true)}
          icon={<Plus className="w-5 h-5" />}
          label="Add Idea"
        />
        <FloatingActionButton
          onClick={() => handleQuickAction('ai-suggest')}
          icon={<Sparkles className="w-5 h-5" />}
          label="AI Suggest"
        />
      </div>

      {/* Add Idea Modal */}
      <AnimatePresence>
        {isAddingIdea && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
            onClick={() => setIsAddingIdea(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="w-full max-w-md"
            >
              <GlassmorphismCard className="p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Add New Idea</h3>
                <div className="space-y-4">
                  <Textarea
                    placeholder="Describe your idea..."
                    value={newIdeaText}
                    onChange={(e) => setNewIdeaText(e.target.value)}
                    className="bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    rows={4}
                  />
                  <div className="flex space-x-2">
                    <Button
                      onClick={handleAddIdea}
                      className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                    >
                      Add Idea
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setIsAddingIdea(false)}
                      className="border-white/20 text-white hover:bg-white/10"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </GlassmorphismCard>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </WarpBackground>
  );
};