import React, { useRef, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface AnimatedBeamProps {
  className?: string;
  containerRef: React.RefObject<HTMLElement>;
  fromRef: React.RefObject<HTMLElement>;
  toRef: React.RefObject<HTMLElement>;
  curvature?: number;
  reverse?: boolean;
  duration?: number;
  delay?: number;
  pathColor?: string;
  pathWidth?: number;
  pathOpacity?: number;
  gradientStartColor?: string;
  gradientStopColor?: string;
  startXOffset?: number;
  startYOffset?: number;
  endXOffset?: number;
  endYOffset?: number;
}

export const AnimatedBeam: React.FC<AnimatedBeamProps> = ({
  className,
  containerRef,
  fromRef,
  toRef,
  curvature = 0,
  reverse = false,
  duration = 3,
  delay = 0,
  pathColor = 'gray',
  pathWidth = 2,
  pathOpacity = 0.2,
  gradientStartColor = '#8b5cf6',
  gradientStopColor = '#06b6d4',
  startXOffset = 0,
  startYOffset = 0,
  endXOffset = 0,
  endYOffset = 0
}) => {
  const id = React.useId();
  const [pathD, setPathD] = useState('');
  const [svgDimensions, setSvgDimensions] = useState({ width: 0, height: 0 });

  const updatePath = React.useCallback(() => {
    if (containerRef.current && fromRef.current && toRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      const fromRect = fromRef.current.getBoundingClientRect();
      const toRect = toRef.current.getBoundingClientRect();

      const relativeFromX = fromRect.left - containerRect.left + fromRect.width / 2 + startXOffset;
      const relativeFromY = fromRect.top - containerRect.top + fromRect.height / 2 + startYOffset;
      const relativeToX = toRect.left - containerRect.left + toRect.width / 2 + endXOffset;
      const relativeToY = toRect.top - containerRect.top + toRect.height / 2 + endYOffset;

      const midX = (relativeFromX + relativeToX) / 2;
      const midY = (relativeFromY + relativeToY) / 2;

      const controlX = midX + curvature;
      const controlY = midY - Math.abs(curvature);

      const d = `M ${relativeFromX},${relativeFromY} Q ${controlX},${controlY} ${relativeToX},${relativeToY}`;
      setPathD(d);
      setSvgDimensions({
        width: containerRect.width,
        height: containerRect.height
      });
    }
  }, [containerRef, fromRef, toRef, curvature, startXOffset, startYOffset, endXOffset, endYOffset]);

  useEffect(() => {
    updatePath();
    const resizeObserver = new ResizeObserver(updatePath);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    return () => resizeObserver.disconnect();
  }, [updatePath]);

  return (
    <svg
      fill="none"
      width={svgDimensions.width}
      height={svgDimensions.height}
      xmlns="http://www.w3.org/2000/svg"
      className={cn(
        'pointer-events-none absolute left-0 top-0 transform-gpu stroke-2',
        className
      )}
      viewBox={`0 0 ${svgDimensions.width} ${svgDimensions.height}`}
    >
      <defs>
        <linearGradient
          className={cn('transform-gpu')}
          id={id}
          gradientUnits="userSpaceOnUse"
          x1="0%"
          x2="100%"
          y1="0%"
          y2="0%"
        >
          <stop stopColor={gradientStartColor} stopOpacity="0" />
          <stop stopColor={gradientStartColor} />
          <stop offset="32.5%" stopColor={gradientStopColor} />
          <stop offset="100%" stopColor={gradientStopColor} stopOpacity="0" />
        </linearGradient>
      </defs>
      
      {/* Static path */}
      <path
        d={pathD}
        stroke={pathColor}
        strokeWidth={pathWidth}
        strokeOpacity={pathOpacity}
        fill="none"
      />
      
      {/* Animated gradient path */}
      <motion.path
        d={pathD}
        stroke={`url(#${id})`}
        strokeWidth={pathWidth}
        fill="none"
        strokeLinecap="round"
        initial={{
          strokeDasharray: '0 100%',
          strokeDashoffset: reverse ? '100%' : '0%'
        }}
        animate={{
          strokeDasharray: ['0 100%', '50% 50%', '100% 0%'],
          strokeDashoffset: reverse ? ['100%', '50%', '0%'] : ['0%', '-50%', '-100%']
        }}
        transition={{
          duration,
          delay,
          repeat: Infinity,
          ease: 'linear'
        }}
      />
    </svg>
  );
};

// Specialized beam components for brainstorming
export const IdeaConnectionBeam: React.FC<{
  containerRef: React.RefObject<HTMLElement>;
  fromRef: React.RefObject<HTMLElement>;
  toRef: React.RefObject<HTMLElement>;
  strength?: 'weak' | 'medium' | 'strong';
  type?: 'related' | 'builds-on' | 'conflicts';
}> = ({ containerRef, fromRef, toRef, strength = 'medium', type = 'related' }) => {
  const strengthConfig = {
    weak: { pathWidth: 1, pathOpacity: 0.3, duration: 4 },
    medium: { pathWidth: 2, pathOpacity: 0.5, duration: 3 },
    strong: { pathWidth: 3, pathOpacity: 0.7, duration: 2 }
  };

  const typeConfig = {
    related: { gradientStartColor: '#8b5cf6', gradientStopColor: '#06b6d4' },
    'builds-on': { gradientStartColor: '#10b981', gradientStopColor: '#34d399' },
    conflicts: { gradientStartColor: '#ef4444', gradientStopColor: '#f87171' }
  };

  return (
    <AnimatedBeam
      containerRef={containerRef}
      fromRef={fromRef}
      toRef={toRef}
      {...strengthConfig[strength]}
      {...typeConfig[type]}
      curvature={30}
    />
  );
};

export const ClusterBeam: React.FC<{
  containerRef: React.RefObject<HTMLElement>;
  fromRef: React.RefObject<HTMLElement>;
  toRef: React.RefObject<HTMLElement>;
  clusterColor?: string;
}> = ({ containerRef, fromRef, toRef, clusterColor = '#8b5cf6' }) => {
  return (
    <AnimatedBeam
      containerRef={containerRef}
      fromRef={fromRef}
      toRef={toRef}
      pathWidth={1}
      pathOpacity={0.4}
      gradientStartColor={clusterColor}
      gradientStopColor={clusterColor}
      duration={5}
      curvature={20}
    />
  );
};