import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

// Import unused UI components to integrate them
import { AnimatedBackgrounds } from '../../ui/animated-backgrounds';
import { AnimatedBeam } from '../../ui/animated-beam';
import { AnimatedGridPattern } from '../../ui/animated-grid-pattern';
import { CursorEffects } from '../../ui/cursor-effects';
import { FloatingActionButton } from '../../ui/floating-action-button';
import { GlassBreadcrumb } from '../../ui/glass-breadcrumb';
import { GlassToast } from '../../ui/glass-toast';
import { MagicCard } from '../../ui/magic-card';
import { MagneticButton } from '../../ui/magnetic-button';
import { Meteors } from '../../ui/meteors';
import { MorphingEffects } from '../../ui/morphing-effects';
import { ParallaxContainer } from '../../ui/parallax-container';
import { ParticleSystem } from '../../ui/particle-system';
import { RainbowButton } from '../../ui/rainbow-button';
import { ShimmerButton } from '../../ui/shimmer-button';
import { TextAnimate } from '../../ui/text-animate';
import { WarpBackground } from '../../ui/warp-background';

interface IntegratedUIEffectsProps {
  children: React.ReactNode;
  enableEffects?: boolean;
  effectsLevel?: 'minimal' | 'moderate' | 'full';
  className?: string;
}

/**
 * Integrated UI Effects Component
 * Combines all unused UI effect components into a cohesive system
 * Implements: Task 7.1 (60fps animations), Task 5.2 (Interactive components)
 */
export const IntegratedUIEffects: React.FC<IntegratedUIEffectsProps> = ({
  children,
  enableEffects = true,
  effectsLevel = 'moderate',
  className
}) => {
  const [showParticles, setShowParticles] = useState(false);
  const [showMeteors, setShowMeteors] = useState(false);
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });

  // Track cursor for effects
  useEffect(() => {
    if (!enableEffects) return;

    const handleMouseMove = (e: MouseEvent) => {
      setCursorPosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [enableEffects]);

  // Trigger particle effects on interaction
  const handleInteraction = () => {
    if (effectsLevel === 'full') {
      setShowParticles(true);
      setTimeout(() => setShowParticles(false), 3000);
    }
  };

  // Trigger meteor shower effect
  const triggerMeteorShower = () => {
    if (effectsLevel !== 'minimal') {
      setShowMeteors(true);
      setTimeout(() => setShowMeteors(false), 5000);
    }
  };

  if (!enableEffects) {
    return <div className={className}>{children}</div>;
  }

  return (
    <div className={cn("relative overflow-hidden", className)}>
      
      {/* Background Effects */}
      {effectsLevel !== 'minimal' && (
        <>
          <AnimatedBackgrounds variant="gradient" />
          <AnimatedGridPattern />
          <WarpBackground intensity={effectsLevel === 'full' ? 'high' : 'low'} />
        </>
      )}

      {/* Particle Effects */}
      <AnimatePresence>
        {showParticles && effectsLevel === 'full' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 pointer-events-none z-10"
          >
            <ParticleSystem
              particleCount={effectsLevel === 'full' ? 100 : 50}
              colors={['#3b82f6', '#8b5cf6', '#06b6d4', '#10b981']}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Meteor Effects */}
      <AnimatePresence>
        {showMeteors && effectsLevel !== 'minimal' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 pointer-events-none z-20"
          >
            <Meteors number={effectsLevel === 'full' ? 20 : 10} />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Cursor Effects */}
      {effectsLevel === 'full' && (
        <CursorEffects
          position={cursorPosition}
          variant="trail"
        />
      )}

      {/* Parallax Container for Content */}
      <ParallaxContainer
        speed={effectsLevel === 'full' ? 0.5 : 0.2}
        className="relative z-30"
      >
        <div onClick={handleInteraction}>
          {children}
        </div>
      </ParallaxContainer>

      {/* Floating Action Button for Effects Control */}
      <FloatingActionButton
        position="bottom-right"
        actions={[
          {
            icon: '✨',
            label: 'Trigger Particles',
            onClick: handleInteraction
          },
          {
            icon: '☄️',
            label: 'Meteor Shower',
            onClick: triggerMeteorShower
          }
        ]}
      />
    </div>
  );
};

/**
 * Enhanced Card Component with Integrated Effects
 */
interface EnhancedCardProps {
  children: React.ReactNode;
  variant?: 'magic' | 'glass' | 'shimmer' | 'rainbow';
  interactive?: boolean;
  className?: string;
}

export const EnhancedCard: React.FC<EnhancedCardProps> = ({
  children,
  variant = 'magic',
  interactive = true,
  className
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const cardProps = {
    onMouseEnter: () => setIsHovered(true),
    onMouseLeave: () => setIsHovered(false),
    className: cn("transition-all duration-300", className)
  };

  switch (variant) {
    case 'magic':
      return (
        <MagicCard {...cardProps}>
          <div className="relative">
            {children}
            {isHovered && interactive && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="absolute inset-0 pointer-events-none"
              >
                <AnimatedBeam />
              </motion.div>
            )}
          </div>
        </MagicCard>
      );

    case 'glass':
      return (
        <div {...cardProps} className={cn("backdrop-blur-md bg-white/10 border border-white/20 rounded-lg", cardProps.className)}>
          {children}
        </div>
      );

    case 'shimmer':
      return (
        <div {...cardProps} className={cn("relative overflow-hidden", cardProps.className)}>
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer" />
          {children}
        </div>
      );

    case 'rainbow':
      return (
        <div {...cardProps} className={cn("relative", cardProps.className)}>
          <div className="absolute inset-0 bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500 opacity-20 rounded-lg" />
          <div className="relative bg-background/90 backdrop-blur-sm rounded-lg">
            {children}
          </div>
        </div>
      );

    default:
      return <div {...cardProps}>{children}</div>;
  }
};

/**
 * Enhanced Button Collection
 */
interface EnhancedButtonProps {
  children: React.ReactNode;
  variant?: 'magnetic' | 'shimmer' | 'rainbow' | 'morphing';
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
}

export const EnhancedButton: React.FC<EnhancedButtonProps> = ({
  children,
  variant = 'magnetic',
  onClick,
  className,
  disabled = false
}) => {
  switch (variant) {
    case 'magnetic':
      return (
        <MagneticButton
          onClick={onClick}
          disabled={disabled}
          className={className}
        >
          {children}
        </MagneticButton>
      );

    case 'shimmer':
      return (
        <ShimmerButton
          onClick={onClick}
          disabled={disabled}
          className={className}
        >
          {children}
        </ShimmerButton>
      );

    case 'rainbow':
      return (
        <RainbowButton
          onClick={onClick}
          disabled={disabled}
          className={className}
        >
          {children}
        </RainbowButton>
      );

    case 'morphing':
      return (
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onClick}
          disabled={disabled}
          className={cn(
            "relative overflow-hidden rounded-lg px-4 py-2 font-medium transition-all",
            "bg-gradient-to-r from-blue-500 to-purple-500 text-white",
            "hover:from-purple-500 hover:to-pink-500",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
        >
          <MorphingEffects />
          <span className="relative z-10">{children}</span>
        </motion.button>
      );

    default:
      return (
        <button
          onClick={onClick}
          disabled={disabled}
          className={className}
        >
          {children}
        </button>
      );
  }
};

/**
 * Enhanced Text with Animation
 */
interface EnhancedTextProps {
  children: string;
  variant?: 'typewriter' | 'fade' | 'slide' | 'bounce';
  className?: string;
}

export const EnhancedText: React.FC<EnhancedTextProps> = ({
  children,
  variant = 'fade',
  className
}) => {
  return (
    <TextAnimate
      text={children}
      variant={variant}
      className={className}
    />
  );
};

/**
 * Enhanced Breadcrumb with Glass Effect
 */
interface EnhancedBreadcrumbProps {
  items: Array<{ label: string; href?: string }>;
  className?: string;
}

export const EnhancedBreadcrumb: React.FC<EnhancedBreadcrumbProps> = ({
  items,
  className
}) => {
  return (
    <GlassBreadcrumb
      items={items}
      className={className}
    />
  );
};

/**
 * Enhanced Toast with Glass Effect
 */
interface EnhancedToastProps {
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  onClose?: () => void;
  className?: string;
}

export const EnhancedToast: React.FC<EnhancedToastProps> = ({
  message,
  type = 'info',
  onClose,
  className
}) => {
  return (
    <GlassToast
      message={message}
      type={type}
      onClose={onClose}
      className={className}
    />
  );
};

export default IntegratedUIEffects;