import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  MessageSquare,
  GitBranch,
  Columns3,
  Grid3x3,
  Users,
  Target,
  Download,
  Settings,
  Plus,
  Search,
  ChevronLeft,
  ChevronRight,
  Sparkles,
  Lightbulb,
  TrendingUp,
  FileText,
  Upload
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { GlassmorphismCard } from './GlassmorphismCard';
import { AnimatedGradientText } from './AnimatedText';
import { InteractiveHoverButton } from './EnhancedButtons';

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  badge?: number;
  isActive?: boolean;
  onClick?: () => void;
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  color: string;
  onClick?: () => void;
}

interface ModernNavigationProps {
  currentView: string;
  onNavigate: (viewId: string) => void;
  onQuickAction: (actionId: string) => void;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
  className?: string;
}

const navigationItems: NavigationItem[] = [
  { id: 'dashboard', label: 'Dashboard', icon: Grid3x3 },
  { id: 'session', label: 'Active Session', icon: MessageSquare },
  { id: 'create', label: 'Create Session', icon: Plus },
  { id: 'collaboration', label: 'Collaboration', icon: Users },
  { id: 'idea-map', label: 'Idea Map', icon: GitBranch },
  { id: 'progress', label: 'Progress', icon: TrendingUp },
  { id: 'export', label: 'Export', icon: Download },
];

const quickActions: QuickAction[] = [
  { id: 'new-session', label: 'New Session', icon: Plus, color: 'from-purple-500 to-blue-500' },
  { id: 'collaboration', label: 'Collaborate', icon: Users, color: 'from-green-500 to-teal-500' },
  { id: 'idea-map', label: 'Idea Map', icon: GitBranch, color: 'from-orange-500 to-red-500' },
  { id: 'export', label: 'Export', icon: Download, color: 'from-blue-500 to-indigo-500' },
  { id: 'import', label: 'Import', icon: Upload, color: 'from-pink-500 to-rose-500' },
  { id: 'settings', label: 'Settings', icon: Settings, color: 'from-gray-500 to-gray-600' },
];

export const ModernNavigation: React.FC<ModernNavigationProps> = ({
  currentView,
  onNavigate,
  onQuickAction,
  collapsed = false,
  onToggleCollapse,
  className
}) => {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  return (
    <motion.div
      initial={false}
      animate={{ width: collapsed ? 80 : 280 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className={cn(
        'h-full flex flex-col bg-gradient-to-b from-gray-900/95 to-gray-800/95 backdrop-blur-xl border-r border-white/10',
        className
      )}
    >
      {/* Header */}
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center justify-between">
          <AnimatePresence>
            {!collapsed && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="flex items-center space-x-3"
              >
                <div className="p-2 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500">
                  <Brain className="w-6 h-6 text-white" />
                </div>
                <AnimatedGradientText className="text-xl font-bold">
                  Brainstorm
                </AnimatedGradientText>
              </motion.div>
            )}
          </AnimatePresence>
          
          <InteractiveHoverButton
            onClick={onToggleCollapse}
            hoverEffect="glow"
            className="p-2 bg-white/10 hover:bg-white/20 border-0"
          >
            {collapsed ? (
              <ChevronRight className="w-4 h-4 text-white" />
            ) : (
              <ChevronLeft className="w-4 h-4 text-white" />
            )}
          </InteractiveHoverButton>
        </div>
      </div>

      {/* Navigation Items */}
      <div className="flex-1 p-4 space-y-2">
        <AnimatePresence>
          {!collapsed && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="mb-6"
            >
              <h3 className="text-xs font-semibold text-white/60 uppercase tracking-wider mb-3">
                Navigation
              </h3>
            </motion.div>
          )}
        </AnimatePresence>

        {navigationItems.map((item) => {
          const Icon = item.icon;
          const isActive = currentView === item.id;
          
          return (
            <motion.div
              key={item.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <button
                onClick={() => onNavigate(item.id)}
                onMouseEnter={() => setHoveredItem(item.id)}
                onMouseLeave={() => setHoveredItem(null)}
                className={cn(
                  'w-full flex items-center space-x-3 p-3 rounded-lg transition-all duration-200',
                  'hover:bg-white/10 group relative overflow-hidden',
                  isActive && 'bg-gradient-to-r from-purple-500/20 to-blue-500/20 border border-purple-500/30'
                )}
              >
                {/* Active indicator */}
                {isActive && (
                  <motion.div
                    layoutId="activeIndicator"
                    className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-purple-500 to-blue-500 rounded-r"
                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                  />
                )}
                
                {/* Icon */}
                <div className={cn(
                  'p-2 rounded-lg transition-all duration-200',
                  isActive 
                    ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white'
                    : 'bg-white/10 text-white/70 group-hover:text-white group-hover:bg-white/20'
                )}>
                  <Icon className="w-4 h-4" />
                </div>
                
                {/* Label */}
                <AnimatePresence>
                  {!collapsed && (
                    <motion.div
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      className="flex-1 text-left"
                    >
                      <span className={cn(
                        'text-sm font-medium transition-colors duration-200',
                        isActive ? 'text-white' : 'text-white/70 group-hover:text-white'
                      )}>
                        {item.label}
                      </span>
                      {item.badge && (
                        <span className="ml-2 px-2 py-1 text-xs bg-red-500 text-white rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
                
                {/* Hover effect */}
                {hoveredItem === item.id && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="absolute inset-0 bg-gradient-to-r from-white/5 to-white/10 rounded-lg"
                  />
                )}
              </button>
            </motion.div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="p-4 border-t border-white/10">
        <AnimatePresence>
          {!collapsed && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="mb-4"
            >
              <h3 className="text-xs font-semibold text-white/60 uppercase tracking-wider mb-3">
                Quick Actions
              </h3>
            </motion.div>
          )}
        </AnimatePresence>

        <div className={cn(
          'grid gap-2',
          collapsed ? 'grid-cols-1' : 'grid-cols-2'
        )}>
          {quickActions.slice(0, collapsed ? 4 : 6).map((action) => {
            const Icon = action.icon;
            
            return (
              <motion.button
                key={action.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => onQuickAction(action.id)}
                className={cn(
                  'p-3 rounded-lg bg-gradient-to-r transition-all duration-200',
                  'hover:shadow-lg group relative overflow-hidden',
                  action.color
                )}
              >
                <div className="flex items-center justify-center space-x-2">
                  <Icon className="w-4 h-4 text-white" />
                  <AnimatePresence>
                    {!collapsed && (
                      <motion.span
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -10 }}
                        className="text-xs font-medium text-white"
                      >
                        {action.label}
                      </motion.span>
                    )}
                  </AnimatePresence>
                </div>
                
                {/* Shimmer effect */}
                <div className="absolute inset-0 -top-1/2 -left-1/2 w-full h-full opacity-30 group-hover:animate-pulse">
                  <div className="w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent transform rotate-45" />
                </div>
              </motion.button>
            );
          })}
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-white/10">
        <AnimatePresence>
          {!collapsed && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center"
            >
              <p className="text-xs text-white/50">
                Enhanced Brainstorming
              </p>
              <div className="flex items-center justify-center mt-2">
                <Sparkles className="w-3 h-3 text-purple-400 mr-1" />
                <span className="text-xs text-purple-400 font-medium">
                  AI Powered
                </span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};