import React, { useState } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { 
  Plus, 
  MoreVertical, 
  Edit2, 
  Trash2, 
  Tag,
  AlertCircle,
  CheckCircle,
  Clock,
  Archive
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';
import { Idea, IdeaStatus, Priority } from '@/types/brainstorm';
import { useUndoRedo } from '@/hooks/useUndoRedo';
import { UndoRedoToolbar } from './UndoRedoToolbar';

interface KanbanViewProps {
  sessionId: string;
  className?: string;
}

interface KanbanColumn {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  color: string;
}

const columns: KanbanColumn[] = [
  {
    id: 'active',
    title: 'Active',
    icon: Clock,
    color: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300',
  },
  {
    id: 'in-progress',
    title: 'In Progress',
    icon: AlertCircle,
    color: 'bg-blue-50 dark:bg-blue-900/20 border-blue-300',
  },
  {
    id: 'completed',
    title: 'Completed',
    icon: CheckCircle,
    color: 'bg-green-50 dark:bg-green-900/20 border-green-300',
  },
  {
    id: 'archived',
    title: 'Archived',
    icon: Archive,
    color: 'bg-gray-50 dark:bg-gray-900 border-gray-200',
  },
];

const IdeaCard: React.FC<{
  idea: Idea;
  index: number;
  onEdit: (idea: Idea) => void;
  onDelete: (ideaId: string) => void;
}> = ({ idea, index, onEdit, onDelete }) => {
  const { theme } = useBrainstormingTheme();
  
  const priorityColors = {
    'low': { bg: theme.colors.accent + '20', text: theme.colors.accent, border: theme.colors.accent + '40' },
    'medium': { bg: theme.colors.primary + '20', text: theme.colors.primary, border: theme.colors.primary + '40' },
    'high': { bg: theme.colors.warning + '20', text: theme.colors.warning, border: theme.colors.warning + '40' },
    'critical': { bg: theme.colors.error + '20', text: theme.colors.error, border: theme.colors.error + '40' },
  };

  return (
    <Draggable draggableId={idea.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
        >
          <Card
            className={cn(
              'mb-3 cursor-move transition-all duration-200 hover:shadow-md group',
              snapshot.isDragging && 'shadow-xl rotate-1 scale-105 ring-2 ring-offset-2'
            )}
            style={{
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.border,
              ringColor: snapshot.isDragging ? theme.colors.primary : undefined,
              boxShadow: snapshot.isDragging ? theme.shadows.lg : theme.shadows.sm
            }}
          >
          <CardContent className="p-3">
            <div className="flex items-start justify-between gap-2 mb-2">
              <p 
                className="text-sm flex-1 leading-relaxed"
                style={{ color: theme.colors.text }}
              >
                {idea.content}
              </p>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    style={{ color: theme.colors.textSecondary }}
                  >
                    <MoreVertical className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent 
                  align="end"
                  style={{
                    backgroundColor: theme.colors.surface,
                    borderColor: theme.colors.border
                  }}
                >
                  <DropdownMenuItem 
                    onClick={() => onEdit(idea)}
                    style={{ color: theme.colors.text }}
                  >
                    <Edit2 className="h-3 w-3 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => onDelete(idea.id)}
                    style={{ color: theme.colors.error }}
                  >
                    <Trash2 className="h-3 w-3 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {idea.priority && (
                  <Badge 
                    variant="outline" 
                    className="text-xs px-2 py-0.5 font-medium"
                    style={{
                      backgroundColor: priorityColors[idea.priority]?.bg,
                      color: priorityColors[idea.priority]?.text,
                      borderColor: priorityColors[idea.priority]?.border
                    }}
                  >
                    {idea.priority}
                  </Badge>
                )}
                
                {idea.tags && idea.tags.length > 0 && (
                  <div className="flex items-center gap-1">
                    <Tag 
                      className="h-3 w-3" 
                      style={{ color: theme.colors.textSecondary }}
                    />
                    <span 
                      className="text-xs"
                      style={{ color: theme.colors.textSecondary }}
                    >
                      {idea.tags.length}
                    </span>
                  </div>
                )}
              </div>
              
              <div 
                className="text-xs opacity-60"
                style={{ color: theme.colors.textSecondary }}
              >
                {new Date(idea.createdAt).toLocaleDateString()}
              </div>
            </div>
          </CardContent>
        </Card>
        </div>
      )}
    </Draggable>
  );
};

export const KanbanView: React.FC<KanbanViewProps> = ({ sessionId, className }) => {
  const { theme } = useBrainstormingTheme();
  const { 
    getIdeasBySession, 
    updateIdea, 
    deleteIdea, 
    addIdea 
  } = useBrainstormStore();
  const undoRedo = useUndoRedo();
  
  const [newIdeaContent, setNewIdeaContent] = useState<{ [key: string]: string }>({});
  // const [editingIdea, setEditingIdea] = useState<Idea | null>(null);
  
  const ideas = getIdeasBySession(sessionId);

  const getIdeasByStatus = (status: string) => {
    return ideas.filter(idea => idea.status === status);
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const { draggableId, destination } = result;
    const newStatus = destination.droppableId;
    const idea = ideas.find(i => i.id === draggableId);
    
    if (idea) {
      undoRedo.recordUpdate(sessionId, draggableId, 
        { status: idea.status }, 
        { status: newStatus }
      );
      updateIdea(draggableId, { status: newStatus });
    }
  };

  const handleAddIdea = (status: string) => {
    const content = newIdeaContent[status]?.trim();
    if (!content) return;

    const newIdea = addIdea(sessionId, content, { status });
    if (newIdea) {
      undoRedo.recordCreate(sessionId, newIdea);
    }

    setNewIdeaContent({ ...newIdeaContent, [status]: '' });
  };

  const handleDeleteIdea = (ideaId: string) => {
    const idea = ideas.find(i => i.id === ideaId);
    if (idea) {
      undoRedo.recordDelete(sessionId, idea);
      deleteIdea(ideaId);
    }
  };

  const handleEditIdea = (idea: Idea) => {
    // setEditingIdea(idea);
    // TODO: Open edit modal
    console.log('Edit idea:', idea);
  };

  return (
    <div 
      className={cn('flex flex-col h-full rounded-lg relative', className)}
      style={{ backgroundColor: theme.colors.background }}
    >
      {/* Undo/Redo Toolbar */}
      <UndoRedoToolbar className="absolute top-4 right-4 z-10" compact={true} />

      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="flex gap-4 p-4 h-full overflow-x-auto">
          {columns.map((column) => {
            const columnIdeas = getIdeasByStatus(column.id);
            const Icon = column.icon;

            return (
              <div
                key={column.id}
                className="flex-shrink-0 w-80 flex flex-col rounded-lg border-2 shadow-sm"
                style={{
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border
                }}
              >
                <div 
                  className="p-4 border-b rounded-t-lg"
                  style={{ 
                    borderColor: theme.colors.border,
                    background: `linear-gradient(135deg, ${theme.colors.primary}10, ${theme.colors.secondary}10)`
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Icon 
                        className="h-5 w-5" 
                        style={{ color: theme.colors.primary }}
                      />
                      <h3 
                        className="font-semibold"
                        style={{ color: theme.colors.text }}
                      >
                        {column.title}
                      </h3>
                      <Badge 
                        variant="secondary" 
                        className="ml-2"
                        style={{
                          backgroundColor: theme.colors.primary + '20',
                          color: theme.colors.primary,
                          border: `1px solid ${theme.colors.primary}40`
                        }}
                      >
                        {columnIdeas.length}
                      </Badge>
                    </div>
                  </div>
                </div>

                <Droppable droppableId={column.id}>
                  {(provided, snapshot) => (
                    <ScrollArea
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={cn(
                        'flex-1 p-3 transition-colors',
                        snapshot.isDraggingOver && 'bg-primary/5'
                      )}
                      style={{
                        backgroundColor: snapshot.isDraggingOver 
                          ? theme.colors.primary + '10' 
                          : 'transparent'
                      }}
                    >
                      {columnIdeas.map((idea, index) => (
                        <IdeaCard
                          key={idea.id}
                          idea={idea}
                          index={index}
                          onEdit={handleEditIdea}
                          onDelete={handleDeleteIdea}
                        />
                      ))}
                      {provided.placeholder}
                      
                      {columnIdeas.length === 0 && (
                        <div 
                          className="text-center py-8 text-sm opacity-50"
                          style={{ color: theme.colors.textSecondary }}
                        >
                          No ideas in {column.title.toLowerCase()}
                        </div>
                      )}
                    </ScrollArea>
                  )}
                </Droppable>

                <div 
                  className="p-3 border-t rounded-b-lg"
                  style={{ 
                    borderColor: theme.colors.border,
                    backgroundColor: theme.colors.background + '80'
                  }}
                >
                  <div className="flex gap-2">
                    <Input
                      placeholder={`Add to ${column.title.toLowerCase()}...`}
                      value={newIdeaContent[column.id] || ''}
                      onChange={(e) => setNewIdeaContent({
                        ...newIdeaContent,
                        [column.id]: e.target.value
                      })}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleAddIdea(column.id);
                        } else if (e.key === 'Escape') {
                          setNewIdeaContent({
                            ...newIdeaContent,
                            [column.id]: ''
                          });
                          e.currentTarget.blur();
                        }
                      }}
                      className="flex-1 text-sm"
                      style={{
                        backgroundColor: theme.colors.surface,
                        borderColor: theme.colors.border,
                        color: theme.colors.text
                      }}
                      aria-label={`Add new idea to ${column.title} column`}
                      title="Press Enter to add idea, Escape to cancel"
                    />
                    <Button
                      size="sm"
                      onClick={() => handleAddIdea(column.id)}
                      disabled={!newIdeaContent[column.id]?.trim()}
                      style={{
                        backgroundColor: theme.colors.primary,
                        color: 'white'
                      }}
                      aria-label={`Add idea to ${column.title} column`}
                      title={`Add new idea to ${column.title}`}
                    >
                      <Plus className="h-4 w-4" aria-hidden="true" />
                    </Button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </DragDropContext>
    </div>
  );
};