/**
 * Virtualized Idea Manager Component
 * 
 * Performance-optimized version of IdeaManager using react-window for large datasets
 */

import React, { useState, useCallback, useMemo, memo } from 'react';
import { FixedSizeList as List } from 'react-window';
import { motion, AnimatePresence } from 'framer-motion';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { useUndoRedo } from '@/hooks/useUndoRedo';
import { UndoRedoToolbar } from './UndoRedoToolbar';
import { Idea, IdeaStatus } from '@/types/brainstorm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  Lightbulb,
  Plus,
  Search,
  Filter,
  Edit2,
  Trash2,
  Link,
  Star,
  Clock,
  Tag,
  ArrowUpDown,
  Loader2,
  CheckSquare,
  Square,
  MoreHorizontal,
  Copy,
  Archive,
  RefreshCw,
  Sparkles,
  Zap,
  Target,
  Layers,
  Palette,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDebounce } from '@/hooks/useDebounce';

interface VirtualizedIdeaManagerProps {
  sessionId: string;
  className?: string;
  onIdeaSelect?: (idea: Idea) => void;
  onIdeaClick?: (idea: Idea) => void;
}

interface IdeaRowProps {
  index: number;
  style: React.CSSProperties;
  data: {
    ideas: Idea[];
    selectedIdeas: Set<string>;
    onEdit: (idea: Idea) => void;
    onDelete: (idea: Idea) => void;
    onLink: (idea: Idea) => void;
    onToggleStar: (idea: Idea) => void;
    onToggleSelect: (ideaId: string) => void;
    onIdeaClick?: (idea: Idea) => void;
  };
}

interface IdeaTemplate {
  id: string;
  name: string;
  content: string;
  tags: string[];
  priority: string;
  category: string;
  icon: React.ComponentType<any>;
}

// Predefined idea templates for quick-add functionality
const IDEA_TEMPLATES: IdeaTemplate[] = [
  {
    id: 'feature',
    name: 'Feature Idea',
    content: 'New feature: ',
    tags: ['feature', 'enhancement'],
    priority: 'medium',
    category: 'Feature',
    icon: Sparkles
  },
  {
    id: 'bug-fix',
    name: 'Bug Fix',
    content: 'Fix: ',
    tags: ['bug', 'fix'],
    priority: 'high',
    category: 'Bug',
    icon: Zap
  },
  {
    id: 'improvement',
    name: 'Improvement',
    content: 'Improve: ',
    tags: ['improvement', 'optimization'],
    priority: 'medium',
    category: 'Improvement',
    icon: Target
  },
  {
    id: 'research',
    name: 'Research',
    content: 'Research: ',
    tags: ['research', 'analysis'],
    priority: 'low',
    category: 'Research',
    icon: Search
  },
  {
    id: 'design',
    name: 'Design',
    content: 'Design: ',
    tags: ['design', 'ui', 'ux'],
    priority: 'medium',
    category: 'Design',
    icon: Palette
  },
  {
    id: 'integration',
    name: 'Integration',
    content: 'Integrate: ',
    tags: ['integration', 'api'],
    priority: 'medium',
    category: 'Integration',
    icon: Layers
  }
];

// Memoized row component for performance
const IdeaRow = memo<IdeaRowProps>(({ index, style, data }) => {
  const { ideas, selectedIdeas, onEdit, onDelete, onLink, onToggleStar, onToggleSelect, onIdeaClick } = data;
  const idea = ideas[index];
  const isSelected = selectedIdeas.has(idea.id);

  const priorityColors = {
    low: 'bg-emerald-100 text-emerald-800 border-emerald-200',
    medium: 'bg-blue-100 text-blue-800 border-blue-200',
    high: 'bg-orange-100 text-orange-800 border-orange-200',
    critical: 'bg-red-100 text-red-800 border-red-200',
  };

  const statusConfig = {
    active: { icon: <Lightbulb className="h-4 w-4" />, color: 'text-yellow-600' },
    'in-progress': { icon: <Clock className="h-4 w-4" />, color: 'text-blue-600' },
    completed: { icon: <Star className="h-4 w-4" />, color: 'text-green-600' },
    archived: { icon: <Archive className="h-4 w-4" />, color: 'text-gray-400' },
  };

  return (
    <div style={style} className="px-4">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.02 }}
      >
        <Card className={cn(
          "mb-2 transition-all duration-200 cursor-pointer group relative overflow-hidden",
          "hover:shadow-lg hover:scale-[1.02]",
          isSelected && "ring-2 ring-primary/50 bg-primary/5"
        )}>
          {/* Selection indicator */}
          <motion.div
            className={cn(
              "absolute left-0 top-0 bottom-0 w-1 transition-all",
              isSelected ? "bg-primary" : "bg-transparent"
            )}
            initial={false}
            animate={{ width: isSelected ? 4 : 0 }}
          />

          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              {/* Selection checkbox */}
              <div className="flex items-center pt-1">
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={() => onToggleSelect(idea.id)}
                  className="transition-all"
                />
              </div>

              {/* Status icon */}
              <div className={cn("mt-1", statusConfig[idea.status]?.color)}>
                {statusConfig[idea.status]?.icon}
              </div>
              
              {/* Content */}
              <div 
                className="flex-1 cursor-pointer"
                onClick={() => onIdeaClick?.(idea)}
              >
                <div className="flex items-start justify-between gap-3">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm line-clamp-2 text-foreground group-hover:text-primary transition-colors">
                      {idea.content}
                    </h4>
                    
                    <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                      <span>{new Date(idea.createdAt).toLocaleDateString()}</span>
                      {idea.connections && idea.connections.length > 0 && (
                        <>
                          <span>•</span>
                          <span>{idea.connections.length} connections</span>
                        </>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 flex-wrap mt-3">
                      <Badge 
                        variant="secondary" 
                        className={cn('text-xs border', priorityColors[idea.priority])}
                      >
                        {idea.priority}
                      </Badge>
                      
                      {idea.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          <Tag className="h-3 w-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                      
                      {idea.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{idea.tags.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  {/* Actions (visible on hover) */}
                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onToggleStar(idea);
                      }}
                      className="h-8 w-8 p-0"
                    >
                      <Star
                        className={cn(
                          'h-4 w-4',
                          idea.status === 'completed' ? 'fill-yellow-400 text-yellow-400' : 'text-muted-foreground'
                        )}
                      />
                    </Button>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit(idea)}>
                          <Edit2 className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onLink(idea)}>
                          <Link className="h-4 w-4 mr-2" />
                          Link
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => navigator.clipboard.writeText(idea.content)}>
                          <Copy className="h-4 w-4 mr-2" />
                          Copy
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => onDelete(idea)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
});

IdeaRow.displayName = 'IdeaRow';

export const VirtualizedIdeaManager: React.FC<VirtualizedIdeaManagerProps> = memo(({
  sessionId,
  className,
  onIdeaSelect,
  onIdeaClick,
}) => {
  const { getIdeasBySession, addIdea, updateIdea, deleteIdea, linkIdeas } = useBrainstormStore();
  const undoRedo = useUndoRedo();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'date' | 'priority'>('date');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingIdea, setEditingIdea] = useState<Idea | null>(null);
  const [loading, setLoading] = useState(false);
  
  // Bulk selection state
  const [selectedIdeas, setSelectedIdeas] = useState<Set<string>>(new Set());
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Get and filter ideas with memoization
  const filteredIdeas = useMemo(() => {
    let ideas = getIdeasBySession(sessionId);

    // Apply search filter
    if (debouncedSearchTerm) {
      const searchLower = debouncedSearchTerm.toLowerCase();
      ideas = ideas.filter(
        (idea) =>
          idea.content.toLowerCase().includes(searchLower) ||
          idea.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Apply priority filter
    if (filterPriority !== 'all') {
      ideas = ideas.filter((idea) => idea.priority === filterPriority);
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      ideas = ideas.filter((idea) => idea.status === filterStatus);
    }

    // Sort ideas
    ideas.sort((a, b) => {
      if (sortBy === 'priority') {
        const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      }
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    return ideas;
  }, [sessionId, debouncedSearchTerm, filterPriority, filterStatus, sortBy, getIdeasBySession]);

  const handleEdit = useCallback((idea: Idea) => {
    setEditingIdea(idea);
    setShowAddDialog(true);
  }, []);

  const handleDelete = useCallback((idea: Idea) => {
    if (confirm('Are you sure you want to delete this idea?')) {
      undoRedo.recordDelete(sessionId, idea);
      deleteIdea(idea.id);
    }
  }, [deleteIdea, undoRedo, sessionId]);

  const handleLink = useCallback((idea: Idea) => {
    // In a real implementation, show a dialog to select ideas to link
    console.log('Link idea:', idea);
  }, []);

  const handleToggleStar = useCallback((idea: Idea) => {
    const newStatus = idea.status === 'completed' ? 'active' : 'completed';
    undoRedo.recordUpdate(sessionId, idea.id, 
      { status: idea.status }, 
      { status: newStatus }
    );
    updateIdea(idea.id, {
      status: newStatus,
    });
  }, [updateIdea, undoRedo, sessionId]);

  const handleSaveIdea = useCallback(async (formData: any) => {
    setLoading(true);
    try {
      if (editingIdea) {
        undoRedo.recordUpdate(sessionId, editingIdea.id, editingIdea, formData);
        updateIdea(editingIdea.id, formData);
      } else {
        const newIdea = addIdea(sessionId, formData.content, {
          status: formData.status,
          priority: formData.priority,
          tags: formData.tags
        });
        if (newIdea) {
          undoRedo.recordCreate(sessionId, newIdea);
        }
      }
      setShowAddDialog(false);
      setEditingIdea(null);
    } finally {
      setLoading(false);
    }
  }, [editingIdea, sessionId, addIdea, updateIdea, undoRedo]);

  // Bulk operation handlers
  const handleToggleSelect = useCallback((ideaId: string) => {
    setSelectedIdeas(prev => {
      const newSelection = new Set(prev);
      if (newSelection.has(ideaId)) {
        newSelection.delete(ideaId);
      } else {
        newSelection.add(ideaId);
      }
      return newSelection;
    });
  }, []);

  const handleSelectAll = useCallback(() => {
    if (selectedIdeas.size === filteredIdeas.length) {
      setSelectedIdeas(new Set());
    } else {
      setSelectedIdeas(new Set(filteredIdeas.map(idea => idea.id)));
    }
  }, [selectedIdeas.size, filteredIdeas]);

  const handleBulkDelete = useCallback(async () => {
    if (selectedIdeas.size === 0) return;
    
    if (confirm(`Are you sure you want to delete ${selectedIdeas.size} selected ideas?`)) {
      const ideasToDelete = filteredIdeas.filter(idea => selectedIdeas.has(idea.id));
      undoRedo.recordBulkDelete(sessionId, ideasToDelete);
      selectedIdeas.forEach(ideaId => deleteIdea(ideaId));
      setSelectedIdeas(new Set());
      setShowBulkActions(false);
    }
  }, [selectedIdeas, deleteIdea, filteredIdeas, sessionId, undoRedo]);

  const handleBulkStatusUpdate = useCallback((newStatus: string) => {
    const ideasToUpdate = filteredIdeas.filter(idea => selectedIdeas.has(idea.id));
    const beforeData = { status: ideasToUpdate[0]?.status }; // Assuming same status for simplicity
    const afterData = { status: newStatus };
    
    undoRedo.recordBulkUpdate(sessionId, Array.from(selectedIdeas), beforeData, afterData);
    selectedIdeas.forEach(ideaId => {
      updateIdea(ideaId, { status: newStatus });
    });
    setSelectedIdeas(new Set());
    setShowBulkActions(false);
  }, [selectedIdeas, updateIdea, filteredIdeas, sessionId, undoRedo]);

  const handleBulkPriorityUpdate = useCallback((newPriority: string) => {
    const ideasToUpdate = filteredIdeas.filter(idea => selectedIdeas.has(idea.id));
    const beforeData = { priority: ideasToUpdate[0]?.priority }; // Assuming same priority for simplicity
    const afterData = { priority: newPriority };
    
    undoRedo.recordBulkUpdate(sessionId, Array.from(selectedIdeas), beforeData, afterData);
    selectedIdeas.forEach(ideaId => {
      updateIdea(ideaId, { priority: newPriority });
    });
    setSelectedIdeas(new Set());
    setShowBulkActions(false);
  }, [selectedIdeas, updateIdea, filteredIdeas, sessionId, undoRedo]);

  const handleTemplateSelect = useCallback((template: IdeaTemplate) => {
    setEditingIdea(null);
    setShowAddDialog(true);
    setShowTemplateSelector(false);
    // Pre-fill form with template data
    setTimeout(() => {
      const form = document.querySelector('[data-template-form]') as HTMLFormElement;
      if (form) {
        (form.querySelector('[name="content"]') as HTMLTextAreaElement).value = template.content;
        (form.querySelector('[name="priority"]') as HTMLSelectElement).value = template.priority;
        (form.querySelector('[name="tags"]') as HTMLInputElement).value = template.tags.join(', ');
      }
    }, 100);
  }, []);

  // Auto-categorization function
  const categorizeIdea = useCallback((content: string): string[] => {
    const categories: { [key: string]: string[] } = {
      'feature': ['feature', 'new', 'add', 'implement', 'create'],
      'bug': ['bug', 'fix', 'error', 'issue', 'problem'],
      'improvement': ['improve', 'optimize', 'enhance', 'better', 'upgrade'],
      'ui': ['ui', 'interface', 'design', 'visual', 'layout'],
      'performance': ['performance', 'speed', 'fast', 'optimize', 'slow'],
      'security': ['security', 'auth', 'permission', 'secure', 'login'],
      'api': ['api', 'endpoint', 'service', 'integration', 'backend']
    };

    const suggestedTags: string[] = [];
    const contentLower = content.toLowerCase();

    Object.entries(categories).forEach(([category, keywords]) => {
      if (keywords.some(keyword => contentLower.includes(keyword))) {
        suggestedTags.push(category);
      }
    });

    return suggestedTags;
  }, []);

  // Update selection state when filtered ideas change
  React.useEffect(() => {
    const filteredIds = new Set(filteredIdeas.map(idea => idea.id));
    setSelectedIdeas(prev => new Set([...prev].filter(id => filteredIds.has(id))));
  }, [filteredIdeas]);

  // Memoized item data for react-window
  const itemData = useMemo(
    () => ({
      ideas: filteredIdeas,
      selectedIdeas,
      onEdit: handleEdit,
      onDelete: handleDelete,
      onLink: handleLink,
      onToggleStar: handleToggleStar,
      onToggleSelect: handleToggleSelect,
      onIdeaClick,
    }),
    [filteredIdeas, selectedIdeas, handleEdit, handleDelete, handleLink, handleToggleStar, handleToggleSelect, onIdeaClick]
  );

  const stats = useMemo(() => {
    const total = filteredIdeas.length;
    const byPriority = filteredIdeas.reduce((acc, idea) => {
      acc[idea.priority] = (acc[idea.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    const completed = filteredIdeas.filter(i => i.status === 'completed').length;
    
    return { total, byPriority, completed };
  }, [filteredIdeas]);

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-primary" />
                Ideas
                {selectedIdeas.size > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {selectedIdeas.size} selected
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                {stats.total} ideas • {stats.completed} completed
                {selectedIdeas.size > 0 && ` • ${selectedIdeas.size} selected`}
              </CardDescription>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Bulk actions */}
            <AnimatePresence>
              {selectedIdeas.size > 0 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="flex items-center gap-2"
                >
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        <CheckSquare className="h-4 w-4 mr-2" />
                        Bulk Actions
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleBulkStatusUpdate('active')}>
                        Set to Active
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleBulkStatusUpdate('in-progress')}>
                        Set to In Progress
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleBulkStatusUpdate('completed')}>
                        Set to Completed
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleBulkStatusUpdate('archived')}>
                        Archive
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleBulkPriorityUpdate('low')}>
                        Set Priority: Low
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleBulkPriorityUpdate('medium')}>
                        Set Priority: Medium
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleBulkPriorityUpdate('high')}>
                        Set Priority: High
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleBulkPriorityUpdate('critical')}>
                        Set Priority: Critical
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={handleBulkDelete} className="text-destructive">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete Selected
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Template selector */}
            <DropdownMenu open={showTemplateSelector} onOpenChange={setShowTemplateSelector}>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Templates
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                {IDEA_TEMPLATES.map(template => (
                  <DropdownMenuItem 
                    key={template.id}
                    onClick={() => handleTemplateSelect(template)}
                    className="flex items-center gap-2"
                  >
                    <template.icon className="h-4 w-4" />
                    <div>
                      <div className="font-medium">{template.name}</div>
                      <div className="text-xs text-muted-foreground">{template.category}</div>
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Undo/Redo controls */}
            <UndoRedoToolbar compact={true} showHistory={false} />

            {/* Add idea button */}
            <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Idea
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>{editingIdea ? 'Edit' : 'Add New'} Idea</DialogTitle>
                  <DialogDescription>
                    Capture and organize your brainstorming ideas with automatic categorization
                  </DialogDescription>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-4 top-4 p-2"
                    onClick={() => setEditingIdea(null)}
                    aria-label="Close dialog"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </DialogHeader>
                <IdeaForm
                  idea={editingIdea}
                  onSave={handleSaveIdea}
                  loading={loading}
                  categorizeIdea={categorizeIdea}
                />
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Bulk selection header */}
        {filteredIdeas.length > 0 && (
          <div className="flex items-center justify-between mb-4 p-3 bg-muted/30 rounded-lg">
            <div className="flex items-center gap-3">
              <Checkbox
                checked={selectedIdeas.size === filteredIdeas.length && filteredIdeas.length > 0}
                indeterminate={selectedIdeas.size > 0 && selectedIdeas.size < filteredIdeas.length}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm font-medium">
                {selectedIdeas.size === 0 
                  ? 'Select all ideas' 
                  : selectedIdeas.size === filteredIdeas.length 
                    ? 'All ideas selected'
                    : `${selectedIdeas.size} of ${filteredIdeas.length} selected`}
              </span>
            </div>
            
            {selectedIdeas.size > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedIdeas(new Set())}
              >
                Clear selection
              </Button>
            )}
          </div>
        )}

        {/* Filters */}
        <div className="flex flex-wrap gap-2 mb-4">
          <div className="flex-1 min-w-[200px]">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" aria-hidden="true" />
              <Input
                placeholder="Search ideas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Escape') {
                    setSearchTerm('');
                    e.currentTarget.blur();
                  }
                }}
                className="pl-8"
                aria-label="Search through your brainstorming ideas"
                title="Type to search ideas, press Escape to clear"
              />
            </div>
          </div>
          
          <Select value={filterPriority} onValueChange={setFilterPriority}>
            <SelectTrigger className="w-[140px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priorities</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="low">Low</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="in-progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="archived">Archived</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            onClick={() => setSortBy(sortBy === 'date' ? 'priority' : 'date')}
            aria-label={`Sort by ${sortBy === 'date' ? 'priority' : 'date'}`}
            title={`Currently sorted by ${sortBy}. Click to sort by ${sortBy === 'date' ? 'priority' : 'date'}`}
          >
            <ArrowUpDown className="h-4 w-4 mr-2" aria-hidden="true" />
            {sortBy === 'date' ? 'Date' : 'Priority'}
          </Button>
        </div>

        {/* Priority stats */}
        <div className="flex gap-2 mb-4">
          {Object.entries(stats.byPriority).map(([priority, count]) => (
            <Badge key={priority} variant="secondary">
              {priority}: {count}
            </Badge>
          ))}
        </div>

        {/* Virtualized list */}
        {filteredIdeas.length > 0 ? (
          <List
            height={600}
            itemCount={filteredIdeas.length}
            itemSize={120}
            width="100%"
            itemData={itemData}
            className="scrollbar-thin"
          >
            {IdeaRow}
          </List>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            {debouncedSearchTerm || filterPriority !== 'all' || filterStatus !== 'all'
              ? 'No ideas match your filters'
              : 'No ideas yet. Click "Add Idea" to get started!'}
          </div>
        )}
      </CardContent>
    </Card>
  );
});

VirtualizedIdeaManager.displayName = 'VirtualizedIdeaManager';

// Idea form component
const IdeaForm: React.FC<{
  idea?: Idea | null;
  onSave: (data: any) => void;
  loading?: boolean;
  categorizeIdea?: (content: string) => string[];
}> = ({ idea, onSave, loading, categorizeIdea }) => {
  const [content, setContent] = useState(idea?.content || '');
  const [priority, setPriority] = useState(idea?.priority || 'medium');
  const [status, setStatus] = useState(idea?.status || 'active');
  const [tags, setTags] = useState(idea?.tags.join(', ') || '');
  const [suggestedTags, setSuggestedTags] = useState<string[]>([]);

  // Update suggested tags when content changes
  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent);
    if (categorizeIdea && newContent.trim()) {
      const suggestions = categorizeIdea(newContent);
      setSuggestedTags(suggestions);
    } else {
      setSuggestedTags([]);
    }
  }, [categorizeIdea]);

  // Add suggested tag to tags
  const addSuggestedTag = useCallback((tag: string) => {
    const currentTags = tags.split(',').map(t => t.trim()).filter(Boolean);
    if (!currentTags.includes(tag)) {
      const newTags = [...currentTags, tag].join(', ');
      setTags(newTags);
    }
    // Remove from suggestions
    setSuggestedTags(prev => prev.filter(t => t !== tag));
  }, [tags]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      content,
      priority,
      status,
      tags: tags.split(',').map(t => t.trim()).filter(Boolean),
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4" data-template-form>
      <div>
        <label className="text-sm font-medium">Idea Content</label>
        <Textarea
          name="content"
          value={content}
          onChange={(e) => handleContentChange(e.target.value)}
          placeholder="Describe your idea..."
          className="mt-1"
          rows={3}
          required
        />
      </div>

      {/* Auto-generated tag suggestions */}
      {suggestedTags.length > 0 && (
        <div>
          <label className="text-sm font-medium mb-2 block">Suggested Tags</label>
          <div className="flex flex-wrap gap-2">
            {suggestedTags.map((tag) => (
              <button
                key={tag}
                type="button"
                onClick={() => addSuggestedTag(tag)}
                className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-primary/10 text-primary hover:bg-primary/20 transition-colors border border-primary/20"
              >
                <Plus className="h-3 w-3 mr-1" />
                {tag}
              </button>
            ))}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Click on tags to add them to your idea
          </p>
        </div>
      )}
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="text-sm font-medium">Priority</label>
          <Select name="priority" value={priority} onValueChange={setPriority}>
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <label className="text-sm font-medium">Status</label>
          <Select name="status" value={status} onValueChange={setStatus}>
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="in-progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="archived">Archived</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div>
        <label className="text-sm font-medium">Tags (comma-separated)</label>
        <Input
          name="tags"
          value={tags}
          onChange={(e) => setTags(e.target.value)}
          placeholder="ui, feature, bug-fix"
          className="mt-1"
        />
        {tags && (
          <div className="flex flex-wrap gap-1 mt-2">
            {tags.split(',').map((tag, index) => {
              const trimmedTag = tag.trim();
              return trimmedTag ? (
                <Badge key={index} variant="secondary" className="text-xs">
                  {trimmedTag}
                </Badge>
              ) : null;
            })}
          </div>
        )}
      </div>
      
      <div className="flex justify-end gap-2">
        <Button 
          type="button" 
          variant="outline" 
          onClick={() => {
            // Reset form and close dialog
            const closeButton = document.querySelector('[aria-label="Close dialog"]') as HTMLButtonElement;
            closeButton?.click();
          }}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={loading || !content}>
          {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
          {idea ? 'Update' : 'Add'} Idea
        </Button>
      </div>
    </form>
  );
};