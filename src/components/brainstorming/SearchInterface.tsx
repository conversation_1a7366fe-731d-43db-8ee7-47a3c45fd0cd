/**
 * Search Interface Component
 * 
 * Provides comprehensive search functionality across brainstorming sessions,
 * ideas, and memories with advanced filtering and highlighting.
 */

import React, { useState, useEffect, useMemo } from 'react';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { SearchQuery, SearchResult, IdeaStatus, Priority } from '@/types/brainstorm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  Search,
  Filter,
  Calendar,
  Tag,
  MessageSquare,
  Lightbulb,
  Brain,
  FileText,
  ChevronDown,
  X,
  Clock,
  Star,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';

interface SearchInterfaceProps {
  onResultSelect?: (result: SearchResult) => void;
  className?: string;
}

export const SearchInterface: React.FC<SearchInterfaceProps> = ({
  onResultSelect,
  className,
}) => {
  const { searchSessions, sessions, ideas, memories } = useBrainstormStore();
  const [searchQuery, setSearchQuery] = useState<SearchQuery>({});
  const [searchText, setSearchText] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedStatuses, setSelectedStatuses] = useState<IdeaStatus[]>([]);
  const [selectedPriorities, setSelectedPriorities] = useState<Priority[]>([]);
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: '',
    end: '',
  });

  // Get all available tags from ideas
  const availableTags = useMemo(() => {
    const tagSet = new Set<string>();
    Object.values(ideas).forEach(idea => {
      idea.tags.forEach(tag => tagSet.add(tag));
    });
    return Array.from(tagSet).sort();
  }, [ideas]);

  // Perform search when query changes
  useEffect(() => {
    if (searchText.trim() || selectedTags.length || selectedStatuses.length || selectedPriorities.length) {
      performSearch();
    } else {
      setResults([]);
    }
  }, [searchText, selectedTags, selectedStatuses, selectedPriorities, dateRange]);

  const performSearch = async () => {
    setIsSearching(true);
    
    const query: SearchQuery = {
      text: searchText.trim() || undefined,
      tags: selectedTags.length > 0 ? selectedTags : undefined,
      status: selectedStatuses.length > 0 ? selectedStatuses : undefined,
      priority: selectedPriorities.length > 0 ? selectedPriorities : undefined,
      dateRange: dateRange.start && dateRange.end ? dateRange : undefined,
    };

    try {
      const searchResults = searchSessions(query);
      setResults(searchResults);
      
      // Add to search history
      if (searchText.trim() && !searchHistory.includes(searchText.trim())) {
        setSearchHistory(prev => [searchText.trim(), ...prev.slice(0, 9)]);
      }
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const handleStatusToggle = (status: IdeaStatus) => {
    setSelectedStatuses(prev => 
      prev.includes(status) 
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  const handlePriorityToggle = (priority: Priority) => {
    setSelectedPriorities(prev => 
      prev.includes(priority) 
        ? prev.filter(p => p !== priority)
        : [...prev, priority]
    );
  };

  const clearFilters = () => {
    setSelectedTags([]);
    setSelectedStatuses([]);
    setSelectedPriorities([]);
    setDateRange({ start: '', end: '' });
    setSearchText('');
  };

  const getResultIcon = (type: SearchResult['type']) => {
    switch (type) {
      case 'idea': return Lightbulb;
      case 'message': return MessageSquare;
      case 'memory': return Brain;
      case 'session': return FileText;
      default: return Search;
    }
  };

  const getResultTypeColor = (type: SearchResult['type']) => {
    switch (type) {
      case 'idea': return 'bg-yellow-100 text-yellow-800';
      case 'message': return 'bg-blue-100 text-blue-800';
      case 'memory': return 'bg-purple-100 text-purple-800';
      case 'session': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const highlightText = (text: string, highlights: string[]) => {
    if (!highlights.length) return text;
    
    let highlightedText = text;
    highlights.forEach(highlight => {
      const regex = new RegExp(`(${highlight})`, 'gi');
      highlightedText = highlightedText.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
    });
    
    return <span dangerouslySetInnerHTML={{ __html: highlightedText }} />;
  };

  return (
    <div className={cn("search-interface", className)}>
      {/* Search Header */}
      <div className="flex items-center gap-2 mb-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search ideas, messages, memories..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
        >
          <Filter className="h-4 w-4 mr-2" />
          Filters
        </Button>
        {(selectedTags.length > 0 || selectedStatuses.length > 0 || selectedPriorities.length > 0 || dateRange.start) && (
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            <X className="h-4 w-4 mr-2" />
            Clear
          </Button>
        )}
      </div>

      {/* Search History */}
      {searchHistory.length > 0 && !searchText && (
        <div className="mb-4">
          <Label className="text-sm text-muted-foreground mb-2 block">Recent Searches</Label>
          <div className="flex flex-wrap gap-2">
            {searchHistory.slice(0, 5).map((query, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => setSearchText(query)}
                className="text-xs"
              >
                <Clock className="h-3 w-3 mr-1" />
                {query}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Filters */}
      <Collapsible open={showFilters} onOpenChange={setShowFilters}>
        <CollapsibleContent className="space-y-4 mb-4 p-4 border rounded-lg">
          {/* Tags Filter */}
          <div>
            <Label className="text-sm font-medium mb-2 block">Tags</Label>
            <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
              {availableTags.map(tag => (
                <Button
                  key={tag}
                  variant={selectedTags.includes(tag) ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleTagToggle(tag)}
                  className="text-xs"
                >
                  <Tag className="h-3 w-3 mr-1" />
                  {tag}
                </Button>
              ))}
            </div>
          </div>

          {/* Status Filter */}
          <div>
            <Label className="text-sm font-medium mb-2 block">Status</Label>
            <div className="flex flex-wrap gap-2">
              {Object.values(IdeaStatus).map(status => (
                <Button
                  key={status}
                  variant={selectedStatuses.includes(status) ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleStatusToggle(status)}
                  className="text-xs"
                >
                  {status.replace('_', ' ')}
                </Button>
              ))}
            </div>
          </div>

          {/* Priority Filter */}
          <div>
            <Label className="text-sm font-medium mb-2 block">Priority</Label>
            <div className="flex flex-wrap gap-2">
              {Object.values(Priority).map(priority => (
                <Button
                  key={priority}
                  variant={selectedPriorities.includes(priority) ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePriorityToggle(priority)}
                  className="text-xs"
                >
                  {priority}
                </Button>
              ))}
            </div>
          </div>

          {/* Date Range Filter */}
          <div>
            <Label className="text-sm font-medium mb-2 block">Date Range</Label>
            <div className="flex gap-2">
              <Input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                className="flex-1"
              />
              <Input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                className="flex-1"
              />
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Results */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <h3 className="text-sm font-medium">Search Results</h3>
          <Badge variant="secondary">{results.length}</Badge>
        </div>
        {isSearching && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
            Searching...
          </div>
        )}
      </div>

      <ScrollArea className="h-96">
        <div className="space-y-3">
          {results.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">
                {searchText || selectedTags.length || selectedStatuses.length || selectedPriorities.length
                  ? 'No results found. Try adjusting your search criteria.'
                  : 'Enter a search term or apply filters to find content.'}
              </p>
            </div>
          ) : (
            results.map((result) => {
              const IconComponent = getResultIcon(result.type);
              
              return (
                <Card
                  key={result.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => onResultSelect?.(result)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <div className={cn("p-1 rounded", getResultTypeColor(result.type))}>
                          <IconComponent className="h-3 w-3" />
                        </div>
                        <CardTitle className="text-sm">{result.title}</CardTitle>
                        <div className="flex items-center gap-1">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={cn(
                                "h-3 w-3",
                                i < Math.round(result.relevance * 5)
                                  ? "fill-yellow-400 text-yellow-400"
                                  : "text-gray-300"
                              )}
                            />
                          ))}
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {format(new Date(result.timestamp), 'MMM dd, yyyy')}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-sm mb-2">
                      {highlightText(result.content, result.highlights)}
                    </p>
                    <CardDescription className="text-xs">
                      Context: {result.context}
                    </CardDescription>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>
      </ScrollArea>
    </div>
  );
};