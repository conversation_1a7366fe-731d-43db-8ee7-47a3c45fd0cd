import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  FileText,
  Code,
  Palette,
  TrendingUp,
  AlertCircle,
  Clock,
  Users,
  Search,
  Sparkles,
  X,
} from 'lucide-react';
import {
  brainstormTemplates,
  getTemplatesByCategory,
  getTemplateRecommendations,
  BrainstormTemplate,
} from '@/lib/brainstorm-templates';
import { cn } from '@/lib/utils';

interface TemplateSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectTemplate: (template: BrainstormTemplate) => void;
}

const categoryIcons = {
  product: FileText,
  technical: Code,
  creative: Palette,
  strategy: TrendingUp,
  'problem-solving': AlertCircle,
};

const categoryColors = {
  product: 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300',
  technical: 'bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300',
  creative: 'bg-pink-100 text-pink-700 dark:bg-pink-900 dark:text-pink-300',
  strategy: 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300',
  'problem-solving': 'bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300',
};

export const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  open,
  onOpenChange,
  onSelectTemplate,
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<BrainstormTemplate | null>(null);

  const getFilteredTemplates = () => {
    let templates = brainstormTemplates;

    // Filter by category
    if (selectedCategory !== 'all') {
      templates = getTemplatesByCategory(selectedCategory as any);
    }

    // Filter by search
    if (searchQuery) {
      const keywords = searchQuery.split(' ').filter(k => k.length > 0);
      templates = getTemplateRecommendations(keywords);
    }

    return templates;
  };

  const handleSelectTemplate = () => {
    if (selectedTemplate) {
      onSelectTemplate(selectedTemplate);
      onOpenChange(false);
      setSelectedTemplate(null);
      setSearchQuery('');
    }
  };

  const categories = ['all', 'product', 'technical', 'creative', 'strategy', 'problem-solving'];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Choose a Brainstorming Template
          </DialogTitle>
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-4 top-4 p-2"
            onClick={() => onOpenChange(false)}
            aria-label="Close dialog"
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Category Tabs */}
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList className="grid grid-cols-6 w-full">
              {categories.map((category) => {
                const Icon = category === 'all' ? Sparkles : categoryIcons[category as keyof typeof categoryIcons];
                return (
                  <TabsTrigger key={category} value={category} className="capitalize">
                    <Icon className="h-4 w-4 mr-1" />
                    {category.replace('-', ' ')}
                  </TabsTrigger>
                );
              })}
            </TabsList>

            <TabsContent value={selectedCategory} className="mt-4">
              <ScrollArea className="h-[400px] pr-4">
                <div className="grid grid-cols-2 gap-4">
                  {getFilteredTemplates().map((template) => {
                    const Icon = categoryIcons[template.category];
                    const isSelected = selectedTemplate?.id === template.id;

                    return (
                      <Card
                        key={template.id}
                        className={cn(
                          'cursor-pointer transition-all hover:shadow-md',
                          isSelected && 'ring-2 ring-primary'
                        )}
                        onClick={() => setSelectedTemplate(template)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-2">
                              <span className="text-2xl">{template.icon}</span>
                              <CardTitle className="text-base">{template.name}</CardTitle>
                            </div>
                            <Badge
                              variant="secondary"
                              className={cn('text-xs', categoryColors[template.category])}
                            >
                              <Icon className="h-3 w-3 mr-1" />
                              {template.category}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <CardDescription className="text-sm mb-3">
                            {template.description}
                          </CardDescription>

                          {template.metadata && (
                            <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                              {template.metadata.estimatedDuration && (
                                <div className="flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {template.metadata.estimatedDuration}
                                </div>
                              )}
                              {template.metadata.participantCount && (
                                <div className="flex items-center gap-1">
                                  <Users className="h-3 w-3" />
                                  {template.metadata.participantCount}
                                </div>
                              )}
                              {template.metadata.difficulty && (
                                <Badge variant="outline" className="text-xs">
                                  {template.metadata.difficulty}
                                </Badge>
                              )}
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>

          {/* Selected Template Details */}
          {selectedTemplate && (
            <Card className="bg-muted/50">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Selected Template Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <p className="text-sm font-medium mb-1">Guiding Prompts:</p>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      {selectedTemplate.prompts.slice(0, 3).map((prompt, i) => (
                        <li key={i} className="flex items-start gap-2">
                          <span className="text-xs">•</span>
                          <span>{prompt}</span>
                        </li>
                      ))}
                      {selectedTemplate.prompts.length > 3 && (
                        <li className="text-xs italic">
                          +{selectedTemplate.prompts.length - 3} more prompts
                        </li>
                      )}
                    </ul>
                  </div>
                  <div>
                    <p className="text-sm font-medium mb-1">Suggested Tags:</p>
                    <div className="flex flex-wrap gap-1">
                      {selectedTemplate.suggestedTags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSelectTemplate} disabled={!selectedTemplate}>
              Use Template
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};