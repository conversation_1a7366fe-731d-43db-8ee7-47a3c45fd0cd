/**
 * Enhanced Choice Selector Component
 * 
 * Sophisticated UI for displaying and selecting contextual brainstorming choices.
 * Supports dynamic choices, follow-ups, code generation triggers, and flow transitions.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Lightbulb,
  Code,
  BarChart3,
  Users,
  ChevronRight,
  Clock,
  Zap,
  Star,
  ArrowRight,
  Sparkles,
  CheckCircle,
  AlertCircle,
  Info,
  Target,
  Brain,
  Settings,
  Play,
  Pause
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';

import { cn } from '@/lib/utils';
import { 
  EnhancedChoice, 
  FollowUpSuggestion,
  enhancedChoiceEngine,
  ChoiceContext
} from '@/lib/brainstorming/enhancedChoiceEngine';
import { flowEngine } from '@/lib/brainstorming/flowEngine';

interface EnhancedChoiceSelectorProps {
  context: ChoiceContext;
  onChoiceSelect: (choice: EnhancedChoice, promptSuggestion?: string) => void;
  className?: string;
  disabled?: boolean;
  showFollowUps?: boolean;
  maxChoices?: number;
  groupByCategory?: boolean;
  
  // Flow-related props
  activeFlowId?: string;
  currentStepId?: string;
  onFlowStart?: (flowId: string) => void;
  onFlowAction?: (action: 'pause' | 'resume' | 'skip' | 'back') => void;
}

interface CategoryGroup {
  category: EnhancedChoice['category'];
  label: string;
  icon: React.ComponentType<any>;
  color: string;
  choices: EnhancedChoice[];
}

const CATEGORY_CONFIG: Record<EnhancedChoice['category'], {
  label: string;
  icon: React.ComponentType<any>;
  color: string;
  description: string;
}> = {
  'topic': {
    label: 'Explore Topics',
    icon: Lightbulb,
    color: 'from-yellow-400 to-orange-500',
    description: 'Deep dive into specific subjects'
  },
  'template': {
    label: 'Apply Templates',
    icon: Settings,
    color: 'from-blue-400 to-purple-500',
    description: 'Use structured methodologies'
  },
  'follow-up': {
    label: 'Follow-ups',
    icon: ArrowRight,
    color: 'from-green-400 to-teal-500',
    description: 'Continue the conversation'
  },
  'code-action': {
    label: 'Generate Code',
    icon: Code,
    color: 'from-purple-400 to-pink-500',
    description: 'Create implementation artifacts'
  },
  'analysis': {
    label: 'Analysis',
    icon: BarChart3,
    color: 'from-cyan-400 to-blue-500',
    description: 'Analyze and evaluate ideas'
  },
  'export': {
    label: 'Export & Share',
    icon: Target,
    color: 'from-gray-400 to-gray-600',
    description: 'Export and share your work'
  }
};

export const EnhancedChoiceSelector: React.FC<EnhancedChoiceSelectorProps> = ({
  context,
  onChoiceSelect,
  className,
  disabled = false,
  showFollowUps = true,
  maxChoices = 8,
  groupByCategory = true,
  activeFlowId,
  currentStepId,
  onFlowStart,
  onFlowAction
}) => {
  const [choices, setChoices] = useState<EnhancedChoice[]>([]);
  const [followUpChoices, setFollowUpChoices] = useState<EnhancedChoice[]>([]);
  const [selectedChoice, setSelectedChoice] = useState<EnhancedChoice | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [expandedFollowUps, setExpandedFollowUps] = useState<string[]>([]);
  const [categoryGroups, setCategoryGroups] = useState<CategoryGroup[]>([]);
  const [flowProgress, setFlowProgress] = useState<any>(null);

  // Load choices when context changes
  useEffect(() => {
    loadChoices();
  }, [context]);

  // Load flow progress if active flow
  useEffect(() => {
    if (activeFlowId && context.session.id) {
      const progress = flowEngine.getFlowProgress(context.session.id);
      setFlowProgress(progress);
    }
  }, [activeFlowId, currentStepId, context.session.id]);

  const loadChoices = useCallback(async () => {
    setIsLoading(true);
    try {
      const generatedChoices = await enhancedChoiceEngine.generateChoices(context);
      const limitedChoices = generatedChoices.slice(0, maxChoices);
      setChoices(limitedChoices);

      if (groupByCategory) {
        const groups = createCategoryGroups(limitedChoices);
        setCategoryGroups(groups);
      }
    } catch (error) {
      console.error('Failed to load choices:', error);
    } finally {
      setIsLoading(false);
    }
  }, [context, maxChoices, groupByCategory]);

  const createCategoryGroups = (choices: EnhancedChoice[]): CategoryGroup[] => {
    const groups = new Map<EnhancedChoice['category'], EnhancedChoice[]>();
    
    choices.forEach(choice => {
      const existing = groups.get(choice.category) || [];
      existing.push(choice);
      groups.set(choice.category, existing);
    });

    return Array.from(groups.entries()).map(([category, choices]) => ({
      category,
      label: CATEGORY_CONFIG[category].label,
      icon: CATEGORY_CONFIG[category].icon,
      color: CATEGORY_CONFIG[category].color,
      choices: choices.sort((a, b) => {
        if (a.priority !== b.priority) {
          const order = { high: 3, medium: 2, low: 1 };
          return order[b.priority] - order[a.priority];
        }
        return b.contextualRelevance - a.contextualRelevance;
      })
    }));
  };

  const handleChoiceClick = async (choice: EnhancedChoice) => {
    if (disabled) return;

    setSelectedChoice(choice);
    
    try {
      const result = await enhancedChoiceEngine.processChoiceSelection(choice, context);
      
      // Load follow-up choices if available
      if (showFollowUps && result.followUpChoices.length > 0) {
        setFollowUpChoices(result.followUpChoices);
        setExpandedFollowUps([choice.id]);
      }

      // Trigger choice selection callback
      onChoiceSelect(choice, result.promptSuggestion);

      // Handle flow transitions
      if (result.nextFlow && onFlowStart) {
        onFlowStart(result.nextFlow);
      }
    } catch (error) {
      console.error('Failed to process choice selection:', error);
    }
  };

  const handleFollowUpClick = (followUp: EnhancedChoice) => {
    if (disabled) return;
    onChoiceSelect(followUp);
    setFollowUpChoices([]);
    setExpandedFollowUps([]);
  };

  const toggleFollowUps = (choiceId: string) => {
    setExpandedFollowUps(prev => 
      prev.includes(choiceId) 
        ? prev.filter(id => id !== choiceId)
        : [...prev, choiceId]
    );
  };

  const renderChoiceCard = (choice: EnhancedChoice, isFollowUp = false) => {
    const isSelected = selectedChoice?.id === choice.id;
    const isExpanded = expandedFollowUps.includes(choice.id);
    
    return (
      <motion.div
        key={choice.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className={cn(
          "group cursor-pointer transition-all duration-200",
          isFollowUp && "ml-4 border-l-2 border-muted pl-4"
        )}
      >
        <Card 
          className={cn(
            "h-full transition-all duration-200 hover:shadow-md",
            isSelected && "border-primary shadow-lg",
            isFollowUp && "bg-muted/30",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          onClick={() => isFollowUp ? handleFollowUpClick(choice) : handleChoiceClick(choice)}
        >
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <div className={cn(
                  "p-2 rounded-lg text-white text-lg flex items-center justify-center bg-gradient-to-r",
                  choice.color ? `bg-[${choice.color}]` : "from-gray-400 to-gray-600"
                )}>
                  {choice.icon || <Brain className="h-4 w-4" />}
                </div>
                <div className="flex-1">
                  <CardTitle className="text-sm font-medium leading-tight">
                    {choice.text}
                  </CardTitle>
                  {choice.description && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {choice.description}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-1 ml-2">
                {/* Priority Badge */}
                <Badge 
                  variant={choice.priority === 'high' ? 'default' : 'secondary'}
                  className="text-xs"
                >
                  {choice.priority === 'high' && <Star className="h-3 w-3 mr-1" />}
                  {choice.priority}
                </Badge>

                {/* Relevance Score */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center">
                        <div className={cn(
                          "h-2 w-8 rounded-full bg-gradient-to-r",
                          choice.contextualRelevance > 0.8 ? "from-green-400 to-green-600" :
                          choice.contextualRelevance > 0.6 ? "from-yellow-400 to-yellow-600" :
                          "from-gray-400 to-gray-600"
                        )}>
                          <div 
                            className="h-full bg-white rounded-full transition-all duration-300"
                            style={{ width: `${choice.contextualRelevance * 100}%` }}
                          />
                        </div>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Relevance: {Math.round(choice.contextualRelevance * 100)}%</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </CardHeader>

          <CardContent className="pt-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {/* Code Generation Indicator */}
                {choice.codeGenerationTrigger && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Badge variant="outline" className="text-xs">
                          <Code className="h-3 w-3 mr-1" />
                          Code Gen
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Will generate {choice.codeGenerationTrigger.type} code</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {/* Estimated Duration */}
                {choice.estimatedDuration && (
                  <Badge variant="outline" className="text-xs">
                    <Clock className="h-3 w-3 mr-1" />
                    {choice.estimatedDuration}
                  </Badge>
                )}

                {/* Difficulty Level */}
                {choice.difficulty && (
                  <Badge variant="outline" className="text-xs">
                    {choice.difficulty === 'advanced' && <AlertCircle className="h-3 w-3 mr-1" />}
                    {choice.difficulty === 'intermediate' && <Info className="h-3 w-3 mr-1" />}
                    {choice.difficulty === 'beginner' && <CheckCircle className="h-3 w-3 mr-1" />}
                    {choice.difficulty}
                  </Badge>
                )}
              </div>

              {/* Follow-up Indicator */}
              {!isFollowUp && choice.followUpSuggestions.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleFollowUps(choice.id);
                  }}
                  className="text-xs"
                >
                  {choice.followUpSuggestions.length} follow-ups
                  <ChevronRight 
                    className={cn(
                      "h-3 w-3 ml-1 transition-transform",
                      isExpanded && "rotate-90"
                    )}
                  />
                </Button>
              )}

              {/* Next Flow Indicator */}
              {choice.nextFlow && (
                <Badge variant="outline" className="text-xs">
                  <ArrowRight className="h-3 w-3 mr-1" />
                  Flow
                </Badge>
              )}
            </div>

            {/* Follow-up Suggestions */}
            {!isFollowUp && showFollowUps && (
              <Collapsible open={isExpanded}>
                <CollapsibleContent className="mt-3 space-y-2">
                  <AnimatePresence>
                    {followUpChoices
                      .filter(followUp => followUp.id.includes(choice.id))
                      .map(followUp => (
                        <motion.div
                          key={followUp.id}
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                        >
                          {renderChoiceCard(followUp, true)}
                        </motion.div>
                      ))}
                  </AnimatePresence>
                </CollapsibleContent>
              </Collapsible>
            )}
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  const renderCategoryGroup = (group: CategoryGroup) => (
    <div key={group.category} className="space-y-3">
      <div className="flex items-center gap-3 px-1">
        <div className={cn(
          "p-2 rounded-lg text-white bg-gradient-to-r",
          group.color
        )}>
          <group.icon className="h-4 w-4" />
        </div>
        <div>
          <h3 className="font-medium text-sm">{group.label}</h3>
          <p className="text-xs text-muted-foreground">
            {CATEGORY_CONFIG[group.category].description}
          </p>
        </div>
        <Badge variant="secondary" className="ml-auto">
          {group.choices.length}
        </Badge>
      </div>
      
      <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2">
        <AnimatePresence>
          {group.choices.map(choice => renderChoiceCard(choice))}
        </AnimatePresence>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 animate-pulse" />
            <span className="text-sm text-muted-foreground">
              Generating contextual choices...
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Flow Progress */}
      {flowProgress && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div>
                <h4 className="font-medium text-sm">{flowProgress.flowName}</h4>
                <p className="text-xs text-muted-foreground">
                  Step {flowProgress.currentStepIndex + 1} of {flowProgress.totalSteps}
                </p>
              </div>
              <div className="flex items-center gap-2">
                {onFlowAction && (
                  <>
                    {flowProgress.canGoBack && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onFlowAction('back')}
                      >
                        Back
                      </Button>
                    )}
                    {flowProgress.canSkip && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onFlowAction('skip')}
                      >
                        Skip
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onFlowAction('pause')}
                    >
                      <Pause className="h-3 w-3" />
                    </Button>
                  </>
                )}
              </div>
            </div>
            
            <div className="w-full bg-white rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${flowProgress.progressPercentage}%` }}
              />
            </div>
            
            <div className="flex justify-between text-xs text-muted-foreground mt-2">
              <span>{flowProgress.completedSteps} completed</span>
              <span>{flowProgress.estimatedTimeRemaining} remaining</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Available Flows */}
      {!activeFlowId && onFlowStart && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm flex items-center gap-2">
              <Play className="h-4 w-4" />
              Guided Workflows
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2 md:grid-cols-2">
              {flowEngine.getAvailableFlows().slice(0, 4).map(flow => (
                <Button
                  key={flow.id}
                  variant="outline"
                  size="sm"
                  onClick={() => onFlowStart(flow.id)}
                  className="justify-start h-auto p-3"
                >
                  <div className="text-left">
                    <div className="font-medium text-xs">{flow.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {flow.description}
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Choice Groups */}
      {groupByCategory ? (
        <div className="space-y-6">
          {categoryGroups.map(group => renderCategoryGroup(group))}
        </div>
      ) : (
        <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2">
          <AnimatePresence>
            {choices.map(choice => renderChoiceCard(choice))}
          </AnimatePresence>
        </div>
      )}

      {/* Empty State */}
      {choices.length === 0 && !isLoading && (
        <Card className="text-center py-8">
          <CardContent>
            <Brain className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="font-medium text-sm mb-2">No choices available</h3>
            <p className="text-xs text-muted-foreground">
              Continue the conversation to generate contextual choices
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};