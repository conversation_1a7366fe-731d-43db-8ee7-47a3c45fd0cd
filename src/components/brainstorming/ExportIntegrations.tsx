import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { PremiumProgress } from '@/components/ui/premium-progress';
import { Badge } from '@/components/ui/badge';
import { 
  Share2, 
  Settings, 
  Check, 
  X, 
  Loader2,
  ExternalLink,
  Key,
  Server,
  FolderOpen
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { 
  JiraIntegration, 
  JiraConfig,
  saveJiraConfig,
  loadJiraConfig
} from '@/lib/integrations/jira-integration';
import { 
  AsanaIntegration, 
  AsanaConfig,
  saveAsanaConfig,
  loadAsanaConfig
} from '@/lib/integrations/asana-integration';
import { 
  TrelloIntegration, 
  TrelloConfig,
  saveTrelloConfig,
  loadTrelloConfig
} from '@/lib/integrations/trello-integration';
import { invoke } from '@tauri-apps/api/core';

interface ExportIntegrationsProps {
  sessionId: string;
  onExportComplete?: () => void;
}

type IntegrationType = 'jira' | 'asana' | 'trello';

interface ExportProgress {
  current: number;
  total: number;
  status: string;
}

export const ExportIntegrations: React.FC<ExportIntegrationsProps> = ({
  sessionId,
  onExportComplete,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<IntegrationType>('jira');
  const [isExporting, setIsExporting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [exportProgress, setExportProgress] = useState<ExportProgress | null>(null);
  const [configuredIntegrations, setConfiguredIntegrations] = useState<string[]>([]);
  
  // Configuration states
  const [jiraConfig, setJiraConfig] = useState<JiraConfig>({
    domain: '',
    email: '',
    apiToken: '',
    projectKey: '',
  });
  
  const [asanaConfig, setAsanaConfig] = useState<AsanaConfig>({
    accessToken: '',
    workspaceGid: '',
    projectGid: '',
  });
  
  const [trelloConfig, setTrelloConfig] = useState<TrelloConfig>({
    apiKey: '',
    apiToken: '',
    boardId: '',
  });

  const { toast } = useToast();
  const { getIdeasBySession, getCurrentSession, getClustersForSession } = useBrainstormStore();

  useEffect(() => {
    loadConfigurations();
  }, []);

  const loadConfigurations = async () => {
    try {
      const configured = await invoke<string[]>('list_configured_integrations');
      setConfiguredIntegrations(configured);

      // Load existing configurations
      const jira = await loadJiraConfig();
      if (jira) setJiraConfig(jira);

      const asana = await loadAsanaConfig();
      if (asana) setAsanaConfig(asana);

      const trello = await loadTrelloConfig();
      if (trello) setTrelloConfig(trello);
    } catch (error) {
      console.error('Failed to load configurations:', error);
    }
  };

  const testConnection = async (integration: IntegrationType) => {
    try {
      let result = false;
      
      switch (integration) {
        case 'jira':
          const jira = new JiraIntegration(jiraConfig);
          result = await jira.testConnection();
          break;
        case 'asana':
          const asana = new AsanaIntegration(asanaConfig);
          result = await asana.testConnection();
          break;
        case 'trello':
          const trello = new TrelloIntegration(trelloConfig);
          result = await trello.testConnection();
          break;
      }

      toast({
        message: result ? 'Connection successful!' : 'Connection failed',
        type: result ? 'success' : 'error',
      });
    } catch (error) {
      toast({
        message: 'Connection test failed',
        type: 'error',
      });
    }
  };

  const saveConfiguration = async (integration: IntegrationType) => {
    setIsSaving(true);
    try {
      switch (integration) {
        case 'jira':
          await saveJiraConfig(jiraConfig);
          break;
        case 'asana':
          await saveAsanaConfig(asanaConfig);
          break;
        case 'trello':
          await saveTrelloConfig(trelloConfig);
          break;
      }

      await loadConfigurations();
      toast({
        message: 'Configuration saved successfully',
        type: 'success',
      });
    } catch (error) {
      toast({
        message: 'Failed to save configuration',
        type: 'error',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const exportToIntegration = async (integration: IntegrationType) => {
    setIsExporting(true);
    setExportProgress({ current: 0, total: 0, status: 'Preparing export...' });

    try {
      const ideas = getIdeasBySession(sessionId);
      const clusters = getClustersForSession(sessionId);
      
      setExportProgress({ current: 0, total: ideas.length, status: 'Exporting ideas...' });

      switch (integration) {
        case 'jira':
          const jira = new JiraIntegration(jiraConfig);
          const jiraResults = await jira.batchCreateIssues(
            ideas,
            (current, total) => setExportProgress({
              current,
              total,
              status: `Creating Jira issues... (${current}/${total})`,
            })
          );
          
          toast({
            message: `Created ${jiraResults.success.length} Jira issues`,
            type: 'success',
          });
          break;

        case 'asana':
          const asana = new AsanaIntegration(asanaConfig);
          const asanaResults = await asana.batchCreateTasks(
            ideas,
            (current, total) => setExportProgress({
              current,
              total,
              status: `Creating Asana tasks... (${current}/${total})`,
            })
          );
          
          toast({
            message: `Created ${asanaResults.success.length} Asana tasks`,
            type: 'success',
          });
          break;

        case 'trello':
          const trello = new TrelloIntegration(trelloConfig);
          const lists = await trello.getBoardLists();
          const defaultListId = lists[0]?.id;
          
          if (!defaultListId) {
            throw new Error('No lists found in Trello board');
          }

          const trelloResults = await trello.batchCreateCards(
            ideas,
            defaultListId,
            (current, total) => setExportProgress({
              current,
              total,
              status: `Creating Trello cards... (${current}/${total})`,
            })
          );
          
          toast({
            message: `Created ${trelloResults.success.length} Trello cards`,
            type: 'success',
          });
          break;
      }

      if (onExportComplete) {
        onExportComplete();
      }
    } catch (error) {
      toast({
        message: `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        type: 'error',
      });
    } finally {
      setIsExporting(false);
      setExportProgress(null);
    }
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(true)}
      >
        <Share2 className="h-4 w-4 mr-2" />
        Export to Project Management
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Export to Project Management Tools</DialogTitle>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as IntegrationType)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="jira">
                Jira
                {configuredIntegrations.includes('jira') && (
                  <Check className="h-3 w-3 ml-1" />
                )}
              </TabsTrigger>
              <TabsTrigger value="asana">
                Asana
                {configuredIntegrations.includes('asana') && (
                  <Check className="h-3 w-3 ml-1" />
                )}
              </TabsTrigger>
              <TabsTrigger value="trello">
                Trello
                {configuredIntegrations.includes('trello') && (
                  <Check className="h-3 w-3 ml-1" />
                )}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="jira" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="jira-domain">Jira Domain</Label>
                  <Input
                    id="jira-domain"
                    placeholder="yourcompany.atlassian.net"
                    value={jiraConfig.domain}
                    onChange={(e) => setJiraConfig({ ...jiraConfig, domain: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="jira-email">Email</Label>
                  <Input
                    id="jira-email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={jiraConfig.email}
                    onChange={(e) => setJiraConfig({ ...jiraConfig, email: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="jira-token">API Token</Label>
                  <Input
                    id="jira-token"
                    type="password"
                    placeholder="Your Jira API token"
                    value={jiraConfig.apiToken}
                    onChange={(e) => setJiraConfig({ ...jiraConfig, apiToken: e.target.value })}
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    <a
                      href="https://id.atlassian.com/manage-profile/security/api-tokens"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="underline"
                    >
                      Create an API token <ExternalLink className="h-3 w-3 inline" />
                    </a>
                  </p>
                </div>
                <div>
                  <Label htmlFor="jira-project">Project Key</Label>
                  <Input
                    id="jira-project"
                    placeholder="PROJ"
                    value={jiraConfig.projectKey}
                    onChange={(e) => setJiraConfig({ ...jiraConfig, projectKey: e.target.value })}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="asana" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="asana-token">Personal Access Token</Label>
                  <Input
                    id="asana-token"
                    type="password"
                    placeholder="Your Asana access token"
                    value={asanaConfig.accessToken}
                    onChange={(e) => setAsanaConfig({ ...asanaConfig, accessToken: e.target.value })}
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    <a
                      href="https://app.asana.com/0/my-apps"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="underline"
                    >
                      Create a personal access token <ExternalLink className="h-3 w-3 inline" />
                    </a>
                  </p>
                </div>
                <div>
                  <Label htmlFor="asana-workspace">Workspace GID</Label>
                  <Input
                    id="asana-workspace"
                    placeholder="1234567890"
                    value={asanaConfig.workspaceGid}
                    onChange={(e) => setAsanaConfig({ ...asanaConfig, workspaceGid: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="asana-project">Project GID</Label>
                  <Input
                    id="asana-project"
                    placeholder="1234567890"
                    value={asanaConfig.projectGid}
                    onChange={(e) => setAsanaConfig({ ...asanaConfig, projectGid: e.target.value })}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="trello" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="trello-key">API Key</Label>
                  <Input
                    id="trello-key"
                    placeholder="Your Trello API key"
                    value={trelloConfig.apiKey}
                    onChange={(e) => setTrelloConfig({ ...trelloConfig, apiKey: e.target.value })}
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    <a
                      href="https://trello.com/app-key"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="underline"
                    >
                      Get your API key <ExternalLink className="h-3 w-3 inline" />
                    </a>
                  </p>
                </div>
                <div>
                  <Label htmlFor="trello-token">API Token</Label>
                  <Input
                    id="trello-token"
                    type="password"
                    placeholder="Your Trello API token"
                    value={trelloConfig.apiToken}
                    onChange={(e) => setTrelloConfig({ ...trelloConfig, apiToken: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="trello-board">Board ID</Label>
                  <Input
                    id="trello-board"
                    placeholder="Board ID"
                    value={trelloConfig.boardId}
                    onChange={(e) => setTrelloConfig({ ...trelloConfig, boardId: e.target.value })}
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    You can find the board ID in the board URL
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {exportProgress && (
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">{exportProgress.status}</p>
              <Progress
                value={(exportProgress.current / exportProgress.total) * 100}
                className="w-full"
              />
            </div>
          )}

          <DialogFooter className="flex justify-between">
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => testConnection(activeTab)}
                disabled={isExporting}
              >
                Test Connection
              </Button>
              <Button
                variant="outline"
                onClick={() => saveConfiguration(activeTab)}
                disabled={isSaving || isExporting}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Key className="h-4 w-4 mr-2" />
                    Save Config
                  </>
                )}
              </Button>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setIsOpen(false)}
                disabled={isExporting}
              >
                Cancel
              </Button>
              <Button
                onClick={() => exportToIntegration(activeTab)}
                disabled={isExporting || !configuredIntegrations.includes(activeTab)}
              >
                {isExporting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Exporting...
                  </>
                ) : (
                  <>
                    <Share2 className="h-4 w-4 mr-2" />
                    Export
                  </>
                )}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};