/**
 * Brainstorming Components Index
 * 
 * This file exports all components related to the enhanced brainstorming system.
 */

// Export VirtualizedIdeaManager as IdeaManager for backward compatibility
export { VirtualizedIdeaManager as IdeaManager } from './VirtualizedIdeaManager';
// Also export with original name
export { VirtualizedIdeaManager } from './VirtualizedIdeaManager';
// Legacy components removed - now using OptimizedBrainstormingSystem
export { MindMapView } from './MindMapView';
export { KanbanView } from './KanbanView';
export { MatrixView } from './MatrixView';
export { TemplateSelector } from './TemplateSelector';
export { TaskGenerator } from './TaskGenerator';
export { MultiModalInput } from './MultiModalInput';
export { PersonaManager } from './PersonaManager';
export { PersonaCreationInterface } from './PersonaCreationInterface';
export { PersonaSwitcher } from './PersonaSwitcher';
// Legacy session components removed - now integrated into OptimizedBrainstormingSystem
export { MemoryInsights } from './MemoryInsights';
export { SearchInterface } from './SearchInterface';
export { VoiceInterface } from './VoiceInterface';
export { CollaborationPanel } from './CollaborationPanel';
export { BrainstormErrorBoundary } from './BrainstormErrorBoundary';
export { ClusterManager } from './ClusterManager';
export { ExportManager } from './ExportManager';

// Export new integrated core components
export { BrainstormingLayoutSystem } from './core/BrainstormingLayoutSystem';
export { EnhancedBrainstormingDashboard } from './core/EnhancedBrainstormingDashboard';
export { ComprehensiveBrainstormingHub } from './core/ComprehensiveBrainstormingHub';

// Export new optimized system components
export { OptimizedBrainstormingSystem } from './core/OptimizedBrainstormingSystem';
export { AnimationOptimizer } from './performance/AnimationOptimizer';
export { AccessibilityEnhancer } from './accessibility/AccessibilityEnhancer';
export { BundleOptimizer } from './performance/BundleOptimizer';

// Export OptimizedBrainstormingSystem as BrainstormingHub for TabContent compatibility
export { OptimizedBrainstormingSystem as BrainstormingHub } from './core/OptimizedBrainstormingSystem';

// Export utility functions
export { formatIdeaStatus, getStatusVariant } from './utils/idea-utils';