/**
 * Flow Manager Component
 * 
 * <PERSON>ages brainstorming flow progression, step navigation, and flow state.
 * Provides UI for flow visualization, progress tracking, and step management.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Play,
  Pause,
  SkipForward,
  ChevronLeft,
  ChevronRight,
  Clock,
  CheckCircle,
  Circle,
  AlertCircle,
  Info,
  Target,
  MapPin,
  Flag,
  BarChart3,
  Users,
  Settings,
  X,
  Maximize2,
  Minimize2,
  RefreshCw
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PremiumProgress } from '@/components/ui/premium-progress';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';

import { cn } from '@/lib/utils';
import { 
  flowEngine,
  BrainstormingFlow,
  FlowStep,
  FlowStepResult
} from '@/lib/brainstorming/flowEngine';
import { EnhancedChoice, ChoiceContext } from '@/lib/brainstorming/enhancedChoiceEngine';
import { useBrainstormStore } from '@/stores/brainstormStore';

interface FlowManagerProps {
  sessionId: string;
  context: ChoiceContext;
  onFlowComplete?: (flowResult: any) => void;
  onStepChange?: (step: FlowStep, stepIndex: number) => void;
  onChoiceRequired?: (step: FlowStep, choices: EnhancedChoice[]) => void;
  className?: string;
  isMinimized?: boolean;
  onToggleMinimize?: () => void;
}

interface FlowState {
  activeFlow: BrainstormingFlow | null;
  currentStep: FlowStep | null;
  progress: any | null;
  isPlaying: boolean;
  isPaused: boolean;
  stepHistory: FlowStepResult[];
}

const STEP_TYPE_CONFIG = {
  'choice-selection': {
    icon: Target,
    color: 'from-blue-400 to-blue-600',
    label: 'Make Choice'
  },
  'template-application': {
    icon: Settings,
    color: 'from-purple-400 to-purple-600',
    label: 'Apply Template'
  },
  'code-generation': {
    icon: Settings,
    color: 'from-green-400 to-green-600',
    label: 'Generate Code'
  },
  'idea-capture': {
    icon: Target,
    color: 'from-yellow-400 to-yellow-600',
    label: 'Capture Ideas'
  },
  'analysis': {
    icon: BarChart3,
    color: 'from-cyan-400 to-cyan-600',
    label: 'Analysis'
  },
  'review': {
    icon: CheckCircle,
    color: 'from-gray-400 to-gray-600',
    label: 'Review'
  }
};

export const FlowManager: React.FC<FlowManagerProps> = ({
  sessionId,
  context,
  onFlowComplete,
  onStepChange,
  onChoiceRequired,
  className,
  isMinimized = false,
  onToggleMinimize
}) => {
  const [flowState, setFlowState] = useState<FlowState>({
    activeFlow: null,
    currentStep: null,
    progress: null,
    isPlaying: false,
    isPaused: false,
    stepHistory: []
  });
  
  const [availableFlows, setAvailableFlows] = useState<BrainstormingFlow[]>([]);
  const [selectedFlowId, setSelectedFlowId] = useState<string | null>(null);
  const [showFlowSelector, setShowFlowSelector] = useState(false);
  const [stepInputs, setStepInputs] = useState<Record<string, any>>({});
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const { updateSession } = useBrainstormStore();

  // Load available flows
  useEffect(() => {
    const flows = flowEngine.getAvailableFlows();
    setAvailableFlows(flows);
  }, []);

  // Monitor flow progress
  useEffect(() => {
    const updateProgress = () => {
      const progress = flowEngine.getFlowProgress(sessionId);
      const currentStep = flowEngine.getCurrentStep(sessionId);
      
      setFlowState(prev => ({
        ...prev,
        progress,
        currentStep,
        isPlaying: !!progress && !progress.isPaused
      }));

      if (onStepChange && currentStep && progress) {
        onStepChange(currentStep, progress.currentStepIndex);
      }
    };

    updateProgress();
    const interval = setInterval(updateProgress, 1000);
    
    return () => clearInterval(interval);
  }, [sessionId, onStepChange]);

  const startFlow = useCallback(async (flowId: string) => {
    try {
      const flowState = await flowEngine.startFlow(flowId, sessionId, context);
      const flow = flowEngine.getFlowById(flowId);
      
      setFlowState(prev => ({
        ...prev,
        activeFlow: flow,
        isPlaying: true,
        isPaused: false
      }));
      
      setSelectedFlowId(flowId);
      setShowFlowSelector(false);

      // Update session metadata
      updateSession(sessionId, {
        metadata: {
          ...context.session.metadata,
          activeFlow: flowId,
          flowStartedAt: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to start flow:', error);
    }
  }, [sessionId, context, updateSession]);

  const executeStep = useCallback(async (
    stepId: string,
    choice?: EnhancedChoice,
    inputs?: Record<string, any>
  ) => {
    try {
      setValidationErrors([]);
      
      const result = await flowEngine.executeStep(
        sessionId,
        stepId,
        choice,
        inputs || stepInputs
      );

      setFlowState(prev => ({
        ...prev,
        stepHistory: [...prev.stepHistory, result]
      }));

      // Clear inputs after successful execution
      setStepInputs({});
      
    } catch (error) {
      console.error('Failed to execute step:', error);
      if (error instanceof Error) {
        setValidationErrors([error.message]);
      }
    }
  }, [sessionId, stepInputs]);

  const pauseFlow = useCallback(() => {
    flowEngine.pauseFlow(sessionId);
    setFlowState(prev => ({ ...prev, isPaused: true, isPlaying: false }));
  }, [sessionId]);

  const resumeFlow = useCallback(() => {
    flowEngine.resumeFlow(sessionId);
    setFlowState(prev => ({ ...prev, isPaused: false, isPlaying: true }));
  }, [sessionId]);

  const skipStep = useCallback(async () => {
    try {
      await flowEngine.skipCurrentStep(sessionId);
    } catch (error) {
      console.error('Failed to skip step:', error);
    }
  }, [sessionId]);

  const goBack = useCallback(async () => {
    try {
      await flowEngine.goToPreviousStep(sessionId);
    } catch (error) {
      console.error('Failed to go back:', error);
    }
  }, [sessionId]);

  const stopFlow = useCallback(() => {
    // Implementation would depend on adding stop functionality to flowEngine
    setFlowState({
      activeFlow: null,
      currentStep: null,
      progress: null,
      isPlaying: false,
      isPaused: false,
      stepHistory: []
    });
    setSelectedFlowId(null);
  }, []);

  const renderFlowSelector = () => (
    <Dialog open={showFlowSelector} onOpenChange={setShowFlowSelector}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Choose a Guided Workflow</DialogTitle>
          <DialogDescription>
            Select a structured brainstorming flow to guide your session
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 md:grid-cols-2">
          {availableFlows.map(flow => (
            <Card 
              key={flow.id}
              className="cursor-pointer hover:shadow-md transition-all duration-200"
              onClick={() => startFlow(flow.id)}
            >
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "p-2 rounded-lg text-white bg-gradient-to-r",
                    flow.color || "from-gray-400 to-gray-600"
                  )}>
                    {flow.icon || <MapPin className="h-4 w-4" />}
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-sm">{flow.name}</CardTitle>
                    <p className="text-xs text-muted-foreground mt-1">
                      {flow.description}
                    </p>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {flow.estimatedDuration}
                    </div>
                    <Badge variant="outline">
                      {flow.steps.length} steps
                    </Badge>
                    <Badge variant="secondary">
                      {flow.difficulty}
                    </Badge>
                  </div>
                  <Badge variant="outline">
                    {flow.category}
                  </Badge>
                </div>
                
                <div className="flex flex-wrap gap-1 mt-2">
                  {flow.tags.slice(0, 3).map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );

  const renderStepTimeline = () => {
    if (!flowState.activeFlow) return null;

    return (
      <div className="space-y-2">
        {flowState.activeFlow.steps.map((step, index) => {
          const isCompleted = index < (flowState.progress?.currentStepIndex || 0);
          const isCurrent = index === (flowState.progress?.currentStepIndex || 0);
          const config = STEP_TYPE_CONFIG[step.type];
          
          return (
            <div
              key={step.id}
              className={cn(
                "flex items-center gap-3 p-2 rounded-lg transition-all duration-200",
                isCurrent && "bg-accent",
                isCompleted && "opacity-60"
              )}
            >
              <div className={cn(
                "p-1.5 rounded-full text-white text-xs bg-gradient-to-r",
                isCompleted ? "from-green-400 to-green-600" :
                isCurrent ? config.color :
                "from-gray-300 to-gray-400"
              )}>
                {isCompleted ? (
                  <CheckCircle className="h-3 w-3" />
                ) : isCurrent ? (
                  <config.icon className="h-3 w-3" />
                ) : (
                  <Circle className="h-3 w-3" />
                )}
              </div>
              
              <div className="flex-1">
                <div className="text-xs font-medium">{step.name}</div>
                <div className="text-xs text-muted-foreground">
                  {step.description}
                </div>
              </div>
              
              <div className="text-xs text-muted-foreground">
                {step.estimatedTime}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderCurrentStep = () => {
    if (!flowState.currentStep) return null;

    const config = STEP_TYPE_CONFIG[flowState.currentStep.type];
    
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className={cn(
              "p-2 rounded-lg text-white bg-gradient-to-r",
              config.color
            )}>
              <config.icon className="h-4 w-4" />
            </div>
            <div className="flex-1">
              <CardTitle className="text-sm">{flowState.currentStep.name}</CardTitle>
              <p className="text-xs text-muted-foreground">
                {flowState.currentStep.description}
              </p>
            </div>
            <Badge variant="outline">{config.label}</Badge>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            {/* Step Prompt */}
            <div className="p-3 bg-muted/50 rounded-lg">
              <p className="text-sm">{flowState.currentStep.prompt}</p>
            </div>

            {/* Instructions */}
            {flowState.currentStep.instructions && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                  <p className="text-sm text-blue-800">
                    {flowState.currentStep.instructions}
                  </p>
                </div>
              </div>
            )}

            {/* Examples */}
            {flowState.currentStep.examples && flowState.currentStep.examples.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-xs font-medium text-muted-foreground">Examples:</h4>
                <div className="space-y-1">
                  {flowState.currentStep.examples.map((example, index) => (
                    <div key={index} className="p-2 bg-muted/30 rounded text-xs">
                      {example}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Required Inputs */}
            {flowState.currentStep.requiredInputs && flowState.currentStep.requiredInputs.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-xs font-medium">Required Information:</h4>
                {flowState.currentStep.requiredInputs.map(input => (
                  <div key={input} className="space-y-1">
                    <label className="text-xs text-muted-foreground">{input}</label>
                    <input
                      type="text"
                      className="w-full p-2 border rounded text-xs"
                      value={stepInputs[input] || ''}
                      onChange={(e) => setStepInputs(prev => ({
                        ...prev,
                        [input]: e.target.value
                      }))}
                    />
                  </div>
                ))}
              </div>
            )}

            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-red-600 mt-0.5" />
                  <div className="space-y-1">
                    {validationErrors.map((error, index) => (
                      <p key={index} className="text-sm text-red-800">{error}</p>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Step Actions */}
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                onClick={() => executeStep(flowState.currentStep.id)}
                disabled={!flowState.isPlaying}
              >
                Complete Step
              </Button>
              
              {flowState.currentStep.allowSkip && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={skipStep}
                >
                  <SkipForward className="h-3 w-3 mr-1" />
                  Skip
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (isMinimized) {
    return (
      <Card className={cn("w-64", className)}>
        <CardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {flowState.activeFlow ? (
                <>
                  <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="text-xs font-medium">
                    {flowState.activeFlow.name}
                  </span>
                </>
              ) : (
                <span className="text-xs text-muted-foreground">No active flow</span>
              )}
            </div>
            
            <div className="flex items-center gap-1">
              {flowState.isPlaying && (
                <Button variant="ghost" size="icon" onClick={pauseFlow} className="h-6 w-6">
                  <Pause className="h-3 w-3" />
                </Button>
              )}
              {onToggleMinimize && (
                <Button variant="ghost" size="icon" onClick={onToggleMinimize} className="h-6 w-6">
                  <Maximize2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
          
          {flowState.progress && (
            <div className="mt-2">
              <PremiumProgress value={flowState.progress.progressPercentage} className="h-1" />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>Step {flowState.progress.currentStepIndex + 1}</span>
                <span>{Math.round(flowState.progress.progressPercentage)}%</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Flow Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Flow Manager
            </CardTitle>
            
            <div className="flex items-center gap-2">
              {!flowState.activeFlow ? (
                <Button
                  size="sm"
                  onClick={() => setShowFlowSelector(true)}
                >
                  <Play className="h-3 w-3 mr-1" />
                  Start Flow
                </Button>
              ) : (
                <>
                  {flowState.isPlaying ? (
                    <Button variant="outline" size="sm" onClick={pauseFlow}>
                      <Pause className="h-3 w-3 mr-1" />
                      Pause
                    </Button>
                  ) : (
                    <Button size="sm" onClick={resumeFlow}>
                      <Play className="h-3 w-3 mr-1" />
                      Resume
                    </Button>
                  )}
                  
                  {flowState.progress?.canGoBack && (
                    <Button variant="outline" size="sm" onClick={goBack}>
                      <ChevronLeft className="h-3 w-3 mr-1" />
                      Back
                    </Button>
                  )}
                  
                  <Button variant="outline" size="sm" onClick={stopFlow}>
                    <X className="h-3 w-3 mr-1" />
                    Stop
                  </Button>
                </>
              )}
              
              {onToggleMinimize && (
                <Button variant="ghost" size="icon" onClick={onToggleMinimize}>
                  <Minimize2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        {flowState.progress && (
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium">{flowState.activeFlow?.name}</span>
                <span className="text-muted-foreground">
                  {flowState.progress.estimatedTimeRemaining} remaining
                </span>
              </div>
              
              <PremiumProgress value={flowState.progress.progressPercentage} />
              
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>
                  Step {flowState.progress.currentStepIndex + 1} of {flowState.progress.totalSteps}
                </span>
                <span>{Math.round(flowState.progress.progressPercentage)}% complete</span>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Flow Content */}
      {flowState.activeFlow && (
        <Tabs defaultValue="current" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="current">Current Step</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
          </TabsList>
          
          <TabsContent value="current" className="space-y-4">
            {renderCurrentStep()}
          </TabsContent>
          
          <TabsContent value="timeline" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Flow Progress</CardTitle>
              </CardHeader>
              <CardContent>
                {renderStepTimeline()}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* Flow Selector Dialog */}
      {renderFlowSelector()}
    </div>
  );
};