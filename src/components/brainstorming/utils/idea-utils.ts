/**
 * Utility functions for idea management
 */

import { IdeaStatus } from '@/types/brainstorm';

/**
 * Format idea status for display
 */
export function formatIdeaStatus(status: IdeaStatus): string {
  switch (status) {
    case 'active':
      return 'Active';
    case 'in-progress':
      return 'In Progress';
    case 'validated':
      return 'Validated';
    case 'archived':
      return 'Archived';
    default:
      return status;
  }
}

/**
 * Get badge variant for status
 */
export function getStatusVariant(status: IdeaStatus): 'default' | 'secondary' | 'outline' | 'destructive' {
  switch (status) {
    case 'active':
      return 'default';
    case 'in-progress':
      return 'secondary';
    case 'validated':
      return 'outline';
    case 'archived':
      return 'destructive';
    default:
      return 'default';
  }
}