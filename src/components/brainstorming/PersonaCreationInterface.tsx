/**
 * Persona Creation Interface
 * 
 * Comprehensive form for creating and editing brainstorming personas
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  User,
  <PERSON>,
  Palette,
  Settings,
  Save,
  X,
  Plus,
  Minus,
  Star,
  Target,
  Lightbulb,
  Shield,
  Eye,
  Users,
  Search,
  Heart,
  Zap,
  TrendingUp,
  BarChart3
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { cn } from '@/lib/utils';
import { usePersonaStore } from '@/stores/personaStore';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';
import { 
  BrainstormingPersona, 
  PersonaRole, 
  PersonaCharacteristics,
  PersonaPromptStyle,
  DEFAULT_PERSONA_PROFILES 
} from '@/types/persona';

interface PersonaCreationInterfaceProps {
  persona?: BrainstormingPersona;
  onSave: (persona: Partial<BrainstormingPersona>) => void;
  onCancel: () => void;
  className?: string;
}

const ROLE_COLORS = {
  [PersonaRole.CREATIVE_THINKER]: '#8B5CF6',
  [PersonaRole.DEVILS_ADVOCATE]: '#EF4444',
  [PersonaRole.PRACTICAL_ANALYST]: '#10B981',
  [PersonaRole.VISIONARY]: '#F59E0B',
  [PersonaRole.FACILITATOR]: '#06B6D4',
  [PersonaRole.RESEARCHER]: '#6366F1',
  [PersonaRole.IMPLEMENTER]: '#84CC16',
  [PersonaRole.CUSTOMER_ADVOCATE]: '#EC4899',
  [PersonaRole.RISK_ASSESSOR]: '#DC2626',
  [PersonaRole.INNOVATOR]: '#7C3AED',
  [PersonaRole.SYNTHESIZER]: '#059669',
  [PersonaRole.QUESTIONER]: '#0891B2'
};

const ROLE_ICONS = {
  [PersonaRole.CREATIVE_THINKER]: Lightbulb,
  [PersonaRole.DEVILS_ADVOCATE]: Shield,
  [PersonaRole.PRACTICAL_ANALYST]: BarChart3,
  [PersonaRole.VISIONARY]: Eye,
  [PersonaRole.FACILITATOR]: Users,
  [PersonaRole.RESEARCHER]: Search,
  [PersonaRole.IMPLEMENTER]: Target,
  [PersonaRole.CUSTOMER_ADVOCATE]: Heart,
  [PersonaRole.RISK_ASSESSOR]: Shield,
  [PersonaRole.INNOVATOR]: Zap,
  [PersonaRole.SYNTHESIZER]: TrendingUp,
  [PersonaRole.QUESTIONER]: Brain
};

export const PersonaCreationInterface: React.FC<PersonaCreationInterfaceProps> = ({
  persona,
  onSave,
  onCancel,
  className
}) => {
  const { theme } = useBrainstormingTheme();
  const { getPersonaTemplates } = usePersonaStore();

  const [formData, setFormData] = useState<Partial<BrainstormingPersona>>({
    name: persona?.name || '',
    description: persona?.description || '',
    role: persona?.role || PersonaRole.CREATIVE_THINKER,
    characteristics: persona?.characteristics || DEFAULT_PERSONA_PROFILES[PersonaRole.CREATIVE_THINKER],
    promptStyle: persona?.promptStyle || {
      questionTypes: [],
      responsePatterns: [],
      encouragementPhrases: [],
      challengingPhrases: [],
      transitionWords: []
    },
    color: persona?.color || ROLE_COLORS[PersonaRole.CREATIVE_THINKER],
    tags: persona?.tags || [],
    examples: persona?.examples || []
  });

  const [newTag, setNewTag] = useState('');
  const [newExample, setNewExample] = useState('');
  const [currentTab, setCurrentTab] = useState('basic');

  const handleRoleChange = (role: PersonaRole) => {
    setFormData(prev => ({
      ...prev,
      role,
      characteristics: DEFAULT_PERSONA_PROFILES[role],
      color: ROLE_COLORS[role]
    }));
  };

  const handleCharacteristicChange = (key: keyof PersonaCharacteristics, value: number) => {
    setFormData(prev => ({
      ...prev,
      characteristics: {
        ...prev.characteristics!,
        [key]: value
      }
    }));
  };

  const handlePromptStyleChange = (key: keyof PersonaPromptStyle, value: string[]) => {
    setFormData(prev => ({
      ...prev,
      promptStyle: {
        ...prev.promptStyle!,
        [key]: value
      }
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(t => t !== tag) || []
    }));
  };

  const addExample = () => {
    if (newExample.trim()) {
      setFormData(prev => ({
        ...prev,
        examples: [...(prev.examples || []), newExample.trim()]
      }));
      setNewExample('');
    }
  };

  const removeExample = (index: number) => {
    setFormData(prev => ({
      ...prev,
      examples: prev.examples?.filter((_, i) => i !== index) || []
    }));
  };

  const handleSave = () => {
    if (!formData.name || !formData.description) {
      return;
    }
    onSave(formData);
  };

  const isValid = formData.name && formData.description;

  return (
    <div className={cn("max-w-4xl mx-auto", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" style={{ color: theme.colors.primary }} />
            {persona ? 'Edit Persona' : 'Create New Persona'}
          </CardTitle>
          <CardDescription>
            Define the characteristics and behavior of your brainstorming persona
          </CardDescription>
        </CardHeader>

        <CardContent>
          <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="characteristics">Characteristics</TabsTrigger>
              <TabsTrigger value="prompts">Prompt Style</TabsTrigger>
              <TabsTrigger value="examples">Examples</TabsTrigger>
            </TabsList>

            {/* Basic Information */}
            <TabsContent value="basic" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Persona Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Creative Thinker"
                    />
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe this persona's approach and perspective..."
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="role">Role</Label>
                    <Select value={formData.role} onValueChange={handleRoleChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a role" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(PersonaRole).map(role => {
                          const Icon = ROLE_ICONS[role];
                          return (
                            <SelectItem key={role} value={role}>
                              <div className="flex items-center gap-2">
                                <Icon className="w-4 h-4" style={{ color: ROLE_COLORS[role] }} />
                                {role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="color">Color</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="color"
                        type="color"
                        value={formData.color}
                        onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                        className="w-16 h-10"
                      />
                      <Input
                        value={formData.color}
                        onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                        placeholder="#8B5CF6"
                        className="flex-1"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label>Tags</Label>
                    <div className="flex gap-2 mb-2">
                      <Input
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="Add a tag..."
                        onKeyPress={(e) => e.key === 'Enter' && addTag()}
                      />
                      <Button onClick={addTag} size="sm">
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {formData.tags?.map(tag => (
                        <Badge key={tag} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                          {tag}
                          <X className="w-3 h-3 ml-1" />
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="p-4 rounded-lg" style={{ backgroundColor: theme.colors.primary + '10' }}>
                    <h4 className="font-medium mb-2" style={{ color: theme.colors.text }}>
                      Preview
                    </h4>
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-10 h-10 rounded-full flex items-center justify-center"
                        style={{ backgroundColor: formData.color + '20' }}
                      >
                        {formData.role && React.createElement(ROLE_ICONS[formData.role], {
                          className: "w-5 h-5",
                          style: { color: formData.color }
                        })}
                      </div>
                      <div>
                        <h5 className="font-medium" style={{ color: theme.colors.text }}>
                          {formData.name || 'Persona Name'}
                        </h5>
                        <p className="text-sm" style={{ color: theme.colors.textSecondary }}>
                          {formData.description || 'Persona description...'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Characteristics */}
            <TabsContent value="characteristics" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(formData.characteristics || {}).map(([key, value]) => (
                  <div key={key} className="space-y-2">
                    <div className="flex justify-between">
                      <Label className="capitalize">
                        {key.replace('_', ' ')}
                      </Label>
                      <span className="text-sm font-medium">{value}%</span>
                    </div>
                    <Slider
                      value={[value]}
                      onValueChange={([newValue]) => handleCharacteristicChange(key as keyof PersonaCharacteristics, newValue)}
                      max={100}
                      step={5}
                      className="w-full"
                    />
                  </div>
                ))}
              </div>

              <div className="mt-6 p-4 rounded-lg" style={{ backgroundColor: theme.colors.accent + '10' }}>
                <h4 className="font-medium mb-2" style={{ color: theme.colors.text }}>
                  Characteristic Profile
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Object.entries(formData.characteristics || {}).map(([key, value]) => (
                    <div key={key} className="text-center">
                      <div className="text-lg font-bold" style={{ color: theme.colors.accent }}>
                        {value}%
                      </div>
                      <div className="text-xs capitalize" style={{ color: theme.colors.textSecondary }}>
                        {key.replace('_', ' ')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* Prompt Style */}
            <TabsContent value="prompts" className="space-y-6">
              <div className="text-center py-8" style={{ color: theme.colors.textSecondary }}>
                Prompt style configuration coming soon...
              </div>
            </TabsContent>

            {/* Examples */}
            <TabsContent value="examples" className="space-y-6">
              <div>
                <Label>Example Responses</Label>
                <div className="flex gap-2 mb-4">
                  <Textarea
                    value={newExample}
                    onChange={(e) => setNewExample(e.target.value)}
                    placeholder="Add an example of how this persona might respond..."
                    rows={2}
                  />
                  <Button onClick={addExample} size="sm" className="self-start">
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
                
                <div className="space-y-2">
                  {formData.examples?.map((example, index) => (
                    <div key={index} className="flex items-start gap-2 p-3 rounded-lg border">
                      <div className="flex-1 text-sm">{example}</div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeExample(index)}
                        className="text-red-500 hover:text-red-600"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <Separator className="my-6" />

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onCancel}>
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={!isValid}>
              <Save className="w-4 h-4 mr-2" />
              {persona ? 'Update Persona' : 'Create Persona'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
