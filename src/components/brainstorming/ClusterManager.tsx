/**
 * Cluster Manager Component
 * 
 * Manages automatic and manual clustering of ideas with AI-powered suggestions
 */

import React, { useState, useEffect } from 'react';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { IdeaCluster, Idea } from '@/types/brainstorm';
import { clusteringService, ClusteringSuggestion } from '@/lib/clustering-service';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sparkles,
  GitBranch,
  Plus,
  Edit,
  Trash2,
  MoreVertical,
  Shuffle,
  Target,
  Loader2,
  Check,
  X,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/useToast';

interface ClusterManagerProps {
  sessionId: string;
  className?: string;
}

export const ClusterManager: React.FC<ClusterManagerProps> = ({
  sessionId,
  className,
}) => {
  const { toast } = useToast();
  const {
    ideas,
    clusters,
    getIdeasBySession,
    createCluster,
    updateCluster,
    deleteCluster,
    addIdeaToCluster,
    removeIdeaFromCluster,
    settings,
    updateSettings,
  } = useBrainstormStore();

  const [suggestions, setSuggestions] = useState<ClusteringSuggestion[]>([]);
  const [isGeneratingSuggestions, setIsGeneratingSuggestions] = useState(false);
  const [selectedSuggestions, setSelectedSuggestions] = useState<Set<string>>(new Set());
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newClusterName, setNewClusterName] = useState('');
  const [newClusterTheme, setNewClusterTheme] = useState('');
  const [selectedIdeas, setSelectedIdeas] = useState<Set<string>>(new Set());
  const [clusterThreshold, setClusterThreshold] = useState(settings.clusterThreshold);

  const sessionIdeas = getIdeasBySession(sessionId);
  const sessionClusters = Object.values(clusters).filter(cluster =>
    cluster.ideaIds.some(ideaId => sessionIdeas.find(idea => idea.id === ideaId))
  );

  // Generate AI clustering suggestions
  const generateSuggestions = async () => {
    if (sessionIdeas.length < 2) {
      toast({
        title: 'Not Enough Ideas',
        description: 'Need at least 2 ideas to generate clusters.',
        variant: 'destructive',
      });
      return;
    }

    setIsGeneratingSuggestions(true);
    try {
      const clusterSuggestions = await clusteringService.suggestClustersWithAI(sessionIdeas, {
        minClusterSize: 2,
        similarityThreshold: clusterThreshold,
        maxClusters: 8,
      });
      
      setSuggestions(clusterSuggestions);
      toast({
        title: 'Clustering Suggestions Generated',
        description: `Found ${clusterSuggestions.length} potential clusters.`,
      });
    } catch (error) {
      console.error('Failed to generate suggestions:', error);
      toast({
        title: 'Clustering Failed',
        description: 'Could not generate clustering suggestions.',
        variant: 'destructive',
      });
    } finally {
      setIsGeneratingSuggestions(false);
    }
  };

  // Apply selected suggestions
  const applySuggestions = () => {
    selectedSuggestions.forEach(suggestionId => {
      const suggestion = suggestions.find(s => s.clusterId === suggestionId);
      if (suggestion) {
        createCluster(suggestion.name, suggestion.theme, suggestion.ideaIds);
      }
    });

    setSuggestions([]);
    setSelectedSuggestions(new Set());
    toast({
      title: 'Clusters Created',
      description: `Applied ${selectedSuggestions.size} clustering suggestions.`,
    });
  };

  // Create manual cluster
  const createManualCluster = () => {
    if (!newClusterName.trim() || selectedIdeas.size === 0) return;

    createCluster(newClusterName, newClusterTheme, Array.from(selectedIdeas));
    
    setShowCreateDialog(false);
    setNewClusterName('');
    setNewClusterTheme('');
    setSelectedIdeas(new Set());
    
    toast({
      title: 'Cluster Created',
      description: `Created cluster "${newClusterName}" with ${selectedIdeas.size} ideas.`,
    });
  };

  // Toggle suggestion selection
  const toggleSuggestion = (suggestionId: string) => {
    const newSelected = new Set(selectedSuggestions);
    if (newSelected.has(suggestionId)) {
      newSelected.delete(suggestionId);
    } else {
      newSelected.add(suggestionId);
    }
    setSelectedSuggestions(newSelected);
  };

  // Toggle idea selection for manual clustering
  const toggleIdeaSelection = (ideaId: string) => {
    const newSelected = new Set(selectedIdeas);
    if (newSelected.has(ideaId)) {
      newSelected.delete(ideaId);
    } else {
      newSelected.add(ideaId);
    }
    setSelectedIdeas(newSelected);
  };

  // Update clustering threshold
  const handleThresholdChange = (value: number[]) => {
    const newThreshold = value[0];
    setClusterThreshold(newThreshold);
    updateSettings({ clusterThreshold: newThreshold });
  };

  // Get unclustered ideas
  const unclusteredIdeas = sessionIdeas.filter(idea => !idea.cluster);

  return (
    <div className={cn("cluster-manager space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <GitBranch className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Idea Clusters</h3>
          <Badge variant="secondary">{sessionClusters.length}</Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={generateSuggestions}
            disabled={isGeneratingSuggestions || sessionIdeas.length < 2}
          >
            {isGeneratingSuggestions ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Sparkles className="h-4 w-4 mr-2" />
            )}
            AI Suggestions
          </Button>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Create Cluster
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Manual Cluster</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="cluster-name">Cluster Name</Label>
                  <Input
                    id="cluster-name"
                    value={newClusterName}
                    onChange={(e) => setNewClusterName(e.target.value)}
                    placeholder="e.g., User Experience Ideas"
                  />
                </div>
                <div>
                  <Label htmlFor="cluster-theme">Theme (Optional)</Label>
                  <Input
                    id="cluster-theme"
                    value={newClusterTheme}
                    onChange={(e) => setNewClusterTheme(e.target.value)}
                    placeholder="e.g., Ideas focused on improving user experience"
                  />
                </div>
                <div>
                  <Label>Select Ideas ({selectedIdeas.size} selected)</Label>
                  <ScrollArea className="h-32 border rounded p-2">
                    {unclusteredIdeas.map(idea => (
                      <div
                        key={idea.id}
                        className="flex items-center space-x-2 py-1"
                      >
                        <input
                          type="checkbox"
                          checked={selectedIdeas.has(idea.id)}
                          onChange={() => toggleIdeaSelection(idea.id)}
                          className="rounded"
                        />
                        <span className="text-sm">{idea.content}</span>
                      </div>
                    ))}
                  </ScrollArea>
                </div>
                <div className="flex gap-2 justify-end">
                  <Button
                    variant="outline"
                    onClick={() => setShowCreateDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={createManualCluster}
                    disabled={!newClusterName.trim() || selectedIdeas.size === 0}
                  >
                    Create Cluster
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Settings */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">Clustering Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm">Auto-cluster new ideas</Label>
            <Switch
              checked={settings.autoCluster}
              onCheckedChange={(checked) => updateSettings({ autoCluster: checked })}
            />
          </div>
          <div>
            <Label className="text-sm">
              Similarity Threshold: {clusterThreshold.toFixed(1)}
            </Label>
            <Slider
              value={[clusterThreshold]}
              onValueChange={handleThresholdChange}
              min={0.3}
              max={0.9}
              step={0.1}
              className="mt-2"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Higher values create fewer, more similar clusters
            </p>
          </div>
        </CardContent>
      </Card>

      {/* AI Suggestions */}
      {suggestions.length > 0 && (
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm">AI Clustering Suggestions</CardTitle>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setSuggestions([])}
                >
                  <X className="h-4 w-4 mr-1" />
                  Dismiss
                </Button>
                <Button
                  size="sm"
                  onClick={applySuggestions}
                  disabled={selectedSuggestions.size === 0}
                >
                  <Check className="h-4 w-4 mr-1" />
                  Apply Selected ({selectedSuggestions.size})
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {suggestions.map(suggestion => (
                <div
                  key={suggestion.clusterId}
                  className={cn(
                    "p-3 border rounded-lg cursor-pointer transition-colors",
                    selectedSuggestions.has(suggestion.clusterId)
                      ? "border-primary bg-primary/5"
                      : "hover:border-primary/50"
                  )}
                  onClick={() => toggleSuggestion(suggestion.clusterId)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm">{suggestion.name}</h4>
                        <Badge variant="outline" className="text-xs">
                          {suggestion.ideaIds.length} ideas
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {Math.round(suggestion.confidence * 100)}% confidence
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground mb-2">
                        {suggestion.theme}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {suggestion.rationale}
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      checked={selectedSuggestions.has(suggestion.clusterId)}
                      onChange={() => toggleSuggestion(suggestion.clusterId)}
                      className="rounded"
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Existing Clusters */}
      <div className="space-y-3">
        {sessionClusters.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <GitBranch className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm text-muted-foreground">
                No clusters yet. Create clusters to organize your ideas.
              </p>
            </CardContent>
          </Card>
        ) : (
          sessionClusters.map(cluster => {
            const clusterIdeas = sessionIdeas.filter(idea => 
              cluster.ideaIds.includes(idea.id)
            );

            return (
              <Card key={cluster.id}>
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: cluster.color }}
                      />
                      <CardTitle className="text-sm">{cluster.name}</CardTitle>
                      <Badge variant="outline" className="text-xs">
                        {clusterIdeas.length} ideas
                      </Badge>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Cluster
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => deleteCluster(cluster.id)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Cluster
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <CardDescription className="text-xs">
                    {cluster.theme}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {clusterIdeas.map(idea => (
                      <div
                        key={idea.id}
                        className="flex items-center justify-between p-2 bg-muted/50 rounded text-xs"
                      >
                        <span className="flex-1">{idea.content}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => removeIdeaFromCluster(idea.id)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* Unclustered Ideas */}
      {unclusteredIdeas.length > 0 && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Unclustered Ideas</CardTitle>
            <CardDescription className="text-xs">
              {unclusteredIdeas.length} ideas not assigned to any cluster
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {unclusteredIdeas.map(idea => (
                <div
                  key={idea.id}
                  className="flex items-center justify-between p-2 border rounded text-xs"
                >
                  <span className="flex-1">{idea.content}</span>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                        <Plus className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      {sessionClusters.map(cluster => (
                        <DropdownMenuItem
                          key={cluster.id}
                          onClick={() => addIdeaToCluster(idea.id, cluster.id)}
                        >
                          <div
                            className="w-3 h-3 rounded mr-2"
                            style={{ backgroundColor: cluster.color }}
                          />
                          {cluster.name}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};