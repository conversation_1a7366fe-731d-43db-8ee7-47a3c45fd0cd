import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Eye,
  EyeOff,
  Volume2,
  VolumeX,
  Keyboard,
  MousePointer,
  Contrast,
  Type,
  Zap,
  Settings,
  HelpCircle,
  AlertCircle,
  CheckCircle,
  X
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

interface AccessibilitySettings {
  highContrast: boolean;
  reducedMotion: boolean;
  largeText: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
  focusIndicators: boolean;
  colorBlindFriendly: boolean;
  fontSize: number;
  lineHeight: number;
  letterSpacing: number;
  colorScheme: 'default' | 'high-contrast' | 'dark' | 'light';
  soundEnabled: boolean;
  voiceAnnouncements: boolean;
}

interface FocusManager {
  currentFocus: HTMLElement | null;
  focusHistory: HTMLElement[];
  trapFocus: boolean;
  restoreOnEscape: boolean;
}

interface AccessibilityEnhancerProps {
  children: React.ReactNode;
  enableA11yPanel?: boolean;
  autoDetectPreferences?: boolean;
  className?: string;
}

/**
 * Accessibility Enhancer
 * Implements: Task 8.1 (ARIA labels), Task 8.2 (Keyboard navigation), Task 8.3 (High contrast), Task 8.4 (Focus management)
 */
export const AccessibilityEnhancer: React.FC<AccessibilityEnhancerProps> = ({
  children,
  enableA11yPanel = true,
  autoDetectPreferences = true,
  className
}) => {
  const [settings, setSettings] = useState<AccessibilitySettings>({
    highContrast: false,
    reducedMotion: false,
    largeText: false,
    screenReader: false,
    keyboardNavigation: true,
    focusIndicators: true,
    colorBlindFriendly: false,
    fontSize: 16,
    lineHeight: 1.5,
    letterSpacing: 0,
    colorScheme: 'default',
    soundEnabled: true,
    voiceAnnouncements: false
  });

  const [showA11yPanel, setShowA11yPanel] = useState(false);
  const [focusManager, setFocusManager] = useState<FocusManager>({
    currentFocus: null,
    focusHistory: [],
    trapFocus: false,
    restoreOnEscape: false
  });

  const [announcements, setAnnouncements] = useState<string[]>([]);
  const announcementRef = useRef<HTMLDivElement>(null);
  const focusableElementsRef = useRef<HTMLElement[]>([]);

  // Auto-detect user preferences
  useEffect(() => {
    if (!autoDetectPreferences) return;

    const detectPreferences = () => {
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
      const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;

      setSettings(prev => ({
        ...prev,
        reducedMotion: prefersReducedMotion,
        highContrast: prefersHighContrast,
        colorScheme: prefersDarkMode ? 'dark' : 'default'
      }));
    };

    detectPreferences();

    // Listen for preference changes
    const mediaQueries = [
      window.matchMedia('(prefers-reduced-motion: reduce)'),
      window.matchMedia('(prefers-contrast: high)'),
      window.matchMedia('(prefers-color-scheme: dark)')
    ];

    mediaQueries.forEach(mq => mq.addEventListener('change', detectPreferences));

    return () => {
      mediaQueries.forEach(mq => mq.removeEventListener('change', detectPreferences));
    };
  }, [autoDetectPreferences]);

  // Apply accessibility settings to document
  useEffect(() => {
    const root = document.documentElement;
    
    // Apply CSS custom properties
    root.style.setProperty('--a11y-font-size', `${settings.fontSize}px`);
    root.style.setProperty('--a11y-line-height', settings.lineHeight.toString());
    root.style.setProperty('--a11y-letter-spacing', `${settings.letterSpacing}px`);

    // Apply classes
    root.classList.toggle('high-contrast', settings.highContrast);
    root.classList.toggle('reduced-motion', settings.reducedMotion);
    root.classList.toggle('large-text', settings.largeText);
    root.classList.toggle('color-blind-friendly', settings.colorBlindFriendly);
    root.classList.toggle('focus-indicators', settings.focusIndicators);

    // Apply color scheme
    root.setAttribute('data-color-scheme', settings.colorScheme);

    return () => {
      // Cleanup on unmount
      root.classList.remove('high-contrast', 'reduced-motion', 'large-text', 'color-blind-friendly', 'focus-indicators');
      root.removeAttribute('data-color-scheme');
    };
  }, [settings]);

  // Keyboard navigation handler
  useEffect(() => {
    if (!settings.keyboardNavigation) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      const { key, altKey, ctrlKey, shiftKey } = event;

      // Accessibility panel toggle (Alt + A)
      if (altKey && key === 'a') {
        event.preventDefault();
        setShowA11yPanel(prev => !prev);
        announce('Accessibility panel toggled');
        return;
      }

      // Skip to main content (Alt + M)
      if (altKey && key === 'm') {
        event.preventDefault();
        const main = document.querySelector('main, [role="main"]') as HTMLElement;
        if (main) {
          main.focus();
          announce('Skipped to main content');
        }
        return;
      }

      // Focus management
      if (key === 'Tab') {
        handleTabNavigation(event);
      }

      if (key === 'Escape') {
        handleEscapeKey(event);
      }

      // Arrow key navigation for grids and lists
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(key)) {
        handleArrowNavigation(event);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [settings.keyboardNavigation]);

  // Tab navigation handler
  const handleTabNavigation = useCallback((event: KeyboardEvent) => {
    const focusableElements = getFocusableElements();
    const currentIndex = focusableElements.indexOf(document.activeElement as HTMLElement);

    if (focusManager.trapFocus) {
      event.preventDefault();
      
      let nextIndex;
      if (event.shiftKey) {
        nextIndex = currentIndex <= 0 ? focusableElements.length - 1 : currentIndex - 1;
      } else {
        nextIndex = currentIndex >= focusableElements.length - 1 ? 0 : currentIndex + 1;
      }
      
      focusableElements[nextIndex]?.focus();
    }

    // Update focus history
    setFocusManager(prev => ({
      ...prev,
      currentFocus: document.activeElement as HTMLElement,
      focusHistory: [...prev.focusHistory.slice(-10), document.activeElement as HTMLElement]
    }));
  }, [focusManager.trapFocus]);

  // Escape key handler
  const handleEscapeKey = useCallback((event: KeyboardEvent) => {
    if (showA11yPanel) {
      setShowA11yPanel(false);
      announce('Accessibility panel closed');
      return;
    }

    if (focusManager.restoreOnEscape && focusManager.focusHistory.length > 0) {
      const previousFocus = focusManager.focusHistory[focusManager.focusHistory.length - 2];
      if (previousFocus && document.contains(previousFocus)) {
        previousFocus.focus();
        announce('Focus restored');
      }
    }
  }, [showA11yPanel, focusManager]);

  // Arrow key navigation for grids and lists
  const handleArrowNavigation = useCallback((event: KeyboardEvent) => {
    const target = event.target as HTMLElement;
    const container = target.closest('[role="grid"], [role="listbox"], [role="menu"]');
    
    if (!container) return;

    event.preventDefault();
    
    const items = Array.from(container.querySelectorAll('[role="gridcell"], [role="option"], [role="menuitem"]')) as HTMLElement[];
    const currentIndex = items.indexOf(target);
    
    if (currentIndex === -1) return;

    let nextIndex = currentIndex;
    const columns = parseInt(container.getAttribute('data-columns') || '1');

    switch (event.key) {
      case 'ArrowUp':
        nextIndex = Math.max(0, currentIndex - columns);
        break;
      case 'ArrowDown':
        nextIndex = Math.min(items.length - 1, currentIndex + columns);
        break;
      case 'ArrowLeft':
        nextIndex = Math.max(0, currentIndex - 1);
        break;
      case 'ArrowRight':
        nextIndex = Math.min(items.length - 1, currentIndex + 1);
        break;
    }

    if (nextIndex !== currentIndex) {
      items[nextIndex]?.focus();
    }
  }, []);

  // Get all focusable elements
  const getFocusableElements = useCallback((): HTMLElement[] => {
    const selectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[role="button"]:not([disabled])',
      '[role="link"]:not([disabled])'
    ].join(', ');

    return Array.from(document.querySelectorAll(selectors)) as HTMLElement[];
  }, []);

  // Announce to screen readers
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!settings.voiceAnnouncements && !settings.screenReader) return;

    setAnnouncements(prev => [...prev.slice(-4), message]);

    // Create temporary announcement element
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }, [settings.voiceAnnouncements, settings.screenReader]);

  // Focus trap utilities
  const trapFocus = useCallback((container: HTMLElement) => {
    setFocusManager(prev => ({
      ...prev,
      trapFocus: true,
      restoreOnEscape: true
    }));

    const focusableElements = container.querySelectorAll(
      'button, input, select, textarea, a[href], [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>;

    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }
  }, []);

  const releaseFocus = useCallback(() => {
    setFocusManager(prev => ({
      ...prev,
      trapFocus: false,
      restoreOnEscape: false
    }));
  }, []);

  // Accessibility panel component
  const AccessibilityPanel: React.FC = () => (
    <AnimatePresence>
      {showA11yPanel && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className="fixed top-4 right-4 z-50 w-96 max-w-[90vw]"
        >
          <Card className="shadow-lg border-2">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Eye className="w-5 h-5" />
                  Accessibility Settings
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowA11yPanel(false)}
                  aria-label="Close accessibility panel"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="visual" className="space-y-4">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="visual">Visual</TabsTrigger>
                  <TabsTrigger value="interaction">Interaction</TabsTrigger>
                  <TabsTrigger value="audio">Audio</TabsTrigger>
                </TabsList>

                <TabsContent value="visual" className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="high-contrast">High Contrast</Label>
                      <Switch
                        id="high-contrast"
                        checked={settings.highContrast}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ ...prev, highContrast: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="large-text">Large Text</Label>
                      <Switch
                        id="large-text"
                        checked={settings.largeText}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ ...prev, largeText: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="color-blind">Color Blind Friendly</Label>
                      <Switch
                        id="color-blind"
                        checked={settings.colorBlindFriendly}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ ...prev, colorBlindFriendly: checked }))
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Font Size: {settings.fontSize}px</Label>
                      <Slider
                        value={[settings.fontSize]}
                        onValueChange={([value]) => 
                          setSettings(prev => ({ ...prev, fontSize: value }))
                        }
                        min={12}
                        max={24}
                        step={1}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Line Height: {settings.lineHeight}</Label>
                      <Slider
                        value={[settings.lineHeight]}
                        onValueChange={([value]) => 
                          setSettings(prev => ({ ...prev, lineHeight: value }))
                        }
                        min={1}
                        max={2}
                        step={0.1}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Color Scheme</Label>
                      <Select
                        value={settings.colorScheme}
                        onValueChange={(value: any) => 
                          setSettings(prev => ({ ...prev, colorScheme: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="default">Default</SelectItem>
                          <SelectItem value="high-contrast">High Contrast</SelectItem>
                          <SelectItem value="dark">Dark</SelectItem>
                          <SelectItem value="light">Light</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="interaction" className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="reduced-motion">Reduced Motion</Label>
                      <Switch
                        id="reduced-motion"
                        checked={settings.reducedMotion}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ ...prev, reducedMotion: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="keyboard-nav">Keyboard Navigation</Label>
                      <Switch
                        id="keyboard-nav"
                        checked={settings.keyboardNavigation}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ ...prev, keyboardNavigation: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="focus-indicators">Focus Indicators</Label>
                      <Switch
                        id="focus-indicators"
                        checked={settings.focusIndicators}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ ...prev, focusIndicators: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="screen-reader">Screen Reader Mode</Label>
                      <Switch
                        id="screen-reader"
                        checked={settings.screenReader}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ ...prev, screenReader: checked }))
                        }
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="audio" className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="sound-enabled">Sound Effects</Label>
                      <Switch
                        id="sound-enabled"
                        checked={settings.soundEnabled}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ ...prev, soundEnabled: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="voice-announcements">Voice Announcements</Label>
                      <Switch
                        id="voice-announcements"
                        checked={settings.voiceAnnouncements}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ ...prev, voiceAnnouncements: checked }))
                        }
                      />
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              <Separator className="my-4" />

              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={() => {
                    localStorage.setItem('accessibility-settings', JSON.stringify(settings));
                    announce('Settings saved');
                  }}
                >
                  Save Settings
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    const defaultSettings: AccessibilitySettings = {
                      highContrast: false,
                      reducedMotion: false,
                      largeText: false,
                      screenReader: false,
                      keyboardNavigation: true,
                      focusIndicators: true,
                      colorBlindFriendly: false,
                      fontSize: 16,
                      lineHeight: 1.5,
                      letterSpacing: 0,
                      colorScheme: 'default',
                      soundEnabled: true,
                      voiceAnnouncements: false
                    };
                    setSettings(defaultSettings);
                    announce('Settings reset to defaults');
                  }}
                >
                  Reset
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );

  // Screen reader announcements area
  const AnnouncementArea: React.FC = () => (
    <div
      ref={announcementRef}
      aria-live="polite"
      aria-atomic="true"
      className="sr-only"
    >
      {announcements.map((announcement, index) => (
        <div key={index}>{announcement}</div>
      ))}
    </div>
  );

  // Accessibility context
  const AccessibilityContext = React.createContext({
    settings,
    announce,
    trapFocus,
    releaseFocus,
    focusManager
  });

  return (
    <AccessibilityContext.Provider value={{
      settings,
      announce,
      trapFocus,
      releaseFocus,
      focusManager
    }}>
      <div className={cn("relative", className)}>
        {children}
        
        {enableA11yPanel && (
          <>
            <Button
              variant="outline"
              size="sm"
              className="fixed bottom-4 left-4 z-40"
              onClick={() => setShowA11yPanel(true)}
              aria-label="Open accessibility settings"
            >
              <Eye className="w-4 h-4" />
            </Button>
            <AccessibilityPanel />
          </>
        )}
        
        <AnnouncementArea />
      </div>
    </AccessibilityContext.Provider>
  );
};

// Hook to use accessibility utilities
export const useAccessibility = () => {
  const context = React.useContext(React.createContext({
    settings: {} as AccessibilitySettings,
    announce: () => {},
    trapFocus: () => {},
    releaseFocus: () => {},
    focusManager: {} as FocusManager
  }));
  
  return context;
};

export default AccessibilityEnhancer;