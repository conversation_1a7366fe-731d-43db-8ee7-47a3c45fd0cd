/**
 * Brainstorming Theme Provider
 * 
 * Provides distinct visual theming for brainstorming mode to clearly separate
 * it from Claude Code sessions. Uses warm, creative colors and different UI patterns.
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Brain, Palette, Lightbulb, Users, Target, Sparkles } from 'lucide-react';

export interface BrainstormingTheme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
  };
  gradients: {
    primary: string;
    secondary: string;
    background: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
  };
}

const BRAINSTORMING_THEMES: Record<string, BrainstormingTheme> = {
  creative: {
    name: 'Creative',
    colors: {
      primary: '#8B5CF6', // Purple
      secondary: '#06B6D4', // Cyan
      accent: '#F59E0B', // Amber
      background: '#FEFBFF', // Very light purple
      surface: '#FFFFFF',
      text: '#1F2937',
      textSecondary: '#6B7280',
      border: '#E5E7EB',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #8B5CF6 0%, #06B6D4 100%)',
      secondary: 'linear-gradient(135deg, #F59E0B 0%, #EF4444 100%)',
      background: 'linear-gradient(135deg, #FEFBFF 0%, #F3E8FF 50%, #DBEAFE 100%)'
    },
    shadows: {
      sm: '0 1px 3px 0 rgba(139, 92, 246, 0.1), 0 1px 2px 0 rgba(139, 92, 246, 0.06)',
      md: '0 4px 6px -1px rgba(139, 92, 246, 0.1), 0 2px 4px -1px rgba(139, 92, 246, 0.06)',
      lg: '0 10px 15px -3px rgba(139, 92, 246, 0.1), 0 4px 6px -2px rgba(139, 92, 246, 0.05)'
    },
    borderRadius: {
      sm: '0.5rem',
      md: '0.75rem',
      lg: '1rem'
    }
  },
  warm: {
    name: 'Warm',
    colors: {
      primary: '#F59E0B', // Amber
      secondary: '#EF4444', // Red
      accent: '#8B5CF6', // Purple
      background: '#FFFBEB', // Very light amber
      surface: '#FFFFFF',
      text: '#1F2937',
      textSecondary: '#6B7280',
      border: '#E5E7EB',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #F59E0B 0%, #EF4444 100%)',
      secondary: 'linear-gradient(135deg, #8B5CF6 0%, #06B6D4 100%)',
      background: 'linear-gradient(135deg, #FFFBEB 0%, #FEF3C7 50%, #FECACA 100%)'
    },
    shadows: {
      sm: '0 1px 3px 0 rgba(245, 158, 11, 0.1), 0 1px 2px 0 rgba(245, 158, 11, 0.06)',
      md: '0 4px 6px -1px rgba(245, 158, 11, 0.1), 0 2px 4px -1px rgba(245, 158, 11, 0.06)',
      lg: '0 10px 15px -3px rgba(245, 158, 11, 0.1), 0 4px 6px -2px rgba(245, 158, 11, 0.05)'
    },
    borderRadius: {
      sm: '0.5rem',
      md: '0.75rem',
      lg: '1rem'
    }
  },
  nature: {
    name: 'Nature',
    colors: {
      primary: '#10B981', // Emerald
      secondary: '#059669', // Emerald dark
      accent: '#F59E0B', // Amber
      background: '#F0FDF4', // Very light green
      surface: '#FFFFFF',
      text: '#1F2937',
      textSecondary: '#6B7280',
      border: '#E5E7EB',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
      secondary: 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)',
      background: 'linear-gradient(135deg, #F0FDF4 0%, #DCFCE7 50%, #BBF7D0 100%)'
    },
    shadows: {
      sm: '0 1px 3px 0 rgba(16, 185, 129, 0.1), 0 1px 2px 0 rgba(16, 185, 129, 0.06)',
      md: '0 4px 6px -1px rgba(16, 185, 129, 0.1), 0 2px 4px -1px rgba(16, 185, 129, 0.06)',
      lg: '0 10px 15px -3px rgba(16, 185, 129, 0.1), 0 4px 6px -2px rgba(16, 185, 129, 0.05)'
    },
    borderRadius: {
      sm: '0.5rem',
      md: '0.75rem',
      lg: '1rem'
    }
  },
  ocean: {
    name: 'Ocean',
    colors: {
      primary: '#0EA5E9', // Sky blue
      secondary: '#0284C7', // Blue
      accent: '#06B6D4', // Cyan
      background: '#F0F9FF', // Very light blue
      surface: '#FFFFFF',
      text: '#1E293B',
      textSecondary: '#64748B',
      border: '#E2E8F0',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #0EA5E9 0%, #0284C7 100%)',
      secondary: 'linear-gradient(135deg, #06B6D4 0%, #0891B2 100%)',
      background: 'linear-gradient(135deg, #F0F9FF 0%, #E0F2FE 50%, #BAE6FD 100%)'
    },
    shadows: {
      sm: '0 1px 3px 0 rgba(14, 165, 233, 0.1), 0 1px 2px 0 rgba(14, 165, 233, 0.06)',
      md: '0 4px 6px -1px rgba(14, 165, 233, 0.1), 0 2px 4px -1px rgba(14, 165, 233, 0.06)',
      lg: '0 10px 15px -3px rgba(14, 165, 233, 0.1), 0 4px 6px -2px rgba(14, 165, 233, 0.05)'
    },
    borderRadius: {
      sm: '0.5rem',
      md: '0.75rem',
      lg: '1rem'
    }
  },
  sunset: {
    name: 'Sunset',
    colors: {
      primary: '#F97316', // Orange
      secondary: '#EA580C', // Orange dark
      accent: '#FBBF24', // Yellow
      background: '#FFF7ED', // Very light orange
      surface: '#FFFFFF',
      text: '#1C1917',
      textSecondary: '#78716C',
      border: '#E7E5E4',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #F97316 0%, #EA580C 100%)',
      secondary: 'linear-gradient(135deg, #FBBF24 0%, #F59E0B 100%)',
      background: 'linear-gradient(135deg, #FFF7ED 0%, #FFEDD5 50%, #FED7AA 100%)'
    },
    shadows: {
      sm: '0 1px 3px 0 rgba(249, 115, 22, 0.1), 0 1px 2px 0 rgba(249, 115, 22, 0.06)',
      md: '0 4px 6px -1px rgba(249, 115, 22, 0.1), 0 2px 4px -1px rgba(249, 115, 22, 0.06)',
      lg: '0 10px 15px -3px rgba(249, 115, 22, 0.1), 0 4px 6px -2px rgba(249, 115, 22, 0.05)'
    },
    borderRadius: {
      sm: '0.5rem',
      md: '0.75rem',
      lg: '1rem'
    }
  },
  midnight: {
    name: 'Midnight',
    colors: {
      primary: '#8B5CF6', // Purple
      secondary: '#7C3AED', // Purple dark
      accent: '#06B6D4', // Cyan
      background: '#0F0F23', // Very dark blue
      surface: '#1E1E3F',
      text: '#F1F5F9',
      textSecondary: '#94A3B8',
      border: '#334155',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)',
      secondary: 'linear-gradient(135deg, #06B6D4 0%, #0891B2 100%)',
      background: 'linear-gradient(135deg, #0F0F23 0%, #1E1E3F 50%, #2D1B69 100%)'
    },
    shadows: {
      sm: '0 1px 3px 0 rgba(139, 92, 246, 0.3), 0 1px 2px 0 rgba(139, 92, 246, 0.2)',
      md: '0 4px 6px -1px rgba(139, 92, 246, 0.3), 0 2px 4px -1px rgba(139, 92, 246, 0.2)',
      lg: '0 10px 15px -3px rgba(139, 92, 246, 0.3), 0 4px 6px -2px rgba(139, 92, 246, 0.2)'
    },
    borderRadius: {
      sm: '0.5rem',
      md: '0.75rem',
      lg: '1rem'
    }
  }
};

interface BrainstormingThemeContextValue {
  theme: BrainstormingTheme;
  themeName: string;
  setTheme: (themeName: string) => void;
  availableThemes: string[];
}

const BrainstormingThemeContext = createContext<BrainstormingThemeContextValue | null>(null);

interface BrainstormingThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: string;
}

export const BrainstormingThemeProvider: React.FC<BrainstormingThemeProviderProps> = ({
  children,
  defaultTheme = 'creative'
}) => {
  const [themeName, setThemeName] = useState(defaultTheme);
  const theme = BRAINSTORMING_THEMES[themeName] || BRAINSTORMING_THEMES.creative;

  useEffect(() => {
    // Apply theme CSS variables to the document
    const root = document.documentElement;
    
    // Set brainstorming-specific CSS variables
    root.style.setProperty('--brainstorm-primary', theme.colors.primary);
    root.style.setProperty('--brainstorm-secondary', theme.colors.secondary);
    root.style.setProperty('--brainstorm-accent', theme.colors.accent);
    root.style.setProperty('--brainstorm-background', theme.colors.background);
    root.style.setProperty('--brainstorm-surface', theme.colors.surface);
    root.style.setProperty('--brainstorm-text', theme.colors.text);
    root.style.setProperty('--brainstorm-text-secondary', theme.colors.textSecondary);
    root.style.setProperty('--brainstorm-border', theme.colors.border);
    root.style.setProperty('--brainstorm-gradient-primary', theme.gradients.primary);
    root.style.setProperty('--brainstorm-gradient-secondary', theme.gradients.secondary);
    root.style.setProperty('--brainstorm-gradient-background', theme.gradients.background);
    root.style.setProperty('--brainstorm-shadow-sm', theme.shadows.sm);
    root.style.setProperty('--brainstorm-shadow-md', theme.shadows.md);
    root.style.setProperty('--brainstorm-shadow-lg', theme.shadows.lg);
    root.style.setProperty('--brainstorm-radius-sm', theme.borderRadius.sm);
    root.style.setProperty('--brainstorm-radius-md', theme.borderRadius.md);
    root.style.setProperty('--brainstorm-radius-lg', theme.borderRadius.lg);

    // Add brainstorming mode class to body
    document.body.classList.add('brainstorming-mode');
    
    return () => {
      // Clean up when leaving brainstorming mode
      document.body.classList.remove('brainstorming-mode');
    };
  }, [theme]);

  const contextValue: BrainstormingThemeContextValue = {
    theme,
    themeName,
    setTheme: setThemeName,
    availableThemes: Object.keys(BRAINSTORMING_THEMES)
  };

  return (
    <BrainstormingThemeContext.Provider value={contextValue}>
      <div 
        className="brainstorming-theme-root"
        style={{
          background: theme.gradients.background,
          minHeight: '100vh'
        }}
      >
        {children}
      </div>
    </BrainstormingThemeContext.Provider>
  );
};

// Default theme fallback for when used outside provider
const defaultTheme: BrainstormingTheme = {
  name: 'Creative',
  colors: {
    primary: '#8B5CF6',      // Violet
    secondary: '#06B6D4',    // Cyan
    accent: '#F59E0B',       // Amber
    background: '#FAFAFA',   // Very light gray
    surface: '#FFFFFF',      // White
    border: '#E5E7EB',       // Light gray
    text: '#111827',         // Dark gray
    textSecondary: '#6B7280', // Medium gray
    success: '#10B981',      // Emerald
    warning: '#F59E0B',      // Amber
    error: '#EF4444',        // Red
    muted: '#F9FAFB'         // Very light gray
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)'
  },
  gradients: {
    background: 'linear-gradient(135deg, #FAFAFA 0%, #F3F4F6 100%)',
    primary: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%)',
    secondary: 'linear-gradient(135deg, #06B6D4 0%, #0891B2 100%)'
  }
};

export const useBrainstormingTheme = (): BrainstormingThemeContextValue => {
  const context = useContext(BrainstormingThemeContext);
  
  // Provide fallback theme when used outside provider
  if (!context) {
    console.warn('useBrainstormingTheme used outside BrainstormingThemeProvider, using default theme');
    return {
      theme: defaultTheme,
      setTheme: () => {
        console.warn('setTheme called outside BrainstormingThemeProvider');
      }
    };
  }
  
  return context;
};

/**
 * Mode Indicator Component
 * Shows clear visual indication that user is in brainstorming mode
 */
interface ModeIndicatorProps {
  className?: string;
}

export const BrainstormingModeIndicator: React.FC<ModeIndicatorProps> = ({ className }) => {
  const { theme, themeName } = useBrainstormingTheme();

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9, x: 50 }}
      animate={{ opacity: 1, scale: 1, x: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className={`fixed top-4 right-4 z-50 ${className}`}
    >
      <div 
        className="flex items-center gap-3 px-4 py-3 rounded-2xl backdrop-blur-md border-2 relative overflow-hidden group"
        style={{
          background: `${theme.colors.surface}DD`,
          borderColor: theme.colors.primary,
          boxShadow: theme.shadows.lg
        }}
      >
        {/* Animated background gradient */}
        <motion.div
          className="absolute inset-0 opacity-20"
          style={{ background: theme.gradients.primary }}
          animate={{ 
            backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        {/* Brain icon with rotating animation */}
        <motion.div
          animate={{ 
            rotate: [0, 5, -5, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{ 
            duration: 2, 
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="relative z-10"
        >
          <Brain 
            className="w-5 h-5" 
            style={{ color: theme.colors.primary }}
          />
        </motion.div>
        
        <div className="flex flex-col relative z-10">
          <span 
            className="text-sm font-semibold leading-tight"
            style={{ color: theme.colors.text }}
          >
            Brainstorming Mode
          </span>
          <span 
            className="text-xs opacity-80 leading-tight"
            style={{ color: theme.colors.textSecondary }}
          >
            {BRAINSTORMING_THEMES[themeName].name} Theme
          </span>
        </div>
        
        {/* Sparkles with staggered animation */}
        <div className="flex gap-1 relative z-10">
          <motion.div
            animate={{ 
              scale: [1, 1.3, 1],
              rotate: [0, 180, 360]
            }}
            transition={{ 
              duration: 2, 
              repeat: Infinity,
              delay: 0
            }}
          >
            <Sparkles 
              className="w-3 h-3" 
              style={{ color: theme.colors.accent }}
            />
          </motion.div>
          
          <motion.div
            animate={{ 
              scale: [1, 1.2, 1],
              rotate: [0, -180, -360]
            }}
            transition={{ 
              duration: 2.5, 
              repeat: Infinity,
              delay: 0.5
            }}
          >
            <Sparkles 
              className="w-2 h-2" 
              style={{ color: theme.colors.secondary }}
            />
          </motion.div>
        </div>
        
        {/* Pulse effect on hover */}
        <motion.div
          className="absolute inset-0 rounded-2xl border-2 opacity-0 group-hover:opacity-100"
          style={{ borderColor: theme.colors.accent }}
          animate={{
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>
    </motion.div>
  );
};

/**
 * Session Breadcrumbs Component
 * Shows navigation context within brainstorming mode
 */
interface SessionBreadcrumbsProps {
  sessionTitle?: string;
  currentView?: string;
  className?: string;
}

export const BrainstormingBreadcrumbs: React.FC<SessionBreadcrumbsProps> = ({
  sessionTitle,
  currentView,
  className
}) => {
  const { theme } = useBrainstormingTheme();

  return (
    <div className={`flex items-center gap-2 text-sm ${className}`}>
      <Brain 
        className="w-4 h-4" 
        style={{ color: theme.colors.primary }}
      />
      <span style={{ color: theme.colors.text }}>Brainstorming Mode</span>
      
      {sessionTitle && (
        <>
          <span style={{ color: theme.colors.textSecondary }}>/</span>
          <span style={{ color: theme.colors.text }}>{sessionTitle}</span>
        </>
      )}
      
      {currentView && (
        <>
          <span style={{ color: theme.colors.textSecondary }}>/</span>
          <span style={{ color: theme.colors.textSecondary }}>{currentView}</span>
        </>
      )}
    </div>
  );
};

/**
 * Theme Selector Component
 * Allows users to switch between different brainstorming themes
 */
interface ThemeSelectorProps {
  className?: string;
}

export const BrainstormingThemeSelector: React.FC<ThemeSelectorProps> = ({ className }) => {
  const { themeName, setTheme, availableThemes, theme } = useBrainstormingTheme();
  const [showDropdown, setShowDropdown] = useState(false);

  return (
    <div className={`relative ${className}`}>
      <motion.button
        onClick={() => setShowDropdown(!showDropdown)}
        className="flex items-center gap-2 px-3 py-2 rounded-lg border backdrop-blur-sm transition-all duration-200 hover:shadow-md"
        style={{
          borderColor: theme.colors.border,
          backgroundColor: `${theme.colors.surface}CC`,
          color: theme.colors.text
        }}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Palette 
          className="w-4 h-4" 
          style={{ color: theme.colors.primary }}
        />
        <span className="text-sm font-medium">{BRAINSTORMING_THEMES[themeName].name}</span>
        <motion.div
          animate={{ rotate: showDropdown ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <div className="w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-current"></div>
        </motion.div>
      </motion.button>

      <AnimatePresence>
        {showDropdown && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.15 }}
            className="absolute top-full left-0 mt-2 p-2 rounded-lg border backdrop-blur-md z-50 min-w-[250px]"
            style={{
              borderColor: theme.colors.border,
              backgroundColor: `${theme.colors.surface}F0`,
              boxShadow: theme.shadows.lg
            }}
          >
            {availableThemes.map(name => {
              const themeData = BRAINSTORMING_THEMES[name];
              const isSelected = name === themeName;
              
              return (
                <motion.button
                  key={name}
                  onClick={() => {
                    setTheme(name);
                    setShowDropdown(false);
                  }}
                  className="w-full p-3 rounded-lg border-2 mb-2 last:mb-0 transition-all duration-200 group"
                  style={{
                    borderColor: isSelected ? themeData.colors.primary : 'transparent',
                    backgroundColor: isSelected ? `${themeData.colors.primary}10` : 'transparent'
                  }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center gap-3">
                    {/* Theme preview circles */}
                    <div className="flex gap-1">
                      <div
                        className="w-4 h-4 rounded-full border"
                        style={{ 
                          backgroundColor: themeData.colors.primary,
                          borderColor: theme.colors.border
                        }}
                      />
                      <div
                        className="w-4 h-4 rounded-full border"
                        style={{ 
                          backgroundColor: themeData.colors.secondary,
                          borderColor: theme.colors.border
                        }}
                      />
                      <div
                        className="w-4 h-4 rounded-full border"
                        style={{ 
                          backgroundColor: themeData.colors.accent,
                          borderColor: theme.colors.border
                        }}
                      />
                    </div>
                    
                    {/* Theme info */}
                    <div className="flex-1 text-left">
                      <div 
                        className="font-medium text-sm"
                        style={{ color: theme.colors.text }}
                      >
                        {themeData.name}
                      </div>
                      <div 
                        className="text-xs mt-1"
                        style={{ color: theme.colors.textSecondary }}
                      >
                        {name === 'creative' && 'Inspiring purple and cyan palette'}
                        {name === 'warm' && 'Energetic amber and red tones'}
                        {name === 'nature' && 'Calming green and earth colors'}
                        {name === 'ocean' && 'Refreshing blue ocean vibes'}
                        {name === 'sunset' && 'Vibrant orange sunset hues'}
                        {name === 'midnight' && 'Elegant dark mode theme'}
                      </div>
                    </div>
                    
                    {/* Selection indicator */}
                    {isSelected && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="w-5 h-5 rounded-full flex items-center justify-center"
                        style={{ backgroundColor: themeData.colors.primary }}
                      >
                        <div className="w-2 h-2 rounded-full bg-white" />
                      </motion.div>
                    )}
                  </div>
                </motion.button>
              );
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Click outside to close */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
};
