/**
 * Persona Manager Component
 *
 * Comprehensive interface for managing brainstorming personas
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  Plus,
  Edit,
  Trash2,
  Copy,
  Star,
  BarChart3,
  Settings,
  Search,
  Filter,
  Brain,
  Lightbulb,
  Shield,
  Target,
  Eye,
  Zap,
  Heart,
  TrendingUp,
  Clock,
  Award,
  Activity,
  X
} from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PremiumProgress } from '@/components/ui/premium-progress';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

import { cn } from '@/lib/utils';
import { usePersonaStore } from '@/stores/personaStore';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';
import { BrainstormingPersona, PersonaRole } from '@/types/persona';

interface PersonaManagerProps {
  className?: string;
  compactMode?: boolean;
  onPersonaSelect?: (persona: BrainstormingPersona) => void;
}

const ROLE_ICONS: Record<PersonaRole, React.ComponentType<any>> = {
  [PersonaRole.CREATIVE_THINKER]: Lightbulb,
  [PersonaRole.DEVILS_ADVOCATE]: Shield,
  [PersonaRole.PRACTICAL_ANALYST]: BarChart3,
  [PersonaRole.VISIONARY]: Eye,
  [PersonaRole.FACILITATOR]: Users,
  [PersonaRole.RESEARCHER]: Search,
  [PersonaRole.IMPLEMENTER]: Target,
  [PersonaRole.CUSTOMER_ADVOCATE]: Heart,
  [PersonaRole.RISK_ASSESSOR]: Shield,
  [PersonaRole.INNOVATOR]: Zap,
  [PersonaRole.SYNTHESIZER]: TrendingUp,
  [PersonaRole.QUESTIONER]: Brain
};

export const PersonaManager: React.FC<PersonaManagerProps> = ({
  className,
  compactMode = false,
  onPersonaSelect
}) => {
  const { theme } = useBrainstormingTheme();
  const {
    personas,
    activePersonaId,
    setActivePersona,
    createPersona,
    updatePersona,
    deletePersona,
    duplicatePersona,
    searchPersonas,
    getPersonasByRole,
    getTopPerformingPersonas
  } = usePersonaStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState<PersonaRole | 'all'>('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [selectedPersona, setSelectedPersona] = useState<BrainstormingPersona | null>(null);

  const filteredPersonas = React.useMemo(() => {
    let result = Object.values(personas);

    if (searchQuery) {
      result = searchPersonas(searchQuery);
    }

    if (selectedRole !== 'all') {
      result = result.filter(persona => persona.role === selectedRole);
    }

    return result.sort((a, b) => {
      // Active persona first, then by last used
      if (a.id === activePersonaId) return -1;
      if (b.id === activePersonaId) return 1;
      return new Date(b.metrics.lastUsed).getTime() - new Date(a.metrics.lastUsed).getTime();
    });
  }, [personas, searchQuery, selectedRole, activePersonaId, searchPersonas]);

  const handlePersonaClick = (persona: BrainstormingPersona) => {
    setActivePersona(persona.id);
    onPersonaSelect?.(persona);
  };

  const handleDuplicatePersona = (persona: BrainstormingPersona) => {
    duplicatePersona(persona.id);
  };

  const handleDeletePersona = (persona: BrainstormingPersona) => {
    if (persona.isCustom) {
      deletePersona(persona.id);
    }
  };

  const getCharacteristicColor = (value: number) => {
    if (value >= 80) return theme.colors.success;
    if (value >= 60) return theme.colors.warning;
    if (value >= 40) return theme.colors.primary;
    return theme.colors.textSecondary;
  };

  const PersonaCard: React.FC<{ persona: BrainstormingPersona }> = ({ persona }) => {
    const RoleIcon = ROLE_ICONS[persona.role];
    const isActive = persona.id === activePersonaId;

    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        layout
      >
        <Card
          className={cn(
            "cursor-pointer transition-all duration-200 hover:shadow-md",
            isActive && "ring-2 ring-offset-2",
            compactMode && "p-2"
          )}
          style={{
            ringColor: isActive ? theme.colors.primary : 'transparent',
            backgroundColor: isActive ? theme.colors.primary + '10' : 'transparent'
          }}
          onClick={() => handlePersonaClick(persona)}
        >
          <CardContent className={cn("p-4", compactMode && "p-3")}>
            <div className="flex items-start gap-3">
              <Avatar className="w-10 h-10">
                <AvatarFallback style={{ backgroundColor: persona.color + '20' }}>
                  <RoleIcon className="w-5 h-5" style={{ color: persona.color }} />
                </AvatarFallback>
              </Avatar>

              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="font-medium truncate" style={{ color: theme.colors.text }}>
                    {persona.name}
                  </h3>
                  <div className="flex items-center gap-1">
                    {isActive && (
                      <Badge variant="default" className="text-xs">
                        Active
                      </Badge>
                    )}
                    {!persona.isCustom && (
                      <Badge variant="outline" className="text-xs">
                        Default
                      </Badge>
                    )}
                  </div>
                </div>

                <p className="text-sm mb-2 line-clamp-2" style={{ color: theme.colors.textSecondary }}>
                  {persona.description}
                </p>

                {!compactMode && (
                  <>
                    <div className="flex flex-wrap gap-1 mb-2">
                      {persona.tags.slice(0, 3).map(tag => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {persona.tags.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{persona.tags.length - 3}
                        </Badge>
                      )}
                    </div>

                    <div className="grid grid-cols-3 gap-2 mb-2">
                      <div className="text-center">
                        <div className="text-sm font-medium" style={{ color: theme.colors.primary }}>
                          {persona.metrics.ideasGenerated}
                        </div>
                        <div className="text-xs" style={{ color: theme.colors.textSecondary }}>
                          Ideas
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm font-medium" style={{ color: theme.colors.primary }}>
                          {persona.metrics.sessionParticipation}
                        </div>
                        <div className="text-xs" style={{ color: theme.colors.textSecondary }}>
                          Sessions
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm font-medium" style={{ color: theme.colors.primary }}>
                          {persona.metrics.averageRating.toFixed(1)}
                        </div>
                        <div className="text-xs" style={{ color: theme.colors.textSecondary }}>
                          Rating
                        </div>
                      </div>
                    </div>

                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Creativity</span>
                        <span>{persona.characteristics.creativity}%</span>
                      </div>
                      <Progress
                        value={persona.characteristics.creativity}
                        className="h-1"
                      />
                    </div>
                  </>
                )}

                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center gap-1 text-xs" style={{ color: theme.colors.textSecondary }}>
                    <Clock className="w-3 h-3" />
                    <span>
                      {new Date(persona.metrics.lastUsed).toLocaleDateString()}
                    </span>
                  </div>

                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-1 h-auto"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDuplicatePersona(persona);
                      }}
                    >
                      <Copy className="w-3 h-3" />
                    </Button>

                    {persona.isCustom && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-1 h-auto text-red-500 hover:text-red-600"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeletePersona(persona);
                        }}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  if (compactMode) {
    return (
      <div className={cn("space-y-3", className)}>
        <div className="flex items-center justify-between">
          <h3 className="font-medium" style={{ color: theme.colors.text }}>
            Personas ({filteredPersonas.length})
          </h3>
          <Button size="sm" onClick={() => setShowCreateDialog(true)}>
            <Plus className="w-4 h-4" />
          </Button>
        </div>

        <div className="space-y-2">
          {filteredPersonas.slice(0, 5).map(persona => (
            <PersonaCard key={persona.id} persona={persona} />
          ))}
        </div>

        {filteredPersonas.length > 5 && (
          <Button variant="outline" size="sm" className="w-full">
            View All ({filteredPersonas.length - 5} more)
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold" style={{ color: theme.colors.text }}>
            Persona Manager
          </h2>
          <p style={{ color: theme.colors.textSecondary }}>
            Manage your brainstorming personas and their characteristics
          </p>
        </div>

        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Create Persona
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4"
                style={{ color: theme.colors.textSecondary }} />
              <Input
                placeholder="Search personas..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedRole} onValueChange={(value) => setSelectedRole(value as PersonaRole | 'all')}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                {Object.values(PersonaRole).map(role => (
                  <SelectItem key={role} value={role}>
                    {role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Personas Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <AnimatePresence>
          {filteredPersonas.map(persona => (
            <PersonaCard key={persona.id} persona={persona} />
          ))}
        </AnimatePresence>
      </div>

      {filteredPersonas.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Users className="w-12 h-12 mx-auto mb-4" style={{ color: theme.colors.textSecondary }} />
            <h3 className="font-medium mb-2" style={{ color: theme.colors.text }}>
              No personas found
            </h3>
            <p className="mb-4" style={{ color: theme.colors.textSecondary }}>
              {searchQuery || selectedRole !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Create your first persona to get started'
              }
            </p>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create Persona
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Create Persona Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Persona</DialogTitle>
            <DialogDescription>
              Create a custom brainstorming persona with unique characteristics
            </DialogDescription>
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-4 top-4 p-2"
              onClick={() => setShowCreateDialog(false)}
              aria-label="Close dialog"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogHeader>
          {/* Persona creation form would go here */}
          <div className="text-center py-8" style={{ color: theme.colors.textSecondary }}>
            Persona creation form coming soon...
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};