/**
 * Voice Interface Component
 * 
 * Provides voice input and output capabilities for hands-free brainstorming
 */

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Settings,
  Play,
  Pause,
  Square,
  Loader2,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/useToast';

interface VoiceInterfaceProps {
  onTranscript?: (text: string) => void;
  onCommand?: (command: string) => void;
  className?: string;
}

interface VoiceSettings {
  language: string;
  speechRate: number;
  speechPitch: number;
  speechVolume: number;
  voiceId: string;
  continuousListening: boolean;
}

const DEFAULT_SETTINGS: VoiceSettings = {
  language: 'en-US',
  speechRate: 1,
  speechPitch: 1,
  speechVolume: 0.8,
  voiceId: '',
  continuousListening: false,
};

const VOICE_COMMANDS = {
  'create idea': 'CREATE_IDEA',
  'new idea': 'CREATE_IDEA',
  'add idea': 'CREATE_IDEA',
  'switch to kanban': 'SWITCH_KANBAN',
  'switch to mind map': 'SWITCH_MINDMAP',
  'switch to matrix': 'SWITCH_MATRIX',
  'export session': 'EXPORT_SESSION',
  'save session': 'SAVE_SESSION',
  'clear all': 'CLEAR_ALL',
  'help': 'SHOW_HELP',
};

export const VoiceInterface: React.FC<VoiceInterfaceProps> = ({
  onTranscript,
  onCommand,
  className,
}) => {
  const { toast } = useToast();
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [settings, setSettings] = useState<VoiceSettings>(DEFAULT_SETTINGS);
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [isSupported, setIsSupported] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);

  // Initialize speech recognition and synthesis
  useEffect(() => {
    // Check for browser support
    const hasRecognition = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
    const hasSynthesis = 'speechSynthesis' in window;
    
    setIsSupported(hasRecognition && hasSynthesis);

    if (hasRecognition) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      
      const recognition = recognitionRef.current;
      recognition.continuous = settings.continuousListening;
      recognition.interimResults = true;
      recognition.lang = settings.language;

      recognition.onstart = () => {
        setIsListening(true);
      };

      recognition.onend = () => {
        setIsListening(false);
      };

      recognition.onresult = (event) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        const fullTranscript = finalTranscript || interimTranscript;
        setTranscript(fullTranscript);

        if (finalTranscript) {
          onTranscript?.(finalTranscript);
          processVoiceCommand(finalTranscript.toLowerCase().trim());
        }
      };

      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
        toast({
          title: 'Voice Recognition Error',
          description: `Error: ${event.error}`,
          variant: 'destructive',
        });
      };
    }

    if (hasSynthesis) {
      synthRef.current = window.speechSynthesis;
      loadVoices();
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (synthRef.current) {
        synthRef.current.cancel();
      }
    };
  }, [settings.language, settings.continuousListening]);

  const loadVoices = () => {
    if (synthRef.current) {
      const voices = synthRef.current.getVoices();
      setAvailableVoices(voices);
      
      if (!settings.voiceId && voices.length > 0) {
        const defaultVoice = voices.find(voice => voice.default) || voices[0];
        setSettings(prev => ({ ...prev, voiceId: defaultVoice.voiceURI }));
      }
    }
  };

  // Load voices when they become available
  useEffect(() => {
    if (synthRef.current) {
      synthRef.current.onvoiceschanged = loadVoices;
    }
  }, []);

  const processVoiceCommand = (text: string) => {
    const command = Object.keys(VOICE_COMMANDS).find(cmd => 
      text.includes(cmd)
    );

    if (command) {
      const commandType = VOICE_COMMANDS[command as keyof typeof VOICE_COMMANDS];
      onCommand?.(commandType);
      
      toast({
        title: 'Voice Command Recognized',
        description: `Executed: ${command}`,
      });
    }
  };

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      try {
        recognitionRef.current.start();
      } catch (error) {
        console.error('Failed to start recognition:', error);
        toast({
          title: 'Voice Recognition Failed',
          description: 'Could not start voice recognition',
          variant: 'destructive',
        });
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
    }
  };

  const speak = (text: string) => {
    if (synthRef.current && text) {
      // Cancel any ongoing speech
      synthRef.current.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      
      // Apply settings
      utterance.rate = settings.speechRate;
      utterance.pitch = settings.speechPitch;
      utterance.volume = settings.speechVolume;
      
      if (settings.voiceId) {
        const voice = availableVoices.find(v => v.voiceURI === settings.voiceId);
        if (voice) {
          utterance.voice = voice;
        }
      }

      utterance.onstart = () => setIsSpeaking(true);
      utterance.onend = () => setIsSpeaking(false);
      utterance.onerror = () => {
        setIsSpeaking(false);
        toast({
          title: 'Speech Error',
          description: 'Failed to speak text',
          variant: 'destructive',
        });
      };

      synthRef.current.speak(utterance);
    }
  };

  const stopSpeaking = () => {
    if (synthRef.current) {
      synthRef.current.cancel();
      setIsSpeaking(false);
    }
  };

  const updateSettings = (newSettings: Partial<VoiceSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  if (!isSupported) {
    return (
      <Card className={cn("voice-interface", className)}>
        <CardHeader>
          <CardTitle className="text-sm flex items-center gap-2">
            <MicOff className="h-4 w-4" />
            Voice Interface
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Voice features are not supported in this browser.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("voice-interface space-y-4", className)}>
      {/* Main Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Mic className="h-4 w-4" />
              Voice Interface
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Voice Input */}
          <div className="flex items-center gap-2">
            <Button
              variant={isListening ? "destructive" : "default"}
              size="sm"
              onClick={isListening ? stopListening : startListening}
              disabled={!isSupported}
            >
              {isListening ? (
                <>
                  <MicOff className="h-4 w-4 mr-2" />
                  Stop Listening
                </>
              ) : (
                <>
                  <Mic className="h-4 w-4 mr-2" />
                  Start Listening
                </>
              )}
            </Button>
            {isListening && (
              <Badge variant="secondary" className="animate-pulse">
                Listening...
              </Badge>
            )}
          </div>

          {/* Transcript */}
          {transcript && (
            <div className="p-3 bg-muted rounded-lg">
              <Label className="text-xs text-muted-foreground">Transcript:</Label>
              <p className="text-sm mt-1">{transcript}</p>
            </div>
          )}

          {/* Voice Output */}
          <div className="flex items-center gap-2">
            <Button
              variant={isSpeaking ? "destructive" : "outline"}
              size="sm"
              onClick={isSpeaking ? stopSpeaking : () => speak("Voice interface is ready for brainstorming.")}
              disabled={!isSupported}
            >
              {isSpeaking ? (
                <>
                  <Square className="h-4 w-4 mr-2" />
                  Stop Speaking
                </>
              ) : (
                <>
                  <Volume2 className="h-4 w-4 mr-2" />
                  Test Voice
                </>
              )}
            </Button>
            {isSpeaking && (
              <Badge variant="secondary" className="animate-pulse">
                Speaking...
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Voice Commands Help */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Voice Commands</CardTitle>
          <CardDescription className="text-xs">
            Say these phrases to control the interface
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 text-xs">
            {Object.keys(VOICE_COMMANDS).map(command => (
              <div key={command} className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  "{command}"
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Settings */}
      {showSettings && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Voice Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Language */}
            <div>
              <Label className="text-xs">Language</Label>
              <Select
                value={settings.language}
                onValueChange={(value) => updateSettings({ language: value })}
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en-US">English (US)</SelectItem>
                  <SelectItem value="en-GB">English (UK)</SelectItem>
                  <SelectItem value="es-ES">Spanish</SelectItem>
                  <SelectItem value="fr-FR">French</SelectItem>
                  <SelectItem value="de-DE">German</SelectItem>
                  <SelectItem value="it-IT">Italian</SelectItem>
                  <SelectItem value="pt-BR">Portuguese</SelectItem>
                  <SelectItem value="ja-JP">Japanese</SelectItem>
                  <SelectItem value="ko-KR">Korean</SelectItem>
                  <SelectItem value="zh-CN">Chinese</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Voice Selection */}
            <div>
              <Label className="text-xs">Voice</Label>
              <Select
                value={settings.voiceId}
                onValueChange={(value) => updateSettings({ voiceId: value })}
              >
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="Select voice" />
                </SelectTrigger>
                <SelectContent>
                  {availableVoices
                    .filter(voice => voice.lang.startsWith(settings.language.split('-')[0]))
                    .map(voice => (
                      <SelectItem key={voice.voiceURI} value={voice.voiceURI}>
                        {voice.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            {/* Speech Rate */}
            <div>
              <Label className="text-xs">Speech Rate: {settings.speechRate}</Label>
              <Slider
                value={[settings.speechRate]}
                onValueChange={([value]) => updateSettings({ speechRate: value })}
                min={0.5}
                max={2}
                step={0.1}
                className="mt-2"
              />
            </div>

            {/* Speech Pitch */}
            <div>
              <Label className="text-xs">Speech Pitch: {settings.speechPitch}</Label>
              <Slider
                value={[settings.speechPitch]}
                onValueChange={([value]) => updateSettings({ speechPitch: value })}
                min={0.5}
                max={2}
                step={0.1}
                className="mt-2"
              />
            </div>

            {/* Speech Volume */}
            <div>
              <Label className="text-xs">Speech Volume: {Math.round(settings.speechVolume * 100)}%</Label>
              <Slider
                value={[settings.speechVolume]}
                onValueChange={([value]) => updateSettings({ speechVolume: value })}
                min={0}
                max={1}
                step={0.1}
                className="mt-2"
              />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};