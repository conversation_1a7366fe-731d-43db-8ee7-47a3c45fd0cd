/**
 * Memory Integration Component
 * 
 * Provides memory-based suggestions and automatically extracts insights
 * from brainstorming sessions for future reference.
 */

import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  Lightbulb,
  ChevronDown,
  ChevronUp,
  Clock,
  Link,
  Star,
  X,
  Sparkles,
  History,
  BookOpen,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  brainstormMemory, 
  type MemoryEntry 
} from '@/lib/brainstorm-memory';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { cn } from '@/lib/utils';

interface MemoryIntegrationProps {
  sessionId: string;
  sessionTags: string[];
  className?: string;
}

interface MemorySuggestion {
  content: string;
  reason: string;
  confidence: number;
  source: MemoryEntry;
}

export const MemoryIntegration: React.FC<MemoryIntegrationProps> = ({
  sessionId,
  sessionTags,
  className,
}) => {
  const { 
    getCurrentSession,
    getIdeasBySession,
    addIdea,
    saveMemory,
  } = useBrainstormStore();

  const [isExpanded, setIsExpanded] = useState(false);
  const [relevantMemories, setRelevantMemories] = useState<MemoryEntry[]>([]);
  const [memorySuggestions, setMemorySuggestions] = useState<MemorySuggestion[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  // Load relevant memories when component mounts or session changes
  useEffect(() => {
    loadRelevantMemories();
  }, [sessionId, sessionTags]);

  // Auto-extract memories when session ideas change
  useEffect(() => {
    const session = getCurrentSession();
    if (session && session.id === sessionId) {
      const ideas = getIdeasBySession(sessionId);
      const clusters = Object.values(useBrainstormStore.getState().clusters);
      
      // Debounce memory extraction to avoid excessive processing
      const timeoutId = setTimeout(() => {
        extractSessionMemories(session, ideas, clusters);
      }, 5000);

      return () => clearTimeout(timeoutId);
    }
  }, [sessionId]);

  const loadRelevantMemories = async () => {
    try {
      setIsProcessing(true);
      
      // Get relevant memories from the memory system
      const memories = brainstormMemory.getRelevantMemories(sessionTags, undefined, 8);
      setRelevantMemories(memories);

      // Generate memory-based suggestions
      const session = getCurrentSession();
      if (session) {
        const ideas = getIdeasBySession(sessionId);
        const suggestions = brainstormMemory.generateMemoryBasedSuggestions(sessionTags, ideas);
        
        // Convert to our suggestion format
        const formattedSuggestions: MemorySuggestion[] = suggestions.map(suggestion => ({
          content: suggestion.content,
          reason: suggestion.reason,
          confidence: suggestion.confidence,
          source: memories.find(m => m.content === suggestion.reason) || memories[0], // Find source memory
        }));
        
        setMemorySuggestions(formattedSuggestions);
      }
    } catch (error) {
      console.error('Failed to load relevant memories:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const extractSessionMemories = async (session: any, ideas: any[], clusters: any[]) => {
    try {
      // Extract memories from the current session
      const newMemories = brainstormMemory.extractMemoriesFromSession(session, ideas, clusters);
      
      // Store new memories
      if (newMemories.length > 0) {
        brainstormMemory.storeMemories(newMemories);
        
        // Also save to the brainstorm store for persistence
        newMemories.forEach(memory => {
          saveMemory({
            type: memory.type,
            content: memory.content,
            sessionId: memory.sourceSessionIds[0],
            context: session.title,
            importance: memory.confidence,
            tags: memory.tags,
            relatedData: {
              ideaIds: memory.relatedIdeaIds,
              clusters: clusters.map(c => c.id),
            },
          });
        });
        
        console.log(`Extracted ${newMemories.length} new memories from session`);
      }
    } catch (error) {
      console.error('Failed to extract session memories:', error);
    }
  };

  const applySuggestion = (suggestion: MemorySuggestion) => {
    // Add the suggestion as a new idea
    addIdea(sessionId, suggestion.content, {
      tags: suggestion.source.tags.slice(0, 3), // Use up to 3 tags from the source memory
      priority: suggestion.confidence > 0.8 ? 'high' : 'medium',
      metadata: {
        sourceMemory: suggestion.source.id,
        suggestionReason: suggestion.reason,
        memoryBased: true,
      },
    });
  };

  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else if (diffInHours < 24 * 7) {
      return `${Math.floor(diffInHours / 24)}d ago`;
    } else {
      return `${Math.floor(diffInHours / (24 * 7))}w ago`;
    }
  };

  const getMemoryTypeIcon = (type: MemoryEntry['type']) => {
    switch (type) {
      case 'insight': return Star;
      case 'pattern': return Brain;
      case 'concept': return Lightbulb;
      case 'lesson': return BookOpen;
      case 'connection': return Link;
      default: return Sparkles;
    }
  };

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-gray-600';
  };

  const hasRelevantData = relevantMemories.length > 0 || memorySuggestions.length > 0;

  if (!hasRelevantData && !isProcessing) {
    return null; // Don't render if no relevant memories
  }

  return (
    <Card className={cn('border-purple-200 bg-purple-50/50', className)}>
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader className="pb-3 cursor-pointer hover:bg-purple-100/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Brain className="w-5 h-5 text-purple-600" />
                <div>
                  <CardTitle className="text-sm text-purple-800">
                    Memory Insights
                  </CardTitle>
                  <CardDescription className="text-xs">
                    {isProcessing ? (
                      'Loading relevant insights...'
                    ) : (
                      `${relevantMemories.length} memories, ${memorySuggestions.length} suggestions`
                    )}
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {memorySuggestions.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {memorySuggestions.length} new
                  </Badge>
                )}
                {isExpanded ? (
                  <ChevronUp className="w-4 h-4 text-purple-600" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-purple-600" />
                )}
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="pt-0">
            <div className="space-y-4">
              {/* Memory-based suggestions */}
              {memorySuggestions.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-purple-800 mb-2 flex items-center gap-1">
                    <Sparkles className="w-4 h-4" />
                    Smart Suggestions
                  </h4>
                  <div className="space-y-2">
                    {memorySuggestions.slice(0, 3).map((suggestion, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="p-3 bg-white rounded-lg border border-purple-100 hover:border-purple-200 transition-colors"
                      >
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900 mb-1">
                              {suggestion.content}
                            </p>
                            <p className="text-xs text-gray-600 mb-2">
                              {suggestion.reason}
                            </p>
                            <div className="flex items-center gap-2">
                              <Badge 
                                variant="outline" 
                                className={cn('text-xs', getConfidenceColor(suggestion.confidence))}
                              >
                                {Math.round(suggestion.confidence * 100)}% confidence
                              </Badge>
                              <div className="flex gap-1">
                                {suggestion.source.tags.slice(0, 2).map((tag, tagIndex) => (
                                  <Badge key={tagIndex} variant="secondary" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => applySuggestion(suggestion)}
                            className="text-xs"
                          >
                            Add Idea
                          </Button>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {/* Relevant memories */}
              {relevantMemories.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-purple-800 mb-2 flex items-center gap-1">
                    <History className="w-4 h-4" />
                    From Previous Sessions
                  </h4>
                  <ScrollArea className="h-32">
                    <div className="space-y-2 pr-2">
                      {relevantMemories.slice(0, 5).map((memory) => {
                        const IconComponent = getMemoryTypeIcon(memory.type);
                        
                        return (
                          <motion.div
                            key={memory.id}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            className="p-2 bg-white rounded border border-purple-100 hover:border-purple-200 transition-colors"
                          >
                            <div className="flex items-start gap-2">
                              <IconComponent className="w-3 h-3 text-purple-600 mt-0.5 flex-shrink-0" />
                              <div className="flex-1 min-w-0">
                                <p className="text-xs text-gray-900 mb-1 line-clamp-2">
                                  {memory.content}
                                </p>
                                <div className="flex items-center gap-2 text-xs text-gray-500">
                                  <Badge variant="outline" className="text-xs">
                                    {memory.type}
                                  </Badge>
                                  <Clock className="w-3 h-3" />
                                  <span>{formatTimeAgo(memory.lastAccessed)}</span>
                                  <span>•</span>
                                  <span>{memory.accessCount} uses</span>
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        );
                      })}
                    </div>
                  </ScrollArea>
                </div>
              )}

              {isProcessing && (
                <div className="text-center py-4">
                  <div className="flex items-center justify-center gap-2 text-purple-600">
                    <Brain className="w-4 h-4 animate-pulse" />
                    <span className="text-sm">Processing memories...</span>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};