import { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw, Home } from 'lucide-react';
import { useBrainstormStore } from '@/stores/brainstormStore';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorCount: number;
}

export class BrainstormErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
      errorCount: 0,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('BrainstormErrorBoundary caught an error:', error, errorInfo);
    
    this.setState(prevState => ({
      errorInfo,
      errorCount: prevState.errorCount + 1,
    }));

    // Log to error tracking service
    this.logErrorToService(error, errorInfo);
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Add error to brainstorm store
    this.addErrorToStore(error, errorInfo);
  }

  logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // In production, send to error tracking service (e.g., Sentry)
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    console.error('Error logged:', errorData);
  };

  addErrorToStore = (error: Error, errorInfo: ErrorInfo) => {
    try {
      const store = useBrainstormStore.getState();
      store.addError({
        type: 'visualization',
        message: error.message,
        details: {
          stack: error.stack,
          componentStack: errorInfo.componentStack,
        },
        resolved: false,
      });
    } catch (storeError) {
      console.error('Failed to add error to store:', storeError);
    }
  };

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return <>{this.props.fallback}</>;
      }

      return (
        <div className="flex items-center justify-center min-h-[400px] p-8">
          <Card className="max-w-2xl w-full">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-destructive">
                <AlertCircle className="h-5 w-5" />
                Something went wrong
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                We encountered an unexpected error while rendering this component.
                The error has been logged and our team will investigate.
              </p>

              {this.state.error && (
                <div className="bg-muted p-4 rounded-lg space-y-2">
                  <p className="font-mono text-sm text-destructive">
                    {this.state.error.message}
                  </p>
                  {process.env.NODE_ENV === 'development' && (
                    <details className="cursor-pointer">
                      <summary className="text-sm text-muted-foreground hover:text-foreground">
                        Show technical details
                      </summary>
                      <pre className="mt-2 text-xs overflow-auto max-h-64 p-2 bg-background rounded">
                        {this.state.error.stack}
                      </pre>
                    </details>
                  )}
                </div>
              )}

              <div className="flex gap-2">
                <Button onClick={this.handleReset} variant="default">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                <Button onClick={this.handleReload} variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reload Page
                </Button>
                <Button
                  onClick={() => window.location.href = '/'}
                  variant="outline"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Button>
              </div>

              {this.state.errorCount > 2 && (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-4 rounded-lg">
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    This error has occurred multiple times. Please try refreshing the page
                    or contact support if the issue persists.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook for error handling in functional components
export const useBrainstormError = () => {
  const { addError, errors, resolveError, clearErrors } = useBrainstormStore();

  const handleError = (error: Error | string, type: string = 'general') => {
    const errorMessage = error instanceof Error ? error.message : error;
    
    addError({
      type: type as any,
      message: errorMessage,
      details: error instanceof Error ? { stack: error.stack } : undefined,
      resolved: false,
    });

    // Show toast notification securely
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-4 right-4 bg-destructive text-destructive-foreground px-4 py-2 rounded-lg shadow-lg z-50 animate-in slide-in-from-bottom-2';
    
    // Create elements safely without innerHTML
    const contentDiv = document.createElement('div');
    contentDiv.className = 'flex items-center gap-2';
    
    const svg = document.createElement('svg');
    svg.className = 'h-4 w-4';
    svg.setAttribute('fill', 'none');
    svg.setAttribute('stroke', 'currentColor');
    svg.setAttribute('viewBox', '0 0 24 24');
    
    const path = document.createElement('path');
    path.setAttribute('stroke-linecap', 'round');
    path.setAttribute('stroke-linejoin', 'round');
    path.setAttribute('stroke-width', '2');
    path.setAttribute('d', 'M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z');
    
    svg.appendChild(path);
    
    const span = document.createElement('span');
    span.textContent = errorMessage; // Safe from XSS
    
    contentDiv.appendChild(svg);
    contentDiv.appendChild(span);
    toast.appendChild(contentDiv);
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
      toast.remove();
    }, 5000);
  };

  const getUnresolvedErrors = () => {
    return errors.filter(e => !e.resolved);
  };

  return {
    handleError,
    errors,
    unresolvedErrors: getUnresolvedErrors(),
    resolveError,
    clearErrors,
  };
};