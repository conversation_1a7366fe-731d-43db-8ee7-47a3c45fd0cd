import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { PremiumProgress } from '@/components/ui/premium-progress';
import {
  Settings,
  Search,
  Key,
  Check,
  X,
  AlertCircle,
  Loader2,
  Globe,
  BookOpen,
  Shield,
  Zap,
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { 
  searchProvider, 
  SearchProvider,
  MultiSearchProvider,
} from '@/lib/web-research-multi';
import { invoke } from '@tauri-apps/api/core';

interface SearchProviderConfigProps {
  onConfigChange?: () => void;
}

export const SearchProviderConfig: React.FC<SearchProviderConfigProps> = ({
  onConfigChange,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [providers, setProviders] = useState<SearchProvider[]>([]);
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  const [isTesting, setIsTesting] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState<string | null>(null);
  const [searchStats, setSearchStats] = useState<any>(null);
  
  // Provider-specific configs
  const [serperApiKey, setSerperApiKey] = useState('');
  const [bingApiKey, setBingApiKey] = useState('');
  const [googleApiKey, setGoogleApiKey] = useState('');
  const [googleSearchEngineId, setGoogleSearchEngineId] = useState('');
  
  const { toast } = useToast();

  useEffect(() => {
    loadProviderConfigs();
    loadSearchStats();
  }, []);

  const loadProviderConfigs = async () => {
    setProviders(searchProvider.getEnabledProviders());
    
    // Load API keys from secure storage
    try {
      const serperConfig = await invoke<string>('load_search_provider_config', {
        provider: 'serper',
      });
      if (serperConfig) {
        const config = JSON.parse(serperConfig);
        setSerperApiKey(config.apiKey || '');
      }
    } catch {}

    try {
      const bingConfig = await invoke<string>('load_search_provider_config', {
        provider: 'bing',
      });
      if (bingConfig) {
        const config = JSON.parse(bingConfig);
        setBingApiKey(config.apiKey || '');
      }
    } catch {}

    try {
      const googleConfig = await invoke<string>('load_search_provider_config', {
        provider: 'google',
      });
      if (googleConfig) {
        const config = JSON.parse(googleConfig);
        setGoogleApiKey(config.apiKey || '');
        setGoogleSearchEngineId(config.searchEngineId || '');
      }
    } catch {}
  };

  const loadSearchStats = async () => {
    try {
      const stats = await searchProvider.getSearchStats();
      setSearchStats(stats);
    } catch (error) {
      console.error('Failed to load search stats:', error);
    }
  };

  const saveProviderConfig = async (providerName: string) => {
    setIsSaving(providerName);
    try {
      let config: any = {};
      
      switch (providerName) {
        case 'Serper':
          config = { apiKey: serperApiKey };
          break;
        case 'Bing':
          config = { apiKey: bingApiKey };
          break;
        case 'Google':
          config = { apiKey: googleApiKey, searchEngineId: googleSearchEngineId };
          break;
      }

      await searchProvider.setProviderConfig(providerName, config);
      
      toast({
        message: `${providerName} configuration saved`,
        type: 'success',
      });
      
      if (onConfigChange) {
        onConfigChange();
      }
    } catch (error) {
      toast({
        message: 'Failed to save configuration',
        type: 'error',
      });
    } finally {
      setIsSaving(null);
    }
  };

  const testProvider = async (provider: SearchProvider) => {
    setIsTesting(provider.name);
    try {
      const isValid = await searchProvider.validateProvider(provider);
      setTestResults({ ...testResults, [provider.name]: isValid });
      
      toast({
        message: isValid 
          ? `${provider.name} is working correctly` 
          : `${provider.name} test failed`,
        type: isValid ? 'success' : 'error',
      });
    } catch (error) {
      setTestResults({ ...testResults, [provider.name]: false });
      toast({
        message: `${provider.name} test failed`,
        type: 'error',
      });
    } finally {
      setIsTesting(null);
    }
  };

  const toggleProvider = (providerName: string, enabled: boolean) => {
    searchProvider.setProviderEnabled(providerName, enabled);
    setProviders(searchProvider.getEnabledProviders());
  };

  const getProviderIcon = (type: string) => {
    switch (type) {
      case 'serper':
        return <Globe className="h-4 w-4" />;
      case 'duckduckgo':
        return <Shield className="h-4 w-4" />;
      case 'bing':
        return <Search className="h-4 w-4" />;
      case 'google':
        return <Globe className="h-4 w-4" />;
      default:
        return <Search className="h-4 w-4" />;
    }
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(true)}
      >
        <Settings className="h-4 w-4 mr-2" />
        Search Providers
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Search Provider Configuration</DialogTitle>
          </DialogHeader>

          <Tabs defaultValue="providers" className="mt-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="providers">Providers</TabsTrigger>
              <TabsTrigger value="priority">Priority & Fallback</TabsTrigger>
              <TabsTrigger value="stats">Statistics</TabsTrigger>
            </TabsList>

            <TabsContent value="providers" className="space-y-4">
              <div className="space-y-4">
                {/* DuckDuckGo (No API key required) */}
                <Card className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Shield className="h-5 w-5 text-green-600" />
                      <div>
                        <h4 className="font-medium">DuckDuckGo</h4>
                        <p className="text-sm text-muted-foreground">
                          Privacy-focused, no API key required
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={providers.some(p => p.name === 'DuckDuckGo' && p.enabled)}
                        onCheckedChange={(checked) => toggleProvider('DuckDuckGo', checked)}
                      />
                      {testResults['DuckDuckGo'] && (
                        <Check className="h-4 w-4 text-green-600" />
                      )}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => testProvider(providers.find(p => p.name === 'DuckDuckGo')!)}
                    disabled={isTesting === 'DuckDuckGo'}
                  >
                    {isTesting === 'DuckDuckGo' ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      'Test Connection'
                    )}
                  </Button>
                </Card>

                {/* Serper API */}
                <Card className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Zap className="h-5 w-5 text-blue-600" />
                      <div>
                        <h4 className="font-medium">Serper (Google Results)</h4>
                        <p className="text-sm text-muted-foreground">
                          Fast Google search results via API
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={providers.some(p => p.name === 'Serper' && p.enabled)}
                        onCheckedChange={(checked) => toggleProvider('Serper', checked)}
                      />
                      {testResults['Serper'] && (
                        <Check className="h-4 w-4 text-green-600" />
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div>
                      <Label htmlFor="serper-key">API Key</Label>
                      <Input
                        id="serper-key"
                        type="password"
                        placeholder="Your Serper API key"
                        value={serperApiKey}
                        onChange={(e) => setSerperApiKey(e.target.value)}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Get your API key from{' '}
                        <a
                          href="https://serper.dev"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="underline"
                        >
                          serper.dev
                        </a>
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => saveProviderConfig('Serper')}
                        disabled={isSaving === 'Serper'}
                      >
                        {isSaving === 'Serper' ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          'Save Config'
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => testProvider(providers.find(p => p.name === 'Serper')!)}
                        disabled={isTesting === 'Serper' || !serperApiKey}
                      >
                        {isTesting === 'Serper' ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          'Test Connection'
                        )}
                      </Button>
                    </div>
                  </div>
                </Card>

                {/* Semantic Scholar (Academic) */}
                <Card className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <BookOpen className="h-5 w-5 text-purple-600" />
                      <div>
                        <h4 className="font-medium">Semantic Scholar</h4>
                        <p className="text-sm text-muted-foreground">
                          Academic papers and research, no API key required
                        </p>
                      </div>
                    </div>
                    <Badge variant="secondary">Specialized</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Automatically used for academic and research queries
                  </p>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="priority" className="space-y-4">
              <Card className="p-4">
                <h4 className="font-medium mb-4">Provider Priority Order</h4>
                <p className="text-sm text-muted-foreground mb-4">
                  Providers are tried in order until one succeeds
                </p>
                <div className="space-y-2">
                  {providers
                    .sort((a, b) => a.priority - b.priority)
                    .map((provider, index) => (
                      <div
                        key={provider.name}
                        className="flex items-center justify-between p-2 rounded-lg bg-muted/50"
                      >
                        <div className="flex items-center gap-3">
                          <span className="text-lg font-medium text-muted-foreground">
                            {index + 1}
                          </span>
                          {getProviderIcon(provider.type)}
                          <span>{provider.name}</span>
                          {!provider.enabled && (
                            <Badge variant="secondary">Disabled</Badge>
                          )}
                        </div>
                        {provider.config?.requiresApiKey && !provider.enabled && (
                          <Badge variant="outline" className="gap-1">
                            <Key className="h-3 w-3" />
                            API Key Required
                          </Badge>
                        )}
                      </div>
                    ))}
                </div>
              </Card>

              <Card className="p-4">
                <h4 className="font-medium mb-2">Fallback Behavior</h4>
                <p className="text-sm text-muted-foreground">
                  When a search provider fails, the system automatically tries the next
                  available provider. This ensures your searches always return results,
                  even if some providers are experiencing issues.
                </p>
              </Card>
            </TabsContent>

            <TabsContent value="stats" className="space-y-4">
              {searchStats ? (
                <>
                  <Card className="p-4">
                    <h4 className="font-medium mb-4">Search Statistics</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Total Searches</p>
                        <p className="text-2xl font-bold">{searchStats.totalSearches || 0}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Active Providers</p>
                        <p className="text-2xl font-bold">
                          {providers.filter(p => p.enabled).length}
                        </p>
                      </div>
                    </div>
                  </Card>

                  <Card className="p-4">
                    <h4 className="font-medium mb-4">Provider Usage</h4>
                    <div className="space-y-3">
                      {Object.entries(searchStats.providerUsage || {}).map(([provider, count]) => (
                        <div key={provider}>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">{provider}</span>
                            <span className="text-sm text-muted-foreground">
                              {count as number} searches
                            </span>
                          </div>
                          <Progress
                            value={((count as number) / searchStats.totalSearches) * 100}
                            className="h-2"
                          />
                        </div>
                      ))}
                    </div>
                  </Card>
                </>
              ) : (
                <Card className="p-8 text-center">
                  <AlertCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">No search statistics available yet</p>
                </Card>
              )}
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};