import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Image,
  Mic,
  MicOff,
  Upload,
  X,
  Pencil,
  Download,
  Trash2,
  Camera,
  Loader2,
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { 
  uploadMediaAttachment, 
  getMediaAttachmentsForIdea, 
  deleteMediaAttachment,
  MediaAttachment,
  formatFileSize
} from '@/lib/brainstorm-media';

interface MultiModalInputProps {
  ideaId?: string;
  onImageAdd: (attachmentId: string, description?: string) => void;
  onAudioAdd: (attachmentId: string, transcript?: string) => void;
  onSketchAdd: (attachmentId: string) => void;
  onAttachmentsUpdate?: (attachmentIds: string[]) => void;
  className?: string;
}

interface MediaItem {
  id: string;
  type: 'image' | 'audio' | 'sketch';
  data: string;
  description?: string;
  timestamp: string;
}

export const MultiModalInput: React.FC<MultiModalInputProps> = ({
  ideaId,
  onImageAdd,
  onAudioAdd,
  onSketchAdd,
  onAttachmentsUpdate,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'image' | 'sketch' | 'voice'>('image');
  const [isRecording, setIsRecording] = useState(false);
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [attachments, setAttachments] = useState<MediaAttachment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  
  const { toast } = useToast();

  // Load existing attachments when idea changes
  useEffect(() => {
    if (ideaId) {
      loadAttachments();
    }
  }, [ideaId]);

  const loadAttachments = async () => {
    if (!ideaId) return;
    
    try {
      const existingAttachments = await getMediaAttachmentsForIdea(ideaId);
      setAttachments(existingAttachments);
    } catch (error) {
      console.error('Failed to load attachments:', error);
    }
  };

  // Image handling
  const handleImageUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || !ideaId) return;

    setIsLoading(true);
    const uploadedIds: string[] = [];

    try {
      for (const file of Array.from(files)) {
        if (!file.type.startsWith('image/')) {
          toast({
            message: 'Please upload only image files',
            type: 'error',
          });
          continue;
        }

        // Upload to Tauri backend
        const attachment = await uploadMediaAttachment(ideaId, file);
        uploadedIds.push(attachment.id);
        
        // Update local state
        setAttachments(prev => [...prev, attachment]);
        onImageAdd(attachment.id);
      }

      // Notify parent of all attachments
      if (onAttachmentsUpdate && uploadedIds.length > 0) {
        const allIds = [...attachments.map(a => a.id), ...uploadedIds];
        onAttachmentsUpdate(allIds);
      }

      toast({
        message: `Uploaded ${uploadedIds.length} image(s)`,
        type: 'success',
      });
    } catch (error) {
      toast({
        message: 'Failed to upload images',
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  }, [ideaId, onImageAdd, onAttachmentsUpdate, attachments, toast]);

  // Sketch handling
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentColor, setCurrentColor] = useState('#000000');
  const [brushSize, setBrushSize] = useState(2);

  const startDrawing = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.beginPath();
    ctx.moveTo(x, y);
    setIsDrawing(true);
  }, []);

  const draw = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.lineWidth = brushSize;
    ctx.strokeStyle = currentColor;
    ctx.lineCap = 'round';
    ctx.lineTo(x, y);
    ctx.stroke();
  }, [isDrawing, currentColor, brushSize]);

  const stopDrawing = useCallback(() => {
    setIsDrawing(false);
  }, []);

  const clearCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
  }, []);

  const saveSketch = useCallback(async () => {
    const canvas = canvasRef.current;
    if (!canvas || !ideaId) return;

    setIsLoading(true);
    try {
      // Convert canvas to blob
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          resolve(blob || new Blob());
        }, 'image/png');
      });

      // Create File from blob
      const file = new File([blob], `sketch_${Date.now()}.png`, { type: 'image/png' });
      
      // Upload to backend
      const attachment = await uploadMediaAttachment(ideaId, file);
      
      // Update state
      setAttachments(prev => [...prev, attachment]);
      onSketchAdd(attachment.id);
      
      if (onAttachmentsUpdate) {
        onAttachmentsUpdate(attachments.map(a => a.id).concat(attachment.id));
      }

      clearCanvas();
      toast({
        message: 'Sketch saved!',
        type: 'success',
      });
    } catch (error) {
      toast({
        message: 'Failed to save sketch',
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  }, [ideaId, onSketchAdd, onAttachmentsUpdate, attachments, clearCanvas, toast]);

  // Voice recording
  const startRecording = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      
      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = async () => {
        if (!ideaId) return;
        
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        const file = new File([audioBlob], `audio_${Date.now()}.webm`, { type: 'audio/webm' });
        
        setIsLoading(true);
        try {
          // Upload to backend
          const attachment = await uploadMediaAttachment(ideaId, file);
          
          // Update state
          setAttachments(prev => [...prev, attachment]);
          onAudioAdd(attachment.id);
          
          if (onAttachmentsUpdate) {
            onAttachmentsUpdate(attachments.map(a => a.id).concat(attachment.id));
          }

          toast({
            message: 'Audio recording saved!',
            type: 'success',
          });
        } catch (error) {
          toast({
            message: 'Failed to save audio',
            type: 'error',
          });
        } finally {
          setIsLoading(false);
        }
        
        audioChunksRef.current = [];
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      toast({
        message: 'Failed to access microphone',
        type: 'error',
      });
    }
  }, [onAudioAdd, toast]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
    }
  }, [isRecording]);

  const removeMediaItem = async (attachmentId: string) => {
    try {
      await deleteMediaAttachment(attachmentId);
      setAttachments(prev => prev.filter(a => a.id !== attachmentId));
      
      if (onAttachmentsUpdate) {
        onAttachmentsUpdate(attachments.filter(a => a.id !== attachmentId).map(a => a.id));
      }
      
      toast({
        message: 'Attachment removed',
        type: 'success',
      });
    } catch (error) {
      toast({
        message: 'Failed to remove attachment',
        type: 'error',
      });
    }
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(true)}
        className={className}
        disabled={!ideaId}
      >
        <Camera className="h-4 w-4 mr-1" />
        Add Media
        {attachments.length > 0 && (
          <span className="ml-1">({attachments.length})</span>
        )}
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Add Media to Brainstorm</DialogTitle>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="image">
                <Image className="h-4 w-4 mr-2" />
                Images
              </TabsTrigger>
              <TabsTrigger value="sketch">
                <Pencil className="h-4 w-4 mr-2" />
                Sketch
              </TabsTrigger>
              <TabsTrigger value="voice">
                <Mic className="h-4 w-4 mr-2" />
                Voice
              </TabsTrigger>
            </TabsList>

            <TabsContent value="image" className="space-y-4">
              <div className="flex items-center gap-4">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleImageUpload}
                  className="hidden"
                />
                <Button onClick={() => fileInputRef.current?.click()}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Images
                </Button>
                <p className="text-sm text-muted-foreground">
                  Upload images to include in your brainstorm
                </p>
              </div>

              <div className="grid grid-cols-3 gap-4">
                {attachments
                  .filter(a => a.mime_type.startsWith('image/'))
                  .map(attachment => (
                    <Card key={attachment.id} className="relative group">
                      <div className="p-2">
                        <div className="text-xs text-muted-foreground mb-1">
                          {attachment.file_name}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatFileSize(attachment.file_size)}
                        </div>
                      </div>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => removeMediaItem(attachment.id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Card>
                  ))}
              </div>
              {isLoading && (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              )}
            </TabsContent>

            <TabsContent value="sketch" className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <input
                    type="color"
                    value={currentColor}
                    onChange={(e) => setCurrentColor(e.target.value)}
                    className="w-8 h-8 rounded cursor-pointer"
                  />
                  <input
                    type="range"
                    min="1"
                    max="10"
                    value={brushSize}
                    onChange={(e) => setBrushSize(Number(e.target.value))}
                    className="w-24"
                  />
                </div>
                <Button variant="outline" onClick={clearCanvas}>
                  <Trash2 className="h-4 w-4 mr-1" />
                  Clear
                </Button>
                <Button 
                  onClick={saveSketch}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-1" />
                      Save Sketch
                    </>
                  )}
                </Button>
              </div>

              <canvas
                ref={canvasRef}
                width={600}
                height={400}
                className="border rounded-lg cursor-crosshair bg-white"
                onMouseDown={startDrawing}
                onMouseMove={draw}
                onMouseUp={stopDrawing}
                onMouseLeave={stopDrawing}
              />
            </TabsContent>

            <TabsContent value="voice" className="space-y-4">
              <div className="flex flex-col items-center gap-4">
                <Button
                  size="lg"
                  variant={isRecording ? 'destructive' : 'default'}
                  onClick={isRecording ? stopRecording : startRecording}
                  className="rounded-full w-20 h-20"
                >
                  {isRecording ? (
                    <MicOff className="h-8 w-8" />
                  ) : (
                    <Mic className="h-8 w-8" />
                  )}
                </Button>
                <p className="text-sm text-muted-foreground">
                  {isRecording ? 'Recording... Click to stop' : 'Click to start recording'}
                </p>
              </div>

              <div className="space-y-2">
                {attachments
                  .filter(a => a.mime_type.startsWith('audio/'))
                  .map(attachment => (
                    <Card key={attachment.id} className="p-4 flex items-center justify-between">
                      <div className="flex-1">
                        <div className="text-sm">{attachment.file_name}</div>
                        <div className="text-xs text-muted-foreground">
                          {formatFileSize(attachment.file_size)}
                        </div>
                      </div>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => removeMediaItem(attachment.id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Card>
                  ))}
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </>
  );
};