/**
 * Web Research Panel Component
 * 
 * Provides interface for AI-enhanced web research during brainstorming
 */

import React, { useState, useEffect } from 'react';
import { webResearchService, ResearchQuery, ResearchResult, ResearchSummary } from '@/lib/web-research';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Search,
  Globe,
  CheckCircle,
  XCircle,
  Clock,
  ExternalLink,
  Sparkles,
  Shield,
  History,
  Loader2,
  Alert<PERSON>riangle,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/useToast';

interface WebResearchPanelProps {
  sessionId: string;
  className?: string;
  onInsightAdd?: (insight: string, sources: string[]) => void;
}

export const WebResearchPanel: React.FC<WebResearchPanelProps> = ({
  sessionId,
  className,
  onInsightAdd,
}) => {
  const { toast } = useToast();
  const [researchQuery, setResearchQuery] = useState('');
  const [researchContext, setResearchContext] = useState('');
  const [autoApprove, setAutoApprove] = useState(false);
  const [pendingApprovals, setPendingApprovals] = useState<ResearchQuery[]>([]);
  const [researchHistory, setResearchHistory] = useState<ResearchQuery[]>([]);
  const [activeResults, setActiveResults] = useState<ResearchResult[]>([]);
  const [activeSummary, setActiveSummary] = useState<ResearchSummary | null>(null);
  const [isResearching, setIsResearching] = useState(false);
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  const [showHistoryDialog, setShowHistoryDialog] = useState(false);

  // Load initial data
  useEffect(() => {
    loadPendingApprovals();
    loadResearchHistory();
  }, [sessionId]);

  const loadPendingApprovals = () => {
    const pending = webResearchService.getPendingApprovals();
    setPendingApprovals(pending.filter(q => q.sessionId === sessionId));
  };

  const loadResearchHistory = () => {
    const history = webResearchService.getResearchHistory(sessionId);
    setResearchHistory(history);
  };

  const handleResearchRequest = async () => {
    if (!researchQuery.trim()) {
      toast({
        title: 'Query Required',
        description: 'Please enter a research query.',
        variant: 'destructive',
      });
      return;
    }

    setIsResearching(true);
    try {
      const queryId = await webResearchService.requestResearch(
        researchQuery,
        researchContext || 'General brainstorming context',
        sessionId,
        autoApprove
      );

      if (autoApprove) {
        await loadResults(queryId);
        toast({
          title: 'Research Complete',
          description: 'Web research results are ready.',
        });
      } else {
        toast({
          title: 'Research Requested',
          description: 'Research request is pending approval.',
        });
        loadPendingApprovals();
      }

      setResearchQuery('');
      setResearchContext('');
      loadResearchHistory();
    } catch (error) {
      console.error('Research request failed:', error);
      toast({
        title: 'Research Failed',
        description: 'Could not initiate web research.',
        variant: 'destructive',
      });
    } finally {
      setIsResearching(false);
    }
  };

  const handleApproveResearch = async (queryId: string) => {
    try {
      await webResearchService.approveResearch(queryId);
      await loadResults(queryId);
      loadPendingApprovals();
      loadResearchHistory();
      
      toast({
        title: 'Research Approved',
        description: 'Web research has been completed.',
      });
    } catch (error) {
      console.error('Research approval failed:', error);
      toast({
        title: 'Approval Failed',
        description: 'Could not approve research request.',
        variant: 'destructive',
      });
    }
  };

  const handleRejectResearch = (queryId: string) => {
    webResearchService.rejectResearch(queryId);
    loadPendingApprovals();
    
    toast({
      title: 'Research Rejected',
      description: 'Research request has been rejected.',
    });
  };

  const loadResults = async (queryId: string) => {
    const results = webResearchService.getCachedResults(queryId);
    if (results) {
      setActiveResults(results);
      
      // Generate summary
      try {
        const summary = await webResearchService.generateSummary(queryId);
        setActiveSummary(summary);
      } catch (error) {
        console.error('Summary generation failed:', error);
      }
    }
  };

  const handleAddInsight = (summary: ResearchSummary) => {
    const insight = `Research Insight: ${summary.overallSummary}\n\nKey Points:\n${summary.keyInsights.map(point => `• ${point}`).join('\n')}`;
    onInsightAdd?.(insight, summary.sources);
    
    toast({
      title: 'Insight Added',
      description: 'Research insights added to brainstorming session.',
    });
  };

  const getQueryStatusIcon = (query: ResearchQuery) => {
    if (query.approved) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    } else {
      return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  return (
    <div className={cn("web-research-panel space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Globe className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Web Research</h3>
          {pendingApprovals.length > 0 && (
            <Badge variant="secondary">{pendingApprovals.length} pending</Badge>
          )}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowHistoryDialog(true)}
          >
            <History className="h-4 w-4 mr-2" />
            History
          </Button>
          {pendingApprovals.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowApprovalDialog(true)}
            >
              <Shield className="h-4 w-4 mr-2" />
              Approvals
            </Button>
          )}
        </div>
      </div>

      {/* Research Request Form */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">Request Research</CardTitle>
          <CardDescription className="text-xs">
            Get AI-enhanced web research to support your brainstorming
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <Label htmlFor="research-query" className="text-xs">Research Query</Label>
            <Input
              id="research-query"
              value={researchQuery}
              onChange={(e) => setResearchQuery(e.target.value)}
              placeholder="e.g., best practices for user onboarding"
              className="mt-1"
            />
          </div>
          <div>
            <Label htmlFor="research-context" className="text-xs">Context (Optional)</Label>
            <Textarea
              id="research-context"
              value={researchContext}
              onChange={(e) => setResearchContext(e.target.value)}
              placeholder="Additional context to help focus the research..."
              rows={2}
              className="mt-1"
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                id="auto-approve"
                checked={autoApprove}
                onCheckedChange={setAutoApprove}
              />
              <Label htmlFor="auto-approve" className="text-xs">
                Auto-approve research
              </Label>
            </div>
            <Button
              onClick={handleResearchRequest}
              disabled={isResearching || !researchQuery.trim()}
              size="sm"
            >
              {isResearching ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Researching...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Research
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Research Summary */}
      {activeSummary && (
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm">Research Summary</CardTitle>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {Math.round(activeSummary.confidence * 100)}% confidence
                </Badge>
                <Button
                  size="sm"
                  onClick={() => handleAddInsight(activeSummary)}
                >
                  <Sparkles className="h-4 w-4 mr-1" />
                  Add to Session
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <p className="text-sm">{activeSummary.overallSummary}</p>
            <div>
              <Label className="text-xs font-medium">Key Insights:</Label>
              <ul className="mt-1 space-y-1">
                {activeSummary.keyInsights.map((insight, index) => (
                  <li key={index} className="text-xs flex items-start gap-2">
                    <span className="text-primary">•</span>
                    <span>{insight}</span>
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Research Results */}
      {activeResults.length > 0 && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Research Results</CardTitle>
            <CardDescription className="text-xs">
              {activeResults.length} sources found
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-48">
              <div className="space-y-3">
                {activeResults.map((result) => (
                  <div key={result.id} className="border rounded p-3">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-sm">{result.title}</h4>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {Math.round(result.relevanceScore * 100)}%
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => window.open(result.url, '_blank')}
                        >
                          <ExternalLink className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mb-2">
                      {result.snippet}
                    </p>
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-muted-foreground">{result.source}</span>
                      {result.cached && (
                        <Badge variant="secondary" className="text-xs">
                          Cached
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Approval Dialog */}
      <Dialog open={showApprovalDialog} onOpenChange={setShowApprovalDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Pending Research Approvals</DialogTitle>
          </DialogHeader>
          <div className="space-y-3">
            {pendingApprovals.length === 0 ? (
              <p className="text-sm text-muted-foreground text-center py-4">
                No pending approvals
              </p>
            ) : (
              pendingApprovals.map((query) => (
                <Card key={query.id}>
                  <CardContent className="p-3">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{query.query}</h4>
                        {query.context && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Context: {query.context}
                          </p>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleApproveResearch(query.id)}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRejectResearch(query.id)}
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Requested: {new Date(query.createdAt).toLocaleString()}
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* History Dialog */}
      <Dialog open={showHistoryDialog} onOpenChange={setShowHistoryDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Research History</DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-96">
            <div className="space-y-3">
              {researchHistory.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No research history
                </p>
              ) : (
                researchHistory.map((query) => (
                  <Card key={query.id}>
                    <CardContent className="p-3">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            {getQueryStatusIcon(query)}
                            <h4 className="font-medium text-sm">{query.query}</h4>
                          </div>
                          {query.context && (
                            <p className="text-xs text-muted-foreground">
                              Context: {query.context}
                            </p>
                          )}
                        </div>
                        {query.approved && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => loadResults(query.id)}
                          >
                            View Results
                          </Button>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(query.createdAt).toLocaleString()}
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </div>
  );
};