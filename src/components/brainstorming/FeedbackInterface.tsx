/**
 * Feedback Interface Component
 * 
 * Collects user feedback on AI responses and adapts behavior based on learning
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { PremiumProgress } from '@/components/ui/premium-progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  TrendingUp,
  Brain,
  BarChart3,
  Settings,
  Star,
  AlertCircle,
  X,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/useToast';

export interface FeedbackEntry {
  id: string;
  messageId: string;
  sessionId: string;
  rating: 'positive' | 'negative';
  category: 'accuracy' | 'relevance' | 'creativity' | 'helpfulness' | 'clarity';
  comment?: string;
  context: string;
  persona?: string;
  timestamp: string;
}

export interface FeedbackAnalytics {
  totalFeedback: number;
  positiveRatio: number;
  categoryBreakdown: Record<string, { positive: number; negative: number }>;
  personaPerformance: Record<string, { positive: number; negative: number }>;
  trends: {
    period: string;
    positiveRatio: number;
  }[];
  improvements: string[];
}

interface FeedbackInterfaceProps {
  messageId?: string;
  sessionId: string;
  messageContent?: string;
  persona?: string;
  className?: string;
  onFeedbackSubmit?: (feedback: FeedbackEntry) => void;
}

export const FeedbackInterface: React.FC<FeedbackInterfaceProps> = ({
  messageId,
  sessionId,
  messageContent,
  persona,
  className,
  onFeedbackSubmit,
}) => {
  const { toast } = useToast();
  const [feedbackHistory, setFeedbackHistory] = useState<FeedbackEntry[]>([]);
  const [analytics, setAnalytics] = useState<FeedbackAnalytics | null>(null);
  const [showDetailedFeedback, setShowDetailedFeedback] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<FeedbackEntry['category']>('helpfulness');
  const [feedbackComment, setFeedbackComment] = useState('');
  const [showAnalytics, setShowAnalytics] = useState(false);

  const feedbackCategories = [
    { id: 'accuracy', label: 'Accuracy', description: 'Information correctness' },
    { id: 'relevance', label: 'Relevance', description: 'Contextual appropriateness' },
    { id: 'creativity', label: 'Creativity', description: 'Novel ideas and approaches' },
    { id: 'helpfulness', label: 'Helpfulness', description: 'Practical value' },
    { id: 'clarity', label: 'Clarity', description: 'Clear communication' },
  ];

  // Load feedback data
  useEffect(() => {
    loadFeedbackHistory();
    generateAnalytics();
  }, [sessionId]);

  const loadFeedbackHistory = () => {
    // Load from localStorage or API
    const stored = localStorage.getItem(`feedback_${sessionId}`);
    if (stored) {
      setFeedbackHistory(JSON.parse(stored));
    }
  };

  const saveFeedbackHistory = (history: FeedbackEntry[]) => {
    localStorage.setItem(`feedback_${sessionId}`, JSON.stringify(history));
    setFeedbackHistory(history);
  };

  const handleQuickFeedback = (rating: 'positive' | 'negative') => {
    if (!messageId) return;

    const feedback: FeedbackEntry = {
      id: generateId(),
      messageId,
      sessionId,
      rating,
      category: 'helpfulness',
      context: messageContent || '',
      persona,
      timestamp: new Date().toISOString(),
    };

    const newHistory = [...feedbackHistory, feedback];
    saveFeedbackHistory(newHistory);
    onFeedbackSubmit?.(feedback);

    toast({
      title: 'Feedback Recorded',
      description: `Thank you for your ${rating} feedback!`,
    });

    generateAnalytics();
  };

  const handleDetailedFeedback = () => {
    if (!messageId) return;

    const feedback: FeedbackEntry = {
      id: generateId(),
      messageId,
      sessionId,
      rating: showDetailedFeedback ? 'negative' : 'positive',
      category: selectedCategory,
      comment: feedbackComment,
      context: messageContent || '',
      persona,
      timestamp: new Date().toISOString(),
    };

    const newHistory = [...feedbackHistory, feedback];
    saveFeedbackHistory(newHistory);
    onFeedbackSubmit?.(feedback);

    setShowDetailedFeedback(false);
    setFeedbackComment('');
    
    toast({
      title: 'Detailed Feedback Recorded',
      description: 'Your feedback will help improve AI responses.',
    });

    generateAnalytics();
  };

  const generateAnalytics = () => {
    const history = feedbackHistory;
    if (history.length === 0) {
      setAnalytics(null);
      return;
    }

    const totalFeedback = history.length;
    const positiveCount = history.filter(f => f.rating === 'positive').length;
    const positiveRatio = positiveCount / totalFeedback;

    // Category breakdown
    const categoryBreakdown: Record<string, { positive: number; negative: number }> = {};
    feedbackCategories.forEach(cat => {
      const categoryFeedback = history.filter(f => f.category === cat.id);
      categoryBreakdown[cat.id] = {
        positive: categoryFeedback.filter(f => f.rating === 'positive').length,
        negative: categoryFeedback.filter(f => f.rating === 'negative').length,
      };
    });

    // Persona performance
    const personaPerformance: Record<string, { positive: number; negative: number }> = {};
    history.forEach(feedback => {
      if (feedback.persona) {
        if (!personaPerformance[feedback.persona]) {
          personaPerformance[feedback.persona] = { positive: 0, negative: 0 };
        }
        personaPerformance[feedback.persona][feedback.rating]++;
      }
    });

    // Trends (last 7 days)
    const trends = generateTrends(history);

    // Improvement suggestions
    const improvements = generateImprovements(categoryBreakdown, personaPerformance);

    setAnalytics({
      totalFeedback,
      positiveRatio,
      categoryBreakdown,
      personaPerformance,
      trends,
      improvements,
    });
  };

  const generateTrends = (history: FeedbackEntry[]) => {
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    }).reverse();

    return last7Days.map(date => {
      const dayFeedback = history.filter(f => 
        f.timestamp.startsWith(date)
      );
      const positive = dayFeedback.filter(f => f.rating === 'positive').length;
      const total = dayFeedback.length;
      
      return {
        period: date,
        positiveRatio: total > 0 ? positive / total : 0,
      };
    });
  };

  const generateImprovements = (
    categoryBreakdown: Record<string, { positive: number; negative: number }>,
    personaPerformance: Record<string, { positive: number; negative: number }>
  ): string[] => {
    const improvements: string[] = [];

    // Category-based improvements
    Object.entries(categoryBreakdown).forEach(([category, stats]) => {
      const total = stats.positive + stats.negative;
      if (total > 0 && stats.positive / total < 0.7) {
        const categoryLabel = feedbackCategories.find(c => c.id === category)?.label;
        improvements.push(`Improve ${categoryLabel?.toLowerCase()} in responses`);
      }
    });

    // Persona-based improvements
    Object.entries(personaPerformance).forEach(([persona, stats]) => {
      const total = stats.positive + stats.negative;
      if (total > 0 && stats.positive / total < 0.6) {
        improvements.push(`Refine ${persona} persona behavior`);
      }
    });

    return improvements.slice(0, 3); // Top 3 improvements
  };

  const generateId = (): string => {
    return `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const getCategoryColor = (category: string, stats: { positive: number; negative: number }) => {
    const total = stats.positive + stats.negative;
    if (total === 0) return 'bg-gray-100';
    
    const ratio = stats.positive / total;
    if (ratio >= 0.8) return 'bg-green-100 text-green-800';
    if (ratio >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <div className={cn("feedback-interface", className)}>
      {/* Quick Feedback Buttons */}
      {messageId && (
        <div className="flex items-center gap-2 mb-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleQuickFeedback('positive')}
            className="flex items-center gap-2"
          >
            <ThumbsUp className="h-4 w-4" />
            Helpful
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleQuickFeedback('negative')}
            className="flex items-center gap-2"
          >
            <ThumbsDown className="h-4 w-4" />
            Not Helpful
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDetailedFeedback(true)}
            className="flex items-center gap-2"
          >
            <MessageSquare className="h-4 w-4" />
            Detailed
          </Button>
        </div>
      )}

      {/* Analytics Summary */}
      {analytics && (
        <Card className="mb-4">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Feedback Summary
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAnalytics(true)}
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                View Details
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">Overall Satisfaction</span>
              <div className="flex items-center gap-2">
                <PremiumProgress 
                  value={analytics.positiveRatio * 100} 
                  className="w-20 h-2" 
                />
                <span className="text-sm font-medium">
                  {Math.round(analytics.positiveRatio * 100)}%
                </span>
              </div>
            </div>
            <div className="text-xs text-muted-foreground">
              Based on {analytics.totalFeedback} feedback entries
            </div>
            {analytics.improvements.length > 0 && (
              <div className="mt-2">
                <Label className="text-xs font-medium">Suggested Improvements:</Label>
                <ul className="mt-1 space-y-1">
                  {analytics.improvements.map((improvement, index) => (
                    <li key={index} className="text-xs flex items-start gap-2">
                      <AlertCircle className="h-3 w-3 text-amber-500 mt-0.5" />
                      <span>{improvement}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Detailed Feedback Dialog */}
      <Dialog open={showDetailedFeedback} onOpenChange={setShowDetailedFeedback}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Detailed Feedback</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label className="text-sm">Feedback Category</Label>
              <Select value={selectedCategory} onValueChange={(value: any) => setSelectedCategory(value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {feedbackCategories.map(category => (
                    <SelectItem key={category.id} value={category.id}>
                      <div>
                        <div className="font-medium">{category.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {category.description}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-sm">Comments (Optional)</Label>
              <Textarea
                value={feedbackComment}
                onChange={(e) => setFeedbackComment(e.target.value)}
                placeholder="Share specific feedback to help improve responses..."
                rows={3}
                className="mt-1"
              />
            </div>
            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={() => setShowDetailedFeedback(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleDetailedFeedback}>
                Submit Feedback
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Analytics Dialog */}
      <Dialog open={showAnalytics} onOpenChange={setShowAnalytics}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Feedback Analytics</DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-4 top-4 p-2"
              onClick={() => setShowAnalytics(false)}
              aria-label="Close dialog"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogHeader>
          {analytics && (
            <div className="space-y-6">
              {/* Overall Stats */}
              <div className="grid grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold">{analytics.totalFeedback}</div>
                    <div className="text-sm text-muted-foreground">Total Feedback</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {Math.round(analytics.positiveRatio * 100)}%
                    </div>
                    <div className="text-sm text-muted-foreground">Positive</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {Math.round((1 - analytics.positiveRatio) * 100)}%
                    </div>
                    <div className="text-sm text-muted-foreground">Negative</div>
                  </CardContent>
                </Card>
              </div>

              {/* Category Breakdown */}
              <div>
                <h4 className="font-medium mb-3">Performance by Category</h4>
                <div className="space-y-2">
                  {feedbackCategories.map(category => {
                    const stats = analytics.categoryBreakdown[category.id] || { positive: 0, negative: 0 };
                    const total = stats.positive + stats.negative;
                    const ratio = total > 0 ? stats.positive / total : 0;
                    
                    return (
                      <div key={category.id} className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center gap-3">
                          <Badge className={getCategoryColor(category.id, stats)}>
                            {category.label}
                          </Badge>
                          <span className="text-sm">{category.description}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <PremiumProgress value={ratio * 100} className="w-16 h-2" />
                          <span className="text-sm font-medium w-12">
                            {total > 0 ? `${Math.round(ratio * 100)}%` : 'N/A'}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Persona Performance */}
              {Object.keys(analytics.personaPerformance).length > 0 && (
                <div>
                  <h4 className="font-medium mb-3">Persona Performance</h4>
                  <div className="space-y-2">
                    {Object.entries(analytics.personaPerformance).map(([persona, stats]) => {
                      const total = stats.positive + stats.negative;
                      const ratio = total > 0 ? stats.positive / total : 0;
                      
                      return (
                        <div key={persona} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex items-center gap-3">
                            <Brain className="h-4 w-4" />
                            <span className="font-medium">{persona}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <PremiumProgress value={ratio * 100} className="w-16 h-2" />
                            <span className="text-sm font-medium w-12">
                              {Math.round(ratio * 100)}%
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Trends */}
              <div>
                <h4 className="font-medium mb-3">7-Day Trend</h4>
                <div className="flex items-end gap-2 h-20">
                  {analytics.trends.map((trend, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center">
                      <div 
                        className="w-full bg-primary rounded-t"
                        style={{ height: `${trend.positiveRatio * 100}%` }}
                      />
                      <div className="text-xs mt-1">
                        {new Date(trend.period).getDate()}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};