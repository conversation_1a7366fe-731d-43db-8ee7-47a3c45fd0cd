/**
 * Brainstorming Navigation Component
 * 
 * Dedicated navigation for brainstorming mode with its own menu structure,
 * visual design, and functionality completely separate from Claude Code navigation.
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  Lightbulb,
  Users,
  Target,
  FileText,
  Settings,
  Download,
  Upload,
  Search,
  Filter,
  Plus,
  Grid3x3,
  List,
  Calendar,
  Clock,
  Tag,
  Star,
  TrendingUp,
  BarChart3,
  Palette,
  HelpCircle,
  Menu,
  X
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useBrainstormingTheme } from './BrainstormingThemeProvider';

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  badge?: string;
  shortcut?: string;
  submenu?: NavigationItem[];
}

const NAVIGATION_ITEMS: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: BarChart3,
    shortcut: 'Cmd+D'
  },
  {
    id: 'sessions',
    label: 'Sessions',
    icon: Brain,
    badge: '5',
    shortcut: 'Cmd+S',
    submenu: [
      { id: 'active', label: 'Active Sessions', icon: Target },
      { id: 'recent', label: 'Recent Sessions', icon: Clock },
      { id: 'archived', label: 'Archived', icon: FileText }
    ]
  },
  {
    id: 'ideas',
    label: 'Ideas',
    icon: Lightbulb,
    badge: '23',
    shortcut: 'Cmd+I',
    submenu: [
      { id: 'all-ideas', label: 'All Ideas', icon: Lightbulb },
      { id: 'favorites', label: 'Favorites', icon: Star },
      { id: 'clusters', label: 'Clusters', icon: Grid3x3 }
    ]
  },
  {
    id: 'collaboration',
    label: 'Collaboration',
    icon: Users,
    shortcut: 'Cmd+U',
    submenu: [
      { id: 'team', label: 'Team Members', icon: Users },
      { id: 'shared', label: 'Shared Sessions', icon: Target },
      { id: 'invites', label: 'Invitations', icon: Plus }
    ]
  },
  {
    id: 'templates',
    label: 'Templates',
    icon: FileText,
    shortcut: 'Cmd+T',
    submenu: [
      { id: 'swot', label: 'SWOT Analysis', icon: Grid3x3 },
      { id: 'design-thinking', label: 'Design Thinking', icon: Palette },
      { id: 'five-whys', label: 'Five Whys', icon: HelpCircle },
      { id: 'custom', label: 'Custom Templates', icon: Plus }
    ]
  }
];

const QUICK_ACTIONS: NavigationItem[] = [
  { id: 'new-session', label: 'New Session', icon: Plus },
  { id: 'search', label: 'Search', icon: Search },
  { id: 'export', label: 'Export', icon: Download },
  { id: 'import', label: 'Import', icon: Upload }
];

interface BrainstormingNavigationProps {
  currentView?: string;
  onViewChange?: (viewId: string) => void;
  onSidebarToggle?: () => void;
  isMobile?: boolean;
  className?: string;
  collapsed?: boolean;
  // Legacy props for backward compatibility
  onNavigate?: (viewId: string) => void;
  onQuickAction?: (actionId: string) => void;
  onToggleCollapse?: () => void;
}

export const BrainstormingNavigation: React.FC<BrainstormingNavigationProps> = ({
  currentView = 'dashboard',
  onViewChange,
  onSidebarToggle,
  isMobile = false,
  className,
  collapsed = false,
  // Legacy props
  onNavigate,
  onQuickAction,
  onToggleCollapse
}) => {
  // Use new props with fallback to legacy props
  const handleNavigate = onViewChange || onNavigate || (() => { });
  const handleQuickAction = onQuickAction || (() => { });
  const handleToggleCollapse = onSidebarToggle || onToggleCollapse || (() => { });
  const { theme } = useBrainstormingTheme();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set(['sessions']));

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const isActive = currentView === item.id;
    const isExpanded = expandedItems.has(item.id);
    const hasSubmenu = item.submenu && item.submenu.length > 0;

    return (
      <div key={item.id}>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => {
            if (hasSubmenu) {
              toggleExpanded(item.id);
            } else {
              handleNavigate(item.id);
            }
          }}
          className={cn(
            "w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-all duration-200",
            level > 0 && "ml-6 text-sm",
            isActive && "shadow-md",
            !collapsed && "justify-start",
            collapsed && "justify-center px-2"
          )}
          style={{
            backgroundColor: isActive ? theme.colors.primary : 'transparent',
            color: isActive ? 'white' : theme.colors.text,
            borderLeft: level > 0 ? `2px solid ${theme.colors.border}` : 'none'
          }}
        >
          <item.icon className={cn("flex-shrink-0", collapsed ? "w-5 h-5" : "w-4 h-4")} />

          {!collapsed && (
            <>
              <span className="flex-1 font-medium">{item.label}</span>

              {item.badge && (
                <Badge
                  variant="secondary"
                  className="text-xs"
                  style={{
                    backgroundColor: isActive ? 'rgba(255,255,255,0.2)' : theme.colors.accent,
                    color: isActive ? 'white' : 'white'
                  }}
                >
                  {item.badge}
                </Badge>
              )}

              {item.shortcut && !hasSubmenu && (
                <span
                  className="text-xs opacity-60"
                  style={{ color: isActive ? 'white' : theme.colors.textSecondary }}
                >
                  {item.shortcut}
                </span>
              )}
            </>
          )}
        </motion.button>

        {/* Submenu */}
        {hasSubmenu && !collapsed && (
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden"
              >
                <div className="py-1">
                  {item.submenu!.map(subItem => renderNavigationItem(subItem, level + 1))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </div>
    );
  };

  return (
    <motion.div
      initial={{ width: collapsed ? 64 : 280 }}
      animate={{ width: collapsed ? 64 : 280 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "flex flex-col h-full border-r shadow-lg",
        className
      )}
      style={{
        backgroundColor: theme.colors.surface,
        borderColor: theme.colors.border
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b" style={{ borderColor: theme.colors.border }}>
        {!collapsed && (
          <div className="flex items-center gap-2">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Brain className="w-6 h-6" style={{ color: theme.colors.primary }} />
            </motion.div>
            <div>
              <h2 className="font-bold" style={{ color: theme.colors.text }}>
                Brainstorming
              </h2>
              <p className="text-xs" style={{ color: theme.colors.textSecondary }}>
                Creative Mode
              </p>
            </div>
          </div>
        )}

        <Button
          variant="ghost"
          size="sm"
          onClick={handleToggleCollapse}
          className="p-1"
        >
          {collapsed || isMobile ? <Menu className="w-4 h-4" /> : <X className="w-4 h-4" />}
        </Button>
      </div>

      {/* Quick Actions */}
      {!collapsed && (
        <div className="p-4 border-b" style={{ borderColor: theme.colors.border }}>
          <h3 className="text-sm font-medium mb-3" style={{ color: theme.colors.textSecondary }}>
            Quick Actions
          </h3>
          <div className="grid grid-cols-2 gap-2">
            {QUICK_ACTIONS.map(action => (
              <Button
                key={action.id}
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction(action.id)}
                className="flex items-center gap-2 text-xs"
                style={{
                  borderColor: theme.colors.border,
                  color: theme.colors.text
                }}
              >
                <action.icon className="w-3 h-3" />
                {action.label}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Navigation Items */}
      <div className="flex-1 overflow-y-auto p-4">
        {!collapsed && (
          <h3 className="text-sm font-medium mb-3" style={{ color: theme.colors.textSecondary }}>
            Navigation
          </h3>
        )}

        <div className="space-y-1">
          {NAVIGATION_ITEMS.map(item => renderNavigationItem(item))}
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t" style={{ borderColor: theme.colors.border }}>
        {!collapsed ? (
          <div className="space-y-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleQuickAction('settings')}
              className="w-full justify-start"
            >
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleQuickAction('help')}
              className="w-full justify-start"
            >
              <HelpCircle className="w-4 h-4 mr-2" />
              Help
            </Button>
          </div>
        ) : (
          <div className="space-y-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleQuickAction('settings')}
              className="w-full justify-center p-2"
            >
              <Settings className="w-4 h-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleQuickAction('help')}
              className="w-full justify-center p-2"
            >
              <HelpCircle className="w-4 h-4" />
            </Button>
          </div>
        )}
      </div>
    </motion.div>
  );
};
