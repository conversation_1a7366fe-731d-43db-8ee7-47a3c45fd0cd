import React, { useMemo } from "react";
import {
  Bar<PERSON>hart3,
  Clock,
  MessageSquare,
  Zap,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Activity,
  Target,
  Brain,
  Hash,
  Calendar,
  Download
} from "lucide-react";
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { PremiumProgress } from "@/components/ui/premium-progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface SessionStats {
  totalMessages: number;
  userMessages: number;
  assistantMessages: number;
  totalTokensUsed: number;
  totalTokensGenerated: number;
  averageResponseTime: number;
  sessionDuration: number;
  estimatedCost: number;
  modelsUsed: Record<string, number>;
  messageTypes: Record<string, number>;
  peakTokensPerMessage: number;
  averageTokensPerMessage: number;
  errorCount: number;
  successfulRequests: number;
}

interface SessionAnalyticsProps {
  stats: SessionStats;
  sessionStartTime: Date;
  onExport?: () => void;
  className?: string;
}

const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
};

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
};

const formatCurrency = (amount: number): string => {
  return `$${amount.toFixed(4)}`;
};

export const SessionAnalytics: React.FC<SessionAnalyticsProps> = ({
  stats,
  sessionStartTime,
  onExport,
  className
}) => {
  const efficiency = useMemo(() => {
    const totalRequests = stats.successfulRequests + stats.errorCount;
    return totalRequests > 0 ? (stats.successfulRequests / totalRequests) * 100 : 100;
  }, [stats.successfulRequests, stats.errorCount]);

  const tokensPerMinute = useMemo(() => {
    const minutes = stats.sessionDuration / 60;
    return minutes > 0 ? stats.totalTokensUsed / minutes : 0;
  }, [stats.totalTokensUsed, stats.sessionDuration]);

  const mostUsedModel = useMemo(() => {
    const models = Object.entries(stats.modelsUsed);
    if (models.length === 0) return null;
    return models.reduce((a, b) => a[1] > b[1] ? a : b);
  }, [stats.modelsUsed]);

  const getEfficiencyColor = (efficiency: number) => {
    if (efficiency >= 95) return "text-green-600";
    if (efficiency >= 85) return "text-yellow-600";
    return "text-red-600";
  };

  const getEfficiencyIcon = (efficiency: number) => {
    if (efficiency >= 95) return TrendingUp;
    if (efficiency >= 85) return Activity;
    return TrendingDown;
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    subtitle?: string;
    icon: React.ComponentType<{ className?: string }>;
    trend?: 'up' | 'down' | 'neutral';
    color?: string;
  }> = ({ title, value, subtitle, icon: Icon, trend, color = "text-foreground" }) => (
    <Card className="relative overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <Icon className={cn("h-4 w-4", color)} />
      </CardHeader>
      <CardContent>
        <div className={cn("text-2xl font-bold", color)}>
          {value}
        </div>
        {subtitle && (
          <p className="text-xs text-muted-foreground mt-1">
            {subtitle}
          </p>
        )}
        {trend && (
          <div className="absolute top-2 right-2">
            {trend === 'up' && <TrendingUp className="h-3 w-3 text-green-500" />}
            {trend === 'down' && <TrendingDown className="h-3 w-3 text-red-500" />}
            {trend === 'neutral' && <Activity className="h-3 w-3 text-yellow-500" />}
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="h-6 w-6" />
            Session Analytics
          </h2>
          <p className="text-sm text-muted-foreground mt-1">
            Started {sessionStartTime.toLocaleString()}
          </p>
        </div>
        {onExport && (
          <Button variant="outline" size="sm" onClick={onExport}>
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        )}
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Messages"
          value={stats.totalMessages}
          subtitle={`${stats.userMessages} sent, ${stats.assistantMessages} received`}
          icon={MessageSquare}
          color="text-blue-600"
        />
        
        <StatCard
          title="Tokens Used"
          value={formatNumber(stats.totalTokensUsed)}
          subtitle={`${formatNumber(stats.totalTokensGenerated)} generated`}
          icon={Hash}
          color="text-purple-600"
        />
        
        <StatCard
          title="Session Duration"
          value={formatDuration(stats.sessionDuration)}
          subtitle={`${formatNumber(tokensPerMinute)} tokens/min`}
          icon={Clock}
          color="text-green-600"
        />
        
        <StatCard
          title="Estimated Cost"
          value={formatCurrency(stats.estimatedCost)}
          subtitle="Based on current usage"
          icon={DollarSign}
          color="text-orange-600"
        />
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Efficiency & Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Success Rate</span>
                <div className="flex items-center gap-2">
                  {React.createElement(getEfficiencyIcon(efficiency), {
                    className: cn("h-4 w-4", getEfficiencyColor(efficiency))
                  })}
                  <span className={cn("text-sm font-bold", getEfficiencyColor(efficiency))}>
                    {efficiency.toFixed(1)}%
                  </span>
                </div>
              </div>
              <PremiumProgress value={efficiency} className="h-2" />
              <div className="text-xs text-muted-foreground">
                {stats.successfulRequests} successful, {stats.errorCount} errors
              </div>
            </div>
            
            <Separator />
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {stats.averageResponseTime.toFixed(1)}s
                </div>
                <div className="text-xs text-muted-foreground">Avg Response Time</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {stats.averageTokensPerMessage.toFixed(0)}
                </div>
                <div className="text-xs text-muted-foreground">Avg Tokens/Message</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Model Usage */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Model Usage
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(stats.modelsUsed).length > 0 ? (
              <div className="space-y-3">
                {Object.entries(stats.modelsUsed)
                  .sort(([,a], [,b]) => b - a)
                  .map(([model, count]) => {
                    const percentage = (count / stats.totalMessages) * 100;
                    return (
                      <div key={model} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{model}</span>
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="text-xs">
                              {count} msgs
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {percentage.toFixed(1)}%
                            </span>
                          </div>
                        </div>
                        <PremiumProgress value={percentage} className="h-1.5" />
                      </div>
                    );
                  })
                }
              </div>
            ) : (
              <div className="text-center text-muted-foreground py-4">
                No model usage data available
              </div>
            )}
            
            {mostUsedModel && (
              <>
                <Separator />
                <div className="text-center">
                  <div className="text-sm text-muted-foreground">Most Used Model</div>
                  <div className="text-lg font-bold text-primary">
                    {mostUsedModel[0]}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {mostUsedModel[1]} messages
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Message Types Distribution */}
      {Object.keys(stats.messageTypes).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Message Types
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(stats.messageTypes).map(([type, count]) => {
                const percentage = (count / stats.totalMessages) * 100;
                const getTypeColor = (type: string) => {
                  switch (type) {
                    case 'code': return 'text-blue-600';
                    case 'error': return 'text-red-600';
                    case 'success': return 'text-green-600';
                    default: return 'text-gray-600';
                  }
                };
                
                return (
                  <div key={type} className="text-center space-y-2">
                    <div className={cn("text-2xl font-bold", getTypeColor(type))}>
                      {count}
                    </div>
                    <div className="text-sm font-medium capitalize">{type}</div>
                    <div className="text-xs text-muted-foreground">
                      {percentage.toFixed(1)}%
                    </div>
                    <PremiumProgress value={percentage} className="h-1" />
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Token Usage Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Token Usage Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Total Consumed</div>
              <div className="text-2xl font-bold text-purple-600">
                {formatNumber(stats.totalTokensUsed)}
              </div>
              <div className="text-xs text-muted-foreground">
                Input + Output tokens
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Peak Usage</div>
              <div className="text-2xl font-bold text-orange-600">
                {formatNumber(stats.peakTokensPerMessage)}
              </div>
              <div className="text-xs text-muted-foreground">
                Highest single message
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">Efficiency</div>
              <div className="text-2xl font-bold text-green-600">
                {formatNumber(tokensPerMinute)}
              </div>
              <div className="text-xs text-muted-foreground">
                Tokens per minute
              </div>
            </div>
          </div>
          
          <Separator className="my-4" />
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Token Usage Progress</span>
              <span className="text-muted-foreground">
                {formatNumber(stats.totalTokensUsed)} / {formatNumber(stats.totalTokensUsed + 10000)}
              </span>
            </div>
            <PremiumProgress 
              value={(stats.totalTokensUsed / (stats.totalTokensUsed + 10000)) * 100} 
              className="h-2" 
            />
            <div className="text-xs text-muted-foreground">
              Estimated remaining capacity
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Session Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Session Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-blue-600">
                  {sessionStartTime.toLocaleTimeString()}
                </div>
                <div className="text-xs text-muted-foreground">Session Started</div>
              </div>
              
              <div>
                <div className="text-lg font-bold text-green-600">
                  {new Date(sessionStartTime.getTime() + stats.sessionDuration * 1000).toLocaleTimeString()}
                </div>
                <div className="text-xs text-muted-foreground">Current Time</div>
              </div>
              
              <div>
                <div className="text-lg font-bold text-purple-600">
                  {stats.totalMessages > 0 ? (stats.sessionDuration / stats.totalMessages).toFixed(1) : '0'}s
                </div>
                <div className="text-xs text-muted-foreground">Avg Time/Message</div>
              </div>
              
              <div>
                <div className="text-lg font-bold text-orange-600">
                  {(stats.totalMessages / Math.max(stats.sessionDuration / 3600, 0.01)).toFixed(1)}
                </div>
                <div className="text-xs text-muted-foreground">Messages/Hour</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};