import React, { useState } from "react";
import { motion } from "framer-motion";
import { 
  <PERSON>ton, 
  MagicCard, 
  MagneticButton, 
  EnhancedSkeleton, 
  SkeletonCard,
  PremiumProgress,
  CircularProgress,
  TextAnimate,
  EnhancedSidebar,
  GlassBreadcrumb,
  BreadcrumbPresets,
  FloatingActionButton,
  FABPresets,
  GlassToast,
  useGlassToast,
  EnhancedNavigation,
  ParticleSystem,
  ParticlePresets,
  ParallaxContainer,
  ParallaxElement,
  AnimatedGrid,
  FlowingGradient,
  GeometricPattern,
  WavePattern,
  MorphingBlob,
  Constellation,
  CustomCursor,
  CursorTrail,
  MagneticCursor,
  RippleEffect,
  SpotlightEffect,
  ShapeMorph,
  TextMorph,
  MorphingButton,
  GlitchEffect,
  MorphingPresets
} from "@/components/ui";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  <PERSON>rkles, 
  Zap, 
  <PERSON>, 
  Star,
  <PERSON>Pointer2,
  <PERSON><PERSON>2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>re,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Folder,
  <PERSON>Text,
  Wand2,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>
} from "lucide-react";

interface EnhancedUIDemoProps {
  onBack?: () => void;
}

export const EnhancedUIDemo: React.FC<EnhancedUIDemoProps> = ({ onBack }) => {
  const [progress, setProgress] = useState(65);
  const [showSkeleton, setShowSkeleton] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [morphingButtonState, setMorphingButtonState] = useState(0);
  const [glitchTrigger, setGlitchTrigger] = useState(false);
  const [showParticles, setShowParticles] = useState(true);
  const toast = useGlassToast();

  return (
    <div className="min-h-screen glass-topbar p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-4"
        >
          <TextAnimate
            animation="blurInUp"
            className="text-4xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent"
          >
            Enhanced UI Components
          </TextAnimate>
          <TextAnimate
            animation="fadeIn"
            delay={0.2}
            className="text-lg text-muted-foreground"
          >
            Premium glassmorphism effects, advanced animations, and modern interactions
          </TextAnimate>
          
          {onBack && (
            <Button variant="glass" onClick={onBack} className="mt-4">
              ← Back to App
            </Button>
          )}
        </motion.div>

        {/* Enhanced Buttons Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="glass-medium shadow-glass">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-blue-500" />
                Enhanced Button Variants
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button variant="glass">Glass Effect</Button>
                <Button variant="magnetic">Magnetic</Button>
                <Button variant="floating">Floating</Button>
                <Button variant="magnetic-glass">Magnetic Glass</Button>
                <Button variant="floating-glow">Floating Glow</Button>
                <Button variant="glass-primary">Glass Primary</Button>
                <MagneticButton withRipple withGlow variant="glass">
                  <MousePointer2 className="w-4 h-4 mr-2" />
                  Magnetic Pro
                </MagneticButton>
                <MagneticButton variant="floating" strength={0.5} maxDistance={60}>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Strong Magnet
                </MagneticButton>
              </div>
            </CardContent>
          </Card>
        </motion.section>

        {/* Magic Cards Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="glass-medium shadow-glass">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="w-5 h-5 text-purple-500" />
                3D Interactive Cards
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <MagicCard
                gradientColor="rgba(59, 130, 246, 0.3)"
                className="min-h-[200px] flex items-center justify-center"
              >
                <div className="text-center space-y-2">
                  <Sparkles className="w-8 h-8 mx-auto text-blue-400" />
                  <h3 className="text-lg font-semibold">3D Tilt Effect</h3>
                  <p className="text-sm text-muted-foreground">
                    Hover for perspective transform
                  </p>
                </div>
              </MagicCard>

              <MagicCard
                gradientColor="rgba(168, 85, 247, 0.3)"
                className="min-h-[200px] flex items-center justify-center"
              >
                <div className="text-center space-y-2">
                  <Rocket className="w-8 h-8 mx-auto text-purple-400" />
                  <h3 className="text-lg font-semibold">Glass Surface</h3>
                  <p className="text-sm text-muted-foreground">
                    Glassmorphism with depth
                  </p>
                </div>
              </MagicCard>

              <MagicCard
                gradientColor="rgba(34, 197, 94, 0.3)"
                className="min-h-[200px] flex items-center justify-center"
              >
                <div className="text-center space-y-2">
                  <Gauge className="w-8 h-8 mx-auto text-green-400" />
                  <h3 className="text-lg font-semibold">Interactive</h3>
                  <p className="text-sm text-muted-foreground">
                    Mouse-following gradients
                  </p>
                </div>
              </MagicCard>
            </CardContent>
          </Card>
        </motion.section>

        {/* Progress & Loading Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-6"
        >
          {/* Progress Indicators */}
          <Card className="glass-medium shadow-glass">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gauge className="w-5 h-5 text-green-500" />
                Premium Progress
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Glass Progress</label>
                  <PremiumProgress 
                    value={progress} 
                    variant="glass" 
                    showPercentage 
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Neon Progress</label>
                  <PremiumProgress 
                    value={progress} 
                    variant="neon" 
                    color="purple"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Gradient Progress</label>
                  <PremiumProgress 
                    value={progress} 
                    variant="gradient" 
                    color="orange"
                  />
                </div>

                <div className="flex gap-2 mt-4">
                  <Button 
                    variant="glass" 
                    size="sm" 
                    onClick={() => setProgress(Math.max(0, progress - 10))}
                  >
                    -10%
                  </Button>
                  <Button 
                    variant="glass" 
                    size="sm" 
                    onClick={() => setProgress(Math.min(100, progress + 10))}
                  >
                    +10%
                  </Button>
                </div>
              </div>

              <div className="flex justify-center">
                <CircularProgress 
                  value={progress} 
                  variant="neon"
                  size={100}
                />
              </div>
            </CardContent>
          </Card>

          {/* Loading States */}
          <Card className="glass-medium shadow-glass">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Loader2 className="w-5 h-5 text-blue-500" />
                Premium Loading States
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex gap-2 mb-4">
                <Button 
                  variant="glass" 
                  size="sm" 
                  onClick={() => setShowSkeleton(!showSkeleton)}
                >
                  {showSkeleton ? 'Hide' : 'Show'} Skeleton
                </Button>
              </div>

              {showSkeleton ? (
                <div className="space-y-4">
                  <EnhancedSkeleton 
                    variant="shimmer" 
                    showAvatar 
                    showButton 
                    lines={3} 
                  />
                  <EnhancedSkeleton 
                    variant="wave" 
                    lines={2} 
                  />
                  <EnhancedSkeleton 
                    variant="pulse" 
                    lines={1} 
                    className="h-8"
                  />
                </div>
              ) : (
                <SkeletonCard />
              )}
            </CardContent>
          </Card>
        </motion.section>

        {/* Text Animations Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="glass-medium shadow-glass">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-yellow-500" />
                Advanced Text Animations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <TextAnimate
                    animation="blurInUp"
                    by="word"
                    className="text-xl font-semibold"
                  >
                    Blur in from below with spring physics
                  </TextAnimate>
                  
                  <TextAnimate
                    animation="slideLeft"
                    by="character"
                    delay={0.5}
                    className="text-lg"
                  >
                    Character by character reveal
                  </TextAnimate>
                </div>

                <div className="space-y-4">
                  <TextAnimate
                    animation="scaleUp"
                    by="word"
                    delay={1}
                    className="text-xl font-semibold"
                  >
                    Scale up with bounce effect
                  </TextAnimate>
                  
                  <TextAnimate
                    animation="fadeIn"
                    by="line"
                    delay={1.5}
                    className="text-lg"
                  >
                    Smooth fade in animation
                    with multiple lines
                    for better presentation
                  </TextAnimate>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.section>

        {/* Layout & Navigation Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="glass-medium shadow-glass">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Menu className="w-5 h-5 text-purple-500" />
                Layout & Navigation Components
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Enhanced Sidebar Demo */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Enhanced Glass Sidebar</h3>
                <div className="relative">
                  <Button 
                    variant="glass" 
                    onClick={() => setShowSidebar(true)}
                    className="mb-4"
                  >
                    <Menu className="w-4 h-4 mr-2" />
                    Open Sidebar Demo
                  </Button>
                  <p className="text-sm text-muted-foreground">
                    Advanced sidebar with glassmorphism, search, collapsible sections, and smooth animations.
                  </p>
                </div>
              </div>

              {/* Breadcrumb Demo */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Glass Breadcrumb Navigation</h3>
                <div className="space-y-3">
                  <GlassBreadcrumb
                    items={BreadcrumbPresets.project("My Project")}
                  />
                  <GlassBreadcrumb
                    items={BreadcrumbPresets.session("My Project", "Chat Session #1")}
                  />
                  <GlassBreadcrumb
                    items={BreadcrumbPresets.file("component.tsx")}
                  />
                </div>
              </div>

              {/* Enhanced Navigation Demo */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Enhanced Navigation</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Vertical Navigation */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">Vertical Glass Navigation</h4>
                    <EnhancedNavigation
                      orientation="vertical"
                      variant="glass"
                      items={[
                        {
                          id: 'home',
                          label: 'Dashboard',
                          icon: Home,
                          isActive: true,
                          onClick: () => toast.info('Dashboard clicked')
                        },
                        {
                          id: 'projects',
                          label: 'Projects',
                          icon: Folder,
                          badge: 3,
                          onClick: () => toast.success('Projects opened'),
                          children: [
                            {
                              id: 'project-1',
                              label: 'Claude Code',
                              icon: FileText,
                              onClick: () => toast.info('Claude Code project')
                            },
                            {
                              id: 'project-2',
                              label: 'AI Assistant',
                              icon: Bot,
                              isNew: true,
                              onClick: () => toast.premium('Premium AI Assistant!')
                            }
                          ]
                        },
                        {
                          id: 'settings',
                          label: 'Settings',
                          icon: Settings,
                          onClick: () => toast.warning('Settings panel')
                        }
                      ]}
                    />
                  </div>

                  {/* Horizontal Navigation */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">Horizontal Pill Navigation</h4>
                    <EnhancedNavigation
                      orientation="horizontal"
                      variant="pills"
                      items={[
                        {
                          id: 'chat',
                          label: 'Chat',
                          icon: MessageSquare,
                          isActive: true,
                          onClick: () => toast.info('Chat selected')
                        },
                        {
                          id: 'agents',
                          label: 'Agents',
                          icon: Bot,
                          badge: '2',
                          onClick: () => toast.success('Agents panel')
                        },
                        {
                          id: 'premium',
                          label: 'Premium',
                          icon: Star,
                          isPremium: true,
                          onClick: () => toast.premium('Premium features!')
                        }
                      ]}
                    />
                  </div>
                </div>
              </div>

              {/* Toast Notifications Demo */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Glass Toast Notifications</h3>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="glass"
                    size="sm"
                    onClick={() => toast.success('Success!', 'Operation completed successfully')}
                  >
                    Success Toast
                  </Button>
                  <Button
                    variant="glass"
                    size="sm"
                    onClick={() => toast.error('Error!', 'Something went wrong')}
                  >
                    Error Toast
                  </Button>
                  <Button
                    variant="glass"
                    size="sm"
                    onClick={() => toast.warning('Warning!', 'Please check your input')}
                  >
                    Warning Toast
                  </Button>
                  <Button
                    variant="glass"
                    size="sm"
                    onClick={() => toast.info('Info', 'Here\'s some useful information')}
                  >
                    Info Toast
                  </Button>
                  <Button
                    variant="glass"
                    size="sm"
                    onClick={() => toast.premium('Premium!', 'Exclusive premium feature unlocked')}
                  >
                    Premium Toast
                  </Button>
                  <Button
                    variant="glass"
                    size="sm"
                    onClick={() => {
                      const id = toast.loading('Loading...', 'Processing your request');
                      setTimeout(() => {
                        toast.dismissToast(id);
                        toast.success('Complete!', 'Processing finished');
                      }, 3000);
                    }}
                  >
                    Loading Toast
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.section>

        {/* Advanced Effects Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card className="glass-medium shadow-glass">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wand2 className="w-5 h-5 text-yellow-500" />
                Advanced Visual Effects
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Particle Systems */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Particle Systems</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">Floating Particles</h4>
                    <div className="relative h-32 glass-subtle rounded-lg overflow-hidden">
                      {showParticles && (
                        <ParticleSystem
                          {...ParticlePresets.floating}
                          width={300}
                          height={128}
                        />
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">Magical Effects</h4>
                    <div className="relative h-32 glass-subtle rounded-lg overflow-hidden">
                      <ParticleSystem
                        {...ParticlePresets.magical}
                        width={300}
                        height={128}
                      />
                    </div>
                  </div>
                </div>
                <Button
                  variant="glass"
                  size="sm"
                  onClick={() => setShowParticles(!showParticles)}
                >
                  {showParticles ? 'Hide' : 'Show'} Particles
                </Button>
              </div>

              {/* Animated Backgrounds */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Animated Backgrounds</h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div className="relative h-24 glass-subtle rounded-lg overflow-hidden">
                    <AnimatedGrid size={20} animate={true} />
                    <div className="absolute inset-0 flex items-center justify-center text-xs font-medium">
                      Animated Grid
                    </div>
                  </div>
                  <div className="relative h-24 glass-subtle rounded-lg overflow-hidden">
                    <GeometricPattern pattern="hexagons" size={30} animate={true} />
                    <div className="absolute inset-0 flex items-center justify-center text-xs font-medium">
                      Geometric Pattern
                    </div>
                  </div>
                  <div className="relative h-24 glass-subtle rounded-lg overflow-hidden">
                    <Constellation starCount={20} animate={true} />
                    <div className="absolute inset-0 flex items-center justify-center text-xs font-medium">
                      Constellation
                    </div>
                  </div>
                </div>
              </div>

              {/* Morphing Effects */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Morphing & Transformation</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium text-muted-foreground">Shape Morphing</h4>
                    <div className="flex items-center justify-center h-32 glass-subtle rounded-lg">
                      <ShapeMorph
                        shapes={MorphingPresets.geometricShapes}
                        duration={2}
                        size={80}
                        color="#3b82f6"
                      />
                    </div>
                  </div>
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium text-muted-foreground">Text Morphing</h4>
                    <div className="flex items-center justify-center h-32 glass-subtle rounded-lg">
                      <TextMorph
                        texts={['Transform', 'Evolve', 'Animate', 'Morph']}
                        duration={2}
                        className="text-2xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Interactive Effects */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Interactive Effects</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium text-muted-foreground">Magnetic Interaction</h4>
                    <div className="flex items-center justify-center h-32 glass-subtle rounded-lg">
                      <MagneticCursor strength={0.3}>
                        <Button variant="glass">
                          <Mouse className="w-4 h-4 mr-2" />
                          Hover me!
                        </Button>
                      </MagneticCursor>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium text-muted-foreground">Ripple Effect</h4>
                    <div className="flex items-center justify-center h-32 glass-subtle rounded-lg">
                      <RippleEffect>
                        <Button variant="glass">
                          <Zap className="w-4 h-4 mr-2" />
                          Click for ripples!
                        </Button>
                      </RippleEffect>
                    </div>
                  </div>
                </div>
              </div>

              {/* Advanced Button Effects */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Advanced Button Effects</h3>
                <div className="flex flex-wrap gap-4">
                  <MorphingButton
                    states={[
                      { text: 'Transform', color: '#3b82f6', icon: <Wand2 className="w-4 h-4" /> },
                      { text: 'Loading...', color: '#f59e0b', icon: <Loader2 className="w-4 h-4 animate-spin" /> },
                      { text: 'Complete!', color: '#10b981', icon: <Star className="w-4 h-4" /> }
                    ]}
                    currentState={morphingButtonState}
                    onClick={() => setMorphingButtonState((prev) => (prev + 1) % 3)}
                  />
                  
                  <GlitchEffect trigger={glitchTrigger} intensity={3}>
                    <Button
                      variant="glass"
                      onClick={() => {
                        setGlitchTrigger(true);
                        setTimeout(() => setGlitchTrigger(false), 1000);
                      }}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Glitch Effect
                    </Button>
                  </GlitchEffect>
                </div>
              </div>

              {/* Parallax Demo */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Parallax Scrolling</h3>
                <div className="h-64 glass-subtle rounded-lg overflow-hidden">
                  <ParallaxContainer
                    height="100%"
                    layers={[
                      {
                        id: 'bg',
                        speed: 0.2,
                        children: (
                          <div className="w-full h-full bg-gradient-to-br from-blue-500/20 to-purple-500/20" />
                        )
                      },
                      {
                        id: 'mid',
                        speed: 0.5,
                        children: (
                          <div className="flex items-center justify-center h-full text-4xl font-bold opacity-30">
                            Parallax
                          </div>
                        )
                      },
                      {
                        id: 'front',
                        speed: 1,
                        children: (
                          <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                              <Layers className="w-8 h-8 mx-auto mb-2 text-blue-500" />
                              <p className="text-sm text-muted-foreground">Scroll to see parallax effect</p>
                            </div>
                          </div>
                        )
                      }
                    ]}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.section>
      </div>
      
      {/* Enhanced Sidebar Component */}
      <EnhancedSidebar
        isOpen={showSidebar}
        onClose={() => setShowSidebar(false)}
        onNavigate={(item) => {
          toast.info(`Navigated to: ${item.label}`);
          setShowSidebar(false);
        }}
      />
      
      {/* Floating Action Button */}
      <FloatingActionButton
        actions={FABPresets.chat(
          () => toast.success('New Chat', 'Created new chat session'),
          () => toast.info('New Agent', 'Opening agent creation wizard')
        )}
        position="bottom-right"
        showLabels={true}
      />
      
      {/* Toast Container */}
      {toast.toasts.length > 0 && (
        <GlassToastContainer
          toasts={toast.toasts}
          onDismiss={toast.dismissToast}
          position="top-right"
        />
      )}
    </div>
  );
};