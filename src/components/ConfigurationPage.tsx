import React, { useState } from 'react';
import {
  Palette,
  Type,
  Monitor,
  Accessibility,
  Layers,
  Download,
  Upload,
  RotateCcw,
  Layout,
  Sliders
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useTheme, ThemePresets, type ThemeConfig } from '@/lib/theme-persistence';
import { useAppConfig } from '@/lib/app-config-api';
import { cn } from '@/lib/utils';
import { useToastContext } from '@/contexts/ToastContext';

// Note: AppConfig interface is now imported from app-config-api.ts

interface ConfigurationPageProps {
  onBack?: () => void;
  className?: string;
}

export const ConfigurationPage: React.FC<ConfigurationPageProps> = ({
  onBack,
  className,
}) => {
  const {
    config: themeConfig,
    updateConfig: updateThemeConfig,
    updateColors,
    updateTypography,
    updateGlassmorphism,
    updateAnimations,
    updateAccessibility,
    updatePerformance,
    resetToDefaults: resetThemeToDefaults,
  } = useTheme();

  const { success: showSuccessToast, error: showErrorToast } = useToastContext();
  const [activeTab, setActiveTab] = useState('appearance');
  const [importText, setImportText] = useState('');

  // Use the app config hook
  const {
    config: appConfig,
    updateToolbarConfig,
    updateWindowConfig,
    updateEditorConfig,
    resetToDefaults: resetAppToDefaults,
    exportConfig: exportAppConfig,
    importConfig: importAppConfig,
  } = useAppConfig();

  const handlePresetApply = (preset: ThemeConfig) => {
    updateThemeConfig(preset);
    showSuccessToast('Theme preset applied');
  };

  const handleExportConfig = async () => {
    try {
      const appConfigJson = await exportAppConfig();
      const fullConfig = {
        theme: themeConfig,
        app: JSON.parse(appConfigJson),
        version: '1.0.0',
        exportedAt: new Date().toISOString(),
      };

      const blob = new Blob([JSON.stringify(fullConfig, null, 2)], {
        type: 'application/json',
      });

      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'claudia-config.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      showSuccessToast('Configuration exported successfully');
    } catch (error) {
      showErrorToast('Failed to export configuration');
    }
  };

  const handleImportConfig = async () => {
    try {
      const parsed = JSON.parse(importText);

      if (parsed.theme) {
        updateThemeConfig(parsed.theme);
      }

      if (parsed.app) {
        await importAppConfig(JSON.stringify(parsed.app));
      }

      setImportText('');
      showSuccessToast('Configuration imported successfully');
    } catch (error) {
      showErrorToast('Failed to import configuration. Please check the format.');
    }
  };

  const resetAllToDefaults = async () => {
    try {
      resetThemeToDefaults();
      await resetAppToDefaults();
      showSuccessToast('All settings reset to defaults');
    } catch (error) {
      showErrorToast('Failed to reset configuration');
    }
  };

  const tabs = [
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'typography', label: 'Typography', icon: Type },
    { id: 'toolbar', label: 'Toolbar', icon: Layout },
    { id: 'window', label: 'Window', icon: Monitor },
    { id: 'editor', label: 'Editor', icon: Sliders },
    { id: 'effects', label: 'Effects', icon: Layers },
    { id: 'accessibility', label: 'Accessibility', icon: Accessibility },
    { id: 'performance', label: 'Performance', icon: Monitor },
  ];

  return (
    <div className={cn('h-full flex flex-col bg-background', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-3">
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              ← Back
            </Button>
          )}
          <div>
            <h1 className="text-xl font-semibold">Configuration</h1>
            <p className="text-sm text-muted-foreground">
              Customize your Claudia experience
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleExportConfig}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={resetAllToDefaults}>
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset All
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="px-4 pt-4">
            <TabsList className="grid grid-cols-4 lg:grid-cols-8 w-full">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-1">
                    <Icon className="w-4 h-4" />
                    <span className="hidden sm:inline">{tab.label}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4">
            {/* Appearance Tab */}
            <TabsContent value="appearance" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Theme & Colors</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Theme Mode */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Theme Mode</Label>
                    <Select
                      value={themeConfig.mode}
                      onValueChange={(value: 'light' | 'dark' | 'auto') => 
                        updateThemeConfig({ mode: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="auto">Auto (System)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Color Palette */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium mb-2 block">Primary Color</Label>
                      <div className="flex items-center gap-2">
                        <input
                          type="color"
                          value={themeConfig.colors.primary}
                          onChange={(e) => updateColors({ primary: e.target.value })}
                          className="w-10 h-10 rounded border border-input"
                        />
                        <Input
                          value={themeConfig.colors.primary}
                          onChange={(e) => updateColors({ primary: e.target.value })}
                          className="flex-1"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium mb-2 block">Accent Color</Label>
                      <div className="flex items-center gap-2">
                        <input
                          type="color"
                          value={themeConfig.colors.accent}
                          onChange={(e) => updateColors({ accent: e.target.value })}
                          className="w-10 h-10 rounded border border-input"
                        />
                        <Input
                          value={themeConfig.colors.accent}
                          onChange={(e) => updateColors({ accent: e.target.value })}
                          className="flex-1"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Theme Presets */}
                  <div>
                    <Label className="text-sm font-medium mb-3 block">Quick Presets</Label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      {Object.entries(ThemePresets).map(([name, preset]) => (
                        <Button
                          key={name}
                          variant="outline"
                          size="sm"
                          onClick={() => handlePresetApply(preset)}
                          className="capitalize"
                        >
                          {name}
                        </Button>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Typography Tab */}
            <TabsContent value="typography" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Text & Typography</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Font Size */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Font Size</Label>
                    <Select
                      value={themeConfig.typography.fontSize}
                      onValueChange={(value: 'small' | 'medium' | 'large' | 'xl') =>
                        updateTypography({ fontSize: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">Small</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="large">Large</SelectItem>
                        <SelectItem value="xl">Extra Large</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Font Family */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Font Family</Label>
                    <Select
                      value={themeConfig.typography.fontFamily}
                      onValueChange={(value) => updateTypography({ fontFamily: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Inter, system-ui, sans-serif">Inter (Default)</SelectItem>
                        <SelectItem value="system-ui, sans-serif">System UI</SelectItem>
                        <SelectItem value="'SF Pro Display', system-ui, sans-serif">SF Pro Display</SelectItem>
                        <SelectItem value="'Segoe UI', system-ui, sans-serif">Segoe UI</SelectItem>
                        <SelectItem value="'JetBrains Mono', monospace">JetBrains Mono</SelectItem>
                        <SelectItem value="'Fira Code', monospace">Fira Code</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Preview */}
                  <div className="p-4 border rounded-lg bg-muted/50">
                    <p className="text-sm text-muted-foreground mb-2">Preview:</p>
                    <div style={{ fontFamily: themeConfig.typography.fontFamily }}>
                      <h3 className="text-lg font-semibold mb-2">Sample Heading</h3>
                      <p className="text-sm">
                        This is how your text will appear with the current typography settings.
                        The quick brown fox jumps over the lazy dog.
                      </p>
                      <code className="text-xs bg-background px-2 py-1 rounded mt-2 inline-block">
                        console.log("Code sample");
                      </code>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Toolbar Tab */}
            <TabsContent value="toolbar" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Toolbar Customization</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Toolbar Visibility */}
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Show Toolbar</Label>
                      <p className="text-xs text-muted-foreground">
                        Toggle the main toolbar visibility
                      </p>
                    </div>
                    <Switch
                      checked={appConfig.toolbar.visible}
                      onCheckedChange={(checked) =>
                        updateToolbarConfig({ visible: checked })
                      }
                    />
                  </div>

                  {/* Toolbar Position */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Toolbar Position</Label>
                    <Select
                      value={appConfig.toolbar.position}
                      onValueChange={(value: 'top' | 'bottom') =>
                        updateToolbarConfig({ position: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="top">Top</SelectItem>
                        <SelectItem value="bottom">Bottom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Toolbar Size */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Button Size</Label>
                    <Select
                      value={appConfig.toolbar.size}
                      onValueChange={(value: 'small' | 'medium' | 'large') =>
                        updateToolbarConfig({ size: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">Small</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="large">Large</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Toolbar Buttons */}
                  <div>
                    <Label className="text-sm font-medium mb-3 block">Visible Buttons</Label>
                    <div className="space-y-3">
                      {Object.entries(appConfig.toolbar.buttons).map(([key, enabled]) => (
                        <div key={key} className="flex items-center justify-between">
                          <Label className="text-sm capitalize">
                            {key.replace(/([A-Z])/g, ' $1').trim()}
                          </Label>
                          <Switch
                            checked={enabled}
                            onCheckedChange={(checked) =>
                              updateToolbarConfig({
                                buttons: { ...appConfig.toolbar.buttons, [key]: checked }
                              })
                            }
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Window Tab */}
            <TabsContent value="window" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Window Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Default Window Size */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium mb-2 block">Default Width</Label>
                      <Input
                        type="number"
                        min="400"
                        max="3840"
                        value={appConfig.window.default_width}
                        onChange={(e) =>
                          updateWindowConfig({ default_width: parseInt(e.target.value) || 1200 })
                        }
                      />
                    </div>
                    <div>
                      <Label className="text-sm font-medium mb-2 block">Default Height</Label>
                      <Input
                        type="number"
                        min="300"
                        max="2160"
                        value={appConfig.window.default_height}
                        onChange={(e) =>
                          updateWindowConfig({ default_height: parseInt(e.target.value) || 800 })
                        }
                      />
                    </div>
                  </div>

                  {/* Window Behavior */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Always on Top</Label>
                        <p className="text-xs text-muted-foreground">
                          Keep window above other applications
                        </p>
                      </div>
                      <Switch
                        checked={appConfig.window.always_on_top}
                        onCheckedChange={(checked) =>
                          updateWindowConfig({ always_on_top: checked })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Minimize to Tray</Label>
                        <p className="text-xs text-muted-foreground">
                          Hide to system tray when minimized
                        </p>
                      </div>
                      <Switch
                        checked={appConfig.window.minimize_to_tray}
                        onCheckedChange={(checked) =>
                          updateWindowConfig({ minimize_to_tray: checked })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Start Maximized</Label>
                        <p className="text-xs text-muted-foreground">
                          Open window in maximized state
                        </p>
                      </div>
                      <Switch
                        checked={appConfig.window.start_maximized}
                        onCheckedChange={(checked) =>
                          updateWindowConfig({ start_maximized: checked })
                        }
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Editor Tab */}
            <TabsContent value="editor" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Editor Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Editor Font Size */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">
                      Editor Font Size: {appConfig.editor.font_size}px
                    </Label>
                    <Slider
                      value={[appConfig.editor.font_size]}
                      onValueChange={([value]) => updateEditorConfig({ font_size: value })}
                      min={10}
                      max={24}
                      step={1}
                      className="w-full"
                    />
                  </div>

                  {/* Tab Size */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">
                      Tab Size: {appConfig.editor.tab_size} spaces
                    </Label>
                    <Slider
                      value={[appConfig.editor.tab_size]}
                      onValueChange={([value]) => updateEditorConfig({ tab_size: value })}
                      min={2}
                      max={8}
                      step={1}
                      className="w-full"
                    />
                  </div>

                  {/* Editor Features */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Line Numbers</Label>
                        <p className="text-xs text-muted-foreground">
                          Show line numbers in editor
                        </p>
                      </div>
                      <Switch
                        checked={appConfig.editor.line_numbers}
                        onCheckedChange={(checked) =>
                          updateEditorConfig({ line_numbers: checked })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Word Wrap</Label>
                        <p className="text-xs text-muted-foreground">
                          Wrap long lines in editor
                        </p>
                      </div>
                      <Switch
                        checked={appConfig.editor.word_wrap}
                        onCheckedChange={(checked) =>
                          updateEditorConfig({ word_wrap: checked })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm font-medium">Minimap</Label>
                        <p className="text-xs text-muted-foreground">
                          Show code minimap overview
                        </p>
                      </div>
                      <Switch
                        checked={appConfig.editor.minimap}
                        onCheckedChange={(checked) =>
                          updateEditorConfig({ minimap: checked })
                        }
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Effects Tab */}
            <TabsContent value="effects" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Visual Effects</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Glassmorphism */}
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Glassmorphism Effects</Label>
                      <p className="text-xs text-muted-foreground">
                        Enable glass-like transparency effects
                      </p>
                    </div>
                    <Switch
                      checked={themeConfig.glassmorphism.enabled}
                      onCheckedChange={(checked) =>
                        updateGlassmorphism({ enabled: checked })
                      }
                    />
                  </div>

                  {themeConfig.glassmorphism.enabled && (
                    <>
                      {/* Blur Intensity */}
                      <div>
                        <Label className="text-sm font-medium mb-2 block">
                          Blur Intensity: {themeConfig.glassmorphism.blur}px
                        </Label>
                        <Slider
                          value={[themeConfig.glassmorphism.blur]}
                          onValueChange={([value]) => updateGlassmorphism({ blur: value })}
                          min={0}
                          max={32}
                          step={1}
                          className="w-full"
                        />
                      </div>

                      {/* Opacity */}
                      <div>
                        <Label className="text-sm font-medium mb-2 block">
                          Opacity: {Math.round(themeConfig.glassmorphism.opacity * 100)}%
                        </Label>
                        <Slider
                          value={[themeConfig.glassmorphism.opacity]}
                          onValueChange={([value]) => updateGlassmorphism({ opacity: value })}
                          min={0.1}
                          max={1}
                          step={0.1}
                          className="w-full"
                        />
                      </div>

                      {/* Intensity Preset */}
                      <div>
                        <Label className="text-sm font-medium mb-2 block">Intensity Preset</Label>
                        <Select
                          value={themeConfig.glassmorphism.intensity}
                          onValueChange={(value: 'subtle' | 'medium' | 'strong') =>
                            updateGlassmorphism({ intensity: value })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="subtle">Subtle</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="strong">Strong</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </>
                  )}

                  {/* Animations */}
                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Animations</Label>
                      <p className="text-xs text-muted-foreground">
                        Enable UI animations and transitions
                      </p>
                    </div>
                    <Switch
                      checked={themeConfig.animations.enabled}
                      onCheckedChange={(checked) =>
                        updateAnimations({ enabled: checked })
                      }
                    />
                  </div>

                  {themeConfig.animations.enabled && (
                    <div>
                      <Label className="text-sm font-medium mb-2 block">Animation Speed</Label>
                      <Select
                        value={themeConfig.animations.duration}
                        onValueChange={(value: 'fast' | 'normal' | 'slow') =>
                          updateAnimations({ duration: value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="fast">Fast</SelectItem>
                          <SelectItem value="normal">Normal</SelectItem>
                          <SelectItem value="slow">Slow</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Accessibility Tab */}
            <TabsContent value="accessibility" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Accessibility Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">High Contrast</Label>
                      <p className="text-xs text-muted-foreground">
                        Increase contrast for better visibility
                      </p>
                    </div>
                    <Switch
                      checked={themeConfig.accessibility.highContrast}
                      onCheckedChange={(checked) =>
                        updateAccessibility({ highContrast: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Focus Rings</Label>
                      <p className="text-xs text-muted-foreground">
                        Show focus indicators for keyboard navigation
                      </p>
                    </div>
                    <Switch
                      checked={themeConfig.accessibility.focusRings}
                      onCheckedChange={(checked) =>
                        updateAccessibility({ focusRings: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Screen Reader Support</Label>
                      <p className="text-xs text-muted-foreground">
                        Optimize for screen reader compatibility
                      </p>
                    </div>
                    <Switch
                      checked={themeConfig.accessibility.screenReader}
                      onCheckedChange={(checked) =>
                        updateAccessibility({ screenReader: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Reduce Motion</Label>
                      <p className="text-xs text-muted-foreground">
                        Minimize animations for motion sensitivity
                      </p>
                    </div>
                    <Switch
                      checked={themeConfig.animations.reduceMotion}
                      onCheckedChange={(checked) =>
                        updateAnimations({ reduceMotion: checked })
                      }
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Performance Tab */}
            <TabsContent value="performance" className="space-y-6 mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">GPU Acceleration</Label>
                      <p className="text-xs text-muted-foreground">
                        Use hardware acceleration for better performance
                      </p>
                    </div>
                    <Switch
                      checked={themeConfig.performance.enableGPUAcceleration}
                      onCheckedChange={(checked) =>
                        updatePerformance({ enableGPUAcceleration: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Virtualization</Label>
                      <p className="text-xs text-muted-foreground">
                        Enable virtual scrolling for large lists
                      </p>
                    </div>
                    <Switch
                      checked={themeConfig.performance.enableVirtualization}
                      onCheckedChange={(checked) =>
                        updatePerformance({ enableVirtualization: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Reduced Data Usage</Label>
                      <p className="text-xs text-muted-foreground">
                        Optimize for limited bandwidth connections
                      </p>
                    </div>
                    <Switch
                      checked={themeConfig.performance.prefersReducedData}
                      onCheckedChange={(checked) =>
                        updatePerformance({ prefersReducedData: checked })
                      }
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Import/Export Section */}
              <Card>
                <CardHeader>
                  <CardTitle>Import & Export</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Import Configuration</Label>
                    <div className="space-y-2">
                      <textarea
                        value={importText}
                        onChange={(e) => setImportText(e.target.value)}
                        placeholder="Paste your configuration JSON here..."
                        className="w-full h-32 p-3 text-sm border rounded-md resize-none bg-background"
                      />
                      <div className="flex gap-2">
                        <Button
                          onClick={handleImportConfig}
                          disabled={!importText.trim()}
                          size="sm"
                        >
                          <Upload className="w-4 h-4 mr-2" />
                          Import
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setImportText('')}
                        >
                          Clear
                        </Button>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Export Configuration</Label>
                      <p className="text-xs text-muted-foreground">
                        Download your current settings as a JSON file
                      </p>
                    </div>
                    <Button variant="outline" size="sm" onClick={handleExportConfig}>
                      <Download className="w-4 h-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
};
