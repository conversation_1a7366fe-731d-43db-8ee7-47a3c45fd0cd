import React, { useState, useRef, use<PERSON>allback, useEffect } from "react";
import { Editor, OnMount, OnChange, Monaco } from "@monaco-editor/react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Save,
  Copy,
  Download,
  Upload,
  Settings,
  Maximize2,
  Minimize2,
  FileCode,
  Sparkles,
  Search,
  Replace,
  Undo,
  Redo,
  Code,
  Terminal,
  Play,
  Bug,
  GitBranch,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  Sun,
  Moon,
  Eye,
  EyeOff,
  Zap
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/contexts/ToastContext";
import { cn } from "@/lib/utils";
import { writeTextFile, readTextFile } from "@tauri-apps/plugin-fs";
import { save, open } from "@tauri-apps/plugin-dialog";

interface CodeEditorProps {
  initialValue?: string;
  language?: string;
  fileName?: string;
  filePath?: string;
  readOnly?: boolean;
  height?: string;
  theme?: "vs-dark" | "vs-light";
  showLineNumbers?: boolean;
  showMinimap?: boolean;
  wordWrap?: "on" | "off";
  fontSize?: number;
  tabSize?: number;
  onSave?: (value: string) => void;
  onChange?: (value: string) => void;
  onLanguageChange?: (language: string) => void;
  className?: string;
  showAIAssist?: boolean;
  onAIAssist?: (code: string, selection: string) => void;
}

interface EditorSettings {
  theme: "vs-dark" | "vs-light";
  fontSize: number;
  tabSize: number;
  wordWrap: "on" | "off";
  showLineNumbers: boolean;
  showMinimap: boolean;
  autoSave: boolean;
  autoSaveDelay: number;
  formatOnSave: boolean;
  formatOnPaste: boolean;
  bracketPairColorization: boolean;
  autoClosingBrackets: "always" | "never" | "languageDefined";
  autoIndent: "none" | "keep" | "brackets" | "advanced" | "full";
}

const LANGUAGE_OPTIONS = [
  { value: "javascript", label: "JavaScript", icon: "🟨" },
  { value: "typescript", label: "TypeScript", icon: "🔷" },
  { value: "python", label: "Python", icon: "🐍" },
  { value: "java", label: "Java", icon: "☕" },
  { value: "csharp", label: "C#", icon: "🟦" },
  { value: "cpp", label: "C++", icon: "🔵" },
  { value: "c", label: "C", icon: "🔵" },
  { value: "go", label: "Go", icon: "🐹" },
  { value: "rust", label: "Rust", icon: "🦀" },
  { value: "php", label: "PHP", icon: "🐘" },
  { value: "ruby", label: "Ruby", icon: "💎" },
  { value: "swift", label: "Swift", icon: "🦉" },
  { value: "kotlin", label: "Kotlin", icon: "🟣" },
  { value: "dart", label: "Dart", icon: "🎯" },
  { value: "html", label: "HTML", icon: "📄" },
  { value: "css", label: "CSS", icon: "🎨" },
  { value: "scss", label: "SCSS", icon: "🎨" },
  { value: "json", label: "JSON", icon: "📋" },
  { value: "xml", label: "XML", icon: "📄" },
  { value: "yaml", label: "YAML", icon: "📝" },
  { value: "markdown", label: "Markdown", icon: "📝" },
  { value: "sql", label: "SQL", icon: "🗃️" },
  { value: "shell", label: "Shell", icon: "🖥️" },
  { value: "dockerfile", label: "Dockerfile", icon: "🐳" },
  { value: "plaintext", label: "Plain Text", icon: "📄" }
];

const DEFAULT_SETTINGS: EditorSettings = {
  theme: "vs-dark",
  fontSize: 14,
  tabSize: 2,
  wordWrap: "on",
  showLineNumbers: true,
  showMinimap: true,
  autoSave: false,
  autoSaveDelay: 1000,
  formatOnSave: true,
  formatOnPaste: true,
  bracketPairColorization: true,
  autoClosingBrackets: "languageDefined",
  autoIndent: "full"
};

export const CodeEditor: React.FC<CodeEditorProps> = ({
  initialValue = "",
  language = "typescript",
  fileName = "untitled",
  filePath,
  readOnly = false,
  height = "400px",
  theme = "vs-dark",
  showLineNumbers = true,
  showMinimap = true,
  wordWrap = "on",
  fontSize = 14,
  tabSize = 2,
  onSave,
  onChange,
  onLanguageChange,
  className,
  showAIAssist = true,
  onAIAssist
}) => {
  const [value, setValue] = useState(initialValue);
  const [currentLanguage, setCurrentLanguage] = useState(language);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [replaceQuery, setReplaceQuery] = useState("");
  const [settings, setSettings] = useState<EditorSettings>({
    ...DEFAULT_SETTINGS,
    theme,
    fontSize,
    tabSize,
    wordWrap,
    showLineNumbers,
    showMinimap
  });
  const [editorStats, setEditorStats] = useState({
    lines: 0,
    words: 0,
    characters: 0,
    selection: { start: 0, end: 0 }
  });

  const editorRef = useRef<any>(null);
  const monacoRef = useRef<Monaco | null>(null);
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null);
  const { addToast } = useToast();

  // Update stats
  const updateStats = useCallback(() => {
    if (!editorRef.current) return;
    
    const model = editorRef.current.getModel();
    if (!model) return;

    const content = model.getValue();
    const lines = model.getLineCount();
    const words = content.trim().split(/\s+/).filter(Boolean).length;
    const characters = content.length;
    
    const selection = editorRef.current.getSelection();
    setEditorStats({
      lines,
      words,
      characters,
      selection: {
        start: model.getOffsetAt({ lineNumber: selection.startLineNumber, column: selection.startColumn }),
        end: model.getOffsetAt({ lineNumber: selection.endLineNumber, column: selection.endColumn })
      }
    });
  }, []);

  // Handle editor mount
  const handleEditorMount: OnMount = (editor, monaco) => {
    editorRef.current = editor;
    monacoRef.current = monaco;
    
    // Set initial options
    editor.updateOptions({
      fontSize: settings.fontSize,
      tabSize: settings.tabSize,
      wordWrap: settings.wordWrap,
      lineNumbers: settings.showLineNumbers ? "on" : "off",
      minimap: { enabled: settings.showMinimap },
      formatOnPaste: settings.formatOnPaste,
      bracketPairColorization: { enabled: settings.bracketPairColorization },
      autoClosingBrackets: settings.autoClosingBrackets,
      autoIndent: settings.autoIndent,
      readOnly
    });

    // Update stats on content change
    editor.onDidChangeModelContent(() => {
      updateStats();
    });

    // Update stats on selection change
    editor.onDidChangeCursorSelection(() => {
      updateStats();
    });

    // Initial stats update
    updateStats();

    // Register custom commands
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave();
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyF, () => {
      setShowSearch(true);
    });

    // AI assist shortcut
    if (showAIAssist && onAIAssist) {
      editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Space, () => {
        const selection = editor.getSelection();
        const selectedText = editor.getModel()?.getValueInRange(selection) || "";
        const fullCode = editor.getValue();
        onAIAssist(fullCode, selectedText);
      });
    }
  };

  // Handle editor change
  const handleEditorChange: OnChange = (value) => {
    setValue(value || "");
    setIsDirty(true);
    
    if (onChange) {
      onChange(value || "");
    }

    // Auto save
    if (settings.autoSave && onSave) {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
      }
      autoSaveTimerRef.current = setTimeout(() => {
        handleSave();
      }, settings.autoSaveDelay);
    }
  };

  // Handle save
  const handleSave = async () => {
    if (readOnly || !isDirty) return;

    setIsSaving(true);
    
    try {
      // Format on save
      if (settings.formatOnSave && editorRef.current) {
        await editorRef.current.getAction('editor.action.formatDocument')?.run();
      }

      const currentValue = editorRef.current?.getValue() || value;
      
      if (filePath) {
        // Save to file
        await writeTextFile(filePath, currentValue);
      }
      
      if (onSave) {
        onSave(currentValue);
      }
      
      setIsDirty(false);
      addToast({
        title: "File saved",
        description: filePath || fileName,
        variant: "success"
      });
    } catch (error) {
      console.error("Failed to save:", error);
      addToast({
        title: "Save failed",
        description: "Could not save the file",
        variant: "error"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle save as
  const handleSaveAs = async () => {
    try {
      const path = await save({
        defaultPath: fileName,
        filters: [{
          name: 'All Files',
          extensions: ['*']
        }]
      });
      
      if (path) {
        await writeTextFile(path, value);
        addToast({
          title: "File saved",
          description: path,
          variant: "success"
        });
      }
    } catch (error) {
      console.error("Failed to save as:", error);
      addToast({
        title: "Save failed",
        description: "Could not save the file",
        variant: "error"
      });
    }
  };

  // Handle open file
  const handleOpenFile = async () => {
    try {
      const path = await open({
        multiple: false,
        filters: [{
          name: 'All Files',
          extensions: ['*']
        }]
      });
      
      if (path && typeof path === 'string') {
        const content = await readTextFile(path);
        setValue(content);
        if (editorRef.current) {
          editorRef.current.setValue(content);
        }
        
        // Detect language from file extension
        const ext = path.split('.').pop()?.toLowerCase();
        const detectedLang = detectLanguageFromExtension(ext || '');
        if (detectedLang) {
          setCurrentLanguage(detectedLang);
          if (onLanguageChange) {
            onLanguageChange(detectedLang);
          }
        }
        
        addToast({
          title: "File opened",
          description: path,
          variant: "success"
        });
      }
    } catch (error) {
      console.error("Failed to open file:", error);
      addToast({
        title: "Open failed",
        description: "Could not open the file",
        variant: "error"
      });
    }
  };

  // Copy to clipboard
  const handleCopy = () => {
    navigator.clipboard.writeText(value);
    addToast({
      title: "Copied",
      description: "Code copied to clipboard",
      variant: "success"
    });
  };

  // Language detection from file extension
  const detectLanguageFromExtension = (ext: string): string => {
    const extensionMap: Record<string, string> = {
      js: 'javascript',
      jsx: 'javascript',
      ts: 'typescript',
      tsx: 'typescript',
      py: 'python',
      java: 'java',
      cs: 'csharp',
      cpp: 'cpp',
      cc: 'cpp',
      cxx: 'cpp',
      c: 'c',
      h: 'c',
      go: 'go',
      rs: 'rust',
      php: 'php',
      rb: 'ruby',
      swift: 'swift',
      kt: 'kotlin',
      dart: 'dart',
      html: 'html',
      htm: 'html',
      css: 'css',
      scss: 'scss',
      sass: 'scss',
      json: 'json',
      xml: 'xml',
      yaml: 'yaml',
      yml: 'yaml',
      md: 'markdown',
      sql: 'sql',
      sh: 'shell',
      bash: 'shell',
      dockerfile: 'dockerfile',
      txt: 'plaintext'
    };
    
    return extensionMap[ext] || 'plaintext';
  };

  // Update settings
  const updateSettings = (newSettings: Partial<EditorSettings>) => {
    const updated = { ...settings, ...newSettings };
    setSettings(updated);
    
    // Apply settings to editor
    if (editorRef.current) {
      editorRef.current.updateOptions({
        fontSize: updated.fontSize,
        tabSize: updated.tabSize,
        wordWrap: updated.wordWrap,
        lineNumbers: updated.showLineNumbers ? "on" : "off",
        minimap: { enabled: updated.showMinimap },
        formatOnPaste: updated.formatOnPaste,
        bracketPairColorization: { enabled: updated.bracketPairColorization },
        autoClosingBrackets: updated.autoClosingBrackets,
        autoIndent: updated.autoIndent
      });
    }
  };

  // Format document
  const handleFormat = () => {
    if (editorRef.current) {
      editorRef.current.getAction('editor.action.formatDocument')?.run();
    }
  };

  // Find and replace
  const handleFind = () => {
    if (editorRef.current && monacoRef.current) {
      const model = editorRef.current.getModel();
      if (!model) return;
      
      const matches = model.findMatches(searchQuery, true, false, false, null, true);
      if (matches.length > 0) {
        editorRef.current.setSelection(matches[0].range);
        editorRef.current.revealRangeInCenter(matches[0].range);
      }
      
      addToast({
        title: "Search results",
        description: `Found ${matches.length} matches`,
        variant: "info"
      });
    }
  };

  const handleReplace = () => {
    if (editorRef.current && monacoRef.current && searchQuery) {
      const model = editorRef.current.getModel();
      if (!model) return;
      
      const matches = model.findMatches(searchQuery, true, false, false, null, true);
      
      editorRef.current.executeEdits('replace', matches.map(match => ({
        range: match.range,
        text: replaceQuery,
        forceMoveMarkers: true
      })));
      
      addToast({
        title: "Replace completed",
        description: `Replaced ${matches.length} occurrences`,
        variant: "success"
      });
    }
  };

  // Cleanup
  useEffect(() => {
    return () => {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
      }
    };
  }, []);

  const languageOption = LANGUAGE_OPTIONS.find(opt => opt.value === currentLanguage) || LANGUAGE_OPTIONS[0];

  return (
    <div className={cn(
      "border rounded-lg overflow-hidden bg-background",
      isFullscreen && "fixed inset-0 z-50",
      className
    )}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-2 border-b bg-muted/50">
        <div className="flex items-center gap-2">
          <FileCode className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">{fileName}</span>
          {isDirty && (
            <Badge variant="secondary" className="text-xs">
              Modified
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-1">
          {/* Language Selector */}
          <Select
            value={currentLanguage}
            onValueChange={(value) => {
              setCurrentLanguage(value);
              if (onLanguageChange) {
                onLanguageChange(value);
              }
            }}
          >
            <SelectTrigger className="w-[140px] h-8 text-xs">
              <SelectValue>
                <span className="flex items-center gap-1">
                  <span>{languageOption.icon}</span>
                  <span>{languageOption.label}</span>
                </span>
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {LANGUAGE_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <span className="flex items-center gap-1">
                    <span>{option.icon}</span>
                    <span>{option.label}</span>
                  </span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Separator orientation="vertical" className="h-6" />

          {/* File Operations */}
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleOpenFile}
                  className="h-8 w-8 p-0"
                >
                  <Upload className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Open file</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSave}
                  disabled={readOnly || !isDirty || isSaving}
                  className="h-8 w-8 p-0"
                >
                  {isSaving ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Save (Ctrl+S)</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSaveAs}
                  disabled={readOnly}
                  className="h-8 w-8 p-0"
                >
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Save as...</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopy}
                  className="h-8 w-8 p-0"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Copy to clipboard</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator orientation="vertical" className="h-6" />

          {/* Edit Operations */}
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => editorRef.current?.trigger('keyboard', 'undo')}
                  disabled={readOnly}
                  className="h-8 w-8 p-0"
                >
                  <Undo className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Undo</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => editorRef.current?.trigger('keyboard', 'redo')}
                  disabled={readOnly}
                  className="h-8 w-8 p-0"
                >
                  <Redo className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Redo</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleFormat}
                  disabled={readOnly}
                  className="h-8 w-8 p-0"
                >
                  <Code className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Format document</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSearch(!showSearch)}
                  className="h-8 w-8 p-0"
                >
                  <Search className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Find & Replace (Ctrl+F)</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator orientation="vertical" className="h-6" />

          {/* AI Assist */}
          {showAIAssist && onAIAssist && (
            <>
              <TooltipProvider delayDuration={0}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const selection = editorRef.current?.getSelection();
                        const selectedText = editorRef.current?.getModel()?.getValueInRange(selection) || "";
                        onAIAssist(value, selectedText);
                      }}
                      className="h-8 px-2 gap-1"
                    >
                      <Sparkles className="h-4 w-4" />
                      <span className="text-xs">AI</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>AI Assist (Ctrl+Space)</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Separator orientation="vertical" className="h-6" />
            </>
          )}

          {/* View Options */}
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateSettings({ theme: settings.theme === "vs-dark" ? "vs-light" : "vs-dark" })}
                  className="h-8 w-8 p-0"
                >
                  {settings.theme === "vs-dark" ? (
                    <Sun className="h-4 w-4" />
                  ) : (
                    <Moon className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle theme</p>
              </TooltipContent>
            </Tooltip>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Settings className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Editor Settings</DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                <DropdownMenuCheckboxItem
                  checked={settings.showLineNumbers}
                  onCheckedChange={(checked) => updateSettings({ showLineNumbers: checked })}
                >
                  Show line numbers
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuCheckboxItem
                  checked={settings.showMinimap}
                  onCheckedChange={(checked) => updateSettings({ showMinimap: checked })}
                >
                  Show minimap
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuCheckboxItem
                  checked={settings.wordWrap === "on"}
                  onCheckedChange={(checked) => updateSettings({ wordWrap: checked ? "on" : "off" })}
                >
                  Word wrap
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuCheckboxItem
                  checked={settings.bracketPairColorization}
                  onCheckedChange={(checked) => updateSettings({ bracketPairColorization: checked })}
                >
                  Bracket pair colorization
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuCheckboxItem
                  checked={settings.autoSave}
                  onCheckedChange={(checked) => updateSettings({ autoSave: checked })}
                >
                  Auto save
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuCheckboxItem
                  checked={settings.formatOnSave}
                  onCheckedChange={(checked) => updateSettings({ formatOnSave: checked })}
                >
                  Format on save
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuCheckboxItem
                  checked={settings.formatOnPaste}
                  onCheckedChange={(checked) => updateSettings({ formatOnPaste: checked })}
                >
                  Format on paste
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem
                  onClick={() => updateSettings({ fontSize: settings.fontSize + 1 })}
                >
                  Increase font size
                </DropdownMenuItem>
                
                <DropdownMenuItem
                  onClick={() => updateSettings({ fontSize: Math.max(10, settings.fontSize - 1) })}
                >
                  Decrease font size
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsFullscreen(!isFullscreen)}
                  className="h-8 w-8 p-0"
                >
                  {isFullscreen ? (
                    <Minimize2 className="h-4 w-4" />
                  ) : (
                    <Maximize2 className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isFullscreen ? "Exit fullscreen" : "Fullscreen"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Search/Replace Bar */}
      <AnimatePresence>
        {showSearch && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="border-b bg-muted/50 overflow-hidden"
          >
            <div className="p-2 flex items-center gap-2">
              <div className="flex-1 flex items-center gap-2">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Find"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleFind()}
                  className="h-7"
                />
                <Replace className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Replace"
                  value={replaceQuery}
                  onChange={(e) => setReplaceQuery(e.target.value)}
                  className="h-7"
                />
              </div>
              <Button size="sm" variant="secondary" onClick={handleFind}>
                Find
              </Button>
              <Button size="sm" variant="secondary" onClick={handleReplace}>
                Replace All
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowSearch(false)}
                className="h-7 w-7 p-0"
              >
                <XCircle className="h-4 w-4" />
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Editor */}
      <div style={{ height: isFullscreen ? "calc(100vh - 88px)" : height }}>
        <Editor
          language={currentLanguage}
          value={value}
          theme={settings.theme}
          onMount={handleEditorMount}
          onChange={handleEditorChange}
          options={{
            readOnly,
            fontSize: settings.fontSize,
            tabSize: settings.tabSize,
            wordWrap: settings.wordWrap,
            lineNumbers: settings.showLineNumbers ? "on" : "off",
            minimap: { enabled: settings.showMinimap },
            scrollBeyondLastLine: false,
            automaticLayout: true,
            formatOnPaste: settings.formatOnPaste,
            bracketPairColorization: { enabled: settings.bracketPairColorization },
            autoClosingBrackets: settings.autoClosingBrackets,
            autoIndent: settings.autoIndent,
            folding: true,
            foldingStrategy: "indentation",
            showFoldingControls: "always",
            renderWhitespace: "selection",
            cursorBlinking: "smooth",
            cursorSmoothCaretAnimation: "on",
            smoothScrolling: true,
            mouseWheelZoom: true,
            padding: { top: 10, bottom: 10 }
          }}
        />
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-2 py-1 border-t bg-muted/50 text-xs text-muted-foreground">
        <div className="flex items-center gap-4">
          <span>Ln {editorStats.selection.start}, Col {editorStats.selection.end}</span>
          <Separator orientation="vertical" className="h-3" />
          <span>{editorStats.lines} lines</span>
          <span>{editorStats.words} words</span>
          <span>{editorStats.characters} characters</span>
        </div>
        <div className="flex items-center gap-4">
          {settings.autoSave && (
            <Badge variant="secondary" className="text-xs">
              <Zap className="h-3 w-3 mr-1" />
              Auto Save
            </Badge>
          )}
          <span>Tab Size: {settings.tabSize}</span>
          <span>{currentLanguage}</span>
        </div>
      </div>
    </div>
  );
};

export default CodeEditor;