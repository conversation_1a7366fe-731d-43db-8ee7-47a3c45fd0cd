import React, { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Send,
  Sparkles,
  Command,
  History,
  Bot,
  User,
  Loader2,
  CheckCircle,
  AlertCircle,
  Info,
  Wand2,
  Users,
  Target,
  GitBranch,
  HelpCircle,
  Clock,
  X
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Popover } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { invoke } from '@tauri-apps/api/core';
import { useToast } from '@/hooks/useToast';
import type { OrchestratedAgent, AgentTask } from '../AgentOrchestraPanel';

interface OrchestratorChatProps {
  onCommand: (command: string) => void;
  commandHistory: string[];
  orchestratedAgents: OrchestratedAgent[];
  onAgentAdd: (agentName: string) => Promise<void>;
  onAgentRemove: (agentId: string) => void;
  onTaskCreate: (task: Partial<AgentTask>) => void;
  onTaskAssign: (taskId: string, agentId: string) => void;
  onWorkflowSave: (name: string) => Promise<void>;
  onWorkflowLoad: (name: string) => Promise<void>;
  onCollaborationSetup: (agent1Id: string, agent2Id: string) => void;
  className?: string;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'system' | 'agent';
  content: string;
  timestamp: string;
  status?: 'pending' | 'success' | 'error';
  agentId?: string;
  suggestions?: string[];
}

interface CommandSuggestion {
  command: string;
  description: string;
  category: 'agent' | 'task' | 'workflow' | 'collaboration' | 'utility';
  icon: React.ComponentType<any>;
}

const COMMAND_SUGGESTIONS: CommandSuggestion[] = [
  // Agent Commands
  {
    command: "/add [agent-name]",
    description: "Add an agent to the orchestra",
    category: "agent",
    icon: Bot
  },
  {
    command: "/remove [agent-id]",
    description: "Remove an agent from the orchestra",
    category: "agent",
    icon: Bot
  },
  {
    command: "/agents",
    description: "List all active agents",
    category: "agent",
    icon: Users
  },
  
  // Task Commands
  {
    command: "/task [agent] \"description\"",
    description: "Assign a task to a specific agent",
    category: "task",
    icon: Target
  },
  {
    command: "/distribute \"task\"",
    description: "Let AI distribute task to best agent",
    category: "task",
    icon: Wand2
  },
  {
    command: "/tasks",
    description: "Show all tasks and their status",
    category: "task",
    icon: Target
  },
  
  // Workflow Commands
  {
    command: "/workflow save \"name\"",
    description: "Save current setup as workflow",
    category: "workflow",
    icon: GitBranch
  },
  {
    command: "/workflow load \"name\"",
    description: "Load a saved workflow",
    category: "workflow",
    icon: GitBranch
  },
  
  // Collaboration Commands
  {
    command: "/collaborate [agent1] [agent2]",
    description: "Set up collaboration between agents",
    category: "collaboration",
    icon: Users
  },
  {
    command: "/isolate [agent]",
    description: "Remove agent from collaborations",
    category: "collaboration",
    icon: Bot
  },
  
  // Utility Commands
  {
    command: "/status",
    description: "Show orchestra status overview",
    category: "utility",
    icon: Info
  },
  {
    command: "/clear",
    description: "Clear completed tasks",
    category: "utility",
    icon: CheckCircle
  },
  {
    command: "/help",
    description: "Show available commands",
    category: "utility",
    icon: HelpCircle
  }
];

const NATURAL_LANGUAGE_EXAMPLES = [
  "Add the code reviewer and test writer agents to help with the API refactoring",
  "Create a task to optimize the database queries and assign it to the best available agent",
  "Show me which agents are currently collaborating",
  "Set up a workflow for code review with security scanning",
  "Help the optimizer agent work together with the code writer",
  "What's the status of all running tasks?",
  "Save this agent configuration as 'API Development Team'"
];

export const OrchestratorChat: React.FC<OrchestratorChatProps> = ({
  onCommand,
  commandHistory,
  orchestratedAgents,
  onAgentAdd,
  onAgentRemove,
  onTaskCreate,
  onWorkflowSave,
  onWorkflowLoad,
  onCollaborationSetup,
  className
}) => {
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: 'welcome',
      type: 'system',
      content: "👋 I'm your Orchestra Assistant. I can help you manage agents, create tasks, and coordinate workflows. Use natural language or type / for commands.",
      timestamp: new Date().toISOString()
    }
  ]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Filter suggestions based on input
  const filteredSuggestions = input.startsWith('/')
    ? COMMAND_SUGGESTIONS.filter(cmd => 
        cmd.command.toLowerCase().includes(input.toLowerCase())
      )
    : [];

  // Handle command submission
  const handleSubmit = async () => {
    if (!input.trim() || isProcessing) return;
    
    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}`,
      type: 'user',
      content: input,
      timestamp: new Date().toISOString()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setIsProcessing(true);
    
    // Process command
    try {
      // Add to history
      onCommand(input);
      
      // Execute real command processing
      const response = await processCommand(input);
      setMessages(prev => [...prev, response]);
      
      // Add follow-up suggestions if applicable
      if (response.suggestions) {
        const suggestionMessage: ChatMessage = {
          id: `sug_${Date.now()}`,
          type: 'system',
          content: "Here are some related actions you might want to take:",
          timestamp: new Date().toISOString(),
          suggestions: response.suggestions
        };
        setMessages(prev => [...prev, suggestionMessage]);
      }
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: `err_${Date.now()}`,
        type: 'system',
        content: `Error: ${error instanceof Error ? error.message : 'Command failed'}`,
        timestamp: new Date().toISOString(),
        status: 'error'
      };
      setMessages(prev => [...prev, errorMessage]);
      
      toast({
        message: `Command failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        type: 'error'
      });
    } finally {
      setIsProcessing(false);
      setInput("");
    }
  };

  // Real command processing with actual functionality
  const processCommand = async (command: string): Promise<ChatMessage> => {
    try {
      // Command responses
      if (command.startsWith('/')) {
        const [cmd, ...args] = command.slice(1).split(' ');
        
        switch (cmd) {
          case 'add':
            const agentName = args.join(' ');
            if (!agentName) {
              throw new Error('Agent name is required. Usage: /add [agent-name]');
            }
            
            await onAgentAdd(agentName);
            return {
              id: `res_${Date.now()}`,
              type: 'system',
              content: `✅ Successfully added agent "${agentName}" to the orchestra`,
              timestamp: new Date().toISOString(),
              status: 'success',
              suggestions: ['/agents', `/task ${agentName} "`, '/status']
            };
            
          case 'remove':
            const agentId = args[0];
            if (!agentId) {
              throw new Error('Agent ID is required. Usage: /remove [agent-id]');
            }
            
            const agent = orchestratedAgents.find(a => String(a.agent.id) === agentId);
            if (!agent) {
              throw new Error(`Agent with ID ${agentId} not found`);
            }
            
            onAgentRemove(agentId);
            return {
              id: `res_${Date.now()}`,
              type: 'system',
              content: `✅ Removed agent "${agent.agent.name}" from the orchestra`,
              timestamp: new Date().toISOString(),
              status: 'success',
              suggestions: ['/agents', '/status']
            };
          
          case 'task':
            if (args.length < 2) {
              throw new Error('Task requires agent and description. Usage: /task [agent] "description"');
            }
            
            const targetAgent = args[0];
            const taskDescription = args.slice(1).join(' ').replace(/"/g, '');
            
            const foundAgent = orchestratedAgents.find(a => 
              a.agent.name.toLowerCase().includes(targetAgent.toLowerCase())
            );
            
            if (!foundAgent) {
              throw new Error(`Agent "${targetAgent}" not found in orchestra`);
            }
            
            const newTask: Partial<AgentTask> = {
              description: taskDescription,
              priority: 'medium',
              requiredCapabilities: ['general'],
              dependencies: []
            };
            
            onTaskCreate(newTask);
            return {
              id: `res_${Date.now()}`,
              type: 'system',
              content: `📋 Task "${taskDescription}" created and assigned to ${foundAgent.agent.name}`,
              timestamp: new Date().toISOString(),
              status: 'success',
              suggestions: ['/tasks', '/status']
            };
            
          case 'distribute':
            const distributedTaskDesc = args.join(' ').replace(/"/g, '');
            if (!distributedTaskDesc) {
              throw new Error('Task description is required. Usage: /distribute "task description"');
            }
            
            // Use AI to determine best agent
            const aiResult = await invoke('analyze_task_for_agent_assignment', {
              taskDescription: distributedTaskDesc,
              availableAgents: orchestratedAgents.map(a => ({
                id: a.agent.id,
                name: a.agent.name,
                capabilities: a.performance.specialties,
                currentLoad: a.progress
              }))
            }) as { bestAgentId: string; confidence: number };
            
            const bestAgent = orchestratedAgents.find(a => String(a.agent.id) === aiResult.bestAgentId);
            if (!bestAgent) {
              throw new Error('No suitable agent found for this task');
            }
            
            const distributedTask: Partial<AgentTask> = {
              description: distributedTaskDesc,
              priority: 'medium',
              requiredCapabilities: ['general'],
              dependencies: []
            };
            
            onTaskCreate(distributedTask);
            return {
              id: `res_${Date.now()}`,
              type: 'system',
              content: `🤖 AI assigned task "${distributedTaskDesc}" to ${bestAgent.agent.name} (${Math.round(aiResult.confidence * 100)}% confidence)`,
              timestamp: new Date().toISOString(),
              status: 'success',
              suggestions: ['/tasks', '/status']
            };
            
          case 'agents':
            const agentsList = orchestratedAgents.map(a => 
              `• ${a.agent.name} (${a.status}) - ${a.progress}% load`
            ).join('\n');
            
            return {
              id: `res_${Date.now()}`,
              type: 'system',
              content: `👥 Active Agents (${orchestratedAgents.length}):\n${agentsList || 'No agents in orchestra'}`,
              timestamp: new Date().toISOString(),
              status: 'success'
            };
            
          case 'tasks':
            // Get tasks from orchestratedAgents
            const allTasks = orchestratedAgents.flatMap(a => a.currentTasks || []);
            const tasksList = allTasks.map(task => 
              `• ${task.description} (${task.status}) - Priority: ${task.priority}`
            ).join('\n');
            
            return {
              id: `res_${Date.now()}`,
              type: 'system',
              content: `📋 Active Tasks (${allTasks.length}):\n${tasksList || 'No active tasks'}`,
              timestamp: new Date().toISOString(),
              status: 'success'
            };
            
          case 'status':
            const workingAgents = orchestratedAgents.filter(a => a.status === 'working').length;
            const totalTasks = orchestratedAgents.flatMap(a => a.currentTasks || []).length;
            const activeTasks = orchestratedAgents.flatMap(a => a.currentTasks || []).filter(t => t.status === 'in_progress').length;
            const completedTasks = orchestratedAgents.flatMap(a => a.currentTasks || []).filter(t => t.status === 'completed').length;
            const collaborations = orchestratedAgents.filter(a => a.collaboratingWith && a.collaboratingWith.length > 0).length;
            
            return {
              id: `res_${Date.now()}`,
              type: 'system',
              content: `📊 Orchestra Status:\n• ${orchestratedAgents.length} total agents (${workingAgents} working)\n• ${totalTasks} total tasks (${activeTasks} active, ${completedTasks} completed)\n• ${collaborations} active collaborations`,
              timestamp: new Date().toISOString(),
              status: 'success'
            };
            
          case 'collaborate':
            if (args.length < 2) {
              throw new Error('Two agent IDs required. Usage: /collaborate [agent1] [agent2]');
            }
            
            const agent1 = orchestratedAgents.find(a => String(a.agent.id) === args[0] || a.agent.name.toLowerCase().includes(args[0].toLowerCase()));
            const agent2 = orchestratedAgents.find(a => String(a.agent.id) === args[1] || a.agent.name.toLowerCase().includes(args[1].toLowerCase()));
            
            if (!agent1 || !agent2) {
              throw new Error('One or both agents not found');
            }
            
            onCollaborationSetup(String(agent1.agent.id), String(agent2.agent.id));
            return {
              id: `res_${Date.now()}`,
              type: 'system',
              content: `🤝 Set up collaboration between ${agent1.agent.name} and ${agent2.agent.name}`,
              timestamp: new Date().toISOString(),
              status: 'success',
              suggestions: ['/status']
            };
            
          case 'workflow':
            const subcommand = args[0];
            const workflowName = args.slice(1).join(' ').replace(/"/g, '');
            
            if (subcommand === 'save') {
              if (!workflowName) {
                throw new Error('Workflow name required. Usage: /workflow save "name"');
              }
              await onWorkflowSave(workflowName);
              return {
                id: `res_${Date.now()}`,
                type: 'system',
                content: `💾 Saved current configuration as workflow "${workflowName}"`,
                timestamp: new Date().toISOString(),
                status: 'success'
              };
            } else if (subcommand === 'load') {
              if (!workflowName) {
                throw new Error('Workflow name required. Usage: /workflow load "name"');
              }
              await onWorkflowLoad(workflowName);
              return {
                id: `res_${Date.now()}`,
                type: 'system',
                content: `📂 Loaded workflow "${workflowName}"`,
                timestamp: new Date().toISOString(),
                status: 'success'
              };
            } else {
              throw new Error('Invalid workflow subcommand. Use "save" or "load"');
            }
            
          case 'clear':
            // Clear completed tasks
            const clearedCount = orchestratedAgents.reduce((count, agent) => {
              const completed = agent.currentTasks?.filter(t => t.status === 'completed').length || 0;
              return count + completed;
            }, 0);
            
            return {
              id: `res_${Date.now()}`,
              type: 'system',
              content: `🧹 Cleared ${clearedCount} completed tasks`,
              timestamp: new Date().toISOString(),
              status: 'success'
            };
            
          case 'help':
            return {
              id: `res_${Date.now()}`,
              type: 'system',
              content: `📚 Available commands:\n${COMMAND_SUGGESTIONS.map(s => `• ${s.command} - ${s.description}`).join('\n')}`,
              timestamp: new Date().toISOString(),
              status: 'success'
            };
            
          default:
            throw new Error(`Unknown command: ${cmd}. Type /help for available commands.`);
        }
      }
      
      // Natural language processing using AI
      const nlpResult = await processNaturalLanguage(command);
      return nlpResult;
      
    } catch (error) {
      throw error;
    }
  };
  
  // Process natural language commands using AI
  const processNaturalLanguage = async (command: string): Promise<ChatMessage> => {
    try {
      const result = await invoke('process_natural_language_command', {
        command,
        orchestraState: {
          agents: orchestratedAgents.map(a => ({
            id: a.agent.id,
            name: a.agent.name,
            status: a.status,
            capabilities: a.performance.specialties
          }))
        }
      }) as { action: string; parameters: any; response: string; suggestions?: string[] };
      
      // Execute the determined action
      switch (result.action) {
        case 'add_agent':
          await onAgentAdd(result.parameters.agentName);
          break;
        case 'create_task':
          onTaskCreate(result.parameters.task);
          break;
        case 'setup_collaboration':
          onCollaborationSetup(result.parameters.agent1Id, result.parameters.agent2Id);
          break;
        // Add more actions as needed
      }
      
      return {
        id: `res_${Date.now()}`,
        type: 'system',
        content: result.response,
        timestamp: new Date().toISOString(),
        status: 'success',
        suggestions: result.suggestions
      };
    } catch (error) {
      // Fallback to simple pattern matching
      const lowerCommand = command.toLowerCase();
      
      if (lowerCommand.includes('add') && lowerCommand.includes('agent')) {
        return {
          id: `res_${Date.now()}`,
          type: 'system',
          content: "I'll help you add agents to the orchestra. Which agents would you like to add?",
          timestamp: new Date().toISOString(),
          suggestions: ['/add code-reviewer', '/add test-writer', '/agents']
        };
      }
      
      if (lowerCommand.includes('status') || lowerCommand.includes('what') || lowerCommand.includes('show')) {
        const workingAgents = orchestratedAgents.filter(a => a.status === 'working').length;
        const totalTasks = orchestratedAgents.flatMap(a => a.currentTasks || []).length;
        
        return {
          id: `res_${Date.now()}`,
          type: 'system',
          content: `📊 Orchestra Status: ${orchestratedAgents.length} agents, ${workingAgents} working, ${totalTasks} tasks`,
          timestamp: new Date().toISOString(),
          suggestions: ['/status', '/agents', '/tasks']
        };
      }
      
      if (lowerCommand.includes('help') || lowerCommand.includes('how')) {
        return {
          id: `res_${Date.now()}`,
          type: 'system',
          content: "I can help you manage agents, create tasks, and coordinate workflows. You can use natural language or slash commands. Type /help for a list of commands.",
          timestamp: new Date().toISOString(),
          suggestions: ['/help', '/agents', '/task']
        };
      }
      
      // Default response for unrecognized commands
      return {
        id: `res_${Date.now()}`,
        type: 'system',
        content: `I'm not sure how to handle: "${command}". Try using specific commands like /add, /task, or /status, or type /help for available commands.`,
        timestamp: new Date().toISOString(),
        suggestions: ['/help', '/status', '/agents']
      };
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Tab' && filteredSuggestions.length > 0) {
      e.preventDefault();
      const selected = filteredSuggestions[selectedSuggestion];
      if (selected) {
        setInput(selected.command);
        setShowSuggestions(false);
      }
    } else if (e.key === 'ArrowUp') {
      if (showSuggestions && filteredSuggestions.length > 0) {
        e.preventDefault();
        setSelectedSuggestion(prev => Math.max(0, prev - 1));
      } else if (!input && commandHistory.length > 0) {
        // Navigate command history
        const lastCommand = commandHistory[commandHistory.length - 1];
        setInput(lastCommand);
      }
    } else if (e.key === 'ArrowDown' && showSuggestions) {
      e.preventDefault();
      setSelectedSuggestion(prev => Math.min(filteredSuggestions.length - 1, prev + 1));
    } else if (e.key === 'Enter') {
      if (showSuggestions && filteredSuggestions.length > 0) {
        const selected = filteredSuggestions[selectedSuggestion];
        if (selected) {
          setInput(selected.command);
          setShowSuggestions(false);
          return;
        }
      }
      handleSubmit();
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  // Auto-scroll to bottom
  useEffect(() => {
    scrollRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Show suggestions when typing commands
  useEffect(() => {
    setShowSuggestions(input.startsWith('/') && input.length > 1);
    setSelectedSuggestion(0);
  }, [input]);

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Chat Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className={cn(
                "flex gap-3",
                message.type === 'user' && "justify-end"
              )}
            >
              {message.type !== 'user' && (
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",
                  message.type === 'system' ? "bg-primary/10" : "bg-muted"
                )}>
                  {message.type === 'system' ? (
                    <Sparkles className="h-4 w-4 text-primary" />
                  ) : (
                    <Bot className="h-4 w-4" />
                  )}
                </div>
              )}
              
              <div className={cn(
                "max-w-[80%] space-y-2",
                message.type === 'user' && "items-end"
              )}>
                <Card className={cn(
                  message.type === 'user' && "bg-primary text-primary-foreground"
                )}>
                  <CardContent className="p-3">
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                    {message.status && (
                      <div className="flex items-center gap-2 mt-2">
                        {message.status === 'pending' && <Loader2 className="h-3 w-3 animate-spin" />}
                        {message.status === 'success' && <CheckCircle className="h-3 w-3 text-green-500" />}
                        {message.status === 'error' && <AlertCircle className="h-3 w-3 text-red-500" />}
                      </div>
                    )}
                  </CardContent>
                </Card>
                
                {message.suggestions && (
                  <div className="flex flex-wrap gap-2">
                    {message.suggestions.map((suggestion, idx) => (
                      <Button
                        key={idx}
                        variant="outline"
                        size="sm"
                        onClick={() => setInput(suggestion)}
                      >
                        {suggestion}
                      </Button>
                    ))}
                  </div>
                )}
                
                <div className="text-xs text-muted-foreground">
                  {new Date(message.timestamp).toLocaleTimeString()}
                </div>
              </div>
              
              {message.type === 'user' && (
                <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center flex-shrink-0">
                  <User className="h-4 w-4" />
                </div>
              )}
            </motion.div>
          ))}
          <div ref={scrollRef} />
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="border-t p-4">
        {/* Command Suggestions */}
        {showSuggestions && filteredSuggestions.length > 0 && (
          <Card className="absolute bottom-20 left-4 right-4 max-h-64 overflow-hidden">
            <ScrollArea className="max-h-64">
              <div className="p-2">
                {filteredSuggestions.map((suggestion, idx) => {
                  const Icon = suggestion.icon;
                  return (
                    <div
                      key={idx}
                      className={cn(
                        "flex items-center gap-3 p-2 rounded cursor-pointer",
                        idx === selectedSuggestion && "bg-accent"
                      )}
                      onClick={() => {
                        setInput(suggestion.command);
                        setShowSuggestions(false);
                      }}
                    >
                      <Icon className="h-4 w-4 text-muted-foreground" />
                      <div className="flex-1">
                        <div className="text-sm font-medium">{suggestion.command}</div>
                        <div className="text-xs text-muted-foreground">{suggestion.description}</div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {suggestion.category}
                      </Badge>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </Card>
        )}
        
        <div className="flex items-center gap-2">
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type a command or describe what you want to do..."
              disabled={isProcessing}
              className="pr-10"
            />
            
            <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
              {commandHistory.length > 0 && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={() => setShowHistory(!showHistory)}
                      >
                        <History className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Command History</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => setInput('/')}
                    >
                      <Command className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Show Commands</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          
          <Button
            onClick={handleSubmit}
            disabled={!input.trim() || isProcessing}
            size="icon"
          >
            {isProcessing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
        
        <div className="flex items-center justify-between mt-2">
          <div className="text-xs text-muted-foreground">
            {input.startsWith('/') ? "Command mode" : "Natural language mode"}
          </div>
          <Popover
            trigger={
              <Button variant="ghost" size="sm" className="h-6 text-xs">
                <HelpCircle className="h-3 w-3 mr-1" />
                Examples
              </Button>
            }
            content={
              <div className="w-96">
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Example Commands</h4>
                  {NATURAL_LANGUAGE_EXAMPLES.map((example, idx) => (
                    <div
                      key={idx}
                      className="text-sm p-2 rounded hover:bg-accent cursor-pointer"
                      onClick={() => setInput(example)}
                    >
                      {example}
                    </div>
                  ))}
                </div>
              </div>
            }
          />
        </div>
      </div>

      {/* Command History Popover */}
      {showHistory && commandHistory.length > 0 && (
        <Card className="absolute bottom-32 left-4 right-4 max-h-48">
          <CardContent className="p-2">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Command History</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={() => setShowHistory(false)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            <ScrollArea className="max-h-32">
              {commandHistory.slice(-10).reverse().map((cmd, idx) => (
                <div
                  key={idx}
                  className="flex items-center gap-2 p-2 hover:bg-accent rounded cursor-pointer"
                  onClick={() => {
                    setInput(cmd);
                    setShowHistory(false);
                  }}
                >
                  <Clock className="h-3 w-3 text-muted-foreground" />
                  <span className="text-sm truncate">{cmd}</span>
                </div>
              ))}
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
