import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Bot,
  Plus,
  Check,
  X,
  Shield,
  Zap,
  Brain,
  Code,
  FileText,
  TestTube,
  Search,
  Sparkles,
  Users,
  AlertCircle,
  Loader2
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { api, type Agent } from "@/lib/api";
import { AGENT_ICONS } from "@/components/CCAgents";
import type { OrchestratedAgent } from "../AgentOrchestraPanel";
import { invoke } from "@tauri-apps/api/core";

interface AgentSelectorProps {
  orchestratedAgents: OrchestratedAgent[];
  onAgentSelect: (agent: Agent) => void;
  onAgentRemove: (agentId: string) => void;
  showDetails: boolean;
  collaborationMode: 'isolated' | 'paired' | 'ensemble';
}

interface AgentWithMetadata extends Agent {
  compatibility?: number;
  recommendationScore?: number;
  recentPerformance?: {
    tasksCompleted: number;
    successRate: number;
    avgCompletionTime: number;
  };
  specialties?: string[];
  requiredMCPServers?: string[];
}

interface AgentPerformanceMetrics {
  agent_id: string;
  tasks_completed: number;
  success_rate: number;
  avg_completion_time: number;
  collaboration_score: number;
  specialty_scores: Record<string, number>;
  last_activity?: string;
}

interface AgentCompatibilityData {
  agent_id: string;
  compatibility_score: number;
  working_relationships: string[];
  skill_gaps: string[];
  recommended_for: string[];
}

interface AgentAnalyticsRequest {
  agent_ids: string[];
  time_range_days?: number;
  include_collaboration_data: boolean;
}

export const AgentSelector: React.FC<AgentSelectorProps> = ({
  orchestratedAgents,
  onAgentSelect,
  onAgentRemove,
  showDetails,
  collaborationMode
}) => {
  const [availableAgents, setAvailableAgents] = useState<AgentWithMetadata[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterCategory, setFilterCategory] = useState<string>("all");
  const [selectedAgentIds, setSelectedAgentIds] = useState<Set<string>>(new Set());
  const [showRecommendations, setShowRecommendations] = useState(true);

  useEffect(() => {
    loadAgents();
  }, [orchestratedAgents]);

  // Real analytics functions
  const getAgentPerformanceMetrics = async (agentIds: string[]): Promise<AgentPerformanceMetrics[]> => {
    try {
      const request: AgentAnalyticsRequest = {
        agent_ids: agentIds,
        time_range_days: 30,
        include_collaboration_data: true
      };
      
      return await invoke('get_agent_performance_metrics', { request });
    } catch (error) {
      console.error("Failed to get performance metrics:", error);
      return [];
    }
  };

  const getAgentCompatibilityData = async (agentIds: string[], currentTeamIds: string[]): Promise<Map<string, AgentCompatibilityData>> => {
    const compatibilityMap = new Map<string, AgentCompatibilityData>();
    
    try {
      for (const agentId of agentIds) {
        if (!currentTeamIds.includes(agentId)) { // Don't calculate compatibility for agents already on team
          const compatibility = await invoke('calculate_agent_compatibility', {
            agentId,
            currentTeamIds
          }) as AgentCompatibilityData;
          
          compatibilityMap.set(agentId, compatibility);
        }
      }
    } catch (error) {
      console.error("Failed to get compatibility data:", error);
    }
    
    return compatibilityMap;
  };

  const getRecommendationScores = async (agentIds: string[], currentTeamIds: string[]): Promise<Map<string, number>> => {
    try {
      const scores = await invoke('get_agent_recommendation_scores', {
        availableAgentIds: agentIds,
        currentTeamIds,
        taskRequirements: null // Could be enhanced to include current task requirements
      }) as Record<string, number>;
      
      return new Map(Object.entries(scores));
    } catch (error) {
      console.error("Failed to get recommendation scores:", error);
      return new Map();
    }
  };

  const loadAgents = async () => {
    try {
      setLoading(true);
      const agents = await api.listAgents();
      
      // Get agent IDs for analytics
      const agentIds = agents.map(agent => String(agent.id)).filter(Boolean);
      const currentTeamIds = orchestratedAgents.map(oa => String(oa.agent.id)).filter(Boolean);
      
      // Fetch real performance metrics
      const performanceMetrics = await getAgentPerformanceMetrics(agentIds);
      const compatibilityData = await getAgentCompatibilityData(agentIds, currentTeamIds);
      const recommendationScores = await getRecommendationScores(agentIds, currentTeamIds);
      
      // Enhance agents with real metadata
      const enhancedAgents: AgentWithMetadata[] = agents.map(agent => {
        const agentId = String(agent.id);
        const metrics = performanceMetrics.find(m => m.agent_id === agentId);
        const compatibility = compatibilityData.get(agentId);
        const recommendationScore = recommendationScores.get(agentId);
        
        return {
          ...agent,
          specialties: inferAgentSpecialties(agent),
          requiredMCPServers: inferRequiredMCPServers(agent),
          compatibility: compatibility?.compatibility_score,
          recommendationScore,
          recentPerformance: metrics ? {
            tasksCompleted: metrics.tasks_completed,
            successRate: metrics.success_rate,
            avgCompletionTime: metrics.avg_completion_time
          } : undefined
        };
      });
      
      setAvailableAgents(enhancedAgents);
    } catch (error) {
      console.error("Failed to load agents:", error);
      // Fallback to mock data if real analytics fail
      const agents = await api.listAgents();
      const enhancedAgents: AgentWithMetadata[] = agents.map(agent => ({
        ...agent,
        specialties: inferAgentSpecialties(agent),
        requiredMCPServers: inferRequiredMCPServers(agent),
        compatibility: calculateCompatibility(agent, orchestratedAgents),
        recommendationScore: calculateRecommendationScore(agent, orchestratedAgents),
        recentPerformance: {
          tasksCompleted: Math.floor(Math.random() * 50),
          successRate: 0.8 + Math.random() * 0.2,
          avgCompletionTime: Math.floor(Math.random() * 300) + 60
        }
      }));
      setAvailableAgents(enhancedAgents);
    } finally {
      setLoading(false);
    }
  };

  const inferAgentSpecialties = (agent: Agent): string[] => {
    const specialties: string[] = [];
    const name = agent.name.toLowerCase();
    const description = (agent.system_prompt || "").toLowerCase();
    
    if (name.includes('code') || description.includes('code')) specialties.push('coding');
    if (name.includes('test') || description.includes('test')) specialties.push('testing');
    if (name.includes('review') || description.includes('review')) specialties.push('reviewing');
    if (name.includes('doc') || description.includes('documentation')) specialties.push('documentation');
    if (name.includes('security') || description.includes('security')) specialties.push('security');
    if (name.includes('performance') || description.includes('performance')) specialties.push('optimization');
    
    return specialties;
  };

  const inferCapabilities = (agent: Agent): string[] => {
    const capabilities: string[] = [];
    const nameLower = agent.name.toLowerCase();
    const promptLower = agent.system_prompt.toLowerCase();
    
    if (nameLower.includes('code') || promptLower.includes('code')) capabilities.push('coding');
    if (nameLower.includes('review') || promptLower.includes('review')) capabilities.push('reviewing');
    if (nameLower.includes('test') || promptLower.includes('test')) capabilities.push('testing');
    if (nameLower.includes('doc') || promptLower.includes('document')) capabilities.push('documentation');
    if (nameLower.includes('security') || promptLower.includes('security')) capabilities.push('security');
    
    return capabilities;
  };

  const inferRequiredMCPServers = (agent: Agent): string[] => {
    const servers: string[] = [];
    const capabilities = inferCapabilities(agent);
    
    if (capabilities.includes('coding') || capabilities.includes('reviewing')) servers.push('filesystem');
    if (capabilities.includes('git_operations')) servers.push('git');
    if (capabilities.includes('database_operations')) servers.push('database');
    if (capabilities.includes('api_testing')) servers.push('http-client');
    
    return servers;
  };

  const calculateCompatibility = (agent: Agent, currentAgents: OrchestratedAgent[]): number => {
    if (currentAgents.length === 0) return 1;
    
    let score = 0;
    const agentSpecialties = inferAgentSpecialties(agent);
    
    currentAgents.forEach(oa => {
      const otherSpecialties = oa.performance.specialties;
      
      // Check for complementary skills
      const overlap = agentSpecialties.filter(s => otherSpecialties.includes(s)).length;
      const unique = agentSpecialties.filter(s => !otherSpecialties.includes(s)).length;
      
      // Higher score for complementary skills
      score += unique * 0.3 - overlap * 0.1;
    });
    
    return Math.min(Math.max(score / currentAgents.length, 0), 1);
  };

  const calculateRecommendationScore = (agent: Agent, currentAgents: OrchestratedAgent[]): number => {
    let score = 0;
    
    // Base score on agent capabilities
    score += (inferCapabilities(agent).length || 0) * 0.1;
    
    // Compatibility with current agents
    score += calculateCompatibility(agent, currentAgents) * 0.3;
    
    // Performance history (mock)
    score += Math.random() * 0.3;
    
    // Boost for certain collaboration modes
    if (collaborationMode === 'ensemble' && currentAgents.length < 4) {
      score += 0.3;
    }
    
    return Math.min(score, 1);
  };

  const filteredAgents = availableAgents
    .filter(agent => {
      // Filter out already selected agents
      if (orchestratedAgents.some(oa => oa.agent.id === agent.id)) return false;
      
      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          agent.name.toLowerCase().includes(query) ||
          agent.system_prompt?.toLowerCase().includes(query) ||
          inferAgentSpecialties(agent).some(s => s.includes(query))
        );
      }
      
      // Category filter
      if (filterCategory !== 'all') {
        return inferAgentSpecialties(agent).includes(filterCategory);
      }
      
      return true;
    })
    .sort((a, b) => {
      // Sort by recommendation score if enabled
      if (showRecommendations) {
        return (b.recommendationScore || 0) - (a.recommendationScore || 0);
      }
      return 0;
    });

  const handleMultiSelect = () => {
    selectedAgentIds.forEach(agentId => {
      const agent = availableAgents.find(a => String(a.id) === agentId);
      if (agent) {
        onAgentSelect(agent);
      }
    });
    setSelectedAgentIds(new Set());
  };

  const getAgentIcon = (agent: AgentWithMetadata) => {
    const iconName = agent.icon as keyof typeof AGENT_ICONS;
    const IconComponent = AGENT_ICONS[iconName] || Bot;
    return IconComponent;
  };

  const getSpecialtyIcon = (specialty: string) => {
    switch (specialty) {
      case 'coding': return Code;
      case 'testing': return TestTube;
      case 'reviewing': return Shield;
      case 'documentation': return FileText;
      case 'security': return Shield;
      case 'optimization': return Zap;
      default: return Brain;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Search and Filters */}
      <div className="space-y-3 mb-4">
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search agents by name, skill, or capability..."
              className="pl-10"
            />
          </div>
          <Select value={filterCategory} onValueChange={setFilterCategory}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="coding">Coding</SelectItem>
              <SelectItem value="testing">Testing</SelectItem>
              <SelectItem value="reviewing">Reviewing</SelectItem>
              <SelectItem value="documentation">Documentation</SelectItem>
              <SelectItem value="security">Security</SelectItem>
              <SelectItem value="optimization">Optimization</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant={showRecommendations ? "default" : "outline"}
              size="sm"
              onClick={() => setShowRecommendations(!showRecommendations)}
            >
              <Sparkles className="h-4 w-4 mr-1" />
              AI Recommendations
            </Button>
            {selectedAgentIds.size > 0 && (
              <Button
                variant="secondary"
                size="sm"
                onClick={handleMultiSelect}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add {selectedAgentIds.size} Agents
              </Button>
            )}
          </div>
          
          <div className="text-sm text-muted-foreground">
            {filteredAgents.length} available agents
          </div>
        </div>
      </div>

      {/* Selected Agents */}
      {orchestratedAgents.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium mb-2">Selected Agents</h4>
          <div className="flex flex-wrap gap-2">
            {orchestratedAgents.map(oa => (
              <Badge
                key={oa.agent.id}
                variant="secondary"
                className="pl-1 pr-2 py-1 flex items-center gap-1"
              >
                <Bot className="h-3 w-3" />
                {oa.agent.name}
                <button
                  onClick={() => oa.agent.id !== undefined && onAgentRemove(String(oa.agent.id))}
                  className="ml-1 hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Agent Grid */}
      <ScrollArea className="flex-1">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {filteredAgents.map((agent) => {
            const IconComponent = getAgentIcon(agent);
            const isSelected = agent.id !== undefined && selectedAgentIds.has(String(agent.id));
            const isRecommended = showRecommendations && (agent.recommendationScore || 0) > 0.7;
            
            return (
              <motion.div
                key={agent.id}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2 }}
              >
                <Card 
                  className={cn(
                    "relative cursor-pointer transition-all",
                    "hover:shadow-md hover:border-primary/50",
                    isSelected && "border-primary ring-2 ring-primary/20",
                    isRecommended && "border-primary/30 bg-primary/5"
                  )}
                  onClick={() => {
                    if (agent.id === undefined) return;
                    const agentIdStr = String(agent.id);
                    
                    if (selectedAgentIds.has(agentIdStr)) {
                      setSelectedAgentIds(prev => {
                        const next = new Set(prev);
                        next.delete(agentIdStr);
                        return next;
                      });
                    } else {
                      setSelectedAgentIds(prev => new Set(prev).add(agentIdStr));
                    }
                  }}
                >
                  {isRecommended && (
                    <div className="absolute -top-2 -right-2 z-10">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <Badge variant="default" className="gap-1">
                              <Sparkles className="h-3 w-3" />
                              Recommended
                            </Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            AI recommends this agent based on your current setup
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  )}
                  
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <div className={cn(
                          "p-2 rounded-lg",
                          isSelected ? "bg-primary text-primary-foreground" : "bg-muted"
                        )}>
                          <IconComponent className="h-4 w-4" />
                        </div>
                        <div>
                          <h4 className="font-medium text-sm">{agent.name}</h4>
                        </div>
                      </div>
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={() => {}}
                      />
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-3">
                    {agent.system_prompt && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {agent.system_prompt.slice(0, 100)}...
                      </p>
                    )}
                    
                    {showDetails && (
                      <>
                        {/* Specialties */}
                        {inferAgentSpecialties(agent).length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {inferAgentSpecialties(agent).map(specialty => {
                              const SpecialtyIcon = getSpecialtyIcon(specialty);
                              return (
                                <Badge key={specialty} variant="outline" className="text-xs gap-1">
                                  <SpecialtyIcon className="h-3 w-3" />
                                  {specialty}
                                </Badge>
                              );
                            })}
                          </div>
                        )}
                        
                        {/* Performance Stats */}
                        {agent.recentPerformance && (
                          <div className="grid grid-cols-3 gap-2 text-xs">
                            <div className="text-center">
                              <div className="font-medium">
                                {agent.recentPerformance.tasksCompleted}
                              </div>
                              <div className="text-muted-foreground">Tasks</div>
                            </div>
                            <div className="text-center">
                              <div className="font-medium">
                                {(agent.recentPerformance.successRate * 100).toFixed(0)}%
                              </div>
                              <div className="text-muted-foreground">Success</div>
                            </div>
                            <div className="text-center">
                              <div className="font-medium">
                                {Math.floor(agent.recentPerformance.avgCompletionTime / 60)}m
                              </div>
                              <div className="text-muted-foreground">Avg Time</div>
                            </div>
                          </div>
                        )}
                        
                        {/* Compatibility Score */}
                        {orchestratedAgents.length > 0 && agent.compatibility !== undefined && (
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-muted-foreground">Team Compatibility</span>
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              <span className="font-medium">
                                {(agent.compatibility * 100).toFixed(0)}%
                              </span>
                            </div>
                          </div>
                        )}
                        
                        {/* Required MCP Servers */}
                        {agent.requiredMCPServers && agent.requiredMCPServers.length > 0 && (
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <AlertCircle className="h-3 w-3" />
                            Requires: {agent.requiredMCPServers.join(', ')}
                          </div>
                        )}
                      </>
                    )}
                    
                    {/* Quick Action */}
                    <Button
                      size="sm"
                      variant={isSelected ? "default" : "outline"}
                      className="w-full"
                      onClick={(e) => {
                        e.stopPropagation();
                        onAgentSelect(agent);
                      }}
                    >
                      {isSelected ? (
                        <>
                          <Check className="h-4 w-4 mr-1" />
                          Selected
                        </>
                      ) : (
                        <>
                          <Plus className="h-4 w-4 mr-1" />
                          Add to Orchestra
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );
};
