import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Users2,
  MessageSquare,
  Share2,
  ArrowLeftRight,
  Zap,
  Activity,
  Brain,
  Shield,
  Code,
  FileText,
  GitBranch,
  Eye,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PremiumProgress } from "@/components/ui/premium-progress";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import type { OrchestratedAgent } from "../AgentOrchestraPanel";
import { invoke } from "@tauri-apps/api/core";
import { listen } from "@tauri-apps/api/event";

interface CollaborationIndicatorProps {
  orchestratedAgents: OrchestratedAgent[];
  onCollaborationClick?: (agent1Id: string, agent2Id: string) => void;
}

interface CollaborationLink {
  id: string;
  agent1Id: string;
  agent2Id: string;
  type: 'active' | 'pending' | 'completed';
  strength: number; // 0-1
  messages: number;
  sharedArtifacts: number;
  startTime: string;
  lastActivity: string;
}

interface CollaborationMessage {
  fromAgent: string;
  toAgent: string;
  type: 'request' | 'response' | 'share' | 'sync';
  content: string;
  timestamp: string;
}

// Backend types matching Rust structs
interface BackendCollaborationChannel {
  id: string;
  agent1_id: string;
  agent2_id: string;
  channel_type: string;
  status: string;
  created_at: string;
  last_activity: string;
  message_count: number;
  shared_artifacts: string[];
  collaboration_strength: number;
}

interface BackendInterAgentMessage {
  id: string;
  channel_id: string;
  from_agent_id: string;
  to_agent_id: string;
  message_type: string;
  content: string;
  metadata: Record<string, string>;
  timestamp: string;
  priority: string;
  requires_response: boolean;
  response_deadline?: string;
}

export const CollaborationIndicator: React.FC<CollaborationIndicatorProps> = ({
  orchestratedAgents,
  onCollaborationClick
}) => {
  const [collaborationLinks, setCollaborationLinks] = useState<CollaborationLink[]>([]);
  const [recentMessages, setRecentMessages] = useState<CollaborationMessage[]>([]);
  const [selectedLink, setSelectedLink] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  // Load real collaboration channels from backend
  useEffect(() => {
    const loadCollaborationData = async () => {
      try {
        const agentIds = orchestratedAgents
          .map(agent => String(agent.agent.id))
          .filter(Boolean);
        
        if (agentIds.length === 0) {
          setCollaborationLinks([]);
          return;
        }
        
        // Get active collaboration channels
        const backendChannels = await invoke('get_collaboration_channels', {
          agentIds
        }) as BackendCollaborationChannel[];
        
        // Convert backend channels to frontend format
        const links: CollaborationLink[] = backendChannels.map(channel => ({
          id: channel.id,
          agent1Id: channel.agent1_id,
          agent2Id: channel.agent2_id,
          type: channel.status === 'active' ? 'active' : 'pending',
          strength: channel.collaboration_strength,
          messages: channel.message_count,
          sharedArtifacts: channel.shared_artifacts.length,
          startTime: channel.created_at,
          lastActivity: channel.last_activity
        }));
        
        setCollaborationLinks(links);
        
        // Auto-create channels for agents that should be collaborating
        await createMissingCollaborationChannels(agentIds);
        
      } catch (error) {
        console.error("Failed to load collaboration data:", error);
        // Fallback to mock data
        generateFallbackCollaborations();
      }
    };
    
    loadCollaborationData();
  }, [orchestratedAgents]);

  // Listen for real-time collaboration events
  useEffect(() => {
    const setupEventListeners = async () => {
      // Listen for new inter-agent messages
      const unlisten1 = await listen('inter-agent-message', (event: any) => {
        const message = event.payload.message as BackendInterAgentMessage;
        
        // Convert to frontend format and add to recent messages
        const frontendMessage: CollaborationMessage = {
          fromAgent: message.from_agent_id,
          toAgent: message.to_agent_id,
          type: mapMessageType(message.message_type),
          content: message.content,
          timestamp: message.timestamp
        };
        
        setRecentMessages(prev => [frontendMessage, ...prev].slice(0, 10));
      });
      
      // Listen for collaboration channel creation
      const unlisten2 = await listen('collaboration-channel-created', (event: any) => {
        const channel = event.payload.channel as BackendCollaborationChannel;
        
        // Convert and add to collaboration links
        const newLink: CollaborationLink = {
          id: channel.id,
          agent1Id: channel.agent1_id,
          agent2Id: channel.agent2_id,
          type: channel.status === 'active' ? 'active' : 'pending',
          strength: channel.collaboration_strength,
          messages: channel.message_count,
          sharedArtifacts: channel.shared_artifacts.length,
          startTime: channel.created_at,
          lastActivity: channel.last_activity
        };
        
        setCollaborationLinks(prev => {
          const existing = prev.find(link => link.id === newLink.id);
          if (existing) {
            return prev.map(link => link.id === newLink.id ? newLink : link);
          }
          return [...prev, newLink];
        });
      });
      
      return () => {
        unlisten1();
        unlisten2();
      };
    };
    
    setupEventListeners();
  }, []);
  
  // Load recent messages for active channels
  useEffect(() => {
    const loadRecentMessages = async () => {
      try {
        const allMessages: CollaborationMessage[] = [];
        
        // Load messages from all active channels
        for (const link of collaborationLinks) {
          const channelMessages = await invoke('get_channel_messages', {
            channelId: link.id,
            limit: 5
          }) as BackendInterAgentMessage[];
          
          const frontendMessages = channelMessages.map(msg => ({
            fromAgent: msg.from_agent_id,
            toAgent: msg.to_agent_id,
            type: mapMessageType(msg.message_type),
            content: msg.content,
            timestamp: msg.timestamp
          }));
          
          allMessages.push(...frontendMessages);
        }
        
        // Sort by timestamp and take most recent
        allMessages.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
        setRecentMessages(allMessages.slice(0, 10));
        
      } catch (error) {
        console.error("Failed to load recent messages:", error);
      }
    };
    
    if (collaborationLinks.length > 0) {
      loadRecentMessages();
    }
  }, [collaborationLinks]);

  // Helper functions for real collaboration protocols
  const createMissingCollaborationChannels = async (agentIds: string[]) => {
    try {
      // Create channels between agents that should be collaborating based on their status
      for (let i = 0; i < agentIds.length; i++) {
        for (let j = i + 1; j < agentIds.length; j++) {
          const agent1 = orchestratedAgents.find(a => String(a.agent.id) === agentIds[i]);
          const agent2 = orchestratedAgents.find(a => String(a.agent.id) === agentIds[j]);
          
          // Create channels if both agents are working and have compatible specialties
          if (agent1 && agent2 && shouldCollaborate(agent1, agent2)) {
            await invoke('create_collaboration_channel', {
              agent1Id: agentIds[i],
              agent2Id: agentIds[j],
              channelType: 'peer_to_peer'
            });
          }
        }
      }
    } catch (error) {
      console.error("Failed to create collaboration channels:", error);
    }
  };

  const shouldCollaborate = (agent1: OrchestratedAgent, agent2: OrchestratedAgent): boolean => {
    // Check if agents should collaborate based on:
    // 1. Both are working
    // 2. Have complementary specialties
    // 3. Are explicitly marked as collaborating
    return (
      (agent1.status === 'working' && agent2.status === 'working') ||
      agent1.collaboratingWith?.includes(agent2.agent.id as never) ||
      agent2.collaboratingWith?.includes(agent1.agent.id as never) ||
      hasComplementarySkills(agent1, agent2)
    );
  };

  const hasComplementarySkills = (agent1: OrchestratedAgent, agent2: OrchestratedAgent): boolean => {
    const agent1Specialties = agent1.performance.specialties;
    const agent2Specialties = agent2.performance.specialties;
    
    // Check for specific complementary pairs
    const complementaryPairs = [
      ['coding', 'reviewing'],
      ['coding', 'testing'],
      ['designing', 'implementing'],
      ['optimization', 'testing'],
      ['documentation', 'coding']
    ];
    
    return complementaryPairs.some(([skill1, skill2]) => 
      (agent1Specialties.includes(skill1) && agent2Specialties.includes(skill2)) ||
      (agent1Specialties.includes(skill2) && agent2Specialties.includes(skill1))
    );
  };

  const mapMessageType = (backendType: string): CollaborationMessage['type'] => {
    switch (backendType) {
      case 'request': return 'request';
      case 'response': return 'response';
      case 'share': return 'share';
      case 'sync': return 'sync';
      case 'notification': return 'share'; // Map notifications to share type
      default: return 'sync';
    }
  };

  const generateFallbackCollaborations = () => {
    // Fallback method when backend fails
    const links: CollaborationLink[] = [];
    
    orchestratedAgents.forEach((agent1, i) => {
      orchestratedAgents.slice(i + 1).forEach(agent2 => {
        if (shouldCollaborate(agent1, agent2)) {
          links.push({
            id: `${agent1.agent.id}_${agent2.agent.id}`,
            agent1Id: String(agent1.agent.id),
            agent2Id: String(agent2.agent.id),
            type: agent1.status === 'working' && agent2.status === 'working' ? 'active' : 'pending',
            strength: calculateCollaborationStrength(agent1, agent2),
            messages: Math.floor(Math.random() * 10) + 1,
            sharedArtifacts: Math.floor(Math.random() * 3),
            startTime: new Date(Date.now() - Math.random() * 3600000).toISOString(),
            lastActivity: new Date().toISOString()
          });
        }
      });
    });
    
    setCollaborationLinks(links);
  };

  // Enhanced collaboration strength calculation
  const calculateCollaborationStrength = (agent1: OrchestratedAgent, agent2: OrchestratedAgent): number => {
    let strength = 0;
    
    // Check specialty overlap
    const specialtyOverlap = agent1.performance.specialties.filter(s => 
      agent2.performance.specialties.includes(s)
    ).length;
    strength += specialtyOverlap * 0.2;
    
    // Check if both are actively working
    if (agent1.status === 'working' && agent2.status === 'working') {
      strength += 0.3;
    }
    
    // Check collaboration score
    strength += (agent1.performance.collaborationScore + agent2.performance.collaborationScore) / 2 * 0.5;
    
    return Math.min(strength, 1);
  };

  // Initiate real collaboration between agents
  const initiateCollaboration = async (agent1Id: string, agent2Id: string, _type: string = 'general') => {
    try {
      const agent1 = orchestratedAgents.find(a => String(a.agent.id) === agent1Id);
      const agent2 = orchestratedAgents.find(a => String(a.agent.id) === agent2Id);
      
      if (!agent1 || !agent2) return;
      
      // Create collaboration channel if not exists
      await invoke('create_collaboration_channel', {
        agent1Id,
        agent2Id,
        channelType: 'peer_to_peer'
      });
      
      // Send initial collaboration message
      const collaborationContent = generateCollaborationContent(agent1, agent2);
      
      await invoke('send_inter_agent_message', {
        fromAgentId: agent1Id,
        toAgentId: agent2Id,
        messageType: 'request',
        content: collaborationContent,
        priority: 'medium',
        requiresResponse: true
      });
      
      // Trigger collaboration click callback
      onCollaborationClick?.(agent1Id, agent2Id);
      
    } catch (error) {
      console.error("Failed to initiate collaboration:", error);
    }
  };
  
  const generateCollaborationContent = (agent1: OrchestratedAgent, agent2: OrchestratedAgent): string => {
    const agent1Specialties = agent1.performance.specialties;
    const agent2Specialties = agent2.performance.specialties;
    
    // Generate contextual collaboration request based on specialties
    if (agent1Specialties.includes('coding') && agent2Specialties.includes('reviewing')) {
      return `Hi ${agent2.agent.name}, I'm working on a coding task and would appreciate your review expertise. Could you help validate my implementation approach?`;
    } else if (agent1Specialties.includes('coding') && agent2Specialties.includes('testing')) {
      return `Hi ${agent2.agent.name}, I've completed the implementation and need comprehensive testing. Could you help with test case design and validation?`;
    } else if (agent1Specialties.includes('optimization') && agent2Specialties.includes('testing')) {
      return `Hi ${agent2.agent.name}, I'm optimizing performance and need to validate that changes don't break functionality. Can you help with regression testing?`;
    } else if (hasComplementarySkills(agent1, agent2)) {
      return `Hi ${agent2.agent.name}, I see we have complementary skills. Would you like to collaborate on the current task? I can handle the ${agent1Specialties[0]} aspects.`;
    } else {
      return `Hi ${agent2.agent.name}, I'd like to collaborate on the current task. Let's coordinate our efforts to maximize efficiency.`;
    }
  };

  const getAgentIcon = (agent: OrchestratedAgent) => {
    // Simple icon mapping based on agent specialties
    if (agent.performance.specialties.includes('coding')) return Code;
    if (agent.performance.specialties.includes('reviewing')) return Shield;
    if (agent.performance.specialties.includes('optimization')) return Zap;
    if (agent.performance.specialties.includes('documentation')) return FileText;
    return Brain;
  };

  const getMessageIcon = (type: CollaborationMessage['type']) => {
    switch (type) {
      case 'request': return MessageSquare;
      case 'response': return ArrowLeftRight;
      case 'share': return Share2;
      case 'sync': return GitBranch;
    }
  };

  const activeCollaborations = collaborationLinks.filter(link => link.type === 'active').length;
  const totalMessages = collaborationLinks.reduce((sum, link) => sum + link.messages, 0);

  return (
    <div className="space-y-4">
      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-3">
        <Card>
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-muted-foreground">Active</p>
                <p className="text-lg font-semibold">{activeCollaborations}</p>
              </div>
              <Activity className="h-4 w-4 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-muted-foreground">Messages</p>
                <p className="text-lg font-semibold">{totalMessages}</p>
              </div>
              <MessageSquare className="h-4 w-4 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-muted-foreground">Agents</p>
                <p className="text-lg font-semibold">{orchestratedAgents.length}</p>
              </div>
              <Users2 className="h-4 w-4 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Collaboration Network */}
      {collaborationLinks.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium">Collaboration Network</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDetails(!showDetails)}
              >
                <Eye className="h-4 w-4 mr-1" />
                {showDetails ? "Hide" : "Show"} Details
              </Button>
            </div>
            
            {/* Visual Network */}
            <div className="relative h-48 bg-muted/20 rounded-lg mb-3">
              {/* Simple visualization - in production, use a proper graph library */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative">
                  {orchestratedAgents.map((agent, index) => {
                    const angle = (index / orchestratedAgents.length) * 2 * Math.PI;
                    const radius = 60;
                    const x = Math.cos(angle) * radius;
                    const y = Math.sin(angle) * radius;
                    const Icon = getAgentIcon(agent);
                    
                    return (
                      <TooltipProvider key={agent.agent.id}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <motion.div
                              className={cn(
                                "absolute w-10 h-10 rounded-full flex items-center justify-center cursor-pointer",
                                "bg-background border-2 shadow-sm",
                                agent.status === 'working' ? "border-primary" : "border-border"
                              )}
                              style={{
                                left: `calc(50% + ${x}px - 20px)`,
                                top: `calc(50% + ${y}px - 20px)`
                              }}
                              whileHover={{ scale: 1.1 }}
                              onClick={() => {
                                // Find collaborations for this agent
                                const agentLinks = collaborationLinks.filter(link => 
                                  link.agent1Id === String(agent.agent.id) || link.agent2Id === String(agent.agent.id)
                                );
                                if (agentLinks.length > 0) {
                                  setSelectedLink(agentLinks[0].id);
                                } else {
                                  // No existing collaborations - find a good collaboration partner
                                  const otherAgents = orchestratedAgents.filter(a => a.agent.id !== agent.agent.id);
                                  const bestPartner = otherAgents.find(other => shouldCollaborate(agent, other));
                                  
                                  if (bestPartner) {
                                    initiateCollaboration(String(agent.agent.id), String(bestPartner.agent.id));
                                  }
                                }
                              }}
                            >
                              <Icon className="h-4 w-4" />
                            </motion.div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="font-medium">{agent.agent.name}</p>
                            <p className="text-xs">{agent.status}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    );
                  })}
                  
                  {/* Collaboration Lines */}
                  <svg className="absolute inset-0 pointer-events-none" style={{ width: '200px', height: '200px', left: 'calc(50% - 100px)', top: 'calc(50% - 100px)' }}>
                    {collaborationLinks.map(link => {
                      const agent1Index = orchestratedAgents.findIndex(a => String(a.agent.id) === link.agent1Id);
                      const agent2Index = orchestratedAgents.findIndex(a => String(a.agent.id) === link.agent2Id);
                      
                      if (agent1Index === -1 || agent2Index === -1) return null;
                      
                      const angle1 = (agent1Index / orchestratedAgents.length) * 2 * Math.PI;
                      const angle2 = (agent2Index / orchestratedAgents.length) * 2 * Math.PI;
                      const radius = 60;
                      
                      const x1 = Math.cos(angle1) * radius + 100;
                      const y1 = Math.sin(angle1) * radius + 100;
                      const x2 = Math.cos(angle2) * radius + 100;
                      const y2 = Math.sin(angle2) * radius + 100;
                      
                      return (
                        <motion.line
                          key={link.id}
                          x1={x1}
                          y1={y1}
                          x2={x2}
                          y2={y2}
                          stroke={
                            link.type === 'active' ? '#22c55e' :
                            link.type === 'pending' ? '#f59e0b' :
                            '#6b7280'
                          }
                          strokeWidth={Math.max(1, link.strength * 3)}
                          strokeDasharray={link.type === 'pending' ? "5 5" : "none"}
                          initial={{ pathLength: 0 }}
                          animate={{ pathLength: 1 }}
                          transition={{ duration: 0.5 }}
                        />
                      );
                    })}
                  </svg>
                </div>
              </div>
            </div>
            
            {/* Collaboration Details */}
            {showDetails && (
              <div className="space-y-2">
                {collaborationLinks.map(link => {
                  const agent1 = orchestratedAgents.find(a => String(a.agent.id) === link.agent1Id);
                  const agent2 = orchestratedAgents.find(a => String(a.agent.id) === link.agent2Id);
                  
                  if (!agent1 || !agent2) return null;
                  
                  return (
                    <Card
                      key={link.id}
                      className={cn(
                        "cursor-pointer transition-all",
                        selectedLink === link.id && "ring-2 ring-primary"
                      )}
                      onClick={() => {
                        setSelectedLink(link.id);
                        onCollaborationClick?.(link.agent1Id, link.agent2Id);
                      }}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Badge variant={
                              link.type === 'active' ? 'default' :
                              link.type === 'pending' ? 'secondary' :
                              'outline'
                            } className="text-xs">
                              {link.type}
                            </Badge>
                            <span className="text-sm font-medium">
                              {agent1.agent.name} ↔ {agent2.agent.name}
                            </span>
                          </div>
                          <div className="flex items-center gap-3 text-xs text-muted-foreground">
                            <span>{link.messages} msgs</span>
                            <span>{link.sharedArtifacts} artifacts</span>
                          </div>
                        </div>
                        
                        <PremiumProgress value={link.strength * 100} className="h-1" />
                        
                        <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                          <span>Started {new Date(link.startTime).toLocaleTimeString()}</span>
                          <span>•</span>
                          <span>Last activity {new Date(link.lastActivity).toLocaleTimeString()}</span>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Recent Messages */}
      {recentMessages.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <h4 className="text-sm font-medium mb-3">Recent Communications</h4>
            <div className="space-y-2">
              <AnimatePresence>
                {recentMessages.slice(0, 5).map((message, index) => {
                  const fromAgent = orchestratedAgents.find(a => String(a.agent.id) === message.fromAgent);
                  const toAgent = orchestratedAgents.find(a => String(a.agent.id) === message.toAgent);
                  const MessageIcon = getMessageIcon(message.type);
                  
                  return (
                    <motion.div
                      key={`${message.timestamp}_${index}`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      className="flex items-center gap-2 text-sm"
                    >
                      <MessageIcon className="h-3 w-3 text-muted-foreground" />
                      <span className="font-medium">{fromAgent?.agent.name}</span>
                      <ArrowLeftRight className="h-3 w-3 text-muted-foreground" />
                      <span className="font-medium">{toAgent?.agent.name}</span>
                      <span className="text-muted-foreground flex-1 truncate">
                        {message.content}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </span>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {collaborationLinks.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center text-muted-foreground">
            <Users2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No active collaborations</p>
            <p className="text-sm mt-2">Agents will collaborate when working on related tasks</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
