import React, { useState, useEffect } from "react";
import { invoke } from '@tauri-apps/api/core';
import {
  Wand2,
  Brain,
  Target,
  Users,
  CheckCircle,
  Loader2,
  Bot,
  Zap,
  Shield,
  Code,
  FileText,
  TestTube,
  GitBranch,
  Lightbulb,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { PremiumProgress } from "@/components/ui/premium-progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import type { AgentTask, OrchestratedAgent } from "../AgentOrchestraPanel";

interface IntelligentTaskRouterProps {
  projectPath: string;
  onClose: () => void;
  onRouteTask: (task: AgentTask, agentIds: string[]) => void;
  availableAgents: OrchestratedAgent[];
}

interface TaskAnalysis {
  complexity: 'simple' | 'moderate' | 'complex';
  estimatedDuration: number; // minutes
  requiredCapabilities: string[];
  suggestedBreakdown?: TaskBreakdown[];
  dependencies: string[];
  priority: AgentTask['priority'];
  confidenceScore: number;
}

interface TaskBreakdown {
  id: string;
  title: string;
  description: string;
  estimatedDuration: number;
  requiredCapabilities: string[];
  suggestedAgent?: string;
}

interface AgentRecommendation {
  agentId: string;
  score: number;
  reasons: string[];
  capabilities: string[];
  currentLoad: number;
  estimatedCompletionTime: number;
}

export const IntelligentTaskRouter: React.FC<IntelligentTaskRouterProps> = ({
  onClose,
  onRouteTask,
  availableAgents
}) => {
  const [taskDescription, setTaskDescription] = useState("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [taskAnalysis, setTaskAnalysis] = useState<TaskAnalysis | null>(null);
  const [agentRecommendations, setAgentRecommendations] = useState<AgentRecommendation[]>([]);
  const [selectedAgents, setSelectedAgents] = useState<Set<string>>(new Set());
  const [autoAssign, setAutoAssign] = useState(true);
  const [customPriority, setCustomPriority] = useState<AgentTask['priority'] | null>(null);
  const [activeTab, setActiveTab] = useState<'analysis' | 'recommendations' | 'breakdown'>('analysis');

  // Analyze task when description changes
  useEffect(() => {
    if (taskDescription.length > 10) {
      const timer = setTimeout(() => {
        analyzeTask();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [taskDescription]);

  const analyzeTask = async () => {
    setIsAnalyzing(true);
    
    try {
      // Real AI task analysis using Tauri backend
      const analysisResult = await invoke('analyze_task_requirements', {
        taskDescription,
        availableAgents: availableAgents.map(agent => ({
          id: agent.agent.id,
          name: agent.agent.name,
          capabilities: agent.performance.specialties,
          currentLoad: agent.progress
        }))
      }) as {
        complexity: 'simple' | 'moderate' | 'complex';
        estimatedDuration: number;
        requiredCapabilities: string[];
        priority: AgentTask['priority'];
        dependencies: string[];
        confidenceScore: number;
        suggestedBreakdown?: TaskBreakdown[];
      };
      
      const analysis: TaskAnalysis = {
        complexity: analysisResult.complexity,
        estimatedDuration: analysisResult.estimatedDuration,
        requiredCapabilities: analysisResult.requiredCapabilities,
        dependencies: analysisResult.dependencies,
        priority: customPriority || analysisResult.priority,
        confidenceScore: analysisResult.confidenceScore,
        suggestedBreakdown: analysisResult.suggestedBreakdown
      };
      
      setTaskAnalysis(analysis);
      
      // Generate agent recommendations using real analysis
      await generateAgentRecommendations(analysis);
      
    } catch (error) {
      console.error('Task analysis failed:', error);
      
      // Fallback to simplified analysis
      const words = taskDescription.toLowerCase().split(' ');
      const complexity = words.length > 50 ? 'complex' : words.length > 20 ? 'moderate' : 'simple';
      
      // Extract basic capabilities
      const requiredCapabilities = extractBasicCapabilities(words);
      
      // Determine priority
      let priority: AgentTask['priority'] = 'medium';
      if (words.includes('urgent') || words.includes('critical') || words.includes('asap')) {
        priority = 'critical';
      } else if (words.includes('important') || words.includes('high')) {
        priority = 'high';
      } else if (words.includes('low') || words.includes('minor')) {
        priority = 'low';
      }
      
      const analysis: TaskAnalysis = {
        complexity,
        estimatedDuration: complexity === 'complex' ? 180 : complexity === 'moderate' ? 90 : 30,
        requiredCapabilities: requiredCapabilities.length > 0 ? requiredCapabilities : ['general'],
        dependencies: [],
        priority: customPriority || priority,
        confidenceScore: 0.6 // Lower confidence for fallback
      };
      
      if (complexity === 'complex') {
        analysis.suggestedBreakdown = generateTaskBreakdown(requiredCapabilities);
      }
      
      setTaskAnalysis(analysis);
      await generateAgentRecommendations(analysis);
    } finally {
      setIsAnalyzing(false);
    }
  };
  
  // Helper function for basic capability extraction (fallback)
  const extractBasicCapabilities = (words: string[]): string[] => {
    const capabilityKeywords = {
      'code': ['code', 'implement', 'function', 'class', 'api', 'develop'],
      'review': ['review', 'check', 'analyze', 'audit', 'inspect'],
      'test': ['test', 'testing', 'unit', 'integration', 'qa'],
      'optimize': ['optimize', 'performance', 'speed', 'efficiency'],
      'document': ['document', 'documentation', 'explain', 'describe'],
      'security': ['security', 'vulnerability', 'secure', 'auth'],
      'design': ['design', 'architecture', 'structure', 'pattern']
    };
    
    const requiredCapabilities: string[] = [];
    Object.entries(capabilityKeywords).forEach(([capability, keywords]) => {
      if (keywords.some(keyword => words.includes(keyword))) {
        requiredCapabilities.push(capability);
      }
    });
    
    return requiredCapabilities;
  };

  const generateTaskBreakdown = (capabilities: string[]): TaskBreakdown[] => {
    // Simulate breaking down complex tasks
    const breakdowns: TaskBreakdown[] = [
      {
        id: 'subtask_1',
        title: 'Initial Analysis & Planning',
        description: 'Analyze requirements and create implementation plan',
        estimatedDuration: 30,
        requiredCapabilities: ['design', 'planning']
      },
      {
        id: 'subtask_2',
        title: 'Core Implementation',
        description: 'Implement main functionality',
        estimatedDuration: 60,
        requiredCapabilities: capabilities.includes('code') ? ['code'] : ['general']
      }
    ];
    
    if (capabilities.includes('test')) {
      breakdowns.push({
        id: 'subtask_3',
        title: 'Testing & Validation',
        description: 'Create and run tests',
        estimatedDuration: 45,
        requiredCapabilities: ['test']
      });
    }
    
    if (capabilities.includes('document')) {
      breakdowns.push({
        id: 'subtask_4',
        title: 'Documentation',
        description: 'Document implementation and usage',
        estimatedDuration: 30,
        requiredCapabilities: ['document']
      });
    }
    
    return breakdowns;
  };

  const generateAgentRecommendations = async (analysis: TaskAnalysis) => {
    try {
      // Use AI-powered agent recommendation system
      const recommendationResult = await invoke('generate_agent_recommendations', {
        taskAnalysis: {
          description: taskDescription,
          complexity: analysis.complexity,
          requiredCapabilities: analysis.requiredCapabilities,
          estimatedDuration: analysis.estimatedDuration,
          priority: analysis.priority
        },
        availableAgents: availableAgents.map(agent => ({
          id: agent.agent.id,
          name: agent.agent.name,
          capabilities: agent.performance.specialties,
          currentLoad: agent.progress,
          successRate: agent.performance.successRate,
          collaborationScore: agent.performance.collaborationScore,
          previousTasks: agent.memory.successfulTasks,
          averageCompletionTime: agent.memory.averageCompletionTime
        }))
      }) as {
        recommendations: {
          agentId: string;
          score: number;
          reasons: string[];
          estimatedCompletionTime: number;
        }[];
      };
      
      const recommendations: AgentRecommendation[] = recommendationResult.recommendations.map(rec => {
        const agent = availableAgents.find(a => String(a.agent.id) === rec.agentId);
        return {
          agentId: rec.agentId,
          score: rec.score,
          reasons: rec.reasons,
          capabilities: agent?.performance.specialties || [],
          currentLoad: agent?.progress || 0,
          estimatedCompletionTime: rec.estimatedCompletionTime
        };
      });
      
      setAgentRecommendations(recommendations);
      
      // Auto-select top agent if enabled
      if (autoAssign && recommendations.length > 0 && recommendations[0].score > 0.5) {
        setSelectedAgents(new Set([recommendations[0].agentId]));
      }
      
    } catch (error) {
      console.error('Agent recommendation failed:', error);
      
      // Fallback to simple scoring
      const recommendations: AgentRecommendation[] = availableAgents.map(agent => {
        let score = 0;
        const reasons: string[] = [];
        
        // Score based on capability match
        const agentCapabilities = agent.performance.specialties;
        const capabilityMatch = analysis.requiredCapabilities.filter(cap => 
          agentCapabilities.some(ac => ac.toLowerCase().includes(cap))
        ).length;
        
        if (capabilityMatch > 0) {
          score += capabilityMatch * 0.3;
          reasons.push(`Matches ${capabilityMatch} required capabilities`);
        }
        
        // Score based on current load
        const loadScore = 1 - (agent.progress / 100);
        score += loadScore * 0.2;
        if (loadScore > 0.8) {
          reasons.push("Currently available");
        }
        
        // Score based on past performance
        score += agent.performance.successRate * 0.3;
        if (agent.performance.successRate > 0.8) {
          reasons.push("High success rate");
        }
        
        // Score based on collaboration potential
        if (agent.performance.collaborationScore > 0.7) {
          score += 0.1;
          reasons.push("Strong collaborator");
        }
        
        // Score based on task complexity match
        if (analysis.complexity === 'complex' && agent.memory.successfulTasks > 10) {
          score += 0.1;
          reasons.push("Experienced with complex tasks");
        }
        
        return {
          agentId: String(agent.agent.id),
          score: Math.min(score, 1),
          reasons,
          capabilities: agentCapabilities,
          currentLoad: agent.progress,
          estimatedCompletionTime: analysis.estimatedDuration * (1 + agent.progress / 100)
        };
      }).sort((a, b) => b.score - a.score);
      
      setAgentRecommendations(recommendations);
      
      // Auto-select top agent if enabled
      if (autoAssign && recommendations.length > 0 && recommendations[0].score > 0.5) {
        setSelectedAgents(new Set([recommendations[0].agentId]));
      }
    }
  };

  const handleCreateTask = () => {
    if (!taskAnalysis || selectedAgents.size === 0) return;
    
    const task: Partial<AgentTask> = {
      description: taskDescription,
      priority: taskAnalysis.priority,
      requiredCapabilities: taskAnalysis.requiredCapabilities,
      dependencies: taskAnalysis.dependencies
    };
    
    onRouteTask(task as AgentTask, Array.from(selectedAgents));
  };

  const handleCreateSubtasks = () => {
    if (!taskAnalysis?.suggestedBreakdown) return;
    
    taskAnalysis.suggestedBreakdown.forEach(subtask => {
      const task: Partial<AgentTask> = {
        description: `${subtask.title}: ${subtask.description}`,
        priority: taskAnalysis.priority,
        requiredCapabilities: subtask.requiredCapabilities,
        dependencies: []
      };
      
      // Find best agent for subtask
      const bestAgent = agentRecommendations.find(rec => 
        subtask.requiredCapabilities.some(cap => 
          rec.capabilities.some(ac => ac.toLowerCase().includes(cap))
        )
      );
      
      if (bestAgent) {
        onRouteTask(task as AgentTask, [bestAgent.agentId]);
      }
    });
  };

  const getCapabilityIcon = (capability: string) => {
    const icons: Record<string, React.ComponentType<any>> = {
      code: Code,
      review: Shield,
      test: TestTube,
      optimize: Zap,
      document: FileText,
      security: Shield,
      design: Brain,
      general: Bot
    };
    return icons[capability] || Bot;
  };

  return (
    <Dialog open onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5 text-primary" />
            Intelligent Task Router
          </DialogTitle>
          <DialogDescription>
            AI-powered task analysis and agent assignment
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          {/* Task Input */}
          <div className="mb-6">
            <Label htmlFor="task-description">Task Description</Label>
            <Textarea
              id="task-description"
              value={taskDescription}
              onChange={(e) => setTaskDescription(e.target.value)}
              placeholder="Describe the task you want to assign. Be specific about requirements, goals, and any constraints..."
              rows={4}
              className="mt-2"
            />
            
            <div className="flex items-center justify-between mt-3">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="auto-assign"
                    checked={autoAssign}
                    onCheckedChange={(checked) => setAutoAssign(checked as boolean)}
                  />
                  <Label htmlFor="auto-assign" className="text-sm cursor-pointer">
                    Auto-assign to best agent
                  </Label>
                </div>
                
                <Select
                  value={customPriority || ""}
                  onValueChange={(value) => setCustomPriority(value as AgentTask['priority'])}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {isAnalyzing && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Analyzing task...
                </div>
              )}
            </div>
          </div>

          {/* Analysis Results */}
          {taskAnalysis && (
            <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="analysis">
                  <Brain className="h-4 w-4 mr-2" />
                  Analysis
                </TabsTrigger>
                <TabsTrigger value="recommendations">
                  <Users className="h-4 w-4 mr-2" />
                  Agent Recommendations
                </TabsTrigger>
                <TabsTrigger value="breakdown" disabled={!taskAnalysis.suggestedBreakdown}>
                  <GitBranch className="h-4 w-4 mr-2" />
                  Task Breakdown
                </TabsTrigger>
              </TabsList>

              <ScrollArea className="h-[400px] mt-4">
                <TabsContent value="analysis" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center justify-between">
                        Task Analysis
                        <Badge variant="outline">
                          {Math.round(taskAnalysis.confidenceScore * 100)}% confidence
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label className="text-sm">Complexity</Label>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant={
                              taskAnalysis.complexity === 'complex' ? 'destructive' :
                              taskAnalysis.complexity === 'moderate' ? 'secondary' :
                              'default'
                            }>
                              {taskAnalysis.complexity}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              ~{taskAnalysis.estimatedDuration} minutes
                            </span>
                          </div>
                        </div>
                        
                        <div>
                          <Label className="text-sm">Priority</Label>
                          <Badge variant={
                            taskAnalysis.priority === 'critical' ? 'destructive' :
                            taskAnalysis.priority === 'high' ? 'secondary' :
                            'outline'
                          } className="mt-1">
                            {taskAnalysis.priority}
                          </Badge>
                        </div>
                      </div>
                      
                      <div>
                        <Label className="text-sm">Required Capabilities</Label>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {taskAnalysis.requiredCapabilities.map(cap => {
                            const Icon = getCapabilityIcon(cap);
                            return (
                              <Badge key={cap} variant="outline" className="gap-1">
                                <Icon className="h-3 w-3" />
                                {cap}
                              </Badge>
                            );
                          })}
                        </div>
                      </div>
                      
                      {taskAnalysis.complexity === 'complex' && (
                        <Alert>
                          <Lightbulb className="h-4 w-4" />
                          <AlertTitle>Complex Task Detected</AlertTitle>
                          <AlertDescription>
                            This task might benefit from being broken down into smaller subtasks.
                            Check the Task Breakdown tab for suggestions.
                          </AlertDescription>
                        </Alert>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="recommendations" className="space-y-3">
                  {agentRecommendations.map((rec) => {
                    const agent = availableAgents.find(a => String(a.agent.id) === rec.agentId);
                    if (!agent) return null;
                    
                    const isSelected = selectedAgents.has(rec.agentId);
                    
                    return (
                      <Card
                        key={rec.agentId}
                        className={cn(
                          "cursor-pointer transition-all",
                          isSelected && "ring-2 ring-primary"
                        )}
                        onClick={() => {
                          const newSelected = new Set(selectedAgents);
                          if (isSelected) {
                            newSelected.delete(rec.agentId);
                          } else {
                            newSelected.add(rec.agentId);
                          }
                          setSelectedAgents(newSelected);
                        }}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <Checkbox checked={isSelected} />
                              <div>
                                <h4 className="font-medium">{agent.agent.name}</h4>
                                <div className="flex items-center gap-2 mt-1">
                                  <PremiumProgress value={rec.score * 100} className="h-2 w-24" />
                                  <span className="text-sm text-muted-foreground">
                                    {Math.round(rec.score * 100)}% match
                                  </span>
                                </div>
                              </div>
                            </div>
                            
                            <div className="text-right">
                              <Badge variant="outline" className="text-xs">
                                {rec.currentLoad}% load
                              </Badge>
                              <p className="text-xs text-muted-foreground mt-1">
                                ~{Math.round(rec.estimatedCompletionTime)}m to complete
                              </p>
                            </div>
                          </div>
                          
                          <div className="space-y-2">
                            {rec.reasons.map((reason, idx) => (
                              <div key={idx} className="flex items-center gap-2 text-sm">
                                <CheckCircle className="h-3 w-3 text-green-500" />
                                {reason}
                              </div>
                            ))}
                          </div>
                          
                          <div className="flex flex-wrap gap-1 mt-3">
                            {rec.capabilities.map(cap => (
                              <Badge key={cap} variant="secondary" className="text-xs">
                                {cap}
                              </Badge>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </TabsContent>

                <TabsContent value="breakdown" className="space-y-3">
                  {taskAnalysis.suggestedBreakdown?.map((subtask, index) => {
                    const Icon = getCapabilityIcon(subtask.requiredCapabilities[0] || 'general');
                    
                    return (
                      <Card key={subtask.id}>
                        <CardContent className="p-4">
                          <div className="flex items-start gap-3">
                            <div className={cn(
                              "p-2 rounded-lg text-white",
                              index === 0 ? "bg-blue-500" :
                              index === 1 ? "bg-purple-500" :
                              index === 2 ? "bg-green-500" :
                              "bg-orange-500"
                            )}>
                              <Icon className="h-4 w-4" />
                            </div>
                            <div className="flex-1">
                              <h4 className="font-medium">{subtask.title}</h4>
                              <p className="text-sm text-muted-foreground mt-1">
                                {subtask.description}
                              </p>
                              <div className="flex items-center gap-4 mt-2 text-xs">
                                <span className="text-muted-foreground">
                                  ~{subtask.estimatedDuration} minutes
                                </span>
                                <div className="flex gap-1">
                                  {subtask.requiredCapabilities.map(cap => (
                                    <Badge key={cap} variant="outline" className="text-xs">
                                      {cap}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                  
                  {taskAnalysis.suggestedBreakdown && (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={handleCreateSubtasks}
                    >
                      <GitBranch className="h-4 w-4 mr-2" />
                      Create All Subtasks
                    </Button>
                  )}
                </TabsContent>
              </ScrollArea>
            </Tabs>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateTask}
            disabled={!taskAnalysis || selectedAgents.size === 0}
          >
            <Target className="h-4 w-4 mr-2" />
            Create & Assign Task
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
