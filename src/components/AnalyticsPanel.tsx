/**
 * Analytics Panel Component
 * Provides comprehensive usage tracking, insights, and performance analytics
 */

import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>hart<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  TrendingDown,
  Activity,
  Clock,
  MessageSquare,
  Code,
  FileText,
  Users,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  Eye,
  EyeOff,
  Settings,
  Zap,
  Target,
  Award,
  AlertCircle,
  CheckCircle,
  ArrowUp,
  ArrowDown,
  Minus
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { PremiumProgress } from "@/components/ui/premium-progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { useToast } from "@/contexts/ToastContext";

interface AnalyticsData {
  sessions: SessionAnalytics;
  performance: PerformanceMetrics;
  usage: UsageStatistics;
  productivity: ProductivityMetrics;
  models: ModelUsage[];
  features: FeatureUsage[];
  timeRange: string;
}

interface SessionAnalytics {
  total: number;
  active: number;
  completed: number;
  averageDuration: number;
  totalTime: number;
  successRate: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
}

interface PerformanceMetrics {
  averageResponseTime: number;
  tokensProcessed: number;
  errorsCount: number;
  uptime: number;
  memoryUsage: number;
  cpuUsage: number;
}

interface UsageStatistics {
  promptsTotal: number;
  filesOpened: number;
  gitOperations: number;
  debugSessions: number;
  codeGenerated: number;
  dailyAverage: number;
}

interface ProductivityMetrics {
  codeQualityScore: number;
  bugFixRate: number;
  featureCompletionTime: number;
  documentationCoverage: number;
  testCoverage: number;
  efficiency: number;
}

interface ModelUsage {
  model: string;
  usage: number;
  cost: number;
  averageResponseTime: number;
  successRate: number;
}

interface FeatureUsage {
  feature: string;
  usage: number;
  lastUsed: Date;
  userSatisfaction: number;
}

interface AnalyticsPanelProps {
  className?: string;
  onExportData?: (data: AnalyticsData) => void;
  onPromptSend?: (prompt: string) => void;
}

export const AnalyticsPanel: React.FC<AnalyticsPanelProps> = ({
  className,
  onExportData,
  onPromptSend
}) => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [timeRange, setTimeRange] = useState<string>("7d");
  const [selectedTab, setSelectedTab] = useState<"overview" | "performance" | "usage" | "productivity">("overview");
  const [loading, setLoading] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const { addToast } = useToast();

  // Mock analytics data generation
  const generateMockData = useCallback((range: string): AnalyticsData => {
    const multiplier = range === "24h" ? 1 : range === "7d" ? 7 : range === "30d" ? 30 : 90;
    
    return {
      sessions: {
        total: Math.floor(Math.random() * 50 * multiplier),
        active: Math.floor(Math.random() * 10),
        completed: Math.floor(Math.random() * 45 * multiplier),
        averageDuration: Math.floor(Math.random() * 45) + 15, // 15-60 minutes
        totalTime: Math.floor(Math.random() * 1000 * multiplier), // minutes
        successRate: Math.floor(Math.random() * 20) + 80, // 80-100%
        trend: Math.random() > 0.5 ? 'up' : Math.random() > 0.5 ? 'down' : 'stable',
        trendPercentage: Math.floor(Math.random() * 30) + 5
      },
      performance: {
        averageResponseTime: Math.floor(Math.random() * 2000) + 500, // 500-2500ms
        tokensProcessed: Math.floor(Math.random() * 100000 * multiplier),
        errorsCount: Math.floor(Math.random() * 10 * multiplier),
        uptime: Math.floor(Math.random() * 5) + 95, // 95-100%
        memoryUsage: Math.floor(Math.random() * 30) + 40, // 40-70%
        cpuUsage: Math.floor(Math.random() * 40) + 20 // 20-60%
      },
      usage: {
        promptsTotal: Math.floor(Math.random() * 200 * multiplier),
        filesOpened: Math.floor(Math.random() * 150 * multiplier),
        gitOperations: Math.floor(Math.random() * 80 * multiplier),
        debugSessions: Math.floor(Math.random() * 30 * multiplier),
        codeGenerated: Math.floor(Math.random() * 500 * multiplier), // lines
        dailyAverage: Math.floor(Math.random() * 20) + 10
      },
      productivity: {
        codeQualityScore: Math.floor(Math.random() * 20) + 80, // 80-100
        bugFixRate: Math.floor(Math.random() * 30) + 70, // 70-100%
        featureCompletionTime: Math.floor(Math.random() * 120) + 60, // 60-180 minutes
        documentationCoverage: Math.floor(Math.random() * 40) + 60, // 60-100%
        testCoverage: Math.floor(Math.random() * 30) + 70, // 70-100%
        efficiency: Math.floor(Math.random() * 25) + 75 // 75-100%
      },
      models: [
        {
          model: "Claude 3.5 Sonnet",
          usage: Math.floor(Math.random() * 60) + 40, // 40-100%
          cost: Math.random() * 50 + 10,
          averageResponseTime: Math.random() * 1000 + 800,
          successRate: Math.random() * 5 + 95
        },
        {
          model: "Claude 3 Opus",
          usage: Math.floor(Math.random() * 30) + 10, // 10-40%
          cost: Math.random() * 80 + 20,
          averageResponseTime: Math.random() * 1500 + 1000,
          successRate: Math.random() * 3 + 97
        },
        {
          model: "Claude 3 Haiku",
          usage: Math.floor(Math.random() * 20) + 5, // 5-25%
          cost: Math.random() * 10 + 2,
          averageResponseTime: Math.random() * 500 + 300,
          successRate: Math.random() * 4 + 94
        }
      ],
      features: [
        { feature: "Code Generation", usage: Math.random() * 100, lastUsed: new Date(), userSatisfaction: Math.random() * 2 + 8 },
        { feature: "File Explorer", usage: Math.random() * 100, lastUsed: new Date(), userSatisfaction: Math.random() * 1.5 + 8.5 },
        { feature: "Git Integration", usage: Math.random() * 100, lastUsed: new Date(), userSatisfaction: Math.random() * 2 + 7.5 },
        { feature: "Debugging", usage: Math.random() * 100, lastUsed: new Date(), userSatisfaction: Math.random() * 2.5 + 7 },
        { feature: "Session Templates", usage: Math.random() * 100, lastUsed: new Date(), userSatisfaction: Math.random() * 2 + 8 },
        { feature: "AI Assistance", usage: Math.random() * 100, lastUsed: new Date(), userSatisfaction: Math.random() * 1 + 9 }
      ],
      timeRange: range
    };
  }, []);

  // Load analytics data
  const loadAnalytics = useCallback(async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      const data = generateMockData(timeRange);
      setAnalyticsData(data);
    } catch (error) {
      addToast({
        title: "Failed to load analytics",
        description: "Could not fetch analytics data",
        variant: "error"
      });
    } finally {
      setLoading(false);
    }
  }, [timeRange, generateMockData, addToast]);

  // Export data
  const handleExport = useCallback(() => {
    if (analyticsData) {
      onExportData?.(analyticsData);
      addToast({
        title: "Data exported",
        description: "Analytics data has been exported successfully",
        variant: "success"
      });
    }
  }, [analyticsData, onExportData, addToast]);

  // AI Insights
  const generateInsights = useCallback(() => {
    if (!analyticsData) return;
    
    const insights = [
      `Your productivity has ${analyticsData.productivity.efficiency > 80 ? 'increased' : 'room for improvement'} with ${analyticsData.productivity.efficiency}% efficiency`,
      `You've processed ${analyticsData.performance.tokensProcessed.toLocaleString()} tokens in the last ${timeRange}`,
      `Most used feature: ${analyticsData.features.sort((a, b) => b.usage - a.usage)[0].feature}`,
      `Average session duration: ${analyticsData.sessions.averageDuration} minutes`
    ].join('. ');

    const prompt = `Based on my coding analytics: ${insights}. Provide personalized recommendations to improve my development workflow and productivity.`;
    onPromptSend?.(prompt);
  }, [analyticsData, timeRange, onPromptSend]);

  useEffect(() => {
    loadAnalytics();
  }, [loadAnalytics]);

  const renderTrendIcon = (trend: 'up' | 'down' | 'stable', percentage: number) => {
    if (trend === 'up') return <ArrowUp className="h-3 w-3 text-green-500" />;
    if (trend === 'down') return <ArrowDown className="h-3 w-3 text-red-500" />;
    return <Minus className="h-3 w-3 text-muted-foreground" />;
  };

  const renderMetricCard = (title: string, value: string | number, subtitle?: string, trend?: { direction: 'up' | 'down' | 'stable', percentage: number }, icon?: React.ReactNode) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {subtitle && <p className="text-xs text-muted-foreground">{subtitle}</p>}
        {trend && (
          <div className="flex items-center gap-1 mt-1">
            {renderTrendIcon(trend.direction, trend.percentage)}
            <span className={cn("text-xs", 
              trend.direction === 'up' ? "text-green-500" : 
              trend.direction === 'down' ? "text-red-500" : 
              "text-muted-foreground"
            )}>
              {trend.percentage}% from last period
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className={cn("h-full flex items-center justify-center", className)}>
        <div className="flex items-center gap-2">
          <RefreshCw className="h-5 w-5 animate-spin" />
          <span>Loading analytics...</span>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className={cn("h-full flex items-center justify-center", className)}>
        <div className="text-center">
          <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
          <p className="text-muted-foreground">No analytics data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("h-full flex flex-col", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-primary" />
          <span className="font-medium">Analytics Dashboard</span>
          <Badge variant="outline" className="text-xs">
            {analyticsData.timeRange}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={generateInsights}
            title="Get AI insights"
          >
            <Eye className="h-3 w-3" />
          </Button>
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleExport}
            title="Export data"
          >
            <Download className="h-3 w-3" />
          </Button>
          
          <Button 
            variant="ghost" 
            size="sm"
            onClick={loadAnalytics}
            disabled={loading}
          >
            <RefreshCw className={cn("h-3 w-3", loading && "animate-spin")} />
          </Button>
        </div>
      </div>

      <Tabs value={selectedTab} onValueChange={(value: any) => setSelectedTab(value)} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-4 mx-4 mt-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="productivity">Productivity</TabsTrigger>
        </TabsList>

        <ScrollArea className="flex-1">
          {/* Overview Tab */}
          <TabsContent value="overview" className="p-4 space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {renderMetricCard(
                "Total Sessions",
                analyticsData.sessions.total,
                `${analyticsData.sessions.active} active`,
                { direction: analyticsData.sessions.trend, percentage: analyticsData.sessions.trendPercentage },
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              )}
              {renderMetricCard(
                "Success Rate",
                `${analyticsData.sessions.successRate}%`,
                `${analyticsData.sessions.completed} completed`,
                undefined,
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              )}
              {renderMetricCard(
                "Total Time",
                `${Math.round(analyticsData.sessions.totalTime / 60)}h`,
                `Avg: ${analyticsData.sessions.averageDuration}m per session`,
                undefined,
                <Clock className="h-4 w-4 text-muted-foreground" />
              )}
              {renderMetricCard(
                "Efficiency",
                `${analyticsData.productivity.efficiency}%`,
                "Overall productivity score",
                undefined,
                <Zap className="h-4 w-4 text-muted-foreground" />
              )}
            </div>

            {/* Model Usage */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  Model Usage Distribution
                </CardTitle>
                <CardDescription>Usage breakdown by AI model</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analyticsData.models.map((model) => (
                    <div key={model.model} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="font-medium">{model.model}</span>
                        <span className="text-muted-foreground">{model.usage}%</span>
                      </div>
                      <PremiumProgress value={model.usage} className="h-2" />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>${model.cost.toFixed(2)} cost</span>
                        <span>{model.averageResponseTime.toFixed(0)}ms avg</span>
                        <span>{model.successRate.toFixed(1)}% success</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Feature Usage */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Feature Usage & Satisfaction
                </CardTitle>
                <CardDescription>How often you use each feature and satisfaction ratings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analyticsData.features.map((feature) => (
                    <div key={feature.feature} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium text-sm">{feature.feature}</div>
                        <div className="text-xs text-muted-foreground">
                          Last used: {feature.lastUsed.toLocaleDateString()}
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="text-center">
                          <div className="text-sm font-medium">{feature.usage.toFixed(0)}%</div>
                          <div className="text-xs text-muted-foreground">Usage</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm font-medium flex items-center gap-1">
                            <Award className="h-3 w-3 text-yellow-500" />
                            {feature.userSatisfaction.toFixed(1)}
                          </div>
                          <div className="text-xs text-muted-foreground">Rating</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Performance Tab */}
          <TabsContent value="performance" className="p-4 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderMetricCard(
                "Response Time",
                `${analyticsData.performance.averageResponseTime}ms`,
                "Average response time",
                undefined,
                <Clock className="h-4 w-4 text-muted-foreground" />
              )}
              {renderMetricCard(
                "Uptime",
                `${analyticsData.performance.uptime}%`,
                "System availability",
                undefined,
                <Activity className="h-4 w-4 text-muted-foreground" />
              )}
              {renderMetricCard(
                "Errors",
                analyticsData.performance.errorsCount,
                `${timeRange} period`,
                undefined,
                <AlertCircle className="h-4 w-4 text-muted-foreground" />
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>System Resources</CardTitle>
                  <CardDescription>Memory and CPU usage</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Memory Usage</span>
                      <span>{analyticsData.performance.memoryUsage}%</span>
                    </div>
                    <PremiumProgress value={analyticsData.performance.memoryUsage} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>CPU Usage</span>
                      <span>{analyticsData.performance.cpuUsage}%</span>
                    </div>
                    <PremiumProgress value={analyticsData.performance.cpuUsage} className="h-2" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Processing Statistics</CardTitle>
                  <CardDescription>Token processing and throughput</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Tokens Processed</span>
                      <span className="font-medium">{analyticsData.performance.tokensProcessed.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Average Per Session</span>
                      <span className="font-medium">
                        {Math.round(analyticsData.performance.tokensProcessed / Math.max(analyticsData.sessions.total, 1)).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Processing Rate</span>
                      <span className="font-medium">
                        {Math.round(analyticsData.performance.tokensProcessed / analyticsData.performance.averageResponseTime * 1000)} tok/s
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Usage Tab */}
          <TabsContent value="usage" className="p-4 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderMetricCard(
                "Prompts Sent",
                analyticsData.usage.promptsTotal,
                `Avg: ${analyticsData.usage.dailyAverage}/day`,
                undefined,
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              )}
              {renderMetricCard(
                "Files Opened",
                analyticsData.usage.filesOpened,
                "Total file operations",
                undefined,
                <FileText className="h-4 w-4 text-muted-foreground" />
              )}
              {renderMetricCard(
                "Git Operations",
                analyticsData.usage.gitOperations,
                "Commits, branches, etc.",
                undefined,
                <Code className="h-4 w-4 text-muted-foreground" />
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {renderMetricCard(
                "Debug Sessions",
                analyticsData.usage.debugSessions,
                "Debugging attempts",
                undefined,
                <Activity className="h-4 w-4 text-muted-foreground" />
              )}
              {renderMetricCard(
                "Code Generated",
                `${analyticsData.usage.codeGenerated} lines`,
                "AI-generated code",
                undefined,
                <Code className="h-4 w-4 text-muted-foreground" />
              )}
            </div>
          </TabsContent>

          {/* Productivity Tab */}
          <TabsContent value="productivity" className="p-4 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {renderMetricCard(
                "Code Quality",
                `${analyticsData.productivity.codeQualityScore}/100`,
                "Quality assessment score",
                undefined,
                <Award className="h-4 w-4 text-muted-foreground" />
              )}
              {renderMetricCard(
                "Bug Fix Rate",
                `${analyticsData.productivity.bugFixRate}%`,
                "Successful bug resolutions",
                undefined,
                <Target className="h-4 w-4 text-muted-foreground" />
              )}
              {renderMetricCard(
                "Feature Time",
                `${analyticsData.productivity.featureCompletionTime}m`,
                "Avg. feature completion",
                undefined,
                <Clock className="h-4 w-4 text-muted-foreground" />
              )}
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Development Metrics</CardTitle>
                <CardDescription>Key productivity indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Documentation Coverage</span>
                    <span>{analyticsData.productivity.documentationCoverage}%</span>
                  </div>
                  <PremiumProgress value={analyticsData.productivity.documentationCoverage} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Test Coverage</span>
                    <span>{analyticsData.productivity.testCoverage}%</span>
                  </div>
                  <PremiumProgress value={analyticsData.productivity.testCoverage} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Overall Efficiency</span>
                    <span>{analyticsData.productivity.efficiency}%</span>
                  </div>
                  <PremiumProgress value={analyticsData.productivity.efficiency} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </ScrollArea>
      </Tabs>
    </div>
  );
};

export default AnalyticsPanel;