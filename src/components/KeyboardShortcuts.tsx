import React, { useState, useEffect, useCallback, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Keyboard,
  Search,
  Edit2,
  Save,
  RotateCcw,
  X,
  Check,
  AlertCircle,
  Copy,
  Download,
  Upload,
  Settings,
  Plus,
  Trash2,
  Command,
  Zap,
  Filter,
  Eye,
  EyeOff,
  Hash,
  BookOpen,
  Code,
  Terminal,
  FileText,
  Send,
  Archive,
  Brain,
  RefreshCw,
  Maximize2,
  HelpCircle,
  Info
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { useToast } from "@/contexts/ToastContext";

export interface KeyboardShortcut {
  id: string;
  name: string;
  description: string;
  category: string;
  keys: string[];
  action: string;
  enabled: boolean;
  customizable: boolean;
  context?: string;
  conflictsWith?: string[];
}

export interface ShortcutCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface KeyboardShortcutsProps {
  shortcuts: KeyboardShortcut[];
  onShortcutChange: (shortcuts: KeyboardShortcut[]) => void;
  onShortcutExecute?: (shortcutId: string) => void;
  className?: string;
}

const DEFAULT_CATEGORIES: ShortcutCategory[] = [
  { id: "general", name: "General", description: "Basic application shortcuts", icon: Zap },
  { id: "editor", name: "Editor", description: "Code editor shortcuts", icon: Code },
  { id: "files", name: "Files", description: "File management shortcuts", icon: FileText },
  { id: "ai", name: "AI", description: "AI and model shortcuts", icon: Brain },
  { id: "session", name: "Session", description: "Session management shortcuts", icon: Terminal },
  { id: "navigation", name: "Navigation", description: "UI navigation shortcuts", icon: Command },
  { id: "search", name: "Search", description: "Search and find shortcuts", icon: Search },
  { id: "custom", name: "Custom", description: "User-defined shortcuts", icon: Settings }
];

const DEFAULT_SHORTCUTS: KeyboardShortcut[] = [
  // General
  {
    id: "new-session",
    name: "New Session",
    description: "Create a new Claude session",
    category: "general",
    keys: ["ctrl", "n"],
    action: "newSession",
    enabled: true,
    customizable: true
  },
  {
    id: "open-settings",
    name: "Open Settings",
    description: "Open application settings",
    category: "general",
    keys: ["ctrl", ","],
    action: "openSettings",
    enabled: true,
    customizable: true
  },
  {
    id: "toggle-sidebar",
    name: "Toggle Sidebar",
    description: "Show or hide the sidebar",
    category: "navigation",
    keys: ["ctrl", "b"],
    action: "toggleSidebar",
    enabled: true,
    customizable: true
  },
  {
    id: "command-palette",
    name: "Command Palette",
    description: "Open the command palette",
    category: "general",
    keys: ["ctrl", "shift", "p"],
    action: "commandPalette",
    enabled: true,
    customizable: true
  },
  {
    id: "quick-search",
    name: "Quick Search",
    description: "Search across files and content",
    category: "search",
    keys: ["ctrl", "p"],
    action: "quickSearch",
    enabled: true,
    customizable: true
  },

  // Editor
  {
    id: "save-file",
    name: "Save File",
    description: "Save the current file",
    category: "editor",
    keys: ["ctrl", "s"],
    action: "saveFile",
    enabled: true,
    customizable: true,
    context: "editor"
  },
  {
    id: "format-document",
    name: "Format Document",
    description: "Format the current document",
    category: "editor",
    keys: ["shift", "alt", "f"],
    action: "formatDocument",
    enabled: true,
    customizable: true,
    context: "editor"
  },
  {
    id: "find-replace",
    name: "Find and Replace",
    description: "Open find and replace dialog",
    category: "search",
    keys: ["ctrl", "h"],
    action: "findReplace",
    enabled: true,
    customizable: true,
    context: "editor"
  },
  {
    id: "go-to-line",
    name: "Go to Line",
    description: "Jump to a specific line number",
    category: "navigation",
    keys: ["ctrl", "g"],
    action: "goToLine",
    enabled: true,
    customizable: true,
    context: "editor"
  },
  {
    id: "duplicate-line",
    name: "Duplicate Line",
    description: "Duplicate the current line",
    category: "editor",
    keys: ["shift", "alt", "down"],
    action: "duplicateLine",
    enabled: true,
    customizable: true,
    context: "editor"
  },
  {
    id: "comment-toggle",
    name: "Toggle Comment",
    description: "Comment or uncomment lines",
    category: "editor",
    keys: ["ctrl", "/"],
    action: "toggleComment",
    enabled: true,
    customizable: true,
    context: "editor"
  },

  // AI
  {
    id: "send-message",
    name: "Send Message",
    description: "Send message to AI",
    category: "ai",
    keys: ["enter"],
    action: "sendMessage",
    enabled: true,
    customizable: true,
    context: "chat"
  },
  {
    id: "new-line",
    name: "New Line",
    description: "Add new line in message",
    category: "ai",
    keys: ["shift", "enter"],
    action: "newLine",
    enabled: true,
    customizable: true,
    context: "chat"
  },
  {
    id: "ai-assist",
    name: "AI Assist",
    description: "Get AI assistance with selected code",
    category: "ai",
    keys: ["ctrl", "space"],
    action: "aiAssist",
    enabled: true,
    customizable: true,
    context: "editor"
  },
  {
    id: "switch-model",
    name: "Switch Model",
    description: "Switch between AI models",
    category: "ai",
    keys: ["ctrl", "m"],
    action: "switchModel",
    enabled: true,
    customizable: true
  },

  // Files
  {
    id: "open-file",
    name: "Open File",
    description: "Open a file from disk",
    category: "files",
    keys: ["ctrl", "o"],
    action: "openFile",
    enabled: true,
    customizable: true
  },
  {
    id: "close-tab",
    name: "Close Tab",
    description: "Close the current tab",
    category: "files",
    keys: ["ctrl", "w"],
    action: "closeTab",
    enabled: true,
    customizable: true
  },
  {
    id: "next-tab",
    name: "Next Tab",
    description: "Switch to next tab",
    category: "navigation",
    keys: ["ctrl", "tab"],
    action: "nextTab",
    enabled: true,
    customizable: true
  },
  {
    id: "prev-tab",
    name: "Previous Tab",
    description: "Switch to previous tab",
    category: "navigation",
    keys: ["ctrl", "shift", "tab"],
    action: "prevTab",
    enabled: true,
    customizable: true
  },

  // Session
  {
    id: "clear-chat",
    name: "Clear Chat",
    description: "Clear the current chat session",
    category: "session",
    keys: ["ctrl", "l"],
    action: "clearChat",
    enabled: true,
    customizable: true
  },
  {
    id: "export-session",
    name: "Export Session",
    description: "Export the current session",
    category: "session",
    keys: ["ctrl", "e"],
    action: "exportSession",
    enabled: true,
    customizable: true
  },
  {
    id: "backup-session",
    name: "Backup Session",
    description: "Create a backup of current session",
    category: "session",
    keys: ["ctrl", "shift", "s"],
    action: "backupSession",
    enabled: true,
    customizable: true
  },

  // Search
  {
    id: "find",
    name: "Find",
    description: "Find text in current document",
    category: "search",
    keys: ["ctrl", "f"],
    action: "find",
    enabled: true,
    customizable: true
  },
  {
    id: "find-next",
    name: "Find Next",
    description: "Find next occurrence",
    category: "search",
    keys: ["f3"],
    action: "findNext",
    enabled: true,
    customizable: true
  },
  {
    id: "find-prev",
    name: "Find Previous",
    description: "Find previous occurrence",
    category: "search",
    keys: ["shift", "f3"],
    action: "findPrev",
    enabled: true,
    customizable: true
  },

  // Navigation
  {
    id: "zoom-in",
    name: "Zoom In",
    description: "Increase font size",
    category: "navigation",
    keys: ["ctrl", "+"],
    action: "zoomIn",
    enabled: true,
    customizable: true
  },
  {
    id: "zoom-out",
    name: "Zoom Out",
    description: "Decrease font size",
    category: "navigation",
    keys: ["ctrl", "-"],
    action: "zoomOut",
    enabled: true,
    customizable: true
  },
  {
    id: "zoom-reset",
    name: "Reset Zoom",
    description: "Reset font size to default",
    category: "navigation",
    keys: ["ctrl", "0"],
    action: "zoomReset",
    enabled: true,
    customizable: true
  },
  {
    id: "fullscreen",
    name: "Toggle Fullscreen",
    description: "Enter or exit fullscreen mode",
    category: "navigation",
    keys: ["f11"],
    action: "toggleFullscreen",
    enabled: true,
    customizable: true
  }
];

export const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({
  shortcuts: initialShortcuts,
  onShortcutChange,
  onShortcutExecute,
  className
}) => {
  const [shortcuts, setShortcuts] = useState<KeyboardShortcut[]>(initialShortcuts || DEFAULT_SHORTCUTS);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [editingShortcut, setEditingShortcut] = useState<KeyboardShortcut | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordedKeys, setRecordedKeys] = useState<string[]>([]);
  const [showConflicts, setShowConflicts] = useState(false);
  const [showEnabledOnly, setShowEnabledOnly] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);

  const { addToast } = useToast();
  const recordingRef = useRef<HTMLDivElement>(null);

  // Filter shortcuts based on search and filters
  const filteredShortcuts = shortcuts.filter(shortcut => {
    if (selectedCategory !== "all" && shortcut.category !== selectedCategory) {
      return false;
    }
    
    if (showEnabledOnly && !shortcut.enabled) {
      return false;
    }

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        shortcut.name.toLowerCase().includes(query) ||
        shortcut.description.toLowerCase().includes(query) ||
        shortcut.keys.join(" ").toLowerCase().includes(query)
      );
    }

    return true;
  });

  // Group shortcuts by category
  const groupedShortcuts = DEFAULT_CATEGORIES.reduce((acc, category) => {
    acc[category.id] = filteredShortcuts.filter(s => s.category === category.id);
    return acc;
  }, {} as Record<string, KeyboardShortcut[]>);

  // Check for conflicts
  const getConflicts = (targetShortcut: KeyboardShortcut): string[] => {
    const targetKeys = targetShortcut.keys.join("+");
    return shortcuts
      .filter(s => 
        s.id !== targetShortcut.id && 
        s.enabled && 
        s.keys.join("+") === targetKeys &&
        (!s.context || !targetShortcut.context || s.context === targetShortcut.context)
      )
      .map(s => s.name);
  };

  // Handle shortcut recording
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isRecording) return;

    e.preventDefault();
    e.stopPropagation();

    const keys: string[] = [];
    
    if (e.ctrlKey || e.metaKey) keys.push("ctrl");
    if (e.altKey) keys.push("alt");
    if (e.shiftKey) keys.push("shift");
    
    // Handle special keys
    const specialKeys = {
      " ": "space",
      "Enter": "enter",
      "Escape": "escape",
      "Tab": "tab",
      "Backspace": "backspace",
      "Delete": "delete",
      "ArrowUp": "up",
      "ArrowDown": "down",
      "ArrowLeft": "left",
      "ArrowRight": "right",
      "Home": "home",
      "End": "end",
      "PageUp": "pageup",
      "PageDown": "pagedown",
      "F1": "f1", "F2": "f2", "F3": "f3", "F4": "f4",
      "F5": "f5", "F6": "f6", "F7": "f7", "F8": "f8",
      "F9": "f9", "F10": "f10", "F11": "f11", "F12": "f12"
    };

    if (specialKeys[e.key as keyof typeof specialKeys]) {
      keys.push(specialKeys[e.key as keyof typeof specialKeys]);
    } else if (e.key.length === 1) {
      keys.push(e.key.toLowerCase());
    }

    if (keys.length > 0) {
      setRecordedKeys(keys);
    }
  }, [isRecording]);

  // Set up keyboard event listeners
  useEffect(() => {
    if (isRecording) {
      window.addEventListener("keydown", handleKeyDown, true);
      return () => {
        window.removeEventListener("keydown", handleKeyDown, true);
      };
    }
  }, [isRecording, handleKeyDown]);

  // Start recording shortcut
  const startRecording = (shortcut: KeyboardShortcut) => {
    setEditingShortcut(shortcut);
    setRecordedKeys([]);
    setIsRecording(true);
    recordingRef.current?.focus();
  };

  // Stop recording and save
  const stopRecording = () => {
    if (editingShortcut && recordedKeys.length > 0) {
      const updated = shortcuts.map(s => 
        s.id === editingShortcut.id 
          ? { ...s, keys: recordedKeys }
          : s
      );
      setShortcuts(updated);
      onShortcutChange(updated);
      
      addToast({
        title: "Shortcut updated",
        description: `"${editingShortcut.name}" is now ${recordedKeys.join("+")}`,
        variant: "success"
      });
    }
    
    setIsRecording(false);
    setEditingShortcut(null);
    setRecordedKeys([]);
  };

  // Cancel recording
  const cancelRecording = () => {
    setIsRecording(false);
    setEditingShortcut(null);
    setRecordedKeys([]);
  };

  // Toggle shortcut enabled state
  const toggleShortcut = (shortcutId: string) => {
    const updated = shortcuts.map(s => 
      s.id === shortcutId ? { ...s, enabled: !s.enabled } : s
    );
    setShortcuts(updated);
    onShortcutChange(updated);
  };

  // Reset shortcuts to default
  const resetToDefaults = () => {
    setShortcuts(DEFAULT_SHORTCUTS);
    onShortcutChange(DEFAULT_SHORTCUTS);
    addToast({
      title: "Shortcuts reset",
      description: "All shortcuts have been reset to defaults",
      variant: "success"
    });
  };

  // Export shortcuts
  const exportShortcuts = async () => {
    try {
      setIsExporting(true);
      const customShortcuts = shortcuts.filter(s => !DEFAULT_SHORTCUTS.find(d => d.id === s.id));
      const exportData = {
        version: "1.0",
        exportDate: new Date().toISOString(),
        shortcuts: customShortcuts,
        modifiedDefaults: shortcuts.filter(s => {
          const defaultShortcut = DEFAULT_SHORTCUTS.find(d => d.id === s.id);
          return defaultShortcut && (
            JSON.stringify(s.keys) !== JSON.stringify(defaultShortcut.keys) ||
            s.enabled !== defaultShortcut.enabled
          );
        })
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const blob = new Blob([dataStr], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `keyboard-shortcuts-${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);

      addToast({
        title: "Shortcuts exported",
        description: "Keyboard shortcuts exported successfully",
        variant: "success"
      });
    } catch (error) {
      console.error("Failed to export shortcuts:", error);
      addToast({
        title: "Export failed",
        description: "Could not export shortcuts",
        variant: "error"
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Import shortcuts
  const importShortcuts = async () => {
    try {
      setIsImporting(true);
      const input = document.createElement("input");
      input.type = "file";
      input.accept = ".json";
      
      input.onchange = async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (!file) return;

        const content = await file.text();
        const importData = JSON.parse(content);
        
        if (importData.shortcuts) {
          const imported = [...shortcuts];
          
          // Add custom shortcuts
          importData.shortcuts.forEach((s: KeyboardShortcut) => {
            if (!imported.find(existing => existing.id === s.id)) {
              imported.push({ ...s, id: `custom-${Date.now()}-${Math.random()}` });
            }
          });
          
          // Apply modified defaults
          if (importData.modifiedDefaults) {
            importData.modifiedDefaults.forEach((s: KeyboardShortcut) => {
              const existingIndex = imported.findIndex(existing => existing.id === s.id);
              if (existingIndex !== -1) {
                imported[existingIndex] = { ...imported[existingIndex], ...s };
              }
            });
          }
          
          setShortcuts(imported);
          onShortcutChange(imported);
          
          addToast({
            title: "Shortcuts imported",
            description: "Keyboard shortcuts imported successfully",
            variant: "success"
          });
        }
      };
      
      input.click();
    } catch (error) {
      console.error("Failed to import shortcuts:", error);
      addToast({
        title: "Import failed",
        description: "Could not import shortcuts",
        variant: "error"
      });
    } finally {
      setIsImporting(false);
    }
  };

  // Format shortcut display
  const formatShortcut = (keys: string[]): string => {
    return keys
      .map(key => {
        const keyMap: Record<string, string> = {
          ctrl: "Ctrl",
          alt: "Alt",
          shift: "Shift",
          meta: "Cmd",
          space: "Space",
          enter: "Enter",
          escape: "Esc",
          tab: "Tab",
          backspace: "Backspace",
          delete: "Del",
          up: "↑",
          down: "↓",
          left: "←",
          right: "→",
        };
        return keyMap[key] || key.toUpperCase();
      })
      .join(" + ");
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="p-4 border-b space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold">Keyboard Shortcuts</h2>
            <p className="text-sm text-muted-foreground">
              Customize keyboard shortcuts for faster workflow
            </p>
          </div>
          <div className="flex items-center gap-2">
            <TooltipProvider delayDuration={0}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={importShortcuts}
                    disabled={isImporting}
                  >
                    <Upload className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Import shortcuts</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={exportShortcuts}
                    disabled={isExporting}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Export shortcuts</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetToDefaults}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Reset to defaults</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search shortcuts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>

          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <Separator />
              {DEFAULT_CATEGORIES.map(cat => (
                <SelectItem key={cat.id} value={cat.id}>
                  <div className="flex items-center gap-2">
                    <cat.icon className="h-4 w-4" />
                    {cat.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowEnabledOnly(!showEnabledOnly)}
            className={cn(
              "gap-2",
              showEnabledOnly && "bg-accent text-accent-foreground"
            )}
          >
            {showEnabledOnly ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
            Enabled Only
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowConflicts(!showConflicts)}
            className={cn(
              "gap-2",
              showConflicts && "bg-accent text-accent-foreground"
            )}
          >
            <AlertCircle className="h-4 w-4" />
            Conflicts
          </Button>
        </div>
      </div>

      {/* Shortcuts List */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-6">
          {DEFAULT_CATEGORIES.map(category => {
            const categoryShortcuts = groupedShortcuts[category.id];
            if (!categoryShortcuts || categoryShortcuts.length === 0) return null;

            return (
              <div key={category.id}>
                <div className="flex items-center gap-2 mb-3">
                  <category.icon className="h-5 w-5 text-muted-foreground" />
                  <h3 className="font-medium">{category.name}</h3>
                  <Badge variant="secondary" className="text-xs">
                    {categoryShortcuts.length}
                  </Badge>
                </div>

                <div className="grid gap-2">
                  {categoryShortcuts.map(shortcut => {
                    const conflicts = getConflicts(shortcut);
                    const hasConflicts = conflicts.length > 0;
                    const showConflictWarning = showConflicts && hasConflicts;

                    return (
                      <Card
                        key={shortcut.id}
                        className={cn(
                          "group hover:shadow-sm transition-shadow",
                          !shortcut.enabled && "opacity-60",
                          showConflictWarning && "border-destructive/50"
                        )}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium truncate">{shortcut.name}</h4>
                                {shortcut.context && (
                                  <Badge variant="outline" className="text-xs">
                                    {shortcut.context}
                                  </Badge>
                                )}
                                {!shortcut.customizable && (
                                  <Badge variant="secondary" className="text-xs">
                                    System
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground truncate">
                                {shortcut.description}
                              </p>
                              {showConflictWarning && (
                                <div className="flex items-center gap-1 mt-1 text-xs text-destructive">
                                  <AlertCircle className="h-3 w-3" />
                                  Conflicts with: {conflicts.join(", ")}
                                </div>
                              )}
                            </div>

                            <div className="flex items-center gap-2 ml-4">
                              {/* Shortcut Display */}
                              <div className="flex items-center gap-1 min-w-0">
                                {shortcut.keys.map((key, index) => (
                                  <React.Fragment key={index}>
                                    {index > 0 && (
                                      <span className="text-xs text-muted-foreground">+</span>
                                    )}
                                    <Badge variant="outline" className="text-xs font-mono">
                                      {key === "ctrl" ? "Ctrl" :
                                       key === "alt" ? "Alt" :
                                       key === "shift" ? "Shift" :
                                       key === "meta" ? "Cmd" :
                                       key.toUpperCase()}
                                    </Badge>
                                  </React.Fragment>
                                ))}
                              </div>

                              {/* Actions */}
                              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                {shortcut.customizable && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => startRecording(shortcut)}
                                    className="h-8 w-8 p-0"
                                  >
                                    <Edit2 className="h-3 w-3" />
                                  </Button>
                                )}
                                
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleShortcut(shortcut.id)}
                                  className="h-8 w-8 p-0"
                                >
                                  {shortcut.enabled ? (
                                    <Eye className="h-3 w-3" />
                                  ) : (
                                    <EyeOff className="h-3 w-3" />
                                  )}
                                </Button>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </ScrollArea>

      {/* Recording Dialog */}
      <Dialog open={isRecording} onOpenChange={() => {}}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Record Shortcut</DialogTitle>
            <DialogDescription>
              Press the key combination for "{editingShortcut?.name}"
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-6">
            <div
              ref={recordingRef}
              tabIndex={0}
              className="p-8 border-2 border-dashed border-muted-foreground rounded-lg text-center focus:outline-none focus:border-primary"
            >
              <Keyboard className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
              {recordedKeys.length > 0 ? (
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Recorded:</p>
                  <div className="flex items-center justify-center gap-1">
                    {recordedKeys.map((key, index) => (
                      <React.Fragment key={index}>
                        {index > 0 && <span className="text-muted-foreground">+</span>}
                        <Badge variant="secondary" className="font-mono">
                          {key.toUpperCase()}
                        </Badge>
                      </React.Fragment>
                    ))}
                  </div>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  Press keys to record shortcut...
                </p>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={cancelRecording}>
              Cancel
            </Button>
            <Button 
              onClick={stopRecording}
              disabled={recordedKeys.length === 0}
            >
              <Check className="h-4 w-4 mr-2" />
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default KeyboardShortcuts;