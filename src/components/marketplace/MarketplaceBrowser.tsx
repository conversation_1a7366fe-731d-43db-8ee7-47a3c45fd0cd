import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  Filter, 
  Grid, 
  List, 
  RefreshCw,
  ArrowLeft,
  Star,
  Download,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useMarketplaceStore, type SortBy, type FilterBy } from '@/stores/marketplaceStore';
import { AgentMarketplaceCard } from './AgentMarketplaceCard';
import { CategoryFilter } from './CategoryFilter';
import { MarketplaceStats } from './MarketplaceStats';
import { AgentDetailsModal } from './AgentDetailsModal';
import { AgentCollections } from './AgentCollections';
import { MarketplaceInitializer } from './MarketplaceInitializer';

import { AnimatedGridPattern } from '@/components/ui/animated-grid-pattern';
import { TextAnimate } from '@/components/ui/text-animate';
import { ShimmerButton } from '@/components/ui/shimmer-button';

interface MarketplaceBrowserProps {
  onBack: () => void;
  className?: string;
}

export const MarketplaceBrowser: React.FC<MarketplaceBrowserProps> = ({ 
  onBack, 
  className 
}) => {

  const {
    agents,
    categories,
    collections,
    isLoading,
    error,
    searchQuery,
    selectedCategory,
    selectedCollection,
    sortBy,
    filterBy,
    currentPage,
    itemsPerPage,
    showDetailsModal,
    selectedAgent,
    
    // Actions
    fetchAgents,
    fetchCategories,
    fetchCollections,
    setSearchQuery,
    setSelectedCategory,
    setSelectedCollection,
    setSortBy,
    setFilterBy,
    setCurrentPage,
    getFilteredAgents,
    clearFilters,
    clearError,
    refreshData,
    closeDetailsModal
  } = useMarketplaceStore();

  const [viewMode, setViewMode] = React.useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = React.useState(false);

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      await Promise.all([
        fetchAgents(),
        fetchCategories(),
        fetchCollections()
      ]);
    };
    loadData();
  }, [fetchAgents, fetchCategories, fetchCollections]);

  const filteredAgents = getFilteredAgents();
  const totalPages = Math.ceil(filteredAgents.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedAgents = filteredAgents.slice(startIndex, startIndex + itemsPerPage);

  const handleSearch = (value: string) => {
    setSearchQuery(value);
  };

  const handleSortChange = (value: SortBy) => {
    setSortBy(value);
  };

  const handleFilterChange = (value: FilterBy) => {
    setFilterBy(value);
  };

  const handleRefresh = async () => {
    clearError();
    await refreshData();
  };

  const handleClearFilters = () => {
    clearFilters();
    setShowFilters(false);
  };

  return (
    <div className={cn("flex flex-col h-full bg-background relative overflow-hidden", className)}>
      {/* Animated background */}
      <div className="absolute inset-0 opacity-30">
        <AnimatedGridPattern
          numSquares={30}
          maxOpacity={0.1}
          duration={3}
          repeatDelay={1}
          className="inset-x-0 inset-y-[-30%] h-[200%] skew-y-12"
        />
      </div>
      
      <div className="w-full max-w-7xl mx-auto flex flex-col h-full relative z-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, type: "spring", stiffness: 100 }}
          className="border-b bg-card/80 backdrop-blur-sm p-6 relative overflow-hidden"
        >
          {/* Header gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5" />
          
          <div className="flex items-center justify-between mb-6 relative z-10">
            <div className="flex items-center gap-3">
              <motion.div
                whileHover={{ scale: 1.1, rotate: -5 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onBack}
                  className="h-8 w-8 hover:bg-blue-500/10 transition-colors duration-300"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </motion.div>
              <div>
                <TextAnimate
                  animation="fadeIn"
                  className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent"
                >
                  Agent Marketplace
                </TextAnimate>
                <motion.p 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                  className="text-sm text-muted-foreground"
                >
                  Discover and install community agents
                </motion.p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ShimmerButton
                   onClick={handleRefresh}
                   disabled={isLoading}
                   className="h-9 px-4"
                   shimmerColor="#3b82f6"
                 >
                   <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
                   Refresh
                 </ShimmerButton>
               </motion.div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>
          </div>

          {/* Search and Controls */}
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search agents, categories, or authors..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex items-center gap-2">
              <Select value={sortBy} onValueChange={handleSortChange}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="popularity">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-3 w-3" />
                      Popularity
                    </div>
                  </SelectItem>
                  <SelectItem value="rating">
                    <div className="flex items-center gap-2">
                      <Star className="h-3 w-3" />
                      Rating
                    </div>
                  </SelectItem>
                  <SelectItem value="recent">Recent</SelectItem>
                  <SelectItem value="downloads">
                    <div className="flex items-center gap-2">
                      <Download className="h-3 w-3" />
                      Downloads
                    </div>
                  </SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterBy} onValueChange={handleFilterChange}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Filter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="featured">Featured</SelectItem>
                  <SelectItem value="free">Free</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex border rounded-md">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none"
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none border-l"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Active Filters */}
          {(selectedCategory || selectedCollection || searchQuery || filterBy !== 'all') && (
            <div className="flex flex-wrap items-center gap-2 mt-4">
              <span className="text-sm text-muted-foreground">Active filters:</span>
              {searchQuery && (
                <Badge variant="secondary" className="gap-1">
                  Search: {searchQuery}
                  <button
                    onClick={() => setSearchQuery('')}
                    className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {selectedCategory && (
                <Badge variant="secondary" className="gap-1">
                  Category: {categories.find(c => c.id === selectedCategory)?.name}
                  <button
                    onClick={() => setSelectedCategory(null)}
                    className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {selectedCollection && (
                <Badge variant="secondary" className="gap-1">
                  Collection: {collections.find(c => c.id === selectedCollection)?.name}
                  <button
                    onClick={() => setSelectedCollection(null)}
                    className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {filterBy !== 'all' && (
                <Badge variant="secondary" className="gap-1">
                  {filterBy.charAt(0).toUpperCase() + filterBy.slice(1)}
                  <button
                    onClick={() => setFilterBy('all')}
                    className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
                  >
                    ×
                  </button>
                </Badge>
              )}
              <Button variant="ghost" size="sm" onClick={handleClearFilters}>
                Clear all
              </Button>
            </div>
          )}
        </motion.div>

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mx-6 mt-4 rounded-lg border border-destructive/50 bg-destructive/10 p-3 text-sm text-destructive"
          >
            <div className="flex items-center justify-between">
              <span>{error}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearError}
                className="h-auto p-1 text-destructive hover:text-destructive"
              >
                ×
              </Button>
            </div>
          </motion.div>
        )}

        {/* Main Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="grid grid-cols-1 xl:grid-cols-4 gap-6 p-6">
            {/* Sidebar */}
            <div className={cn("xl:col-span-1", !showFilters && "hidden xl:block")}>
              <div className="space-y-6">
                <MarketplaceStats />
                <CategoryFilter />
                <AgentCollections />
              </div>
            </div>

            {/* Agents Grid/List */}
            <div className="xl:col-span-3">
              {isLoading && agents.length === 0 ? (
                <div className="flex items-center justify-center h-64">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : filteredAgents.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex flex-col items-center justify-center min-h-64 text-center"
                >
                  {agents.length === 0 ? (
                    <div className="w-full max-w-4xl">
                      <div className="text-6xl mb-4">🚀</div>
                      <h3 className="text-lg font-medium mb-2">Marketplace Not Initialized</h3>
                      <p className="text-sm text-muted-foreground mb-6">
                        The marketplace database needs to be initialized with sample agents.
                      </p>
                      <MarketplaceInitializer />
                    </div>
                  ) : (
                    <>
                      <div className="text-6xl mb-4">🔍</div>
                      <h3 className="text-lg font-medium mb-2">No agents found</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Try adjusting your search or filters
                      </p>
                      <Button onClick={handleClearFilters} variant="outline">
                        Clear Filters
                      </Button>
                    </>
                  )}
                </motion.div>
              ) : (
                <>
                  {/* Results Info */}
                  <div className="flex items-center justify-between mb-6">
                    <p className="text-sm text-muted-foreground">
                      Showing {startIndex + 1}-{Math.min(startIndex + itemsPerPage, filteredAgents.length)} of {filteredAgents.length} agents
                    </p>
                  </div>

                  {/* Agents Display */}
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={`${viewMode}-${currentPage}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.2 }}
                      className={cn(
                        viewMode === 'grid'
                          ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
                          : "space-y-4"
                      )}
                    >
                      {paginatedAgents.map((agent, index) => (
                        <AgentMarketplaceCard
                          key={agent.id}
                          agent={agent}
                          viewMode={viewMode}
                          index={index}
                        />
                      ))}
                    </motion.div>
                  </AnimatePresence>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="mt-8 flex justify-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                          let page;
                          if (totalPages <= 5) {
                            page = i + 1;
                          } else if (currentPage <= 3) {
                            page = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            page = totalPages - 4 + i;
                          } else {
                            page = currentPage - 2 + i;
                          }
                          
                          return (
                            <Button
                              key={page}
                              variant={page === currentPage ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setCurrentPage(page)}
                            >
                              {page}
                            </Button>
                          );
                        })}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Agent Details Modal */}
      <AgentDetailsModal
        isOpen={showDetailsModal}
        agent={selectedAgent}
        onClose={closeDetailsModal}
      />
    </div>
  );
};