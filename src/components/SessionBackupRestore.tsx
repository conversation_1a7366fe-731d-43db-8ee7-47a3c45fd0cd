import React, { useState } from "react";
import {
  Download,
  Upload,
  Save,
  FolderOpen,
  Cloud,
  HardDrive,
  Clock,
  FileJson,
  Archive,
  AlertCircle,
  Check,
  Loader2,
  Trash2,
  RotateCcw,
  <PERSON>tings,
  Shield
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { api, type Session } from "@/lib/api";
import { useToast } from "@/contexts/ToastContext";
import { save, open } from "@tauri-apps/plugin-dialog";
import { writeTextFile, readTextFile } from "@tauri-apps/plugin-fs";
import type { ClaudeStreamMessage } from "./AgentExecution";

interface SessionBackup {
  version: string;
  timestamp: string;
  session: Session;
  messages: ClaudeStreamMessage[];
  settings?: any;
  metadata: {
    totalMessages: number;
    totalTokens?: number;
    projectPath?: string;
    duration?: number;
  };
}

interface BackupHistory {
  id: string;
  name: string;
  timestamp: string;
  size: number;
  location: "local" | "cloud";
  autoBackup?: boolean;
}

interface SessionBackupRestoreProps {
  session: Session | null;
  messages: ClaudeStreamMessage[];
  settings?: any;
  onRestore?: (backup: SessionBackup) => void;
  className?: string;
}

export const SessionBackupRestore: React.FC<SessionBackupRestoreProps> = ({
  session,
  messages,
  settings,
  onRestore,
  className
}) => {
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [backupHistory, setBackupHistory] = useState<BackupHistory[]>([]);
  const [selectedBackup, setSelectedBackup] = useState<string | null>(null);
  const [autoBackupEnabled, setAutoBackupEnabled] = useState(false);
  const [autoBackupInterval, setAutoBackupInterval] = useState("30"); // minutes
  const [encryptBackups, setEncryptBackups] = useState(false);
  const [compressionEnabled, setCompressionEnabled] = useState(true);
  const [showRestoreDialog, setShowRestoreDialog] = useState(false);
  const { addToast } = useToast();

  // Create backup
  const createBackup = async (isAuto: boolean = false) => {
    if (!session) {
      addToast({
        title: "No session to backup",
        description: "Please start a session before creating a backup",
        variant: "error"
      });
      return;
    }

    try {
      setIsBackingUp(true);

      // Prepare backup data
      const backup: SessionBackup = {
        version: "1.0.0",
        timestamp: new Date().toISOString(),
        session,
        messages,
        settings,
        metadata: {
          totalMessages: messages.length,
          totalTokens: messages.reduce((sum, msg) => {
            const usage = (msg as any).usage;
            return sum + (usage ? usage.input_tokens + usage.output_tokens : 0);
          }, 0),
          projectPath: session.project_path,
          duration: Date.now() - new Date(session.created_at).getTime()
        }
      };

      // Compress if enabled
      let backupData = JSON.stringify(backup, null, 2);
      if (compressionEnabled) {
        // In a real implementation, you'd use a compression library
        // For now, we'll just remove whitespace
        backupData = JSON.stringify(backup);
      }

      // Encrypt if enabled
      if (encryptBackups) {
        // In a real implementation, you'd use encryption
        // For now, we'll just add a marker
        backupData = `ENCRYPTED:${backupData}`;
      }

      if (isAuto) {
        // Auto backup to local storage
        const autoBackupKey = `session-backup-auto-${session.id}`;
        localStorage.setItem(autoBackupKey, backupData);
        
        // Update history
        const historyItem: BackupHistory = {
          id: `auto-${Date.now()}`,
          name: `Auto Backup - ${new Date().toLocaleString()}`,
          timestamp: new Date().toISOString(),
          size: new Blob([backupData]).size,
          location: "local",
          autoBackup: true
        };
        
        setBackupHistory(prev => [historyItem, ...prev.slice(0, 9)]);
        
        if (!isAuto) {
          addToast({
            title: "Auto backup created",
            description: "Session automatically backed up",
            variant: "success"
          });
        }
      } else {
        // Manual backup - let user choose location
        const filePath = await save({
          defaultPath: `claude-session-${session.id}-${Date.now()}.json`,
          filters: [{
            name: 'JSON Files',
            extensions: ['json']
          }]
        });

        if (filePath) {
          await writeTextFile(filePath, backupData);
          
          // Update history
          const historyItem: BackupHistory = {
            id: `manual-${Date.now()}`,
            name: filePath.split('/').pop() || 'backup.json',
            timestamp: new Date().toISOString(),
            size: new Blob([backupData]).size,
            location: "local"
          };
          
          setBackupHistory(prev => [historyItem, ...prev]);
          
          addToast({
            title: "Backup created",
            description: `Session backed up to ${filePath}`,
            variant: "success"
          });
        }
      }
    } catch (error) {
      console.error("Failed to create backup:", error);
      addToast({
        title: "Backup failed",
        description: "Could not create session backup",
        variant: "error"
      });
    } finally {
      setIsBackingUp(false);
    }
  };

  // Restore from backup
  const restoreFromBackup = async () => {
    try {
      setIsRestoring(true);

      const filePath = await open({
        multiple: false,
        filters: [{
          name: 'JSON Files',
          extensions: ['json']
        }]
      });

      if (filePath && typeof filePath === 'string') {
        let backupData = await readTextFile(filePath);
        
        // Decrypt if needed
        if (backupData.startsWith('ENCRYPTED:')) {
          // In a real implementation, you'd decrypt here
          backupData = backupData.replace('ENCRYPTED:', '');
        }

        const backup: SessionBackup = JSON.parse(backupData);
        
        // Validate backup
        if (!backup.version || !backup.session || !backup.messages) {
          throw new Error("Invalid backup file format");
        }

        // Show confirmation dialog
        setSelectedBackup(filePath);
        setShowRestoreDialog(true);
      }
    } catch (error) {
      console.error("Failed to restore backup:", error);
      addToast({
        title: "Restore failed",
        description: "Could not restore session from backup",
        variant: "error"
      });
    } finally {
      setIsRestoring(false);
    }
  };

  // Confirm restore
  const confirmRestore = async () => {
    if (!selectedBackup) return;

    try {
      const backupData = await readTextFile(selectedBackup);
      let data = backupData;
      
      // Decrypt if needed
      if (data.startsWith('ENCRYPTED:')) {
        data = data.replace('ENCRYPTED:', '');
      }

      const backup: SessionBackup = JSON.parse(data);
      
      if (onRestore) {
        onRestore(backup);
        addToast({
          title: "Session restored",
          description: "Successfully restored session from backup",
          variant: "success"
        });
      }
      
      setShowRestoreDialog(false);
    } catch (error) {
      console.error("Failed to confirm restore:", error);
      addToast({
        title: "Restore failed",
        description: "Could not restore session",
        variant: "error"
      });
    }
  };

  // Setup auto backup
  React.useEffect(() => {
    if (!autoBackupEnabled || !session) return;

    const interval = parseInt(autoBackupInterval) * 60 * 1000; // Convert to milliseconds
    const timer = setInterval(() => {
      createBackup(true);
    }, interval);

    return () => clearInterval(timer);
  }, [autoBackupEnabled, autoBackupInterval, session]);

  // Load backup history from localStorage
  React.useEffect(() => {
    const savedHistory = localStorage.getItem('session-backup-history');
    if (savedHistory) {
      setBackupHistory(JSON.parse(savedHistory));
    }
  }, []);

  // Save backup history to localStorage
  React.useEffect(() => {
    localStorage.setItem('session-backup-history', JSON.stringify(backupHistory));
  }, [backupHistory]);

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Archive className="h-5 w-5" />
            Session Backup & Restore
          </CardTitle>
          <CardDescription>
            Save and restore your session data, messages, and settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="backup" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="backup">Backup</TabsTrigger>
              <TabsTrigger value="restore">Restore</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            
            <TabsContent value="backup" className="space-y-4">
              {/* Quick Actions */}
              <div className="grid gap-4 md:grid-cols-2">
                <Button
                  onClick={() => createBackup(false)}
                  disabled={!session || isBackingUp}
                  className="w-full"
                >
                  {isBackingUp ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Download className="h-4 w-4 mr-2" />
                  )}
                  Create Backup
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => setAutoBackupEnabled(!autoBackupEnabled)}
                  className="w-full"
                >
                  {autoBackupEnabled ? (
                    <Check className="h-4 w-4 mr-2" />
                  ) : (
                    <Clock className="h-4 w-4 mr-2" />
                  )}
                  {autoBackupEnabled ? "Auto Backup On" : "Enable Auto Backup"}
                </Button>
              </div>

              {/* Session Info */}
              {session && (
                <Card>
                  <CardContent className="pt-6">
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Session ID:</span>
                        <span className="font-mono">{session.id.slice(0, 8)}...</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Messages:</span>
                        <span>{messages.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Created:</span>
                        <span>{new Date(session.created_at).toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Size estimate:</span>
                        <span>
                          {(new Blob([JSON.stringify({ session, messages })]).size / 1024).toFixed(1)} KB
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Backup History */}
              <div>
                <h4 className="text-sm font-medium mb-2">Recent Backups</h4>
                <ScrollArea className="h-[200px] border rounded-md">
                  {backupHistory.length === 0 ? (
                    <div className="p-4 text-center text-sm text-muted-foreground">
                      No backups yet
                    </div>
                  ) : (
                    <div className="p-2 space-y-2">
                      {backupHistory.map((backup) => (
                        <div
                          key={backup.id}
                          className="flex items-center justify-between p-2 rounded-md hover:bg-accent"
                        >
                          <div className="flex items-center gap-2">
                            {backup.location === "cloud" ? (
                              <Cloud className="h-4 w-4" />
                            ) : (
                              <HardDrive className="h-4 w-4" />
                            )}
                            <div>
                              <p className="text-sm font-medium">{backup.name}</p>
                              <p className="text-xs text-muted-foreground">
                                {new Date(backup.timestamp).toLocaleString()}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {backup.autoBackup && (
                              <Badge variant="secondary" className="text-xs">
                                Auto
                              </Badge>
                            )}
                            <span className="text-xs text-muted-foreground">
                              {(backup.size / 1024).toFixed(1)} KB
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            </TabsContent>
            
            <TabsContent value="restore" className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Restoring a backup will replace your current session data. Make sure to backup current session first.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <Button
                  onClick={restoreFromBackup}
                  disabled={isRestoring}
                  className="w-full"
                >
                  {isRestoring ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Upload className="h-4 w-4 mr-2" />
                  )}
                  Choose Backup File
                </Button>

                <div className="text-sm text-muted-foreground">
                  <p>Supported formats:</p>
                  <ul className="list-disc list-inside mt-1">
                    <li>Claude session backup files (.json)</li>
                    <li>Encrypted backups (if encryption key is available)</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="settings" className="space-y-4">
              {/* Auto Backup Settings */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="auto-backup">Auto Backup</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically save session at regular intervals
                    </p>
                  </div>
                  <Switch
                    id="auto-backup"
                    checked={autoBackupEnabled}
                    onCheckedChange={setAutoBackupEnabled}
                  />
                </div>

                {autoBackupEnabled && (
                  <div>
                    <Label htmlFor="backup-interval">Backup Interval</Label>
                    <Select value={autoBackupInterval} onValueChange={setAutoBackupInterval}>
                      <SelectTrigger id="backup-interval">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">Every 5 minutes</SelectItem>
                        <SelectItem value="15">Every 15 minutes</SelectItem>
                        <SelectItem value="30">Every 30 minutes</SelectItem>
                        <SelectItem value="60">Every hour</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="encryption">Encrypt Backups</Label>
                    <p className="text-sm text-muted-foreground">
                      Protect backup files with encryption
                    </p>
                  </div>
                  <Switch
                    id="encryption"
                    checked={encryptBackups}
                    onCheckedChange={setEncryptBackups}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="compression">Compression</Label>
                    <p className="text-sm text-muted-foreground">
                      Reduce backup file size
                    </p>
                  </div>
                  <Switch
                    id="compression"
                    checked={compressionEnabled}
                    onCheckedChange={setCompressionEnabled}
                  />
                </div>

                <div className="pt-4 border-t">
                  <Button
                    variant="destructive"
                    onClick={() => {
                      setBackupHistory([]);
                      addToast({
                        title: "History cleared",
                        description: "Backup history has been cleared",
                        variant: "success"
                      });
                    }}
                    className="w-full"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Clear Backup History
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Restore Confirmation Dialog */}
      <Dialog open={showRestoreDialog} onOpenChange={setShowRestoreDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Restore</DialogTitle>
            <DialogDescription>
              Are you sure you want to restore this backup? This will replace your current session data.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Current session data will be lost unless you create a backup first.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRestoreDialog(false)}>
              Cancel
            </Button>
            <Button onClick={confirmRestore}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Restore Backup
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SessionBackupRestore;