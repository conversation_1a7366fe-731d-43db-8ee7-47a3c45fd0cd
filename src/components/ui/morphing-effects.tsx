import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useAnimation, useInView } from 'framer-motion';
import { cn } from '@/lib/utils';

// Shape morphing component
interface ShapeMorphProps {
  shapes: string[];
  duration?: number;
  autoPlay?: boolean;
  className?: string;
  color?: string;
  size?: number;
}

export const ShapeMorph: React.FC<ShapeMorphProps> = ({
  shapes,
  duration = 2,
  autoPlay = true,
  className,
  color = '#3b82f6',
  size = 100
}) => {
  const [currentShapeIndex, setCurrentShapeIndex] = useState(0);

  useEffect(() => {
    if (!autoPlay) return;

    const interval = setInterval(() => {
      setCurrentShapeIndex(prev => (prev + 1) % shapes.length);
    }, duration * 1000);

    return () => clearInterval(interval);
  }, [shapes.length, duration, autoPlay]);

  return (
    <div className={cn("flex items-center justify-center", className)}>
      <svg width={size} height={size} viewBox="0 0 100 100">
        <motion.path
          d={shapes[currentShapeIndex]}
          fill={color}
          animate={{ d: shapes[currentShapeIndex] }}
          transition={{
            duration,
            ease: "easeInOut"
          }}
        />
      </svg>
    </div>
  );
};

// Text morphing effect
interface TextMorphProps {
  texts: string[];
  duration?: number;
  className?: string;
  autoPlay?: boolean;
  morphSpeed?: number;
}

export const TextMorph: React.FC<TextMorphProps> = ({
  texts,
  duration = 3,
  className,
  autoPlay = true,
  morphSpeed = 0.05
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [displayText, setDisplayText] = useState(texts[0] || '');

  useEffect(() => {
    if (!autoPlay || texts.length < 2) return;

    const interval = setInterval(() => {
      const nextIndex = (currentIndex + 1) % texts.length;
      const currentText = texts[currentIndex];
      const nextText = texts[nextIndex];
      
      // Morphing animation
      let step = 0;
      const maxLength = Math.max(currentText.length, nextText.length);
      
      const morph = () => {
        step++;
        const progress = step / (1 / morphSpeed);
        
        if (progress >= 1) {
          setDisplayText(nextText);
          setCurrentIndex(nextIndex);
          return;
        }
        
        let morphedText = '';
        for (let i = 0; i < maxLength; i++) {
          const currentChar = currentText[i] || '';
          const nextChar = nextText[i] || '';
          
          if (Math.random() < progress) {
            morphedText += nextChar;
          } else {
            morphedText += currentChar;
          }
        }
        
        setDisplayText(morphedText);
        setTimeout(morph, 50);
      };
      
      setTimeout(morph, duration * 800); // Start morphing before interval ends
    }, duration * 1000);

    return () => clearInterval(interval);
  }, [texts, currentIndex, duration, autoPlay, morphSpeed]);

  return (
    <motion.div
      className={className}
      key={displayText}
      initial={{ filter: "blur(1px)" }}
      animate={{ filter: "blur(0px)" }}
      transition={{ duration: 0.3 }}
    >
      {displayText}
    </motion.div>
  );
};

// Liquid morphing transition
interface LiquidTransitionProps {
  isVisible: boolean;
  children: React.ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right';
  duration?: number;
  className?: string;
}

export const LiquidTransition: React.FC<LiquidTransitionProps> = ({
  isVisible,
  children,
  direction = 'up',
  duration = 0.8,
  className
}) => {
  const getClipPath = (progress: number) => {
    const curves = [];
    const steps = 5;
    
    for (let i = 0; i <= steps; i++) {
      const x = (i / steps) * 100;
      const noise = Math.sin((i / steps) * Math.PI * 4) * 10;
      const y = progress * 100 + noise;
      curves.push(`${x}% ${Math.max(0, Math.min(100, y))}%`);
    }
    
    switch (direction) {
      case 'down':
        return `polygon(0% 0%, 100% 0%, ${curves.reverse().join(', ')}, 0% ${progress * 100}%)`;
      case 'left':
        return `polygon(${progress * 100}% 0%, 100% 0%, 100% 100%, ${progress * 100}% 100%)`;
      case 'right':
        return `polygon(0% 0%, ${(1 - progress) * 100}% 0%, ${(1 - progress) * 100}% 100%, 0% 100%)`;
      default: // up
        return `polygon(0% 100%, 100% 100%, ${curves.join(', ')}, 0% ${(1 - progress) * 100}%)`;
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className={className}
          initial={{ clipPath: getClipPath(0) }}
          animate={{ clipPath: getClipPath(1) }}
          exit={{ clipPath: getClipPath(0) }}
          transition={{
            duration,
            ease: [0.4, 0, 0.2, 1]
          }}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Blob morphing background
interface BlobMorphProps {
  complexity?: number;
  size?: number;
  color?: string;
  speed?: number;
  className?: string;
}

export const BlobMorph: React.FC<BlobMorphProps> = ({
  complexity = 8,
  size = 200,
  color = 'rgba(59, 130, 246, 0.1)',
  speed = 1,
  className
}) => {
  const generateRandomPath = () => {
    const center = 50;
    const baseRadius = 30;
    let path = '';
    
    for (let i = 0; i < complexity; i++) {
      const angle = (i / complexity) * Math.PI * 2;
      const radiusVariation = (Math.random() - 0.5) * 20;
      const radius = baseRadius + radiusVariation;
      const x = center + Math.cos(angle) * radius;
      const y = center + Math.sin(angle) * radius;
      
      if (i === 0) {
        path += `M ${x} ${y}`;
      } else {
        const prevAngle = ((i - 1) / complexity) * Math.PI * 2;
        const prevRadiusVariation = (Math.random() - 0.5) * 20;
        const prevRadius = baseRadius + prevRadiusVariation;
        const prevX = center + Math.cos(prevAngle) * prevRadius;
        const prevY = center + Math.sin(prevAngle) * prevRadius;
        
        const cpX = (prevX + x) / 2 + (Math.random() - 0.5) * 10;
        const cpY = (prevY + y) / 2 + (Math.random() - 0.5) * 10;
        
        path += ` Q ${cpX} ${cpY} ${x} ${y}`;
      }
    }
    
    return path + ' Z';
  };

  const [paths, setPaths] = useState([generateRandomPath(), generateRandomPath(), generateRandomPath()]);

  useEffect(() => {
    const interval = setInterval(() => {
      setPaths([generateRandomPath(), generateRandomPath(), generateRandomPath()]);
    }, (3000 / speed));

    return () => clearInterval(interval);
  }, [speed]);

  return (
    <div className={cn("flex items-center justify-center", className)}>
      <svg width={size} height={size} viewBox="0 0 100 100">
        <motion.path
          d={paths[0]}
          fill={color}
          animate={{ d: paths }}
          transition={{
            duration: 3 / speed,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </svg>
    </div>
  );
};

// Particle burst effect
interface ParticleBurstProps {
  trigger: boolean;
  particleCount?: number;
  colors?: string[];
  size?: number;
  duration?: number;
  className?: string;
  onComplete?: () => void;
}

export const ParticleBurst: React.FC<ParticleBurstProps> = ({
  trigger,
  particleCount = 20,
  colors = ['#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b'],
  size = 4,
  duration = 1,
  className,
  onComplete
}) => {
  const [particles, setParticles] = useState<Array<{
    id: string;
    angle: number;
    distance: number;
    color: string;
    size: number;
  }>>([]);

  useEffect(() => {
    if (!trigger) return;

    const newParticles = Array.from({ length: particleCount }, (_, i) => ({
      id: `particle-${i}`,
      angle: (i / particleCount) * Math.PI * 2,
      distance: Math.random() * 100 + 50,
      color: colors[Math.floor(Math.random() * colors.length)],
      size: size * (0.5 + Math.random() * 0.5)
    }));

    setParticles(newParticles);

    const timer = setTimeout(() => {
      setParticles([]);
      onComplete?.();
    }, duration * 1000);

    return () => clearTimeout(timer);
  }, [trigger, particleCount, colors, size, duration, onComplete]);

  return (
    <div className={cn("absolute inset-0 pointer-events-none", className)}>
      <AnimatePresence>
        {particles.map(particle => (
          <motion.div
            key={particle.id}
            className="absolute rounded-full"
            style={{
              backgroundColor: particle.color,
              width: particle.size,
              height: particle.size,
              left: '50%',
              top: '50%'
            }}
            initial={{
              x: -particle.size / 2,
              y: -particle.size / 2,
              scale: 0,
              opacity: 1
            }}
            animate={{
              x: Math.cos(particle.angle) * particle.distance - particle.size / 2,
              y: Math.sin(particle.angle) * particle.distance - particle.size / 2,
              scale: [0, 1, 0],
              opacity: [1, 1, 0]
            }}
            transition={{
              duration,
              ease: "easeOut"
            }}
          />
        ))}
      </AnimatePresence>
    </div>
  );
};

// Morphing button with state transitions
interface MorphingButtonProps {
  states: Array<{
    text: string;
    icon?: React.ReactNode;
    color?: string;
    width?: number;
  }>;
  currentState: number;
  onClick?: () => void;
  className?: string;
}

export const MorphingButton: React.FC<MorphingButtonProps> = ({
  states,
  currentState,
  onClick,
  className
}) => {
  const state = states[currentState] || states[0];

  return (
    <motion.button
      onClick={onClick}
      className={cn(
        "relative overflow-hidden rounded-lg px-4 py-2 font-medium",
        "transition-all duration-300 ease-out",
        className
      )}
      animate={{
        backgroundColor: state.color || '#3b82f6',
        width: state.width || 'auto'
      }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      transition={{ type: "spring", stiffness: 400, damping: 30 }}
    >
      <motion.div
        className="flex items-center justify-center gap-2"
        layout
        transition={{ type: "spring", stiffness: 400, damping: 30 }}
      >
        <AnimatePresence mode="wait">
          {state.icon && (
            <motion.div
              key={`icon-${currentState}`}
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              exit={{ scale: 0, rotate: 180 }}
              transition={{ duration: 0.3 }}
            >
              {state.icon}
            </motion.div>
          )}
        </AnimatePresence>
        
        <AnimatePresence mode="wait">
          <motion.span
            key={`text-${currentState}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {state.text}
          </motion.span>
        </AnimatePresence>
      </motion.div>
    </motion.button>
  );
};

// Glitch effect
interface GlitchEffectProps {
  children: React.ReactNode;
  intensity?: number;
  speed?: number;
  className?: string;
  trigger?: boolean;
}

export const GlitchEffect: React.FC<GlitchEffectProps> = ({
  children,
  intensity = 5,
  speed = 0.1,
  className,
  trigger = false
}) => {
  const [glitchOffset, setGlitchOffset] = useState({ x: 0, y: 0 });

  useEffect(() => {
    if (!trigger) {
      setGlitchOffset({ x: 0, y: 0 });
      return;
    }

    const interval = setInterval(() => {
      setGlitchOffset({
        x: (Math.random() - 0.5) * intensity,
        y: (Math.random() - 0.5) * intensity
      });
    }, speed * 1000);

    return () => clearInterval(interval);
  }, [trigger, intensity, speed]);

  return (
    <motion.div
      className={cn("relative", className)}
      animate={{
        x: glitchOffset.x,
        y: glitchOffset.y
      }}
      transition={{ duration: speed }}
    >
      {children}
      {trigger && (
        <>
          <motion.div
            className="absolute inset-0 opacity-70"
            style={{
              color: '#ff0000',
              transform: `translate(${glitchOffset.x * 0.5}px, ${glitchOffset.y * 0.5}px)`,
              mixBlendMode: 'multiply'
            }}
          >
            {children}
          </motion.div>
          <motion.div
            className="absolute inset-0 opacity-70"
            style={{
              color: '#00ff00',
              transform: `translate(${-glitchOffset.x * 0.5}px, ${-glitchOffset.y * 0.5}px)`,
              mixBlendMode: 'multiply'
            }}
          >
            {children}
          </motion.div>
        </>
      )}
    </motion.div>
  );
};

// Preset morphing configurations
export const MorphingPresets = {
  geometricShapes: [
    "M 50 10 L 90 90 L 10 90 Z", // Triangle
    "M 20 20 L 80 20 L 80 80 L 20 80 Z", // Square  
    "M 50 50 m -40 0 a 40 40 0 1 0 80 0 a 40 40 0 1 0 -80 0", // Circle
    "M 50 10 L 70 30 L 90 50 L 70 70 L 50 90 L 30 70 L 10 50 L 30 30 Z" // Octagon
  ],

  loadingStates: [
    { text: "Loading...", color: "#6b7280" },
    { text: "Processing...", color: "#f59e0b" },
    { text: "Almost done...", color: "#3b82f6" },
    { text: "Complete!", color: "#10b981" }
  ],

  actionButtons: [
    { text: "Click me", color: "#3b82f6", width: 120 },
    { text: "Loading...", color: "#f59e0b", width: 140 },
    { text: "Success!", color: "#10b981", width: 130 }
  ]
};

export default {
  ShapeMorph,
  TextMorph,
  LiquidTransition,
  BlobMorph,
  ParticleBurst,
  MorphingButton,
  GlitchEffect,
  MorphingPresets
};