import React, { useEffect, useRef, useState, useCallback } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';
import { cn } from '@/lib/utils';

interface ParallaxLayer {
  id: string;
  speed: number;
  offset?: number;
  opacity?: number;
  scale?: number;
  blur?: number;
  children: React.ReactNode;
  className?: string;
}

interface ParallaxContainerProps {
  layers: ParallaxLayer[];
  height?: string | number;
  className?: string;
  direction?: 'vertical' | 'horizontal';
  springConfig?: {
    stiffness: number;
    damping: number;
    mass: number;
  };
  enableMouseParallax?: boolean;
  mouseStrength?: number;
}

export const ParallaxContainer: React.FC<ParallaxContainerProps> = ({
  layers,
  height = '100vh',
  className,
  direction = 'vertical',
  springConfig = { stiffness: 400, damping: 40, mass: 0.1 },
  enableMouseParallax = false,
  mouseStrength = 0.1
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  // Scroll-based parallax
  const { scrollYProgress, scrollXProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  // Spring animations for smoother parallax
  const smoothScrollY = useSpring(scrollYProgress, springConfig);
  const smoothScrollX = useSpring(scrollXProgress, springConfig);

  // Mouse-based parallax
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!enableMouseParallax || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    
    const x = (event.clientX - rect.left - centerX) / centerX;
    const y = (event.clientY - rect.top - centerY) / centerY;
    
    setMousePosition({ x, y });
  }, [enableMouseParallax]);

  // Update dimensions
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current;
        setDimensions({ width: offsetWidth, height: offsetHeight });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  return (
    <div
      ref={containerRef}
      className={cn("relative overflow-hidden", className)}
      style={{ height }}
      onMouseMove={handleMouseMove}
      onMouseLeave={() => setMousePosition({ x: 0, y: 0 })}
    >
      {layers.map((layer) => {
        // Calculate parallax transforms
        const scrollProgress = direction === 'vertical' ? smoothScrollY : smoothScrollX;
        
        const parallaxDistance = layer.speed * 100;
        const scrollTransform = useTransform(
          scrollProgress,
          [0, 1],
          direction === 'vertical' 
            ? [0, -parallaxDistance]
            : [-parallaxDistance, 0]
        );

        // Mouse parallax
        const mouseX = mousePosition.x * mouseStrength * layer.speed * 20;
        const mouseY = mousePosition.y * mouseStrength * layer.speed * 20;

        return (
          <motion.div
            key={layer.id}
            className={cn(
              "absolute inset-0 will-change-transform",
              layer.className
            )}
            style={{
              [direction === 'vertical' ? 'y' : 'x']: scrollTransform,
              x: enableMouseParallax ? mouseX : undefined,
              y: enableMouseParallax ? 
                (direction === 'vertical' ? 
                  useTransform(scrollProgress, [0, 1], [mouseY, mouseY - parallaxDistance]) :
                  mouseY
                ) : undefined,
              opacity: layer.opacity ?? 1,
              scale: layer.scale ?? 1,
              filter: layer.blur ? `blur(${layer.blur}px)` : undefined,
              zIndex: Math.round((1 - layer.speed) * 100) // Slower layers go behind
            }}
          >
            {layer.children}
          </motion.div>
        );
      })}
    </div>
  );
};

// Individual parallax element for more granular control
interface ParallaxElementProps {
  speed?: number;
  direction?: 'vertical' | 'horizontal' | 'both';
  offset?: number;
  children: React.ReactNode;
  className?: string;
  enableMouseParallax?: boolean;
  mouseStrength?: number;
}

export const ParallaxElement: React.FC<ParallaxElementProps> = ({
  speed = 0.5,
  direction = 'vertical',
  offset = 0,
  children,
  className,
  enableMouseParallax = false,
  mouseStrength = 0.1
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const { scrollYProgress, scrollXProgress } = useScroll({
    target: elementRef,
    offset: ["start end", "end start"]
  });

  // Transform based on direction
  const yTransform = useTransform(
    scrollYProgress,
    [0, 1],
    [offset, offset - (speed * 100)]
  );
  
  const xTransform = useTransform(
    scrollXProgress,
    [0, 1],
    [offset, offset - (speed * 100)]
  );

  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!enableMouseParallax || !elementRef.current) return;

    const rect = elementRef.current.getBoundingClientRect();
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    
    const x = (event.clientX - rect.left - centerX) / centerX;
    const y = (event.clientY - rect.top - centerY) / centerY;
    
    setMousePosition({ x, y });
  }, [enableMouseParallax]);

  return (
    <motion.div
      ref={elementRef}
      className={cn("will-change-transform", className)}
      style={{
        y: direction === 'vertical' || direction === 'both' ? yTransform : undefined,
        x: direction === 'horizontal' || direction === 'both' ? xTransform : undefined
      }}
      animate={enableMouseParallax ? {
        x: mousePosition.x * mouseStrength * 20,
        y: mousePosition.y * mouseStrength * 20
      } : undefined}
      transition={{ type: "spring", stiffness: 400, damping: 40 }}
      onMouseMove={handleMouseMove}
      onMouseLeave={() => setMousePosition({ x: 0, y: 0 })}
    >
      {children}
    </motion.div>
  );
};

// Preset parallax configurations
export const ParallaxPresets = {
  depth: (content: React.ReactNode[]): ParallaxLayer[] => [
    {
      id: 'background',
      speed: 0.1,
      opacity: 0.3,
      blur: 2,
      children: content[0] || <div className="bg-gradient-to-b from-blue-500/20 to-purple-500/20 w-full h-full" />
    },
    {
      id: 'midground',
      speed: 0.5,
      opacity: 0.7,
      children: content[1] || <div className="flex items-center justify-center h-full text-4xl font-bold opacity-20">Mid Layer</div>
    },
    {
      id: 'foreground',
      speed: 1,
      children: content[2] || <div className="flex items-center justify-center h-full text-6xl font-bold">Foreground</div>
    }
  ],

  floating: (elements: React.ReactNode[]): ParallaxLayer[] => 
    elements.map((element, index) => ({
      id: `floating-${index}`,
      speed: 0.2 + (index * 0.2),
      offset: index * 50,
      children: element
    })),

  hero: (backgroundImage: string, content: React.ReactNode): ParallaxLayer[] => [
    {
      id: 'hero-bg',
      speed: 0.3,
      children: (
        <div 
          className="w-full h-full bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${backgroundImage})` }}
        />
      )
    },
    {
      id: 'hero-overlay',
      speed: 0.6,
      children: (
        <div className="w-full h-full bg-gradient-to-b from-black/50 to-black/80" />
      )
    },
    {
      id: 'hero-content',
      speed: 1,
      children: (
        <div className="flex items-center justify-center h-full text-white z-10">
          {content}
        </div>
      )
    }
  ]
};

// Hook for creating dynamic parallax effects
export const useParallax = (speed: number = 0.5) => {
  const { scrollYProgress } = useScroll();
  
  const y = useTransform(
    scrollYProgress,
    [0, 1],
    [0, -speed * 100]
  );

  const opacity = useTransform(
    scrollYProgress,
    [0, 0.3, 0.7, 1],
    [0, 1, 1, 0]
  );

  const scale = useTransform(
    scrollYProgress,
    [0, 0.5, 1],
    [0.8, 1, 1.2]
  );

  return { y, opacity, scale, scrollYProgress };
};

export default ParallaxContainer;