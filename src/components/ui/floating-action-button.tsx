import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  MessageSquare, 
  Bot, 
  Zap, 
  Bookmark, 
  Share2, 
  Download,
  Edit3,
  Copy,
  Trash2,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface FABAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  disabled?: boolean;
}

interface FloatingActionButtonProps {
  actions?: FABAction[];
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  trigger?: 'click' | 'hover';
  mainIcon?: React.ComponentType<{ className?: string }>;
  mainLabel?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showLabels?: boolean;
  onMainClick?: () => void;
}

const defaultActions: FABAction[] = [
  {
    id: 'new-chat',
    label: 'New Chat',
    icon: MessageSquare,
    onClick: () => console.log('New chat'),
    color: 'primary'
  },
  {
    id: 'new-agent',
    label: 'Create Agent',
    icon: Bot,
    onClick: () => console.log('New agent'),
    color: 'secondary'
  },
  {
    id: 'quick-action',
    label: 'Quick Action',
    icon: Zap,
    onClick: () => console.log('Quick action'),
    color: 'warning'
  }
];

const positionClasses = {
  'bottom-right': 'bottom-6 right-6',
  'bottom-left': 'bottom-6 left-6', 
  'top-right': 'top-6 right-6',
  'top-left': 'top-6 left-6'
};

const sizeClasses = {
  sm: 'w-12 h-12',
  md: 'w-14 h-14',
  lg: 'w-16 h-16'
};

const iconSizeClasses = {
  sm: 'w-5 h-5',
  md: 'w-6 h-6', 
  lg: 'w-7 h-7'
};

const colorStyles = {
  primary: 'from-blue-500 to-blue-600 hover:from-blue-400 hover:to-blue-500',
  secondary: 'from-purple-500 to-purple-600 hover:from-purple-400 hover:to-purple-500',
  success: 'from-green-500 to-green-600 hover:from-green-400 hover:to-green-500',
  warning: 'from-yellow-500 to-yellow-600 hover:from-yellow-400 hover:to-yellow-500',
  danger: 'from-red-500 to-red-600 hover:from-red-400 hover:to-red-500'
};

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  actions = defaultActions,
  position = 'bottom-right',
  trigger = 'click',
  mainIcon: MainIcon = Plus,
  mainLabel = 'Actions',
  size = 'md',
  className,
  showLabels = true,
  onMainClick
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [hoveredAction, setHoveredAction] = useState<string | null>(null);

  useEffect(() => {
    if (trigger === 'hover') {
      return;
    }
    
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.fab-container')) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('click', handleClickOutside);
    }
    
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isOpen, trigger]);

  const handleMainButtonClick = () => {
    if (onMainClick) {
      onMainClick();
    } else {
      setIsOpen(!isOpen);
    }
  };

  const handleMouseEnter = () => {
    if (trigger === 'hover') {
      setIsOpen(true);
    }
  };

  const handleMouseLeave = () => {
    if (trigger === 'hover') {
      setIsOpen(false);
    }
  };

  const getActionPosition = (index: number, total: number) => {
    const spacing = size === 'sm' ? 60 : size === 'md' ? 70 : 80;
    const angle = (Math.PI / 4) * index / Math.max(total - 1, 1); // Spread over 45 degrees
    
    if (position.includes('bottom')) {
      return {
        bottom: spacing * (index + 1),
        [position.includes('right') ? 'right' : 'left']: Math.sin(angle) * 20
      };
    } else {
      return {
        top: spacing * (index + 1),
        [position.includes('right') ? 'right' : 'left']: Math.sin(angle) * 20
      };
    }
  };

  const actionVariants = {
    hidden: { 
      scale: 0, 
      opacity: 0,
      y: position.includes('bottom') ? 20 : -20
    },
    visible: (index: number) => ({
      scale: 1,
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 30,
        delay: index * 0.1
      }
    }),
    exit: {
      scale: 0,
      opacity: 0,
      y: position.includes('bottom') ? 20 : -20,
      transition: { duration: 0.2 }
    }
  };

  const labelVariants = {
    hidden: { opacity: 0, x: position.includes('right') ? 10 : -10 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { delay: 0.2 }
    },
    exit: { 
      opacity: 0, 
      x: position.includes('right') ? 10 : -10,
      transition: { duration: 0.1 }
    }
  };

  return (
    <div
      className={cn(
        "fab-container fixed z-50",
        positionClasses[position],
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Action Buttons */}
      <AnimatePresence>
        {isOpen && actions.map((action, index) => {
          const ActionIcon = action.icon;
          const actionStyle = getActionPosition(index, actions.length);
          
          return (
            <motion.div
              key={action.id}
              variants={actionVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              custom={index}
              style={actionStyle}
              className="absolute"
            >
              <div className="relative group">
                {/* Action Button */}
                <motion.button
                  whileHover={{ 
                    scale: 1.1, 
                    y: -2,
                    transition: { type: "spring", stiffness: 400, damping: 30 }
                  }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    action.onClick();
                    setIsOpen(false);
                  }}
                  onMouseEnter={() => setHoveredAction(action.id)}
                  onMouseLeave={() => setHoveredAction(null)}
                  disabled={action.disabled}
                  className={cn(
                    "relative overflow-hidden rounded-full shadow-glass-lg",
                    "glass-strong border border-white/20",
                    "flex items-center justify-center",
                    "transition-all duration-300",
                    sizeClasses[size === 'lg' ? 'md' : 'sm'],
                    action.disabled && "opacity-50 cursor-not-allowed"
                  )}
                >
                  {/* Gradient background */}
                  <div className={cn(
                    "absolute inset-0 bg-gradient-to-br opacity-80",
                    colorStyles[action.color || 'primary']
                  )} />
                  
                  {/* Icon */}
                  <ActionIcon className={cn(
                    "relative z-10 text-white",
                    iconSizeClasses[size === 'lg' ? 'md' : 'sm']
                  )} />
                </motion.button>

                {/* Label */}
                {showLabels && hoveredAction === action.id && (
                  <AnimatePresence>
                    <motion.div
                      variants={labelVariants}
                      initial="hidden"
                      animate="visible"
                      exit="exit"
                      className={cn(
                        "absolute top-1/2 -translate-y-1/2",
                        "px-3 py-1.5 rounded-lg glass-strong shadow-glass",
                        "text-sm font-medium text-white whitespace-nowrap",
                        "border border-white/20",
                        position.includes('right') 
                          ? "right-full mr-3" 
                          : "left-full ml-3"
                      )}
                    >
                      {action.label}
                      
                      {/* Arrow */}
                      <div className={cn(
                        "absolute top-1/2 -translate-y-1/2 w-2 h-2",
                        "bg-[var(--glass-bg-strong)] border-r border-b border-white/20",
                        "rotate-45",
                        position.includes('right') 
                          ? "right-[-4px]" 
                          : "left-[-4px]"
                      )} />
                    </motion.div>
                  </AnimatePresence>
                )}
              </div>
            </motion.div>
          );
        })}
      </AnimatePresence>

      {/* Main FAB */}
      <motion.button
        whileHover={{ 
          scale: 1.05,
          y: -2,
          transition: { type: "spring", stiffness: 400, damping: 30 }
        }}
        whileTap={{ scale: 0.95 }}
        onClick={handleMainButtonClick}
        className={cn(
          "relative overflow-hidden rounded-full shadow-glass-xl",
          "glass-strong border border-white/30",
          "flex items-center justify-center",
          "transition-all duration-300",
          "bg-gradient-to-br from-blue-500 to-purple-600",
          "hover:from-blue-400 hover:to-purple-500",
          sizeClasses[size]
        )}
        title={mainLabel}
      >
        {/* Rotating background gradient */}
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
          className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600 opacity-90"
        />
        
        {/* Icon */}
        <motion.div
          animate={{ rotate: isOpen ? 45 : 0 }}
          transition={{ duration: 0.3 }}
          className="relative z-10"
        >
          {isOpen ? (
            <X className={cn("text-white", iconSizeClasses[size])} />
          ) : (
            <MainIcon className={cn("text-white", iconSizeClasses[size])} />
          )}
        </motion.div>
        
        {/* Ripple effect */}
        <motion.div
          initial={{ scale: 0, opacity: 0.6 }}
          animate={{ 
            scale: isOpen ? 1.5 : 0, 
            opacity: isOpen ? 0 : 0.6 
          }}
          transition={{ duration: 0.6 }}
          className="absolute inset-0 bg-white rounded-full"
        />
      </motion.button>
    </div>
  );
};

// Preset FAB configurations
export const FABPresets = {
  chat: (onNewChat: () => void, onNewAgent: () => void): FABAction[] => [
    {
      id: 'new-chat',
      label: 'New Chat',
      icon: MessageSquare,
      onClick: onNewChat,
      color: 'primary'
    },
    {
      id: 'new-agent',
      label: 'Create Agent',
      icon: Bot,
      onClick: onNewAgent,
      color: 'secondary'
    }
  ],

  editor: (onSave: () => void, onCopy: () => void, onDelete: () => void): FABAction[] => [
    {
      id: 'save',
      label: 'Save',
      icon: Download,
      onClick: onSave,
      color: 'success'
    },
    {
      id: 'copy',
      label: 'Copy',
      icon: Copy,
      onClick: onCopy,
      color: 'secondary'
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: Trash2,
      onClick: onDelete,
      color: 'danger'
    }
  ],

  project: (onEdit: () => void, onShare: () => void, onBookmark: () => void): FABAction[] => [
    {
      id: 'edit',
      label: 'Edit',
      icon: Edit3,
      onClick: onEdit,
      color: 'primary'
    },
    {
      id: 'share',
      label: 'Share',
      icon: Share2,
      onClick: onShare,
      color: 'secondary'
    },
    {
      id: 'bookmark',
      label: 'Bookmark',
      icon: Bookmark,
      onClick: onBookmark,
      color: 'warning'
    }
  ]
};

export default FloatingActionButton;