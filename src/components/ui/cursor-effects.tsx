import React, { useEffect, useRef, useState, useCallback } from 'react';
import { motion, useSpring, useMotionValue } from 'framer-motion';
import { cn } from '@/lib/utils';

// Custom cursor component
interface CustomCursorProps {
  variant?: 'dot' | 'ring' | 'cross' | 'glow' | 'trail';
  size?: number;
  color?: string;
  mixBlendMode?: string;
  className?: string;
  hideNativeCursor?: boolean;
}

export const CustomCursor: React.FC<CustomCursorProps> = ({
  variant = 'dot',
  size = 20,
  color = '#3b82f6',
  mixBlendMode = 'difference',
  className,
  hideNativeCursor = true
}) => {
  const cursorRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isClicking, setIsClicking] = useState(false);
  
  const cursorX = useMotionValue(0);
  const cursorY = useMotionValue(0);
  
  const springConfig = { damping: 30, stiffness: 700, mass: 0.5 };
  const cursorXSpring = useSpring(cursorX, springConfig);
  const cursorYSpring = useSpring(cursorY, springConfig);

  useEffect(() => {
    if (hideNativeCursor) {
      document.body.style.cursor = 'none';
    }

    const updateCursor = (e: MouseEvent) => {
      cursorX.set(e.clientX);
      cursorY.set(e.clientY);
      setIsVisible(true);
    };

    const handleMouseDown = () => setIsClicking(true);
    const handleMouseUp = () => setIsClicking(false);
    const handleMouseLeave = () => setIsVisible(false);
    const handleMouseEnter = () => setIsVisible(true);

    document.addEventListener('mousemove', updateCursor);
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mouseleave', handleMouseLeave);
    document.addEventListener('mouseenter', handleMouseEnter);

    return () => {
      if (hideNativeCursor) {
        document.body.style.cursor = 'auto';
      }
      document.removeEventListener('mousemove', updateCursor);
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mouseleave', handleMouseLeave);
      document.removeEventListener('mouseenter', handleMouseEnter);
    };
  }, [cursorX, cursorY, hideNativeCursor]);

  const renderCursor = () => {
    switch (variant) {
      case 'ring':
        return (
          <div
            className="border-2 rounded-full"
            style={{
              width: size,
              height: size,
              borderColor: color,
              backgroundColor: isClicking ? color : 'transparent'
            }}
          />
        );
      case 'cross':
        return (
          <div className="relative" style={{ width: size, height: size }}>
            <div
              className="absolute top-1/2 left-0 transform -translate-y-1/2"
              style={{
                width: size,
                height: 2,
                backgroundColor: color
              }}
            />
            <div
              className="absolute left-1/2 top-0 transform -translate-x-1/2"
              style={{
                width: 2,
                height: size,
                backgroundColor: color
              }}
            />
          </div>
        );
      case 'glow':
        return (
          <div
            className="rounded-full"
            style={{
              width: size,
              height: size,
              backgroundColor: color,
              boxShadow: `0 0 ${size}px ${color}, 0 0 ${size * 2}px ${color}`,
              opacity: 0.8
            }}
          />
        );
      case 'trail':
        return (
          <div
            className="rounded-full"
            style={{
              width: size,
              height: size,
              background: `radial-gradient(circle, ${color} 0%, transparent 70%)`,
              opacity: 0.6
            }}
          />
        );
      default: // dot
        return (
          <div
            className="rounded-full"
            style={{
              width: size,
              height: size,
              backgroundColor: color
            }}
          />
        );
    }
  };

  if (!isVisible) return null;

  return (
    <motion.div
      ref={cursorRef}
      className={cn(
        "fixed top-0 left-0 pointer-events-none z-[9999] flex items-center justify-center",
        className
      )}
      style={{
        x: cursorXSpring,
        y: cursorYSpring,
        mixBlendMode: mixBlendMode as any,
        transform: 'translate(-50%, -50%)'
      }}
      animate={{
        scale: isClicking ? 0.8 : 1
      }}
      transition={{ type: "spring", stiffness: 600, damping: 30 }}
    >
      {renderCursor()}
    </motion.div>
  );
};

// Cursor trail effect
interface CursorTrailProps {
  trailLength?: number;
  size?: number;
  color?: string;
  decay?: number;
  className?: string;
}

export const CursorTrail: React.FC<CursorTrailProps> = ({
  trailLength = 20,
  size = 8,
  color = '#3b82f6',
  decay = 0.95,
  className
}) => {
  const [trail, setTrail] = useState<Array<{ x: number; y: number; opacity: number }>>([]);

  useEffect(() => {
    const updateTrail = (e: MouseEvent) => {
      setTrail(prevTrail => {
        const newTrail = [
          { x: e.clientX, y: e.clientY, opacity: 1 },
          ...prevTrail.map(point => ({ ...point, opacity: point.opacity * decay }))
        ].slice(0, trailLength);
        
        return newTrail;
      });
    };

    document.addEventListener('mousemove', updateTrail);
    return () => document.removeEventListener('mousemove', updateTrail);
  }, [trailLength, decay]);

  return (
    <div className={cn("fixed inset-0 pointer-events-none z-50", className)}>
      {trail.map((point, index) => (
        <motion.div
          key={index}
          className="absolute rounded-full"
          style={{
            left: point.x - size / 2,
            top: point.y - size / 2,
            width: size * (1 - index / trailLength),
            height: size * (1 - index / trailLength),
            backgroundColor: color,
            opacity: point.opacity
          }}
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.1 }}
        />
      ))}
    </div>
  );
};

// Magnetic cursor effect for elements
interface MagneticCursorProps {
  children: React.ReactNode;
  strength?: number;
  className?: string;
}

export const MagneticCursor: React.FC<MagneticCursorProps> = ({
  children,
  strength = 0.3,
  className
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!elementRef.current) return;

    const rect = elementRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const deltaX = (e.clientX - centerX) * strength;
    const deltaY = (e.clientY - centerY) * strength;
    
    setPosition({ x: deltaX, y: deltaY });
  }, [strength]);

  const handleMouseLeave = useCallback(() => {
    setPosition({ x: 0, y: 0 });
  }, []);

  return (
    <motion.div
      ref={elementRef}
      className={className}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      animate={{ x: position.x, y: position.y }}
      transition={{ type: "spring", stiffness: 400, damping: 30 }}
    >
      {children}
    </motion.div>
  );
};

// Ripple effect on click
interface RippleEffectProps {
  children: React.ReactNode;
  color?: string;
  duration?: number;
  className?: string;
}

export const RippleEffect: React.FC<RippleEffectProps> = ({
  children,
  color = 'rgba(59, 130, 246, 0.3)',
  duration = 0.6,
  className
}) => {
  const [ripples, setRipples] = useState<Array<{
    id: string;
    x: number;
    y: number;
    size: number;
  }>>([]);

  const handleClick = useCallback((e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const size = Math.max(rect.width, rect.height) * 2;

    const newRipple = {
      id: Date.now().toString(),
      x,
      y,
      size
    };

    setRipples(prev => [...prev, newRipple]);

    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
    }, duration * 1000);
  }, [duration]);

  return (
    <div
      className={cn("relative overflow-hidden", className)}
      onClick={handleClick}
    >
      {children}
      {ripples.map(ripple => (
        <motion.div
          key={ripple.id}
          className="absolute rounded-full pointer-events-none"
          style={{
            left: ripple.x - ripple.size / 2,
            top: ripple.y - ripple.size / 2,
            width: ripple.size,
            height: ripple.size,
            backgroundColor: color
          }}
          initial={{ scale: 0, opacity: 1 }}
          animate={{ scale: 1, opacity: 0 }}
          transition={{ duration, ease: "easeOut" }}
        />
      ))}
    </div>
  );
};

// Spotlight effect that follows cursor
interface SpotlightEffectProps {
  size?: number;
  intensity?: number;
  color?: string;
  className?: string;
  children: React.ReactNode;
}

export const SpotlightEffect: React.FC<SpotlightEffectProps> = ({
  size = 300,
  intensity = 0.8,
  color = '#ffffff',
  className,
  children
}) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    setMousePosition({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  }, []);

  return (
    <div
      ref={containerRef}
      className={cn("relative overflow-hidden", className)}
      onMouseMove={handleMouseMove}
    >
      <div className="relative z-10">
        {children}
      </div>
      <motion.div
        className="absolute inset-0 pointer-events-none"
        style={{
          background: `radial-gradient(${size}px circle at ${mousePosition.x}px ${mousePosition.y}px, 
            ${color}${Math.round(intensity * 255).toString(16).padStart(2, '0')} 0%, 
            transparent 50%)`
        }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.2 }}
      />
    </div>
  );
};

// Cursor glow effect
interface CursorGlowProps {
  size?: number;
  color?: string;
  intensity?: number;
  blur?: number;
  className?: string;
}

export const CursorGlow: React.FC<CursorGlowProps> = ({
  size = 100,
  color = '#3b82f6',
  intensity = 0.5,
  blur = 50,
  className
}) => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updatePosition = (e: MouseEvent) => {
      setPosition({ x: e.clientX, y: e.clientY });
      setIsVisible(true);
    };

    const handleMouseLeave = () => setIsVisible(false);
    const handleMouseEnter = () => setIsVisible(true);

    document.addEventListener('mousemove', updatePosition);
    document.addEventListener('mouseleave', handleMouseLeave);
    document.addEventListener('mouseenter', handleMouseEnter);

    return () => {
      document.removeEventListener('mousemove', updatePosition);
      document.removeEventListener('mouseleave', handleMouseLeave);
      document.removeEventListener('mouseenter', handleMouseEnter);
    };
  }, []);

  if (!isVisible) return null;

  return (
    <motion.div
      className={cn("fixed pointer-events-none z-40", className)}
      style={{
        left: position.x - size / 2,
        top: position.y - size / 2,
        width: size,
        height: size,
        background: `radial-gradient(circle, ${color} 0%, transparent 70%)`,
        filter: `blur(${blur}px)`,
        opacity: intensity
      }}
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ type: "spring", stiffness: 400, damping: 30 }}
    />
  );
};

// Preset cursor configurations
export const CursorPresets = {
  minimal: {
    cursor: <CustomCursor variant="dot" size={8} color="#000000" />,
    effects: []
  },

  neon: {
    cursor: <CustomCursor variant="glow" size={20} color="#00ffff" />,
    effects: [<CursorTrail key="trail" color="#00ffff" trailLength={15} />]
  },

  retro: {
    cursor: <CustomCursor variant="cross" size={16} color="#ff6b35" />,
    effects: [<CursorGlow key="glow" color="#ff6b35" intensity={0.3} />]
  },

  premium: {
    cursor: <CustomCursor variant="ring" size={24} color="#3b82f6" />,
    effects: [
      <CursorTrail key="trail" color="#3b82f6" trailLength={20} decay={0.9} />,
      <CursorGlow key="glow" color="#3b82f6" size={150} intensity={0.4} />
    ]
  }
};

export default {
  CustomCursor,
  CursorTrail,
  MagneticCursor,
  RippleEffect,
  SpotlightEffect,
  CursorGlow,
  CursorPresets
};