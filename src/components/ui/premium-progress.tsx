import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface PremiumProgressProps {
  value: number; // 0-100
  className?: string;
  variant?: "default" | "glass" | "neon" | "gradient" | "pulse";
  size?: "sm" | "default" | "lg";
  showPercentage?: boolean;
  animated?: boolean;
  color?: "blue" | "green" | "purple" | "orange" | "red";
}

const variants = {
  default: {
    track: "bg-muted",
    fill: "bg-primary"
  },
  glass: {
    track: "glass-subtle",
    fill: "glass-medium bg-gradient-to-r from-blue-500/30 to-purple-500/30"
  },
  neon: {
    track: "bg-slate-900 border border-blue-500/20",
    fill: "bg-gradient-to-r from-cyan-400 to-blue-500 shadow-[0_0_20px_rgba(59,130,246,0.5)]"
  },
  gradient: {
    track: "bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-800",
    fill: "bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"
  },
  pulse: {
    track: "bg-muted",
    fill: "bg-primary animate-pulse"
  }
};

const sizes = {
  sm: "h-1",
  default: "h-2",
  lg: "h-3"
};

const colors = {
  blue: "from-blue-400 to-blue-600",
  green: "from-green-400 to-green-600", 
  purple: "from-purple-400 to-purple-600",
  orange: "from-orange-400 to-orange-600",
  red: "from-red-400 to-red-600"
};

export const PremiumProgress: React.FC<PremiumProgressProps> = ({
  value,
  className,
  variant = "default",
  size = "default",
  showPercentage = false,
  animated = true,
  color = "blue"
}) => {
  const clampedValue = Math.min(Math.max(value, 0), 100);
  
  return (
    <div className={cn("w-full", className)}>
      {showPercentage && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-foreground">Progress</span>
          <motion.span 
            className="text-sm font-medium text-muted-foreground"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            key={clampedValue}
          >
            {Math.round(clampedValue)}%
          </motion.span>
        </div>
      )}
      
      <div
        className={cn(
          "relative w-full overflow-hidden rounded-full",
          variants[variant].track,
          sizes[size]
        )}
      >
        {/* Background shimmer effect for glass variant */}
        {variant === "glass" && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer-slide" />
        )}
        
        {/* Progress fill */}
        <motion.div
          className={cn(
            "relative h-full rounded-full overflow-hidden",
            variant === "gradient" || variant === "neon" 
              ? `bg-gradient-to-r ${colors[color]}`
              : variants[variant].fill,
            variant === "neon" && "animate-pulse"
          )}
          initial={{ width: 0 }}
          animate={{ width: `${clampedValue}%` }}
          transition={
            animated
              ? {
                  duration: 1,
                  ease: "easeOut",
                  type: "spring",
                  stiffness: 100,
                  damping: 20
                }
              : { duration: 0 }
          }
        >
          {/* Inner glow effect */}
          {variant === "neon" && (
            <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent animate-shimmer-slide" />
          )}
          
          {/* Moving highlight */}
          {variant === "glass" && clampedValue > 0 && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
              animate={{
                x: ["-100%", "100%"],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "linear",
              }}
            />
          )}
        </motion.div>
        
        {/* Completion celebration effect */}
        {clampedValue >= 100 && variant !== "default" && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-green-400/30 to-green-600/30 rounded-full"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ 
              opacity: [0, 1, 0], 
              scale: [0.8, 1.1, 1],
            }}
            transition={{ 
              duration: 0.8,
              ease: "easeOut"
            }}
          />
        )}
      </div>
    </div>
  );
};

// Circular progress variant
interface CircularProgressProps {
  value: number;
  size?: number;
  strokeWidth?: number;
  className?: string;
  showPercentage?: boolean;
  color?: string;
  variant?: "default" | "glass" | "neon";
}

export const CircularProgress: React.FC<CircularProgressProps> = ({
  value,
  size = 120,
  strokeWidth = 8,
  className,
  showPercentage = true,
  color = "#3b82f6",
  variant = "default"
}) => {
  const clampedValue = Math.min(Math.max(value, 0), 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const offset = circumference - (clampedValue / 100) * circumference;

  const getStrokeClasses = () => {
    switch (variant) {
      case "glass":
        return "stroke-blue-500/60 drop-shadow-[0_0_8px_rgba(59,130,246,0.3)]";
      case "neon":
        return "stroke-cyan-400 drop-shadow-[0_0_12px_rgba(34,211,238,0.6)]";
      default:
        return "stroke-primary";
    }
  };

  return (
    <div className={cn("relative inline-flex items-center justify-center", className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          className="text-muted opacity-20"
        />
        
        {/* Progress circle */}
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeLinecap="round"
          className={getStrokeClasses()}
          style={{
            strokeDasharray: circumference,
            strokeDashoffset: offset,
          }}
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset: offset }}
          transition={{
            duration: 1.5,
            ease: "easeOut",
            type: "spring",
            stiffness: 100,
            damping: 20
          }}
        />
        
        {/* Neon glow effect */}
        {variant === "neon" && (
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="rgba(34,211,238,0.3)"
            strokeWidth={strokeWidth + 4}
            fill="transparent"
            strokeLinecap="round"
            style={{
              strokeDasharray: circumference,
              strokeDashoffset: offset,
            }}
            animate={{ strokeDashoffset: offset }}
            transition={{
              duration: 1.5,
              ease: "easeOut"
            }}
          />
        )}
      </svg>
      
      {/* Percentage text */}
      {showPercentage && (
        <motion.div 
          className="absolute inset-0 flex items-center justify-center"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5, duration: 0.3 }}
        >
          <span className={cn(
            "text-2xl font-bold",
            variant === "neon" && "text-cyan-400 drop-shadow-[0_0_8px_rgba(34,211,238,0.6)]"
          )}>
            {Math.round(clampedValue)}%
          </span>
        </motion.div>
      )}
    </div>
  );
};