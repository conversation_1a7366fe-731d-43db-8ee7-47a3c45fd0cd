import React, { useRef, useState, useCallback } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface MagneticButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  strength?: number; // 0-1, how strong the magnetic effect is
  maxDistance?: number; // maximum distance for magnetic effect in pixels
  withRipple?: boolean;
  withGlow?: boolean;
  variant?: "default" | "glass" | "primary" | "floating";
  size?: "sm" | "default" | "lg";
}

export const MagneticButton = React.forwardRef<HTMLButtonElement, MagneticButtonProps>(
  ({ 
    children, 
    className, 
    strength = 0.3, 
    maxDistance = 50,
    withRipple = false,
    withGlow = false,
    variant = "default",
    size = "default",
    onMouseMove,
    onMouseEnter,
    onMouseLeave,
    ...props 
  }, ref) => {
    const buttonRef = useRef<HTMLButtonElement>(null);
    const [transform, setTransform] = useState({ x: 0, y: 0, scale: 1 });
    const [isHovered, setIsHovered] = useState(false);

    const handleMouseMove = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
      if (!buttonRef.current) return;

      const rect = buttonRef.current.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      const distance = Math.sqrt(
        Math.pow(e.clientX - centerX, 2) + Math.pow(e.clientY - centerY, 2)
      );

      if (distance < maxDistance) {
        const offsetX = (e.clientX - centerX) * strength;
        const offsetY = (e.clientY - centerY) * strength;
        const scale = 1 + (maxDistance - distance) / maxDistance * 0.1;

        setTransform({ x: offsetX, y: offsetY, scale });
      } else {
        setTransform({ x: 0, y: 0, scale: 1 });
      }

      onMouseMove?.(e);
    }, [strength, maxDistance, onMouseMove]);

    const handleMouseEnter = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
      setIsHovered(true);
      onMouseEnter?.(e);
    }, [onMouseEnter]);

    const handleMouseLeave = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
      setIsHovered(false);
      setTransform({ x: 0, y: 0, scale: 1 });
      onMouseLeave?.(e);
    }, [onMouseLeave]);

    const variants = {
      default: "bg-primary text-primary-foreground shadow-md hover:shadow-lg",
      glass: "glass-medium shadow-glass hover:shadow-glass-lg",
      primary: "bg-blue-500 text-white shadow-md hover:shadow-lg",
      floating: "glass-strong shadow-floating hover:shadow-ambient"
    };

    const sizes = {
      sm: "h-8 px-3 text-xs",
      default: "h-10 px-4 py-2 text-sm",
      lg: "h-12 px-6 text-base"
    };

    return (
      <motion.button
        ref={(node) => {
          buttonRef.current = node;
          if (typeof ref === 'function') {
            ref(node);
          } else if (ref) {
            ref.current = node;
          }
        }}
        className={cn(
          "inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 transform-gpu",
          "disabled:pointer-events-none disabled:opacity-50",
          variants[variant],
          sizes[size],
          withRipple && "ripple",
          withGlow && "glow-subtle",
          className
        )}
        onMouseMove={handleMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        animate={{
          x: transform.x,
          y: transform.y,
          scale: transform.scale,
        }}
        transition={{
          type: "spring",
          stiffness: 400,
          damping: 25,
          mass: 0.5
        }}
        whileHover={{ 
          y: -2,
          transition: { duration: 0.2 }
        }}
        whileTap={{ 
          scale: 0.95,
          transition: { duration: 0.1 }
        }}
        {...props}
      >
        {/* Ripple effect container */}
        {withRipple && (
          <span className="absolute inset-0 overflow-hidden rounded-lg">
            <motion.span
              className="absolute inset-0 bg-white/20 rounded-full scale-0"
              animate={isHovered ? { scale: 2, opacity: [0, 0.3, 0] } : {}}
              transition={{ duration: 0.6, ease: "easeOut" }}
            />
          </span>
        )}
        
        {/* Content */}
        <span className="relative z-10 flex items-center gap-2">
          {children}
        </span>

        {/* Ambient glow overlay */}
        {withGlow && isHovered && (
          <motion.div
            className="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-400/20 to-purple-400/20"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          />
        )}
      </motion.button>
    );
  }
);

MagneticButton.displayName = "MagneticButton";