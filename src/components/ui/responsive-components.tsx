import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

// Breakpoint definitions
export const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
} as const;

export type Breakpoint = keyof typeof breakpoints;

// Responsive utilities hook
export const useResponsive = () => {
  const [screenSize, setScreenSize] = useState<{
    width: number;
    height: number;
    breakpoint: Breakpoint;
  }>({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
    breakpoint: 'lg'
  });

  const getBreakpoint = useCallback((width: number): Breakpoint => {
    if (width >= breakpoints['2xl']) return '2xl';
    if (width >= breakpoints.xl) return 'xl';
    if (width >= breakpoints.lg) return 'lg';
    if (width >= breakpoints.md) return 'md';
    if (width >= breakpoints.sm) return 'sm';
    return 'xs';
  }, []);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      setScreenSize({
        width,
        height,
        breakpoint: getBreakpoint(width)
      });
    };

    handleResize(); // Set initial values
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [getBreakpoint]);

  const isMobile = screenSize.breakpoint === 'xs' || screenSize.breakpoint === 'sm';
  const isTablet = screenSize.breakpoint === 'md';
  const isDesktop = screenSize.breakpoint === 'lg' || screenSize.breakpoint === 'xl' || screenSize.breakpoint === '2xl';

  return {
    ...screenSize,
    isMobile,
    isTablet,
    isDesktop,
    isXs: screenSize.breakpoint === 'xs',
    isSm: screenSize.breakpoint === 'sm',
    isMd: screenSize.breakpoint === 'md',
    isLg: screenSize.breakpoint === 'lg',
    isXl: screenSize.breakpoint === 'xl',
    is2xl: screenSize.breakpoint === '2xl'
  };
};

// Responsive container component
interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: Breakpoint | 'none';
  padding?: {
    xs?: string;
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
    '2xl'?: string;
  };
}

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className,
  maxWidth = 'xl',
  padding = {
    xs: 'px-4',
    sm: 'px-6',
    md: 'px-8',
    lg: 'px-12',
    xl: 'px-16',
    '2xl': 'px-20'
  }
}) => {
  const { breakpoint } = useResponsive();
  
  const maxWidthClass = maxWidth === 'none' ? '' : `max-w-${maxWidth}`;
  const paddingClass = padding[breakpoint] || padding.md || 'px-4';

  return (
    <div className={cn('mx-auto w-full', maxWidthClass, paddingClass, className)}>
      {children}
    </div>
  );
};

// Responsive grid component
interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  gap?: string;
  className?: string;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 5,
    '2xl': 6
  },
  gap = 'gap-4',
  className
}) => {
  const { breakpoint } = useResponsive();
  
  const currentColumns = columns[breakpoint] || columns.md || 3;
  const gridClass = `grid-cols-${currentColumns}`;

  return (
    <div className={cn('grid', gridClass, gap, className)}>
      {children}
    </div>
  );
};

// Responsive text component
interface ResponsiveTextProps {
  children: React.ReactNode;
  size?: {
    xs?: string;
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
    '2xl'?: string;
  };
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
}

export const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  children,
  size = {
    xs: 'text-sm',
    sm: 'text-base',
    md: 'text-lg',
    lg: 'text-xl',
    xl: 'text-2xl',
    '2xl': 'text-3xl'
  },
  className,
  as: Component = 'div'
}) => {
  const { breakpoint } = useResponsive();
  
  const textSize = size[breakpoint] || size.md || 'text-base';

  return (
    <Component className={cn(textSize, className)}>
      {children}
    </Component>
  );
};

// Responsive sidebar component
interface ResponsiveSidebarProps {
  children: React.ReactNode;
  isOpen: boolean;
  onClose: () => void;
  className?: string;
  behavior?: 'overlay' | 'push' | 'auto';
}

export const ResponsiveSidebar: React.FC<ResponsiveSidebarProps> = ({
  children,
  isOpen,
  onClose,
  className,
  behavior = 'auto'
}) => {
  const { isMobile, isTablet } = useResponsive();
  
  const shouldOverlay = behavior === 'overlay' || (behavior === 'auto' && (isMobile || isTablet));
  
  if (shouldOverlay) {
    return (
      <>
        {/* Overlay */}
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={onClose}
          />
        )}
        
        {/* Sidebar */}
        <motion.div
          initial={{ x: '-100%' }}
          animate={{ x: isOpen ? 0 : '-100%' }}
          transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          className={cn(
            'fixed left-0 top-0 h-full bg-white shadow-lg z-50',
            'w-80 max-w-[90vw]',
            className
          )}
        >
          {children}
        </motion.div>
      </>
    );
  }

  // Push behavior for desktop
  return (
    <motion.div
      initial={false}
      animate={{ width: isOpen ? 320 : 0 }}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      className={cn(
        'h-full bg-white border-r border-gray-200 overflow-hidden',
        className
      )}
    >
      <div className="w-80">
        {children}
      </div>
    </motion.div>
  );
};

// Responsive modal component
interface ResponsiveModalProps {
  children: React.ReactNode;
  isOpen: boolean;
  onClose: () => void;
  className?: string;
  title?: string;
}

export const ResponsiveModal: React.FC<ResponsiveModalProps> = ({
  children,
  isOpen,
  onClose,
  className,
  title
}) => {
  const { isMobile } = useResponsive();

  if (!isOpen) return null;

  if (isMobile) {
    // Full screen modal on mobile
    return (
      <motion.div
        initial={{ y: '100%' }}
        animate={{ y: 0 }}
        exit={{ y: '100%' }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        className="fixed inset-0 bg-white z-50 flex flex-col"
      >
        {title && (
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold">{title}</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              ✕
            </button>
          </div>
        )}
        <div className={cn('flex-1 overflow-auto p-4', className)}>
          {children}
        </div>
      </motion.div>
    );
  }

  // Centered modal on desktop
  return (
    <>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={onClose}
      />
      
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        className={cn(
          'fixed left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2',
          'bg-white rounded-lg shadow-xl z-50',
          'w-full max-w-lg max-h-[90vh] overflow-auto',
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {title && (
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold">{title}</h2>
            <button
              onClick={onClose}
              className="p-1 hover:bg-gray-100 rounded-full"
            >
              ✕
            </button>
          </div>
        )}
        <div className="p-6">
          {children}
        </div>
      </motion.div>
    </>
  );
};

// Responsive navigation tabs
interface ResponsiveTabsProps {
  tabs: Array<{
    id: string;
    label: string;
    content: React.ReactNode;
  }>;
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

export const ResponsiveTabs: React.FC<ResponsiveTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className
}) => {
  const { isMobile } = useResponsive();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const activeTabData = tabs.find(tab => tab.id === activeTab);

  if (isMobile) {
    // Dropdown style on mobile
    return (
      <div className={cn('w-full', className)}>
        <div className="relative">
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="w-full flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg"
          >
            <span className="font-medium">
              {activeTabData?.label || 'Select Tab'}
            </span>
            <span className={cn(
              'transform transition-transform',
              isDropdownOpen ? 'rotate-180' : ''
            )}>
              ↓
            </span>
          </button>

          {isDropdownOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10"
            >
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => {
                    onTabChange(tab.id);
                    setIsDropdownOpen(false);
                  }}
                  className={cn(
                    'w-full text-left p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0',
                    tab.id === activeTab ? 'bg-blue-50 text-blue-600' : ''
                  )}
                >
                  {tab.label}
                </button>
              ))}
            </motion.div>
          )}
        </div>

        <div className="mt-4">
          {activeTabData?.content}
        </div>
      </div>
    );
  }

  // Horizontal tabs on desktop
  return (
    <div className={cn('w-full', className)}>
      <div className="flex border-b border-gray-200">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={cn(
              'px-6 py-3 text-sm font-medium border-b-2 transition-colors',
              tab.id === activeTab
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            )}
          >
            {tab.label}
          </button>
        ))}
      </div>

      <div className="mt-6">
        {activeTabData?.content}
      </div>
    </div>
  );
};

// Responsive image component
interface ResponsiveImageProps {
  src: string;
  alt: string;
  sizes?: {
    xs?: string;
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
    '2xl'?: string;
  };
  className?: string;
  aspectRatio?: 'square' | 'video' | 'wide' | 'auto';
}

export const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  src,
  alt,
  sizes = {
    xs: '100vw',
    sm: '100vw',
    md: '50vw',
    lg: '33vw',
    xl: '25vw',
    '2xl': '20vw'
  },
  className,
  aspectRatio = 'auto'
}) => {
  const { breakpoint } = useResponsive();
  
  const aspectRatioClass = {
    square: 'aspect-square',
    video: 'aspect-video',
    wide: 'aspect-[21/9]',
    auto: ''
  }[aspectRatio];

  const sizesString = Object.entries(sizes)
    .map(([bp, size]) => `(min-width: ${breakpoints[bp as Breakpoint]}px) ${size}`)
    .reverse()
    .join(', ');

  return (
    <div className={cn('relative overflow-hidden', aspectRatioClass, className)}>
      <img
        src={src}
        alt={alt}
        sizes={sizesString}
        className="w-full h-full object-cover"
        loading="lazy"
      />
    </div>
  );
};

export default {
  useResponsive,
  ResponsiveContainer,
  ResponsiveGrid,
  ResponsiveText,
  ResponsiveSidebar,
  ResponsiveModal,
  ResponsiveTabs,
  ResponsiveImage,
  breakpoints
};