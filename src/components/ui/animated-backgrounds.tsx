import React, { useEffect, useRef, useState } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { cn } from '@/lib/utils';
import { ParticleSystem } from './particle-system';

// Animated Grid Pattern Background
interface AnimatedGridProps {
  size?: number;
  strokeWidth?: number;
  className?: string;
  animate?: boolean;
  color?: string;
}

export const AnimatedGrid: React.FC<AnimatedGridProps> = ({
  size = 40,
  strokeWidth = 1,
  className,
  animate = true,
  color = 'rgba(255, 255, 255, 0.1)'
}) => {
  return (
    <div className={cn("absolute inset-0 overflow-hidden", className)}>
      <svg
        width="100%"
        height="100%"
        className="absolute inset-0"
      >
        <defs>
          <pattern
            id="animated-grid"
            width={size}
            height={size}
            patternUnits="userSpaceOnUse"
          >
            <motion.path
              d={`M ${size} 0 L 0 0 0 ${size}`}
              fill="none"
              stroke={color}
              strokeWidth={strokeWidth}
              initial={{ pathLength: 0, opacity: 0 }}
              animate={animate ? {
                pathLength: 1,
                opacity: [0, 1, 0.5, 1],
              } : { pathLength: 1, opacity: 1 }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "easeInOut"
              }}
            />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#animated-grid)" />
      </svg>
    </div>
  );
};

// Flowing Gradient Background
interface FlowingGradientProps {
  colors?: string[];
  speed?: number;
  className?: string;
  blur?: number;
}

export const FlowingGradient: React.FC<FlowingGradientProps> = ({
  colors = [
    '#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981'
  ],
  speed = 1,
  className,
  blur = 100
}) => {
  return (
    <div className={cn("absolute inset-0 overflow-hidden", className)}>
      <motion.div
        className="absolute inset-0 opacity-30"
        style={{
          background: `linear-gradient(45deg, ${colors.join(', ')})`,
          backgroundSize: '400% 400%',
          filter: `blur(${blur}px)`
        }}
        animate={{
          backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
        }}
        transition={{
          duration: 10 / speed,
          repeat: Infinity,
          ease: "linear"
        }}
      />
      <motion.div
        className="absolute inset-0 opacity-20"
        style={{
          background: `radial-gradient(circle, ${colors.reverse().join(', ')})`,
          backgroundSize: '300% 300%',
          filter: `blur(${blur * 0.5}px)`
        }}
        animate={{
          backgroundPosition: ['50% 0%', '50% 100%', '50% 0%']
        }}
        transition={{
          duration: 15 / speed,
          repeat: Infinity,
          ease: "linear"
        }}
      />
    </div>
  );
};

// Geometric Pattern Background
interface GeometricPatternProps {
  pattern?: 'hexagons' | 'triangles' | 'diamonds' | 'circles';
  size?: number;
  spacing?: number;
  color?: string;
  className?: string;
  animate?: boolean;
}

export const GeometricPattern: React.FC<GeometricPatternProps> = ({
  pattern = 'hexagons',
  size = 60,
  spacing = 10,
  color = 'rgba(255, 255, 255, 0.05)',
  className,
  animate = true
}) => {
  const getPath = () => {
    const s = size / 2;
    switch (pattern) {
      case 'hexagons':
        const h = s * Math.sqrt(3) / 2;
        return `M ${s} 0 L ${s * 1.5} ${h} L ${s} ${h * 2} L 0 ${h * 2} L ${-s * 0.5} ${h} L 0 0 Z`;
      case 'triangles':
        return `M ${s} 0 L ${s * 2} ${s * Math.sqrt(3)} L 0 ${s * Math.sqrt(3)} Z`;
      case 'diamonds':
        return `M ${s} 0 L ${s * 2} ${s} L ${s} ${s * 2} L 0 ${s} Z`;
      case 'circles':
        return `M 0 ${s} A ${s} ${s} 0 1 1 0 ${-s} A ${s} ${s} 0 1 1 0 ${s}`;
      default:
        return '';
    }
  };

  return (
    <div className={cn("absolute inset-0 overflow-hidden", className)}>
      <svg width="100%" height="100%" className="absolute inset-0">
        <defs>
          <pattern
            id={`geometric-${pattern}`}
            width={size + spacing}
            height={size + spacing}
            patternUnits="userSpaceOnUse"
          >
            <motion.path
              d={getPath()}
              fill="none"
              stroke={color}
              strokeWidth={1}
              transform={`translate(${spacing / 2}, ${spacing / 2})`}
              initial={{ scale: 0, opacity: 0 }}
              animate={animate ? {
                scale: [0, 1, 0.8, 1],
                opacity: [0, 1, 0.6, 1],
                rotate: [0, 360]
              } : { scale: 1, opacity: 1 }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill={`url(#geometric-${pattern})`} />
      </svg>
    </div>
  );
};

// Wave Pattern Background
interface WavePatternProps {
  amplitude?: number;
  frequency?: number;
  speed?: number;
  color?: string;
  layers?: number;
  className?: string;
}

export const WavePattern: React.FC<WavePatternProps> = ({
  amplitude = 50,
  frequency = 0.02,
  speed = 1,
  color = 'rgba(59, 130, 246, 0.1)',
  layers = 3,
  className
}) => {
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 });
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        setDimensions({
          width: containerRef.current.offsetWidth,
          height: containerRef.current.offsetHeight
        });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  const generateWavePath = (offset: number, layerIndex: number) => {
    const points = [];
    const stepSize = 10;
    const layerAmplitude = amplitude * (1 - layerIndex * 0.2);
    const yOffset = dimensions.height * 0.5 + (layerIndex * 30);

    for (let x = 0; x <= dimensions.width; x += stepSize) {
      const y = yOffset + Math.sin((x * frequency) + offset) * layerAmplitude;
      points.push(`${x},${y}`);
    }

    return `M ${points.join(' L ')} L ${dimensions.width},${dimensions.height} L 0,${dimensions.height} Z`;
  };

  return (
    <div ref={containerRef} className={cn("absolute inset-0 overflow-hidden", className)}>
      <svg width="100%" height="100%" className="absolute inset-0">
        {Array.from({ length: layers }, (_, i) => (
          <motion.path
            key={i}
            fill={color}
            opacity={0.3 - (i * 0.1)}
            animate={{
              d: [
                generateWavePath(0, i),
                generateWavePath(Math.PI * 2, i),
                generateWavePath(0, i)
              ]
            }}
            transition={{
              duration: 8 / speed,
              repeat: Infinity,
              ease: "linear",
              delay: i * 0.5
            }}
          />
        ))}
      </svg>
    </div>
  );
};

// Morphing Blob Background
interface MorphingBlobProps {
  color?: string;
  size?: number;
  complexity?: number;
  speed?: number;
  className?: string;
}

export const MorphingBlob: React.FC<MorphingBlobProps> = ({
  color = 'rgba(139, 92, 246, 0.1)',
  size = 300,
  complexity = 8,
  speed = 1,
  className
}) => {
  const generatePath = (radiusVariation: number[]) => {
    const center = size / 2;
    const baseRadius = size * 0.3;
    
    let path = '';
    for (let i = 0; i < complexity; i++) {
      const angle = (i / complexity) * Math.PI * 2;
      const radius = baseRadius + (radiusVariation[i] * baseRadius * 0.5);
      const x = center + Math.cos(angle) * radius;
      const y = center + Math.sin(angle) * radius;
      
      if (i === 0) {
        path += `M ${x} ${y}`;
      } else {
        // Create smooth curves using quadratic Bézier curves
        const prevAngle = ((i - 1) / complexity) * Math.PI * 2;
        const prevRadius = baseRadius + (radiusVariation[i - 1] * baseRadius * 0.5);
        const prevX = center + Math.cos(prevAngle) * prevRadius;
        const prevY = center + Math.sin(prevAngle) * prevRadius;
        
        const cpX = (prevX + x) / 2;
        const cpY = (prevY + y) / 2;
        path += ` Q ${cpX} ${cpY} ${x} ${y}`;
      }
    }
    return path + ' Z';
  };

  const variations = Array.from({ length: 3 }, () =>
    Array.from({ length: complexity }, () => Math.random() * 2 - 1)
  );

  return (
    <div className={cn("absolute inset-0 flex items-center justify-center", className)}>
      <svg width={size} height={size}>
        <motion.path
          fill={color}
          animate={{
            d: variations.map(variation => generatePath(variation))
          }}
          transition={{
            duration: 10 / speed,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </svg>
    </div>
  );
};

// Constellation Background
interface ConstellationProps {
  starCount?: number;
  connectionDistance?: number;
  starSize?: number;
  color?: string;
  animate?: boolean;
  className?: string;
}

export const Constellation: React.FC<ConstellationProps> = ({
  starCount = 50,
  connectionDistance = 100,
  starSize = 2,
  color = 'rgba(255, 255, 255, 0.6)',
  animate = true,
  className
}) => {
  const [stars, setStars] = useState<Array<{ x: number; y: number; opacity: number }>>([]);
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 });
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current;
        setDimensions({ width: offsetWidth, height: offsetHeight });
        
        // Generate random stars
        setStars(Array.from({ length: starCount }, () => ({
          x: Math.random() * offsetWidth,
          y: Math.random() * offsetHeight,
          opacity: Math.random() * 0.8 + 0.2
        })));
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, [starCount]);

  const getConnections = () => {
    const connections = [];
    for (let i = 0; i < stars.length; i++) {
      for (let j = i + 1; j < stars.length; j++) {
        const distance = Math.sqrt(
          Math.pow(stars[i].x - stars[j].x, 2) + 
          Math.pow(stars[i].y - stars[j].y, 2)
        );
        if (distance < connectionDistance) {
          connections.push({
            x1: stars[i].x,
            y1: stars[i].y,
            x2: stars[j].x,
            y2: stars[j].y,
            opacity: (1 - distance / connectionDistance) * 0.3
          });
        }
      }
    }
    return connections;
  };

  return (
    <div ref={containerRef} className={cn("absolute inset-0 overflow-hidden", className)}>
      <svg width="100%" height="100%" className="absolute inset-0">
        {/* Connection lines */}
        {getConnections().map((connection, index) => (
          <motion.line
            key={`connection-${index}`}
            x1={connection.x1}
            y1={connection.y1}
            x2={connection.x2}
            y2={connection.y2}
            stroke={color}
            strokeWidth={0.5}
            opacity={connection.opacity}
            initial={{ pathLength: 0 }}
            animate={animate ? { pathLength: [0, 1, 0] } : { pathLength: 1 }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: index * 0.1,
              ease: "easeInOut"
            }}
          />
        ))}
        
        {/* Stars */}
        {stars.map((star, index) => (
          <motion.circle
            key={`star-${index}`}
            cx={star.x}
            cy={star.y}
            r={starSize}
            fill={color}
            opacity={star.opacity}
            initial={{ scale: 0 }}
            animate={animate ? {
              scale: [0, 1, 0.5, 1],
              opacity: [star.opacity * 0.3, star.opacity, star.opacity * 0.6, star.opacity]
            } : { scale: 1 }}
            transition={{
              duration: 4,
              repeat: Infinity,
              delay: index * 0.05,
              ease: "easeInOut"
            }}
          />
        ))}
      </svg>
    </div>
  );
};

// Preset background configurations
export const BackgroundPresets = {
  cosmic: () => ({
    layers: [
      <Constellation key="constellation" starCount={80} animate={true} />,
      <FlowingGradient key="gradient" colors={['#1e1b4b', '#312e81', '#581c87', '#6b21a8']} speed={0.5} />,
      <ParticleSystem key="particles" particleCount={30} physics="orbital" />
    ]
  }),

  digital: () => ({
    layers: [
      <AnimatedGrid key="grid" size={60} animate={true} />,
      <GeometricPattern key="geometric" pattern="hexagons" animate={true} />,
      <FlowingGradient key="gradient" colors={['#0ea5e9', '#3b82f6', '#6366f1']} />
    ]
  }),

  organic: () => ({
    layers: [
      <MorphingBlob key="blob1" color="rgba(16, 185, 129, 0.1)" size={400} />,
      <MorphingBlob key="blob2" color="rgba(59, 130, 246, 0.08)" size={300} speed={1.5} />,
      <WavePattern key="waves" amplitude={30} layers={2} speed={0.8} />
    ]
  }),

  minimal: () => ({
    layers: [
      <AnimatedGrid key="grid" size={80} strokeWidth={0.5} animate={false} />,
      <FlowingGradient key="gradient" colors={['#f8fafc', '#e2e8f0']} speed={0.3} blur={200} />
    ]
  })
};

export default {
  AnimatedGrid,
  FlowingGradient,
  GeometricPattern,
  WavePattern,
  MorphingBlob,
  Constellation,
  BackgroundPresets
};