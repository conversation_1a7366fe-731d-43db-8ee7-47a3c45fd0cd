import React, { useEffect, useRef, useMemo, useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface Particle {
  id: string;
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  opacity: number;
  color: string;
  life: number;
  maxLife: number;
  type: 'dot' | 'spark' | 'glow' | 'bubble';
}

interface ParticleSystemProps {
  particleCount?: number;
  colors?: string[];
  speed?: number;
  size?: { min: number; max: number };
  opacity?: { min: number; max: number };
  life?: { min: number; max: number };
  spawn?: 'edges' | 'center' | 'mouse' | 'random';
  physics?: 'float' | 'gravity' | 'magnetic' | 'orbital';
  interactive?: boolean;
  className?: string;
  width?: number;
  height?: number;
  particleTypes?: Array<'dot' | 'spark' | 'glow' | 'bubble'>;
  connectionLines?: boolean;
  connectionDistance?: number;
}

const defaultColors = [
  'rgba(59, 130, 246, 0.6)', // blue
  'rgba(139, 92, 246, 0.6)', // purple  
  'rgba(16, 185, 129, 0.6)', // emerald
  'rgba(245, 158, 11, 0.6)', // amber
  'rgba(239, 68, 68, 0.6)',  // red
];

export const ParticleSystem: React.FC<ParticleSystemProps> = ({
  particleCount = 50,
  colors = defaultColors,
  speed = 1,
  size = { min: 2, max: 6 },
  opacity = { min: 0.1, max: 0.8 },
  life = { min: 3000, max: 8000 },
  spawn = 'random',
  physics = 'float',
  interactive = true,
  className,
  width: propWidth,
  height: propHeight,
  particleTypes = ['dot', 'glow'],
  connectionLines = false,
  connectionDistance = 100
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const particlesRef = useRef<Particle[]>([]);
  const mouseRef = useRef({ x: 0, y: 0 });
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 });

  // Get random value in range
  const random = (min: number, max: number) => Math.random() * (max - min) + min;

  // Create a new particle
  const createParticle = (x?: number, y?: number): Particle => {
    const particleType = particleTypes[Math.floor(Math.random() * particleTypes.length)];
    const particleLife = random(life.min, life.max);
    
    let startX, startY;
    
    switch (spawn) {
      case 'edges':
        const edge = Math.floor(Math.random() * 4);
        switch (edge) {
          case 0: startX = 0; startY = random(0, dimensions.height); break;
          case 1: startX = dimensions.width; startY = random(0, dimensions.height); break;
          case 2: startX = random(0, dimensions.width); startY = 0; break;
          case 3: startX = random(0, dimensions.width); startY = dimensions.height; break;
          default: startX = 0; startY = 0;
        }
        break;
      case 'center':
        startX = dimensions.width / 2 + random(-50, 50);
        startY = dimensions.height / 2 + random(-50, 50);
        break;
      case 'mouse':
        startX = mouseRef.current.x + random(-20, 20);
        startY = mouseRef.current.y + random(-20, 20);
        break;
      default:
        startX = random(0, dimensions.width);
        startY = random(0, dimensions.height);
    }

    return {
      id: Math.random().toString(36).substr(2, 9),
      x: x ?? startX,
      y: y ?? startY,
      vx: random(-speed, speed),
      vy: random(-speed, speed),
      size: random(size.min, size.max),
      opacity: random(opacity.min, opacity.max),
      color: colors[Math.floor(Math.random() * colors.length)],
      life: particleLife,
      maxLife: particleLife,
      type: particleType
    };
  };

  // Update particle physics
  const updateParticle = (particle: Particle, deltaTime: number): Particle => {
    let newParticle = { ...particle };
    
    // Apply physics
    switch (physics) {
      case 'gravity':
        newParticle.vy += 0.01 * speed;
        break;
      case 'magnetic':
        const mouseDistance = Math.sqrt(
          Math.pow(mouseRef.current.x - particle.x, 2) + 
          Math.pow(mouseRef.current.y - particle.y, 2)
        );
        if (mouseDistance < 150) {
          const force = (150 - mouseDistance) / 150 * 0.02;
          const angle = Math.atan2(
            mouseRef.current.y - particle.y,
            mouseRef.current.x - particle.x
          );
          newParticle.vx += Math.cos(angle) * force;
          newParticle.vy += Math.sin(angle) * force;
        }
        break;
      case 'orbital':
        const centerX = dimensions.width / 2;
        const centerY = dimensions.height / 2;
        const orbitDistance = Math.sqrt(
          Math.pow(particle.x - centerX, 2) + 
          Math.pow(particle.y - centerY, 2)
        );
        const orbitAngle = Math.atan2(particle.y - centerY, particle.x - centerX);
        newParticle.x = centerX + Math.cos(orbitAngle + 0.01) * orbitDistance;
        newParticle.y = centerY + Math.sin(orbitAngle + 0.01) * orbitDistance;
        break;
      default: // float
        // Add some floating motion
        newParticle.vx += random(-0.01, 0.01);
        newParticle.vy += random(-0.01, 0.01);
    }

    // Apply velocity
    if (physics !== 'orbital') {
      newParticle.x += newParticle.vx;
      newParticle.y += newParticle.vy;
    }

    // Boundary collision
    if (newParticle.x < 0 || newParticle.x > dimensions.width) {
      newParticle.vx *= -0.8;
      newParticle.x = Math.max(0, Math.min(dimensions.width, newParticle.x));
    }
    if (newParticle.y < 0 || newParticle.y > dimensions.height) {
      newParticle.vy *= -0.8;
      newParticle.y = Math.max(0, Math.min(dimensions.height, newParticle.y));
    }

    // Update life
    newParticle.life -= deltaTime;
    newParticle.opacity = (newParticle.life / newParticle.maxLife) * random(opacity.min, opacity.max);

    return newParticle;
  };

  // Render particle
  const renderParticle = (ctx: CanvasRenderingContext2D, particle: Particle) => {
    ctx.save();
    ctx.globalAlpha = particle.opacity;
    
    switch (particle.type) {
      case 'dot':
        ctx.fillStyle = particle.color;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
        break;
        
      case 'spark':
        ctx.strokeStyle = particle.color;
        ctx.lineWidth = particle.size / 2;
        ctx.beginPath();
        ctx.moveTo(particle.x - particle.size, particle.y);
        ctx.lineTo(particle.x + particle.size, particle.y);
        ctx.moveTo(particle.x, particle.y - particle.size);
        ctx.lineTo(particle.x, particle.y + particle.size);
        ctx.stroke();
        break;
        
      case 'glow':
        const gradient = ctx.createRadialGradient(
          particle.x, particle.y, 0,
          particle.x, particle.y, particle.size * 3
        );
        gradient.addColorStop(0, particle.color);
        gradient.addColorStop(1, 'transparent');
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size * 3, 0, Math.PI * 2);
        ctx.fill();
        break;
        
      case 'bubble':
        ctx.strokeStyle = particle.color;
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.stroke();
        
        // Inner glow
        const bubbleGradient = ctx.createRadialGradient(
          particle.x, particle.y, 0,
          particle.x, particle.y, particle.size
        );
        bubbleGradient.addColorStop(0, 'transparent');
        bubbleGradient.addColorStop(0.8, particle.color.replace('0.6', '0.1'));
        bubbleGradient.addColorStop(1, particle.color.replace('0.6', '0.3'));
        ctx.fillStyle = bubbleGradient;
        ctx.fill();
        break;
    }
    
    ctx.restore();
  };

  // Render connection lines
  const renderConnections = (ctx: CanvasRenderingContext2D, particles: Particle[]) => {
    ctx.save();
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;
    
    for (let i = 0; i < particles.length; i++) {
      for (let j = i + 1; j < particles.length; j++) {
        const distance = Math.sqrt(
          Math.pow(particles[i].x - particles[j].x, 2) + 
          Math.pow(particles[i].y - particles[j].y, 2)
        );
        
        if (distance < connectionDistance) {
          const alpha = (1 - distance / connectionDistance) * 0.3;
          ctx.globalAlpha = alpha;
          ctx.beginPath();
          ctx.moveTo(particles[i].x, particles[i].y);
          ctx.lineTo(particles[j].x, particles[j].y);
          ctx.stroke();
        }
      }
    }
    
    ctx.restore();
  };

  // Animation loop
  const animate = (currentTime: number) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, dimensions.width, dimensions.height);

    // Update particles
    particlesRef.current = particlesRef.current
      .map(particle => updateParticle(particle, 16)) // ~60fps
      .filter(particle => particle.life > 0);

    // Add new particles if needed
    while (particlesRef.current.length < particleCount) {
      particlesRef.current.push(createParticle());
    }

    // Render connection lines first (behind particles)
    if (connectionLines) {
      renderConnections(ctx, particlesRef.current);
    }

    // Render particles
    particlesRef.current.forEach(particle => {
      renderParticle(ctx, particle);
    });

    animationRef.current = requestAnimationFrame(animate);
  };

  // Handle mouse movement
  const handleMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!interactive) return;
    
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    mouseRef.current = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };

    // Spawn particles on mouse movement for certain spawn modes
    if (spawn === 'mouse' && Math.random() < 0.3) {
      particlesRef.current.push(createParticle());
    }
  };

  // Initialize particles
  useEffect(() => {
    particlesRef.current = Array.from({ length: particleCount }, () => createParticle());
  }, [particleCount, spawn, physics]);

  // Handle canvas resize
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const updateDimensions = () => {
      const width = propWidth || canvas.offsetWidth || 800;
      const height = propHeight || canvas.offsetHeight || 600;
      
      canvas.width = width;
      canvas.height = height;
      setDimensions({ width, height });
    };

    updateDimensions();
    
    const resizeObserver = new ResizeObserver(updateDimensions);
    resizeObserver.observe(canvas);
    
    return () => resizeObserver.disconnect();
  }, [propWidth, propHeight]);

  // Start animation
  useEffect(() => {
    animationRef.current = requestAnimationFrame(animate);
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [dimensions, physics, spawn]);

  return (
    <canvas
      ref={canvasRef}
      onMouseMove={handleMouseMove}
      className={cn(
        "absolute inset-0 pointer-events-none",
        interactive && "pointer-events-auto",
        className
      )}
      style={{
        width: propWidth || '100%',
        height: propHeight || '100%'
      }}
    />
  );
};

// Preset particle configurations
export const ParticlePresets = {
  floating: {
    particleCount: 30,
    physics: 'float' as const,
    spawn: 'random' as const,
    particleTypes: ['dot', 'glow'] as const,
    speed: 0.5,
    connectionLines: true
  },
  
  magical: {
    particleCount: 60,
    physics: 'float' as const,
    spawn: 'random' as const,
    particleTypes: ['spark', 'glow'] as const,
    colors: [
      'rgba(255, 215, 0, 0.8)',   // gold
      'rgba(255, 105, 180, 0.6)', // hot pink
      'rgba(138, 43, 226, 0.7)',  // blue violet
      'rgba(0, 255, 255, 0.6)'    // cyan
    ],
    speed: 1.5
  },
  
  cosmic: {
    particleCount: 40,
    physics: 'orbital' as const,
    spawn: 'center' as const,
    particleTypes: ['glow', 'bubble'] as const,
    colors: [
      'rgba(147, 51, 234, 0.6)', // purple
      'rgba(59, 130, 246, 0.6)', // blue
      'rgba(16, 185, 129, 0.5)', // emerald
    ],
    speed: 0.8,
    connectionLines: true
  },
  
  interactive: {
    particleCount: 25,
    physics: 'magnetic' as const,
    spawn: 'mouse' as const,
    particleTypes: ['dot', 'spark'] as const,
    interactive: true,
    speed: 2
  },
  
  bubbles: {
    particleCount: 20,
    physics: 'gravity' as const,
    spawn: 'edges' as const,
    particleTypes: ['bubble'] as const,
    speed: 0.3,
    size: { min: 8, max: 20 },
    life: { min: 5000, max: 12000 }
  }
};

export default ParticleSystem;