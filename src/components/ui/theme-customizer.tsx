import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useTheme, ThemePresets, type ThemeConfig } from '@/lib/theme-persistence';
import { Button } from './button';
import { Card, CardContent, CardHeader, CardTitle } from './card';
import { Slider } from './slider';
import { Switch } from './switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';
import { 
  Palette, 
  Settings, 
  Monitor, 
  Sun, 
  Moon, 
  Zap, 
  Eye, 
  Type, 
  Layers,
  Download,
  Upload,
  RotateCcw,
  X
} from 'lucide-react';

interface ThemeCustomizerProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export const ThemeCustomizer: React.FC<ThemeCustomizerProps> = ({
  isOpen,
  onClose,
  className
}) => {
  const {
    config,
    resolvedMode,
    updateConfig,
    updateGlassmorphism,
    updateAnimations,
    updateColors,
    updateTypography,
    updateAccessibility,
    updatePerformance,
    resetToDefaults,
    toggleDarkMode,
    exportConfig,
    importConfig
  } = useTheme();

  const [activeSection, setActiveSection] = useState<string>('general');
  const [importText, setImportText] = useState('');

  const sections = [
    { id: 'general', label: 'General', icon: Settings },
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'glassmorphism', label: 'Glass Effects', icon: Layers },
    { id: 'animations', label: 'Animations', icon: Zap },
    { id: 'typography', label: 'Typography', icon: Type },
    { id: 'accessibility', label: 'Accessibility', icon: Eye },
    { id: 'performance', label: 'Performance', icon: Monitor }
  ];

  const handlePresetApply = (preset: ThemeConfig) => {
    updateConfig(preset);
  };

  const handleExport = () => {
    const configJson = exportConfig();
    navigator.clipboard.writeText(configJson);
    // Could show a toast notification here
  };

  const handleImport = () => {
    if (importConfig(importText)) {
      setImportText('');
      // Could show a success toast here
    } else {
      // Could show an error toast here
    }
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        className={cn(
          'bg-white dark:bg-gray-900 rounded-lg shadow-xl',
          'w-full max-w-4xl max-h-[90vh] overflow-hidden',
          'flex flex-col',
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <Palette className="w-6 h-6 text-blue-500" />
            <h2 className="text-xl font-semibold">Theme Customizer</h2>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm" onClick={resetToDefaults}>
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar */}
          <div className="w-64 border-r border-gray-200 dark:border-gray-700 p-4 overflow-y-auto">
            <nav className="space-y-2">
              {sections.map(section => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={cn(
                      'w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors',
                      activeSection === section.id
                        ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                        : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                    )}
                  >
                    <Icon className="w-4 h-4" />
                    {section.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeSection}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="space-y-6"
              >
                {/* General Section */}
                {activeSection === 'general' && (
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Color Scheme</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="flex items-center gap-4">
                          <Button
                            variant={config.mode === 'light' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => updateConfig({ mode: 'light' })}
                          >
                            <Sun className="w-4 h-4 mr-2" />
                            Light
                          </Button>
                          <Button
                            variant={config.mode === 'dark' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => updateConfig({ mode: 'dark' })}
                          >
                            <Moon className="w-4 h-4 mr-2" />
                            Dark
                          </Button>
                          <Button
                            variant={config.mode === 'auto' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => updateConfig({ mode: 'auto' })}
                          >
                            <Monitor className="w-4 h-4 mr-2" />
                            Auto
                          </Button>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Current: {resolvedMode} mode
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Quick Presets</CardTitle>
                      </CardHeader>
                      <CardContent className="grid grid-cols-2 gap-3">
                        <Button
                          variant="outline"
                          className="h-auto p-4 flex-col"
                          onClick={() => handlePresetApply(ThemePresets.minimal)}
                        >
                          <div className="font-medium">Minimal</div>
                          <div className="text-xs text-gray-500">Clean & simple</div>
                        </Button>
                        <Button
                          variant="outline"
                          className="h-auto p-4 flex-col"
                          onClick={() => handlePresetApply(ThemePresets.premium)}
                        >
                          <div className="font-medium">Premium</div>
                          <div className="text-xs text-gray-500">Full effects</div>
                        </Button>
                        <Button
                          variant="outline"
                          className="h-auto p-4 flex-col"
                          onClick={() => handlePresetApply(ThemePresets.accessible)}
                        >
                          <div className="font-medium">Accessible</div>
                          <div className="text-xs text-gray-500">High contrast</div>
                        </Button>
                        <Button
                          variant="outline"
                          className="h-auto p-4 flex-col"
                          onClick={() => handlePresetApply(ThemePresets.performance)}
                        >
                          <div className="font-medium">Performance</div>
                          <div className="text-xs text-gray-500">Optimized</div>
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {/* Appearance Section */}
                {activeSection === 'appearance' && (
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Color Palette</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium mb-2">Primary Color</label>
                            <div className="flex items-center gap-2">
                              <input
                                type="color"
                                value={config.colors.primary}
                                onChange={(e) => updateColors({ primary: e.target.value })}
                                className="w-10 h-10 rounded border border-gray-300"
                              />
                              <input
                                type="text"
                                value={config.colors.primary}
                                onChange={(e) => updateColors({ primary: e.target.value })}
                                className="flex-1 px-3 py-2 border border-gray-300 rounded"
                              />
                            </div>
                          </div>
                          <div>
                            <label className="block text-sm font-medium mb-2">Accent Color</label>
                            <div className="flex items-center gap-2">
                              <input
                                type="color"
                                value={config.colors.accent}
                                onChange={(e) => updateColors({ accent: e.target.value })}
                                className="w-10 h-10 rounded border border-gray-300"
                              />
                              <input
                                type="text"
                                value={config.colors.accent}
                                onChange={(e) => updateColors({ accent: e.target.value })}
                                className="flex-1 px-3 py-2 border border-gray-300 rounded"
                              />
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {/* Glassmorphism Section */}
                {activeSection === 'glassmorphism' && (
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Glass Effects</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">Enable Glassmorphism</span>
                          <Switch
                            checked={config.glassmorphism.enabled}
                            onCheckedChange={(checked) => updateGlassmorphism({ enabled: checked })}
                          />
                        </div>

                        {config.glassmorphism.enabled && (
                          <>
                            <div>
                              <label className="block text-sm font-medium mb-2">Intensity</label>
                              <Select
                                value={config.glassmorphism.intensity}
                                onValueChange={(value: 'subtle' | 'medium' | 'strong') => 
                                  updateGlassmorphism({ intensity: value })
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="subtle">Subtle</SelectItem>
                                  <SelectItem value="medium">Medium</SelectItem>
                                  <SelectItem value="strong">Strong</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-2">
                                Blur: {config.glassmorphism.blur}px
                              </label>
                              <Slider
                                value={[config.glassmorphism.blur]}
                                onValueChange={(value) => updateGlassmorphism({ blur: value[0] })}
                                min={0}
                                max={40}
                                step={2}
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-2">
                                Opacity: {Math.round(config.glassmorphism.opacity * 100)}%
                              </label>
                              <Slider
                                value={[config.glassmorphism.opacity]}
                                onValueChange={(value) => updateGlassmorphism({ opacity: value[0] })}
                                min={0.1}
                                max={1}
                                step={0.1}
                              />
                            </div>
                          </>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                )}

                {/* Animations Section */}
                {activeSection === 'animations' && (
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Animation Settings</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">Enable Animations</span>
                          <Switch
                            checked={config.animations.enabled}
                            onCheckedChange={(checked) => updateAnimations({ enabled: checked })}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="font-medium">Reduce Motion</span>
                          <Switch
                            checked={config.animations.reduceMotion}
                            onCheckedChange={(checked) => updateAnimations({ reduceMotion: checked })}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-2">Animation Speed</label>
                          <Select
                            value={config.animations.duration}
                            onValueChange={(value: 'fast' | 'normal' | 'slow') => 
                              updateAnimations({ duration: value })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="fast">Fast</SelectItem>
                              <SelectItem value="normal">Normal</SelectItem>
                              <SelectItem value="slow">Slow</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {/* Typography Section */}
                {activeSection === 'typography' && (
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Typography</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div>
                          <label className="block text-sm font-medium mb-2">Font Size</label>
                          <Select
                            value={config.typography.fontSize}
                            onValueChange={(value: 'small' | 'medium' | 'large' | 'xl') => 
                              updateTypography({ fontSize: value })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="small">Small</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="large">Large</SelectItem>
                              <SelectItem value="xl">Extra Large</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-2">Font Family</label>
                          <Select
                            value={config.typography.fontFamily}
                            onValueChange={(value) => updateTypography({ fontFamily: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Inter, system-ui, sans-serif">Inter</SelectItem>
                              <SelectItem value="system-ui, sans-serif">System</SelectItem>
                              <SelectItem value="Georgia, serif">Georgia</SelectItem>
                              <SelectItem value="'JetBrains Mono', monospace">JetBrains Mono</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {/* Accessibility Section */}
                {activeSection === 'accessibility' && (
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Accessibility</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">High Contrast</span>
                          <Switch
                            checked={config.accessibility.highContrast}
                            onCheckedChange={(checked) => updateAccessibility({ highContrast: checked })}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="font-medium">Focus Rings</span>
                          <Switch
                            checked={config.accessibility.focusRings}
                            onCheckedChange={(checked) => updateAccessibility({ focusRings: checked })}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="font-medium">Screen Reader Optimized</span>
                          <Switch
                            checked={config.accessibility.screenReader}
                            onCheckedChange={(checked) => updateAccessibility({ screenReader: checked })}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {/* Performance Section */}
                {activeSection === 'performance' && (
                  <div className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Performance</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">GPU Acceleration</span>
                          <Switch
                            checked={config.performance.enableGPUAcceleration}
                            onCheckedChange={(checked) => updatePerformance({ enableGPUAcceleration: checked })}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="font-medium">Virtualization</span>
                          <Switch
                            checked={config.performance.enableVirtualization}
                            onCheckedChange={(checked) => updatePerformance({ enableVirtualization: checked })}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="font-medium">Reduce Data Usage</span>
                          <Switch
                            checked={config.performance.prefersReducedData}
                            onCheckedChange={(checked) => updatePerformance({ prefersReducedData: checked })}
                          />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Import/Export</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-2">Import Configuration</label>
                          <textarea
                            value={importText}
                            onChange={(e) => setImportText(e.target.value)}
                            placeholder="Paste theme configuration JSON here..."
                            className="w-full h-32 px-3 py-2 border border-gray-300 rounded resize-none"
                          />
                          <Button
                            onClick={handleImport}
                            disabled={!importText.trim()}
                            className="mt-2"
                            size="sm"
                          >
                            <Upload className="w-4 h-4 mr-2" />
                            Import
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ThemeCustomizer;