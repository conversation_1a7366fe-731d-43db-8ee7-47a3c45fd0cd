import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface EnhancedSkeletonProps {
  className?: string;
  variant?: "default" | "shimmer" | "pulse" | "wave";
  lines?: number;
  showAvatar?: boolean;
  showButton?: boolean;
  rounded?: boolean;
}

const shimmerVariants = {
  initial: { backgroundPosition: "-200% 0" },
  animate: {
    backgroundPosition: "200% 0",
    transition: {
      repeat: Infinity,
      duration: 2,
      ease: "linear"
    }
  }
};

const pulseVariants = {
  initial: { opacity: 0.6 },
  animate: {
    opacity: [0.6, 1, 0.6],
    transition: {
      repeat: Infinity,
      duration: 1.5,
      ease: "easeInOut"
    }
  }
};

const waveVariants = {
  initial: { scaleX: 0 },
  animate: {
    scaleX: [0, 1, 0],
    transition: {
      repeat: Infinity,
      duration: 2,
      ease: "easeInOut"
    }
  }
};

function Skeleton({
  className,
  variant = "shimmer",
  lines = 3,
  showAvatar = false,
  showButton = false,
  rounded = false,
  ...props
}: EnhancedSkeletonProps) {
  const baseClasses = cn(
    "glass-subtle rounded-md",
    rounded && "rounded-full",
    className
  );

  const getVariantClasses = () => {
    switch (variant) {
      case "shimmer":
        return "bg-gradient-to-r from-muted via-muted-foreground/10 to-muted bg-[length:200%_100%]";
      case "pulse":
        return "bg-muted";
      case "wave":
        return "bg-muted relative overflow-hidden";
      default:
        return "bg-muted animate-pulse";
    }
  };

  const getMotionVariants = () => {
    switch (variant) {
      case "shimmer":
        return shimmerVariants;
      case "pulse":
        return pulseVariants;
      case "wave":
        return waveVariants;
      default:
        return {};
    }
  };

  if (lines === 1 && !showAvatar && !showButton) {
    return (
      <motion.div
        className={cn(baseClasses, getVariantClasses())}
        variants={getMotionVariants()}
        initial="initial"
        animate="animate"
        {...props}
      >
        {variant === "wave" && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
            variants={waveVariants}
            style={{ originX: 0 }}
          />
        )}
      </motion.div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Avatar + Title Section */}
      {showAvatar && (
        <div className="flex items-center space-x-4">
          <motion.div
            className={cn("h-12 w-12 rounded-full", getVariantClasses())}
            variants={getMotionVariants()}
            initial="initial"
            animate="animate"
          />
          <div className="space-y-2 flex-1">
            <motion.div
              className={cn("h-4 rounded", getVariantClasses())}
              style={{ width: "60%" }}
              variants={getMotionVariants()}
              initial="initial"
              animate="animate"
            />
            <motion.div
              className={cn("h-3 rounded", getVariantClasses())}
              style={{ width: "40%" }}
              variants={getMotionVariants()}
              initial="initial"
              animate="animate"
            />
          </div>
        </div>
      )}

      {/* Content Lines */}
      <div className="space-y-2">
        {Array.from({ length: lines }).map((_, index) => {
          const width = index === lines - 1 ? "75%" : "100%";
          return (
            <motion.div
              key={index}
              className={cn("h-4 rounded", getVariantClasses())}
              style={{ width }}
              variants={getMotionVariants()}
              initial="initial"
              animate="animate"
              transition={{
                ...getMotionVariants().animate?.transition,
                delay: index * 0.1,
              }}
            >
              {variant === "wave" && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                  variants={waveVariants}
                  style={{ originX: 0 }}
                  transition={{
                    ...waveVariants.animate.transition,
                    delay: index * 0.1,
                  }}
                />
              )}
            </motion.div>
          );
        })}
      </div>

      {/* Button Section */}
      {showButton && (
        <div className="flex space-x-2 pt-2">
          <motion.div
            className={cn("h-9 w-20 rounded", getVariantClasses())}
            variants={getMotionVariants()}
            initial="initial"
            animate="animate"
          />
          <motion.div
            className={cn("h-9 w-16 rounded", getVariantClasses())}
            variants={getMotionVariants()}
            initial="initial"
            animate="animate"
          />
        </div>
      )}
    </div>
  );
}

// Specific skeleton components for common use cases
function SkeletonCard() {
  return (
    <div className="glass-medium p-6 rounded-xl shadow-glass">
      <Skeleton variant="shimmer" showAvatar showButton lines={3} />
    </div>
  );
}

function SkeletonList({ items = 5 }: { items?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: items }).map((_, i) => (
        <div key={i} className="glass-subtle p-4 rounded-lg">
          <Skeleton variant="pulse" showAvatar lines={2} />
        </div>
      ))}
    </div>
  );
}

function SkeletonTable() {
  return (
    <div className="glass-medium rounded-xl overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b border-glass-border-medium">
        <div className="flex space-x-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton
              key={i}
              variant="shimmer"
              className="h-4 flex-1"
            />
          ))}
        </div>
      </div>
      
      {/* Rows */}
      <div className="divide-y divide-glass-border-subtle">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="p-4">
            <div className="flex space-x-4">
              {Array.from({ length: 4 }).map((_, j) => (
                <Skeleton
                  key={j}
                  variant="wave"
                  className="h-4 flex-1"
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export { 
  Skeleton as EnhancedSkeleton, 
  SkeletonCard, 
  SkeletonList, 
  SkeletonTable 
};