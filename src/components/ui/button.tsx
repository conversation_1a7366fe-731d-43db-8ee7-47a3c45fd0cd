import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

/**
 * Button variants configuration using class-variance-authority
 */
const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 transform-gpu",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90",
        outline:
          "border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        glass: "glass-medium shadow-glass text-foreground hover:shadow-glass-lg hover:bg-[var(--glass-bg-strong)] hover:-translate-y-0.5",
        "glass-primary": "glass-medium shadow-glass text-primary-foreground bg-primary/20 hover:bg-primary/30 hover:shadow-glass-lg hover:-translate-y-0.5",
        magnetic: "bg-primary text-primary-foreground shadow magnetic",
        floating: "glass-strong shadow-floating hover:shadow-ambient hover:-translate-y-1 hover:scale-[1.02]",
        "magnetic-glass": "glass-medium shadow-glass text-foreground magnetic-strong ripple glow-subtle",
        "floating-glow": "glass-strong shadow-floating glow-strong float-gentle hover:shadow-ambient hover:-translate-y-2",
        shimmer: "bg-gradient-to-r from-blue-500 via-purple-500 to-blue-500 bg-size-200 text-white animate-shimmer-slide",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);



/**
 * Button component with multiple variants and sizes
 * 
 * @example
 * <Button variant="outline" size="lg" onClick={() => console.log('clicked')}>
 *   Click me
 * </Button>
 */
const Button = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<"button"> & VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }
>(({ className, variant, size, ...props }, ref) => {
  return (
    <button
      data-slot="button"
      ref={ref}
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
});

Button.displayName = "Button";

export { Button, buttonVariants };