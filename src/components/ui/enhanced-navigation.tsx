import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronRight, ChevronDown, ExternalLink, Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface NavigationItem {
  id: string;
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  href?: string;
  onClick?: () => void;
  badge?: string | number;
  isActive?: boolean;
  isNew?: boolean;
  isPremium?: boolean;
  children?: NavigationItem[];
  external?: boolean;
}

interface EnhancedNavigationProps {
  items: NavigationItem[];
  orientation?: 'horizontal' | 'vertical';
  variant?: 'default' | 'pills' | 'underline' | 'glass';
  size?: 'sm' | 'md' | 'lg';
  allowMultipleOpen?: boolean;
  className?: string;
  onItemClick?: (item: NavigationItem) => void;
}

interface NavigationItemProps {
  item: NavigationItem;
  orientation: 'horizontal' | 'vertical';
  variant: 'default' | 'pills' | 'underline' | 'glass';
  size: 'sm' | 'md' | 'lg';
  level?: number;
  isOpen?: boolean;
  onToggle?: () => void;
  onItemClick?: (item: NavigationItem) => void;
}

const sizeClasses = {
  sm: 'text-xs px-2 py-1',
  md: 'text-sm px-3 py-2',
  lg: 'text-base px-4 py-3'
};

const iconSizeClasses = {
  sm: 'w-3 h-3',
  md: 'w-4 h-4',
  lg: 'w-5 h-5'
};

const variantClasses = {
  default: {
    base: 'hover:bg-accent hover:text-accent-foreground',
    active: 'bg-accent text-accent-foreground'
  },
  pills: {
    base: 'rounded-full hover:bg-accent hover:text-accent-foreground',
    active: 'bg-primary text-primary-foreground rounded-full'
  },
  underline: {
    base: 'border-b-2 border-transparent hover:border-accent',
    active: 'border-b-2 border-primary text-primary'
  },
  glass: {
    base: 'glass-subtle hover:glass-medium hover:shadow-glass',
    active: 'glass-strong shadow-glass text-primary'
  }
};

const NavigationItemComponent: React.FC<NavigationItemProps> = ({
  item,
  orientation,
  variant,
  size,
  level = 0,
  isOpen,
  onToggle,
  onItemClick
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const Icon = item.icon;
  const hasChildren = item.children && item.children.length > 0;
  const indentation = level * 16;

  const handleClick = () => {
    if (hasChildren) {
      onToggle?.();
    } else {
      onItemClick?.(item);
      item.onClick?.();
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: orientation === 'vertical' ? -10 : 0, y: orientation === 'horizontal' ? -10 : 0 },
    visible: { 
      opacity: 1, 
      x: 0, 
      y: 0,
      transition: { type: "spring", stiffness: 400, damping: 30 }
    },
    hover: {
      x: orientation === 'vertical' ? 2 : 0,
      y: orientation === 'horizontal' ? -2 : 0,
      transition: { type: "spring", stiffness: 400, damping: 30 }
    }
  };

  return (
    <div>
      {/* Main Item */}
      <motion.div
        variants={itemVariants}
        initial="hidden"
        animate="visible"
        whileHover="hover"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{ 
          paddingLeft: orientation === 'vertical' ? `${indentation}px` : undefined 
        }}
      >
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={handleClick}
          className={cn(
            "relative flex items-center gap-2 transition-all duration-200 rounded-lg",
            "w-full text-left group overflow-hidden",
            sizeClasses[size],
            variantClasses[variant].base,
            item.isActive && variantClasses[variant].active
          )}
        >
          {/* Background gradient effect */}
          <motion.div
            className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300"
            animate={{
              background: isHovered 
                ? "linear-gradient(45deg, #3b82f6, #8b5cf6)" 
                : "transparent"
            }}
          />

          {/* Icon */}
          {Icon && (
            <motion.div
              animate={{ rotate: isHovered ? 5 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <Icon className={cn(
                iconSizeClasses[size],
                item.isActive ? "text-primary" : "text-muted-foreground group-hover:text-foreground",
                "transition-colors relative z-10"
              )} />
            </motion.div>
          )}

          {/* Label */}
          <span className="flex-1 relative z-10 font-medium">
            {item.label}
          </span>

          {/* Badges and indicators */}
          <div className="flex items-center gap-1 relative z-10">
            {/* New indicator */}
            {item.isNew && (
              <motion.span
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full"
              >
                New
              </motion.span>
            )}

            {/* Premium indicator */}
            {item.isPremium && (
              <motion.div
                animate={{ rotate: [0, 5, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <Star className="w-3 h-3 text-yellow-400 fill-current" />
              </motion.div>
            )}

            {/* Badge */}
            {item.badge && (
              <motion.span
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                whileHover={{ scale: 1.1 }}
                className="bg-accent text-accent-foreground text-xs px-2 py-0.5 rounded-full min-w-[1.25rem] text-center"
              >
                {item.badge}
              </motion.span>
            )}

            {/* External link indicator */}
            {item.external && (
              <ExternalLink className="w-3 h-3 text-muted-foreground" />
            )}

            {/* Expand/collapse indicator */}
            {hasChildren && (
              <motion.div
                animate={{ rotate: isOpen ? (orientation === 'vertical' ? 90 : 180) : 0 }}
                transition={{ duration: 0.2 }}
              >
                {orientation === 'vertical' ? (
                  <ChevronRight className="w-4 h-4 text-muted-foreground" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-muted-foreground" />
                )}
              </motion.div>
            )}
          </div>

          {/* Active indicator line */}
          {item.isActive && variant === 'default' && (
            <motion.div
              layoutId="activeIndicator"
              className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r"
              transition={{ type: "spring", stiffness: 500, damping: 30 }}
            />
          )}
        </motion.button>
      </motion.div>

      {/* Children */}
      <AnimatePresence>
        {hasChildren && isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className={cn(
              orientation === 'vertical' ? 'ml-2 border-l border-border' : 'mt-2'
            )}>
              {item.children!.map((child) => (
                <NavigationItemComponent
                  key={child.id}
                  item={child}
                  orientation={orientation}
                  variant={variant}
                  size={size}
                  level={level + 1}
                  onItemClick={onItemClick}
                />
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export const EnhancedNavigation: React.FC<EnhancedNavigationProps> = ({
  items,
  orientation = 'vertical',
  variant = 'default',
  size = 'md',
  allowMultipleOpen = false,
  className,
  onItemClick
}) => {
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());

  const toggleItem = (itemId: string) => {
    setOpenItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        if (!allowMultipleOpen) {
          newSet.clear();
        }
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <motion.nav
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        "space-y-1",
        orientation === 'horizontal' && "flex space-y-0 space-x-1",
        variant === 'glass' && "p-2 glass-subtle rounded-lg",
        className
      )}
    >
      {items.map((item) => (
        <NavigationItemComponent
          key={item.id}
          item={item}
          orientation={orientation}
          variant={variant}
          size={size}
          isOpen={openItems.has(item.id)}
          onToggle={() => toggleItem(item.id)}
          onItemClick={onItemClick}
        />
      ))}
    </motion.nav>
  );
};

// Utility function to create navigation items from routes
export const createNavigationFromRoutes = (
  routes: Array<{ path: string; label: string; icon?: any; children?: any[] }>
): NavigationItem[] => {
  return routes.map(route => ({
    id: route.path,
    label: route.label,
    icon: route.icon,
    href: route.path,
    children: route.children ? createNavigationFromRoutes(route.children) : undefined
  }));
};

// Preset navigation configurations
export const NavigationPresets = {
  sidebar: (onNavigate: (path: string) => void): NavigationItem[] => [
    {
      id: 'dashboard',
      label: 'Dashboard',
      onClick: () => onNavigate('/dashboard'),
      isActive: true
    },
    {
      id: 'projects',
      label: 'Projects',
      onClick: () => onNavigate('/projects'),
      badge: 5
    },
    {
      id: 'settings',
      label: 'Settings',
      onClick: () => onNavigate('/settings')
    }
  ],

  topbar: (onNavigate: (path: string) => void): NavigationItem[] => [
    {
      id: 'home',
      label: 'Home',
      onClick: () => onNavigate('/'),
      isActive: true
    },
    {
      id: 'features',
      label: 'Features',
      children: [
        {
          id: 'feature-1',
          label: 'Feature 1',
          onClick: () => onNavigate('/features/1')
        },
        {
          id: 'feature-2',
          label: 'Feature 2',
          onClick: () => onNavigate('/features/2'),
          isNew: true
        }
      ]
    },
    {
      id: 'about',
      label: 'About',
      onClick: () => onNavigate('/about')
    }
  ]
};

export default EnhancedNavigation;