"use client";

import { cn } from "@/lib/utils";
import React, {
  ReactNode,
  useCallback,
  useState,
} from "react";



interface MagicContainerProps {
  children?: ReactNode;
  className?: string;
}

const MagicContainer = ({ children, className }: MagicContainerProps) => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const [mouseX, setMouseX] = useState(0);
  const [mouseY, setMouseY] = useState(0);

  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setMouseX(e.clientX - rect.left);
        setMouseY(e.clientY - rect.top);
      }
    },
    [],
  );



  return (
    <div
      ref={containerRef}
      onMouseMove={handleMouseMove}

      className={cn("group relative overflow-hidden", className)}
    >
      <div
        className="pointer-events-none absolute -inset-px opacity-0 transition duration-300 group-hover:opacity-100"
        style={{
          background: `radial-gradient(600px circle at ${mouseX}px ${mouseY}px, rgba(255,182,255,.1), transparent 40%)`,
        }}
      />
      {children}
    </div>
  );
};

interface MagicCardProps {
  children: ReactNode;
  className?: string;
  gradientSize?: number;
  gradientColor?: string;
  gradientOpacity?: number;
}

const MagicCard = ({
  children,
  className,
  gradientSize = 200,
  gradientColor = "#262626",
  gradientOpacity = 0.8,
}: MagicCardProps) => {
  const [mousePosition, setMousePosition] = useState({ x: -gradientSize, y: -gradientSize });
  const [isHovered, setIsHovered] = useState(false);
  const [tiltAngles, setTiltAngles] = useState({ rotateX: 0, rotateY: 0 });

  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      setMousePosition({ x, y });

      // Calculate 3D tilt based on mouse position
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      
      // Calculate tilt angles (max 15 degrees)
      const rotateY = ((x - centerX) / centerX) * 15;
      const rotateX = ((centerY - y) / centerY) * 15;
      
      setTiltAngles({ rotateX, rotateY });
    },
    []
  );

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
    setMousePosition({ x: -gradientSize, y: -gradientSize });
    setTiltAngles({ rotateX: 0, rotateY: 0 });
  }, [gradientSize]);

  return (
    <div
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={cn(
        "group relative overflow-hidden rounded-xl glass-medium shadow-glass transform-gpu transition-all duration-300 hover:shadow-glass-lg",
        className,
      )}
      style={{
        transform: `perspective(1000px) rotateX(${tiltAngles.rotateX}deg) rotateY(${tiltAngles.rotateY}deg) translateZ(${isHovered ? '20px' : '0px'})`,
        transformStyle: 'preserve-3d',
        transition: isHovered 
          ? 'transform 0.1s ease-out, box-shadow 0.3s ease' 
          : 'transform 0.6s cubic-bezier(0.23, 1, 0.32, 1), box-shadow 0.3s ease'
      }}
    >
      {/* 3D depth highlight */}
      <div 
        className="absolute inset-0 rounded-xl transition-opacity duration-300"
        style={{
          background: `linear-gradient(
            ${135 + tiltAngles.rotateY}deg, 
            rgba(255,255,255,0.1) 0%, 
            transparent 50%, 
            rgba(0,0,0,0.1) 100%
          )`,
          opacity: isHovered ? 0.6 : 0
        }}
      />
      
      <div
        className="pointer-events-none absolute transition-opacity duration-300 ease-in-out"
        style={{
          background: `radial-gradient(${gradientSize}px circle at ${mousePosition.x}px ${mousePosition.y}px, ${gradientColor}, transparent 100%)`,
          opacity: isHovered ? gradientOpacity : 0,
          left: 0,
          top: 0,
          right: 0,
          bottom: 0,
        }}
      />
      <div 
        className="relative z-10 p-4"
        style={{
          transform: `translateZ(10px)`,
        }}
      >
        {children}
      </div>
    </div>
  );
};

export { MagicCard, MagicContainer };