import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Home, 
  MessageSquare, 
  Bot, 
  Settings, 
  BarChart3, 
  Network, 
  FileText, 
  ShoppingBag, 
  Lightbulb, 
  Palette,
  Folder,
  ChevronRight,
  Search,
  History,
  Bookmark,
  Plus
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  action: () => void;
  badge?: string | number;
  section: 'navigation' | 'tools' | 'recent';
}

interface EnhancedSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  onNavigate?: (item: SidebarItem) => void;
  className?: string;
}

interface SidebarSection {
  title: string;
  items: SidebarItem[];
}

export const EnhancedSidebar: React.FC<EnhancedSidebarProps> = ({
  isOpen,
  onClose,
  onNavigate,
  className
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [recentItems, setRecentItems] = useState<SidebarItem[]>([]);

  // Mock data for recent items
  useEffect(() => {
    setRecentItems([
      {
        id: 'recent-1',
        label: 'Chat Session #1',
        icon: MessageSquare,
        action: () => console.log('Open recent chat'),
        section: 'recent'
      },
      {
        id: 'recent-2', 
        label: 'Agent Run: Code Review',
        icon: Bot,
        action: () => console.log('Open recent agent'),
        section: 'recent'
      }
    ]);
  }, []);

  const navigationItems: SidebarItem[] = [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      action: () => console.log('Navigate to home'),
      section: 'navigation'
    },
    {
      id: 'projects',
      label: 'Projects',
      icon: Folder,
      action: () => console.log('Navigate to projects'),
      section: 'navigation'
    },
    {
      id: 'new-chat',
      label: 'New Chat',
      icon: Plus,
      action: () => console.log('Create new chat'),
      section: 'navigation'
    }
  ];

  const toolItems: SidebarItem[] = [
    {
      id: 'agents',
      label: 'Agents',
      icon: Bot,
      action: () => console.log('Open agents'),
      badge: '3',
      section: 'tools'
    },
    {
      id: 'marketplace',
      label: 'Marketplace',
      icon: ShoppingBag,
      action: () => console.log('Open marketplace'),
      section: 'tools'
    },
    {
      id: 'brainstorming',
      label: 'Brainstorming',
      icon: Lightbulb,
      action: () => console.log('Open brainstorming'),
      section: 'tools'
    },
    {
      id: 'ui-demo',
      label: 'UI Demo',
      icon: Palette,
      action: () => console.log('Open UI demo'),
      section: 'tools'
    },
    {
      id: 'usage',
      label: 'Usage Dashboard',
      icon: BarChart3,
      action: () => console.log('Open usage'),
      section: 'tools'
    },
    {
      id: 'mcp',
      label: 'MCP Servers',
      icon: Network,
      action: () => console.log('Open MCP'),
      section: 'tools'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      action: () => console.log('Open settings'),
      section: 'tools'
    }
  ];

  const sections: SidebarSection[] = [
    {
      title: 'Navigation',
      items: navigationItems
    },
    {
      title: 'Tools',
      items: toolItems
    },
    {
      title: 'Recent',
      items: recentItems
    }
  ];

  const filteredSections = sections.map(section => ({
    ...section,
    items: section.items.filter(item =>
      item.label.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(section => section.items.length > 0);

  const handleItemClick = (item: SidebarItem) => {
    item.action();
    onNavigate?.(item);
    onClose();
  };

  const sidebarVariants = {
    closed: {
      x: '-100%',
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 40,
        duration: 0.3
      }
    },
    open: {
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 40,
        duration: 0.4
      }
    }
  };

  const overlayVariants = {
    closed: {
      opacity: 0,
      transition: { duration: 0.2 }
    },
    open: {
      opacity: 1,
      transition: { duration: 0.3 }
    }
  };

  const contentVariants = {
    closed: {
      opacity: 0,
      y: 20
    },
    open: {
      opacity: 1,
      y: 0,
      transition: {
        delay: 0.1,
        duration: 0.3,
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    closed: {
      opacity: 0,
      x: -20
    },
    open: {
      opacity: 1,
      x: 0
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            variants={overlayVariants}
            initial="closed"
            animate="open"
            exit="closed"
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
            onClick={onClose}
          />
          
          {/* Sidebar */}
          <motion.div
            variants={sidebarVariants}
            initial="closed"
            animate="open"
            exit="closed"
            className={cn(
              "fixed left-0 top-0 h-full w-80 z-50",
              "glass-strong shadow-glass-xl border-r border-white/10",
              "flex flex-col",
              className
            )}
          >
            {/* Header */}
            <motion.div
              variants={contentVariants}
              className="flex items-center justify-between p-6 border-b border-white/10"
            >
              <h2 className="text-lg font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Navigation
              </h2>
              <Button
                variant="glass"
                size="sm"
                onClick={onClose}
                className="w-8 h-8 p-0 hover:bg-red-500/20 hover:text-red-400"
              >
                <X className="w-4 h-4" />
              </Button>
            </motion.div>

            {/* Search */}
            <motion.div
              variants={contentVariants}
              className="p-4 border-b border-white/10"
            >
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search navigation..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 glass-subtle border-white/20 focus:border-blue-400/50 focus:ring-blue-400/30"
                />
              </div>
            </motion.div>

            {/* Content */}
            <motion.div
              variants={contentVariants}
              className="flex-1 overflow-y-auto p-4 space-y-6"
            >
              {filteredSections.map((section) => (
                <motion.div
                  key={section.title}
                  variants={itemVariants}
                  className="space-y-2"
                >
                  {/* Section Header */}
                  <div className="flex items-center justify-between">
                    <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
                      {section.title}
                    </h3>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setActiveSection(
                        activeSection === section.title ? null : section.title
                      )}
                      className="p-1 hover:bg-white/10 rounded-sm transition-colors"
                    >
                      <ChevronRight
                        className={cn(
                          "w-3 h-3 transition-transform duration-200",
                          activeSection === section.title && "rotate-90"
                        )}
                      />
                    </motion.button>
                  </div>

                  {/* Section Items */}
                  <AnimatePresence>
                    {(activeSection === section.title || activeSection === null) && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.2 }}
                        className="space-y-1"
                      >
                        {section.items.map((item) => {
                          const Icon = item.icon;
                          return (
                            <motion.button
                              key={item.id}
                              variants={itemVariants}
                              whileHover={{ 
                                x: 4,
                                scale: 1.02,
                                transition: { type: "spring", stiffness: 400, damping: 30 }
                              }}
                              whileTap={{ scale: 0.98 }}
                              onClick={() => handleItemClick(item)}
                              className={cn(
                                "w-full flex items-center gap-3 p-3 rounded-lg",
                                "glass-subtle hover:glass-medium hover:shadow-glass",
                                "text-left transition-all duration-200",
                                "group relative overflow-hidden"
                              )}
                            >
                              {/* Hover gradient */}
                              <motion.div
                                className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300"
                                style={{
                                  background: "linear-gradient(45deg, #3b82f6, #8b5cf6)"
                                }}
                              />
                              
                              <Icon className="w-4 h-4 text-blue-400 group-hover:text-blue-300 transition-colors relative z-10" />
                              <span className="flex-1 text-sm relative z-10">{item.label}</span>
                              
                              {item.badge && (
                                <motion.span
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="bg-blue-500/20 text-blue-300 text-xs px-2 py-0.5 rounded-full relative z-10"
                                >
                                  {item.badge}
                                </motion.span>
                              )}
                            </motion.button>
                          );
                        })}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </motion.div>

            {/* Footer */}
            <motion.div
              variants={contentVariants}
              className="p-4 border-t border-white/10"
            >
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                <span>All systems operational</span>
              </div>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default EnhancedSidebar;