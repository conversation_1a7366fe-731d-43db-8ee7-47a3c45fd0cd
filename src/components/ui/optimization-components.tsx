import React, { memo, useMemo, useCallback, useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { usePerformanceMonitor, performanceTracker } from '@/lib/performance-monitor';

// Optimized virtualized list for large datasets
interface VirtualizedListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
  className?: string;
}

export const VirtualizedList = memo(<T,>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className
}: VirtualizedListProps<T>) => {
  const [scrollTop, setScrollTop] = useState(0);
  const { renderCount } = usePerformanceMonitor('VirtualizedList');

  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
  }, [items, visibleRange]);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.startIndex * itemHeight;

  return (
    <div
      className={cn("overflow-auto", className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div
              key={visibleRange.startIndex + index}
              style={{ height: itemHeight }}
            >
              {renderItem(item, visibleRange.startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

// Optimized animation wrapper with reduced re-renders
interface OptimizedMotionProps {
  children: React.ReactNode;
  animate?: any;
  initial?: any;
  exit?: any;
  transition?: any;
  className?: string;
  style?: React.CSSProperties;
  layoutId?: string;
}

export const OptimizedMotion = memo<OptimizedMotionProps>(({
  children,
  animate,
  initial,
  exit,
  transition,
  className,
  style,
  layoutId
}) => {
  const { renderCount } = usePerformanceMonitor('OptimizedMotion');

  const memoizedTransition = useMemo(() => ({
    type: "spring",
    stiffness: 400,
    damping: 30,
    mass: 0.8,
    ...transition
  }), [transition]);

  return (
    <motion.div
      className={className}
      style={style}
      initial={initial}
      animate={animate}
      exit={exit}
      transition={memoizedTransition}
      layoutId={layoutId}
    >
      {children}
    </motion.div>
  );
});

// Lazy loading image component with intersection observer
interface LazyImageProps {
  src: string;
  alt: string;
  placeholder?: string;
  className?: string;
  width?: number;
  height?: number;
  onLoad?: () => void;
  onError?: () => void;
}

export const LazyImage = memo<LazyImageProps>(({
  src,
  alt,
  placeholder,
  className,
  width,
  height,
  onLoad,
  onError
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [error, setError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const { renderCount } = usePerformanceMonitor('LazyImage');

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setError(true);
    onError?.();
  }, [onError]);

  return (
    <div
      ref={imgRef}
      className={cn("relative overflow-hidden", className)}
      style={{ width, height }}
    >
      {placeholder && !isLoaded && !error && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
      
      {isInView && (
        <motion.img
          src={src}
          alt={alt}
          className={cn("w-full h-full object-cover", className)}
          onLoad={handleLoad}
          onError={handleError}
          initial={{ opacity: 0 }}
          animate={{ opacity: isLoaded ? 1 : 0 }}
          transition={{ duration: 0.3 }}
        />
      )}
      
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-500">
          Failed to load
        </div>
      )}
    </div>
  );
});

// Debounced input component
interface DebouncedInputProps {
  value: string;
  onChange: (value: string) => void;
  delay?: number;
  placeholder?: string;
  className?: string;
}

export const DebouncedInput = memo<DebouncedInputProps>(({
  value,
  onChange,
  delay = 300,
  placeholder,
  className
}) => {
  const [localValue, setLocalValue] = useState(value);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const { renderCount } = usePerformanceMonitor('DebouncedInput');

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);

    clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      onChange(newValue);
    }, delay);
  }, [onChange, delay]);

  useEffect(() => {
    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, []);

  return (
    <input
      value={localValue}
      onChange={handleChange}
      placeholder={placeholder}
      className={cn(
        "px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
        className
      )}
    />
  );
});

// Optimized accordion component
interface AccordionItem {
  id: string;
  title: string;
  content: React.ReactNode;
}

interface OptimizedAccordionProps {
  items: AccordionItem[];
  allowMultiple?: boolean;
  className?: string;
}

export const OptimizedAccordion = memo<OptimizedAccordionProps>(({
  items,
  allowMultiple = false,
  className
}) => {
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());
  const { renderCount } = usePerformanceMonitor('OptimizedAccordion');

  const toggleItem = useCallback((id: string) => {
    setOpenItems(prev => {
      const newSet = new Set(prev);
      
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        if (!allowMultiple) {
          newSet.clear();
        }
        newSet.add(id);
      }
      
      return newSet;
    });
  }, [allowMultiple]);

  const memoizedItems = useMemo(() => {
    return items.map(item => ({
      ...item,
      isOpen: openItems.has(item.id)
    }));
  }, [items, openItems]);

  return (
    <div className={cn("space-y-2", className)}>
      {memoizedItems.map(item => (
        <AccordionItemComponent
          key={item.id}
          item={item}
          onToggle={toggleItem}
        />
      ))}
    </div>
  );
});

// Memoized accordion item to prevent unnecessary re-renders
const AccordionItemComponent = memo<{
  item: AccordionItem & { isOpen: boolean };
  onToggle: (id: string) => void;
}>(({ item, onToggle }) => {
  const handleToggle = useCallback(() => {
    onToggle(item.id);
  }, [item.id, onToggle]);

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <button
        onClick={handleToggle}
        className="w-full px-4 py-3 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
      >
        <div className="flex justify-between items-center">
          <span className="font-medium">{item.title}</span>
          <motion.span
            animate={{ rotate: item.isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            ↓
          </motion.span>
        </div>
      </button>
      
      <AnimatePresence>
        {item.isOpen && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="px-4 py-3 border-t border-gray-200">
              {item.content}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
});

// Performance monitoring dashboard component
export const PerformanceDashboard = memo(() => {
  const [stats, setStats] = useState<Record<string, any>>({});
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setStats(performanceTracker.getAllStats());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  if (!isVisible || process.env.NODE_ENV !== 'development') {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-500 text-white px-3 py-2 rounded-lg text-sm z-50"
      >
        📊 Perf
      </button>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm z-50"
    >
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-sm font-semibold">Performance Stats</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>
      
      <div className="space-y-2 text-xs">
        {Object.entries(stats).map(([name, stat]) => (
          <div key={name} className="border-b border-gray-100 pb-1">
            <div className="font-medium truncate">{name}</div>
            <div className="text-gray-600">
              Avg: {stat.averageRenderTime?.toFixed(1)}ms | 
              Renders: {stat.totalRenders}
            </div>
          </div>
        ))}
      </div>
      
      <button
        onClick={() => performanceTracker.clearMetrics()}
        className="mt-3 w-full text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded"
      >
        Clear Metrics
      </button>
    </motion.div>
  );
});

// Bundle size reporter component
export const BundleSizeReporter = memo(() => {
  const [isVisible, setIsVisible] = useState(false);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-16 right-4 bg-purple-500 text-white px-3 py-2 rounded-lg text-sm z-50"
      >
        📦 Bundle
      </button>
      
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="fixed inset-4 bg-white border border-gray-200 rounded-lg shadow-lg p-6 z-50 overflow-auto"
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Bundle Analysis</h2>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          
          <div className="prose prose-sm max-w-none">
            <p>Bundle analysis tools would be integrated here in a real implementation.</p>
            <p>This would show:</p>
            <ul>
              <li>Component bundle sizes</li>
              <li>Tree shaking effectiveness</li>
              <li>Unused code detection</li>
              <li>Import cost analysis</li>
            </ul>
          </div>
        </motion.div>
      )}
    </>
  );
});

export default {
  VirtualizedList,
  OptimizedMotion,
  LazyImage,
  DebouncedInput,
  OptimizedAccordion,
  PerformanceDashboard,
  BundleSizeReporter
};