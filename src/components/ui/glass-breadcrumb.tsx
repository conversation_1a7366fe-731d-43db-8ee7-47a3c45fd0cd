import React from 'react';
import { motion } from 'framer-motion';
import { ChevronRight, Home, Folder, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface BreadcrumbItem {
  id: string;
  label: string;
  href?: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick?: () => void;
  isActive?: boolean;
}

interface GlassBreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
  separator?: React.ReactNode;
}

export const GlassBreadcrumb: React.FC<GlassBreadcrumbProps> = ({
  items,
  className,
  showHome = true,
  separator
}) => {
  const allItems = showHome ? [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      onClick: () => console.log('Navigate to home')
    },
    ...items
  ] : items;

  const containerVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { type: "spring", stiffness: 400, damping: 30 }
    }
  };

  const separatorVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { delay: 0.1 }
    }
  };

  return (
    <motion.nav
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        "flex items-center space-x-1 p-3 rounded-lg",
        "glass-subtle shadow-glass border border-white/10",
        "backdrop-blur-md",
        className
      )}
      aria-label="Breadcrumb navigation"
    >
      <ol className="flex items-center space-x-1">
        {allItems.map((item, index) => {
          const Icon = item.icon;
          const isLast = index === allItems.length - 1;
          
          return (
            <React.Fragment key={item.id}>
              <motion.li
                variants={itemVariants}
                className="flex items-center"
              >
                <motion.button
                  whileHover={{ 
                    scale: 1.05,
                    y: -1,
                    transition: { type: "spring", stiffness: 400, damping: 30 }
                  }}
                  whileTap={{ scale: 0.98 }}
                  onClick={item.onClick}
                  disabled={isLast || item.isActive}
                  className={cn(
                    "flex items-center gap-2 px-3 py-1.5 rounded-md text-sm",
                    "transition-all duration-200 relative overflow-hidden group",
                    isLast || item.isActive
                      ? "text-foreground cursor-default glass-medium"
                      : "text-muted-foreground hover:text-foreground hover:glass-medium hover:shadow-glass"
                  )}
                  aria-current={isLast ? "page" : undefined}
                >
                  {/* Hover effect background */}
                  {!isLast && !item.isActive && (
                    <motion.div
                      className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300"
                      style={{
                        background: "linear-gradient(45deg, #3b82f6, #8b5cf6)"
                      }}
                    />
                  )}
                  
                  {Icon && (
                    <Icon className={cn(
                      "w-4 h-4 transition-colors relative z-10",
                      isLast || item.isActive 
                        ? "text-blue-400" 
                        : "group-hover:text-blue-400"
                    )} />
                  )}
                  
                  <span className="relative z-10 font-medium">
                    {item.label}
                  </span>
                  
                  {/* Active indicator */}
                  {(isLast || item.isActive) && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400"
                    />
                  )}
                </motion.button>
              </motion.li>
              
              {/* Separator */}
              {!isLast && (
                <motion.li
                  variants={separatorVariants}
                  className="flex items-center"
                  aria-hidden="true"
                >
                  {separator || (
                    <ChevronRight className="w-4 h-4 text-muted-foreground/60" />
                  )}
                </motion.li>
              )}
            </React.Fragment>
          );
        })}
      </ol>
    </motion.nav>
  );
};

// Utility function to create breadcrumb items from path
export const createBreadcrumbsFromPath = (
  path: string,
  baseItems: Partial<BreadcrumbItem>[] = []
): BreadcrumbItem[] => {
  const segments = path.split('/').filter(Boolean);
  
  return segments.map((segment, index) => ({
    id: `segment-${index}`,
    label: segment.charAt(0).toUpperCase() + segment.slice(1),
    onClick: () => console.log(`Navigate to: ${segments.slice(0, index + 1).join('/')}`),
    ...baseItems[index]
  }));
};

// Preset breadcrumb configurations
export const BreadcrumbPresets = {
  project: (projectName: string): BreadcrumbItem[] => [
    {
      id: 'projects',
      label: 'Projects',
      icon: Folder,
      onClick: () => console.log('Navigate to projects')
    },
    {
      id: 'project',
      label: projectName,
      icon: Folder,
      isActive: true
    }
  ],
  
  session: (projectName: string, sessionName: string): BreadcrumbItem[] => [
    {
      id: 'projects',
      label: 'Projects',
      icon: Folder,
      onClick: () => console.log('Navigate to projects')
    },
    {
      id: 'project',
      label: projectName,
      icon: Folder,
      onClick: () => console.log(`Navigate to project: ${projectName}`)
    },
    {
      id: 'session',
      label: sessionName,
      icon: FileText,
      isActive: true
    }
  ],
  
  file: (fileName: string): BreadcrumbItem[] => [
    {
      id: 'files',
      label: 'Files',
      icon: Folder,
      onClick: () => console.log('Navigate to files')
    },
    {
      id: 'file',
      label: fileName,
      icon: FileText,
      isActive: true
    }
  ]
};

export default GlassBreadcrumb;