// Enhanced UI Components Export
export { Button, buttonVariants } from "./button";
export { <PERSON>, <PERSON><PERSON>eader, CardFooter, CardTitle, CardDescription, CardContent } from "./card";
export { MagicCard, MagicContainer } from "./magic-card";
export { MagneticButton } from "./magnetic-button";
export { ShimmerButton } from "./shimmer-button";
export { TextAnimate } from "./text-animate";
export { EnhancedSkeleton, SkeletonCard, SkeletonList, SkeletonTable } from "./enhanced-skeleton";
export { PremiumProgress, CircularProgress } from "./premium-progress";

// Layout & Navigation Components
export { EnhancedSidebar } from "./enhanced-sidebar";
export { GlassBreadcrumb, createBreadcrumbsFromPath, BreadcrumbPresets } from "./glass-breadcrumb";
export { FloatingActionButton, FABPresets } from "./floating-action-button";
export { GlassToast, GlassToastContainer, useGlassToast } from "./glass-toast";
export { EnhancedNavigation, NavigationPresets, createNavigationFromRoutes } from "./enhanced-navigation";

// Advanced Effects Components
export { ParticleSystem, ParticlePresets } from "./particle-system";
export { ParallaxContainer, ParallaxElement, ParallaxPresets, useParallax } from "./parallax-container";
export { AnimatedGrid, FlowingGradient, GeometricPattern, WavePattern, MorphingBlob, Constellation, BackgroundPresets } from "./animated-backgrounds";
export { CustomCursor, CursorTrail, MagneticCursor, RippleEffect, SpotlightEffect, CursorGlow, CursorPresets } from "./cursor-effects";
export { ShapeMorph, TextMorph, LiquidTransition, BlobMorph, ParticleBurst, MorphingButton, GlitchEffect, MorphingPresets } from "./morphing-effects";

// Re-export existing components
export { Alert } from "./alert";
export { Badge } from "./badge";
export { Checkbox } from "./checkbox";
export { Dialog } from "./dialog";
export { Input } from "./input";
export { Label } from "./label";

export { RadioGroup } from "./radio-group";
export { ScrollArea } from "./scroll-area";
export { Select } from "./select";
export { Separator } from "./separator";

export { Slider } from "./slider";
export { Switch } from "./switch";
export { Tabs } from "./tabs";
export { Textarea } from "./textarea";
export { Toast } from "./toast";
export { Tooltip } from "./tooltip";
export { Popover } from "./popover";
export { DropdownMenu } from "./dropdown-menu";
export { Collapsible } from "./collapsible";

// Animated components
export { AnimatedBeam } from "./animated-beam";
export { AnimatedGridPattern } from "./animated-grid-pattern";
export { Meteors } from "./meteors";
export { RainbowButton } from "./rainbow-button";
export { WarpBackground } from "./warp-background";

// Utility components
export { SplitPane } from "./split-pane";
export { Pagination } from "./pagination";
export { ToastStack } from "./ToastStack";

// Performance & Optimization Components
export { 
  VirtualizedList,
  VirtualizedList as OptimizedVirtualizedList,
  OptimizedMotion,
  LazyImage,
  DebouncedInput,
  OptimizedAccordion,
  PerformanceDashboard,
  BundleSizeReporter
} from "./optimization-components";