import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle, 
  AlertCircle, 
  XCircle, 
  Info, 
  X, 
  Loader2,
  Zap,
  Bell
} from 'lucide-react';
import { cn } from '@/lib/utils';

export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'loading' | 'premium';

export interface ToastData {
  id: string;
  type: ToastType;
  title: string;
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
  dismissible?: boolean;
  premium?: boolean;
}

interface GlassToastProps {
  toast: ToastData;
  onDismiss: (id: string) => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

const toastIcons = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertCircle,
  info: Info,
  loading: Loader2,
  premium: Zap
};

const toastColors = {
  success: {
    icon: 'text-green-400',
    gradient: 'from-green-500/20 to-emerald-500/20',
    border: 'border-green-500/30',
    glow: 'shadow-green-500/20'
  },
  error: {
    icon: 'text-red-400',
    gradient: 'from-red-500/20 to-pink-500/20',
    border: 'border-red-500/30',
    glow: 'shadow-red-500/20'
  },
  warning: {
    icon: 'text-yellow-400',
    gradient: 'from-yellow-500/20 to-orange-500/20',
    border: 'border-yellow-500/30',
    glow: 'shadow-yellow-500/20'
  },
  info: {
    icon: 'text-blue-400',
    gradient: 'from-blue-500/20 to-cyan-500/20',
    border: 'border-blue-500/30',
    glow: 'shadow-blue-500/20'
  },
  loading: {
    icon: 'text-purple-400',
    gradient: 'from-purple-500/20 to-indigo-500/20',
    border: 'border-purple-500/30',
    glow: 'shadow-purple-500/20'
  },
  premium: {
    icon: 'text-yellow-400',
    gradient: 'from-yellow-500/20 to-purple-500/20',
    border: 'border-gradient-to-r border-yellow-500/30',
    glow: 'shadow-yellow-500/20'
  }
};

export const GlassToast: React.FC<GlassToastProps> = ({
  toast,
  onDismiss,
  position = 'top-right'
}) => {
  const [progress, setProgress] = useState(100);
  const Icon = toastIcons[toast.type];
  const colors = toastColors[toast.type];
  
  useEffect(() => {
    if (!toast.duration || toast.type === 'loading') return;
    
    const startTime = Date.now();
    const interval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const remaining = Math.max(0, toast.duration! - elapsed);
      const progressPercent = (remaining / toast.duration!) * 100;
      
      setProgress(progressPercent);
      
      if (progressPercent <= 0) {
        onDismiss(toast.id);
        clearInterval(interval);
      }
    }, 50);
    
    return () => clearInterval(interval);
  }, [toast.duration, toast.id, toast.type, onDismiss]);

  const toastVariants = {
    hidden: {
      opacity: 0,
      scale: 0.8,
      y: position.includes('top') ? -50 : 50,
      x: position.includes('right') ? 50 : position.includes('left') ? -50 : 0
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      x: 0,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 30,
        duration: 0.4
      }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      y: position.includes('top') ? -50 : 50,
      x: position.includes('right') ? 100 : position.includes('left') ? -100 : 0,
      transition: { duration: 0.2 }
    }
  };

  return (
    <motion.div
      variants={toastVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      layout
      className={cn(
        "relative overflow-hidden rounded-xl p-4 min-w-[320px] max-w-[420px]",
        "glass-strong shadow-glass-xl backdrop-blur-md",
        "border border-white/20",
        colors.border,
        colors.glow
      )}
    >
      {/* Background gradient */}
      <div className={cn(
        "absolute inset-0 bg-gradient-to-br opacity-50",
        colors.gradient
      )} />
      
      {/* Premium sparkle effect */}
      {toast.premium && (
        <motion.div
          animate={{
            opacity: [0.3, 0.8, 0.3],
            scale: [0.8, 1.2, 0.8]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full blur-sm"
        />
      )}
      
      {/* Content */}
      <div className="relative z-10 flex items-start gap-3">
        {/* Icon */}
        <motion.div
          animate={toast.type === 'loading' ? { rotate: 360 } : {}}
          transition={toast.type === 'loading' ? { 
            duration: 1, 
            repeat: Infinity, 
            ease: "linear" 
          } : {}}
          className="flex-shrink-0 mt-0.5"
        >
          <Icon className={cn("w-5 h-5", colors.icon)} />
        </motion.div>
        
        {/* Text content */}
        <div className="flex-1 min-w-0">
          <motion.h4
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="font-semibold text-sm text-foreground"
          >
            {toast.title}
          </motion.h4>
          
          {toast.description && (
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-xs text-muted-foreground mt-1"
            >
              {toast.description}
            </motion.p>
          )}
          
          {/* Action button */}
          {toast.action && (
            <motion.button
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={toast.action.onClick}
              className={cn(
                "mt-2 px-3 py-1 rounded-md text-xs font-medium",
                "glass-subtle hover:glass-medium transition-all duration-200",
                "border border-white/20 hover:border-white/30"
              )}
            >
              {toast.action.label}
            </motion.button>
          )}
        </div>
        
        {/* Dismiss button */}
        {toast.dismissible !== false && (
          <motion.button
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            whileHover={{ scale: 1.1, backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
            whileTap={{ scale: 0.9 }}
            onClick={() => onDismiss(toast.id)}
            className="flex-shrink-0 p-1 rounded-md transition-colors duration-200"
          >
            <X className="w-4 h-4 text-muted-foreground hover:text-foreground" />
          </motion.button>
        )}
      </div>
      
      {/* Progress bar */}
      {toast.duration && toast.type !== 'loading' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="absolute bottom-0 left-0 right-0 h-1 bg-white/10"
        >
          <motion.div
            initial={{ width: '100%' }}
            animate={{ width: `${progress}%` }}
            className={cn(
              "h-full bg-gradient-to-r",
              toast.type === 'success' ? 'from-green-400 to-emerald-400' :
              toast.type === 'error' ? 'from-red-400 to-pink-400' :
              toast.type === 'warning' ? 'from-yellow-400 to-orange-400' :
              toast.type === 'info' ? 'from-blue-400 to-cyan-400' :
              'from-purple-400 to-indigo-400'
            )}
          />
        </motion.div>
      )}
    </motion.div>
  );
};

// Toast container component
interface GlassToastContainerProps {
  toasts: ToastData[];
  onDismiss: (id: string) => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  maxToasts?: number;
}

const positionClasses = {
  'top-right': 'top-4 right-4 items-end',
  'top-left': 'top-4 left-4 items-start',
  'bottom-right': 'bottom-4 right-4 items-end',
  'bottom-left': 'bottom-4 left-4 items-start',
  'top-center': 'top-4 left-1/2 -translate-x-1/2 items-center',
  'bottom-center': 'bottom-4 left-1/2 -translate-x-1/2 items-center'
};

export const GlassToastContainer: React.FC<GlassToastContainerProps> = ({
  toasts,
  onDismiss,
  position = 'top-right',
  maxToasts = 5
}) => {
  const visibleToasts = toasts.slice(0, maxToasts);
  
  return (
    <div className={cn(
      "fixed z-[100] flex flex-col gap-2 pointer-events-none",
      positionClasses[position]
    )}>
      <AnimatePresence mode="popLayout">
        {visibleToasts.map((toast) => (
          <div key={toast.id} className="pointer-events-auto">
            <GlassToast
              toast={toast}
              onDismiss={onDismiss}
              position={position}
            />
          </div>
        ))}
      </AnimatePresence>
      
      {/* Overflow indicator */}
      {toasts.length > maxToasts && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="glass-subtle rounded-lg p-2 text-xs text-muted-foreground text-center pointer-events-auto"
        >
          +{toasts.length - maxToasts} more notification{toasts.length - maxToasts !== 1 ? 's' : ''}
        </motion.div>
      )}
    </div>
  );
};

// Hook for managing toasts
export const useGlassToast = () => {
  const [toasts, setToasts] = useState<ToastData[]>([]);
  
  const addToast = (toastData: Omit<ToastData, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const toast: ToastData = {
      id,
      duration: 5000,
      dismissible: true,
      ...toastData
    };
    
    setToasts(prev => [toast, ...prev]);
    return id;
  };
  
  const dismissToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };
  
  const dismissAll = () => {
    setToasts([]);
  };
  
  // Convenience methods
  const success = (title: string, description?: string, options?: Partial<ToastData>) => 
    addToast({ type: 'success', title, description, ...options });
    
  const error = (title: string, description?: string, options?: Partial<ToastData>) => 
    addToast({ type: 'error', title, description, ...options });
    
  const warning = (title: string, description?: string, options?: Partial<ToastData>) => 
    addToast({ type: 'warning', title, description, ...options });
    
  const info = (title: string, description?: string, options?: Partial<ToastData>) => 
    addToast({ type: 'info', title, description, ...options });
    
  const loading = (title: string, description?: string, options?: Partial<ToastData>) => 
    addToast({ type: 'loading', title, description, duration: 0, dismissible: false, ...options });
    
  const premium = (title: string, description?: string, options?: Partial<ToastData>) => 
    addToast({ type: 'premium', title, description, premium: true, ...options });
  
  return {
    toasts,
    addToast,
    dismissToast,
    dismissAll,
    success,
    error,
    warning,
    info,
    loading,
    premium
  };
};

export default GlassToast;