/**
 * Enhanced Loading States
 * 
 * Comprehensive loading components for different use cases
 */

import React from 'react';
import { motion } from 'framer-motion';
import { Loader2, Brain, Code, Users, Zap, GitBranch } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className 
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6', 
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  };

  return (
    <Loader2 className={cn('animate-spin', sizeClasses[size], className)} />
  );
};

interface SkeletonProps {
  className?: string;
  count?: number;
}

export const Skeleton: React.FC<SkeletonProps> = ({ className, count = 1 }) => {
  return (
    <>
      {Array.from({ length: count }).map((_, i) => (
        <div
          key={i}
          className={cn(
            'animate-pulse rounded-md bg-muted',
            className
          )}
        />
      ))}
    </>
  );
};

interface SessionLoadingProps {
  type: 'claude' | 'brainstorm' | 'orchestra' | 'workflow';
  message?: string;
  progress?: number;
}

export const SessionLoading: React.FC<SessionLoadingProps> = ({
  type,
  message,
  progress
}) => {
  const getIcon = () => {
    switch (type) {
      case 'claude': return Code;
      case 'brainstorm': return Brain;
      case 'orchestra': return Users;
      case 'workflow': return GitBranch;
      default: return Zap;
    }
  };

  const Icon = getIcon();

  const getDefaultMessage = () => {
    switch (type) {
      case 'claude': return 'Initializing Claude session...';
      case 'brainstorm': return 'Loading brainstorming session...';
      case 'orchestra': return 'Setting up agent orchestra...';
      case 'workflow': return 'Configuring cross-session workflow...';
      default: return 'Loading...';
    }
  };

  return (
    <div className="flex flex-col items-center justify-center p-8">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col items-center gap-4"
      >
        <div className="relative">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
            className="w-12 h-12 rounded-full border-2 border-primary/20 border-t-primary"
          />
          <div className="absolute inset-0 flex items-center justify-center">
            <Icon className="h-6 w-6 text-primary" />
          </div>
        </div>
        
        <div className="text-center space-y-2">
          <p className="font-medium">{message || getDefaultMessage()}</p>
          {progress !== undefined && (
            <div className="w-64">
              <div className="flex justify-between text-sm text-muted-foreground mb-1">
                <span>Progress</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <div className="w-full bg-secondary rounded-full h-2">
                <motion.div
                  className="bg-primary h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

interface TableSkeletonProps {
  rows?: number;
  columns?: number;
}

export const TableSkeleton: React.FC<TableSkeletonProps> = ({ 
  rows = 5, 
  columns = 4 
}) => {
  return (
    <div className="space-y-3">
      {/* Header */}
      <div className="flex gap-4">
        {Array.from({ length: columns }).map((_, i) => (
          <Skeleton key={i} className="h-4 flex-1" />
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex gap-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton key={colIndex} className="h-8 flex-1" />
          ))}
        </div>
      ))}
    </div>
  );
};

interface CardSkeletonProps {
  count?: number;
}

export const CardSkeleton: React.FC<CardSkeletonProps> = ({ count = 3 }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="p-4 border rounded-lg space-y-3">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-2/3" />
          <div className="flex gap-2">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-20" />
          </div>
        </div>
      ))}
    </div>
  );
};

interface MessageSkeletonProps {
  count?: number;
}

export const MessageSkeleton: React.FC<MessageSkeletonProps> = ({ count = 3 }) => {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="flex gap-3">
          <Skeleton className="h-8 w-8 rounded-full flex-shrink-0" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-3/4" />
          </div>
        </div>
      ))}
    </div>
  );
};

interface ProgressIndicatorProps {
  progress: number;
  steps: string[];
  currentStep: number;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  progress,
  steps,
  currentStep
}) => {
  return (
    <div className="w-full space-y-4">
      {/* Progress bar */}
      <div className="w-full bg-secondary rounded-full h-2">
        <motion.div
          className="bg-primary h-2 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.5 }}
        />
      </div>
      
      {/* Steps */}
      <div className="flex justify-between">
        {steps.map((step, index) => (
          <div
            key={index}
            className={cn(
              'flex flex-col items-center text-xs',
              index < currentStep ? 'text-primary' :
              index === currentStep ? 'text-foreground' :
              'text-muted-foreground'
            )}
          >
            <div
              className={cn(
                'w-3 h-3 rounded-full mb-1',
                index < currentStep ? 'bg-primary' :
                index === currentStep ? 'bg-primary animate-pulse' :
                'bg-muted'
              )}
            />
            <span className="text-center max-w-16 leading-tight">{step}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default {
  LoadingSpinner,
  Skeleton,
  SessionLoading,
  TableSkeleton,
  CardSkeleton,
  MessageSkeleton,
  ProgressIndicator
};