import React, { useState, useCallback } from "react";
import { X, FileText, FileCode, Save, Circle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Tabs,
  Ta<PERSON><PERSON>ontent,
  Ta<PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";

export interface FileTab {
  id: string;
  path: string;
  name: string;
  content?: string;
  language?: string;
  isDirty?: boolean;
  isActive?: boolean;
}

interface FileTabsProps {
  tabs: FileTab[];
  activeTabId?: string;
  onTabChange?: (tabId: string) => void;
  onTabClose?: (tabId: string) => void;
  onTabCloseAll?: () => void;
  onTabCloseOthers?: (tabId: string) => void;
  onTabSave?: (tabId: string) => void;
  onTabSaveAll?: () => void;
  className?: string;
  renderContent?: (tab: FileTab) => React.ReactNode;
}

// Get file icon based on extension
const getFileIcon = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase();
  
  switch (ext) {
    case "ts":
    case "tsx":
    case "js":
    case "jsx":
    case "py":
    case "java":
    case "cpp":
    case "c":
    case "cs":
    case "go":
    case "rs":
      return FileCode;
    default:
      return FileText;
  }
};

export const FileTabs: React.FC<FileTabsProps> = ({
  tabs,
  activeTabId,
  onTabChange,
  onTabClose,
  onTabCloseAll,
  onTabCloseOthers,
  onTabSave,
  onTabSaveAll,
  className,
  renderContent
}) => {
  const [hoveredTab, setHoveredTab] = useState<string | null>(null);

  const handleTabClose = useCallback((e: React.MouseEvent, tabId: string) => {
    e.stopPropagation();
    onTabClose?.(tabId);
  }, [onTabClose]);

  const handleSaveShortcut = useCallback((e: KeyboardEvent) => {
    if ((e.metaKey || e.ctrlKey) && e.key === 's') {
      e.preventDefault();
      if (e.shiftKey) {
        onTabSaveAll?.();
      } else if (activeTabId) {
        onTabSave?.(activeTabId);
      }
    }
  }, [activeTabId, onTabSave, onTabSaveAll]);

  React.useEffect(() => {
    window.addEventListener('keydown', handleSaveShortcut);
    return () => window.removeEventListener('keydown', handleSaveShortcut);
  }, [handleSaveShortcut]);

  if (tabs.length === 0) {
    return (
      <div className={cn("flex items-center justify-center h-full text-muted-foreground", className)}>
        <p className="text-sm">No files open</p>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full", className)}>
      <div className="border-b">
        <ScrollArea className="w-full">
          <div className="flex items-center">
            <TabsList className="h-10 p-0 bg-transparent rounded-none border-none">
              {tabs.map((tab) => {
                const Icon = getFileIcon(tab.name);
                const isActive = tab.id === activeTabId;
                
                return (
                  <ContextMenuTrigger key={tab.id}>
                    <TabsTrigger
                      value={tab.id}
                      onClick={() => onTabChange?.(tab.id)}
                      onMouseEnter={() => setHoveredTab(tab.id)}
                      onMouseLeave={() => setHoveredTab(null)}
                      className={cn(
                        "relative flex items-center gap-2 px-3 h-10 border-r",
                        "data-[state=active]:bg-background",
                        "data-[state=active]:border-b-2 data-[state=active]:border-b-primary",
                        "hover:bg-accent/50",
                        isActive && "bg-background"
                      )}
                    >
                      <Icon className="h-4 w-4 flex-shrink-0" />
                      <span className="text-sm truncate max-w-[120px]">
                        {tab.name}
                      </span>
                      {tab.isDirty && (
                        <Circle className="h-2 w-2 fill-current" />
                      )}
                      {(isActive || hoveredTab === tab.id) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="ml-1 h-4 w-4 p-0 hover:bg-accent"
                          onClick={(e) => handleTabClose(e, tab.id)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </TabsTrigger>
                    
                    <ContextMenu>
                      <ContextMenuContent>
                        <ContextMenuItem onClick={() => onTabSave?.(tab.id)}>
                          <Save className="h-4 w-4 mr-2" />
                          Save
                        </ContextMenuItem>
                        <ContextMenuItem onClick={() => onTabSaveAll?.()}>
                          <Save className="h-4 w-4 mr-2" />
                          Save All
                        </ContextMenuItem>
                        <ContextMenuSeparator />
                        <ContextMenuItem onClick={() => onTabClose?.(tab.id)}>
                          <X className="h-4 w-4 mr-2" />
                          Close
                        </ContextMenuItem>
                        <ContextMenuItem onClick={() => onTabCloseOthers?.(tab.id)}>
                          <X className="h-4 w-4 mr-2" />
                          Close Others
                        </ContextMenuItem>
                        <ContextMenuItem onClick={() => onTabCloseAll?.()}>
                          <X className="h-4 w-4 mr-2" />
                          Close All
                        </ContextMenuItem>
                      </ContextMenuContent>
                    </ContextMenu>
                  </ContextMenuTrigger>
                );
              })}
            </TabsList>
            <ScrollBar orientation="horizontal" />
          </div>
        </ScrollArea>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {activeTabId && renderContent && (
          <Tabs value={activeTabId} className="h-full">
            {tabs.map((tab) => (
              <TabsContent
                key={tab.id}
                value={tab.id}
                className="h-full m-0 border-0 p-0 data-[state=inactive]:hidden"
              >
                {renderContent(tab)}
              </TabsContent>
            ))}
          </Tabs>
        )}
      </div>
    </div>
  );
};

export default FileTabs;