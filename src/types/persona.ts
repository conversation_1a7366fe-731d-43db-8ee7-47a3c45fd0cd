/**
 * Persona Management Types
 * 
 * Comprehensive type definitions for brainstorming personas
 */

export interface PersonaCharacteristics {
  creativity: number; // 0-100
  analytical: number; // 0-100
  critical: number; // 0-100
  optimistic: number; // 0-100
  practical: number; // 0-100
  innovative: number; // 0-100
  collaborative: number; // 0-100
  detail_oriented: number; // 0-100
}

export interface PersonaPromptStyle {
  questionTypes: string[];
  responsePatterns: string[];
  encouragementPhrases: string[];
  challengingPhrases: string[];
  transitionWords: string[];
}

export interface PersonaMetrics {
  totalContributions: number;
  ideasGenerated: number;
  questionsAsked: number;
  challengesMade: number;
  collaborations: number;
  sessionParticipation: number;
  averageRating: number;
  lastUsed: string;
}

export interface BrainstormingPersona {
  id: string;
  name: string;
  description: string;
  role: PersonaRole;
  characteristics: PersonaCharacteristics;
  promptStyle: PersonaPromptStyle;
  avatar?: string;
  color: string;
  isActive: boolean;
  isCustom: boolean;
  createdAt: string;
  updatedAt: string;
  metrics: PersonaMetrics;
  tags: string[];
  examples: string[];
}

export enum PersonaRole {
  CREATIVE_THINKER = 'creative_thinker',
  DEVILS_ADVOCATE = 'devils_advocate',
  PRACTICAL_ANALYST = 'practical_analyst',
  VISIONARY = 'visionary',
  FACILITATOR = 'facilitator',
  RESEARCHER = 'researcher',
  IMPLEMENTER = 'implementer',
  CUSTOMER_ADVOCATE = 'customer_advocate',
  RISK_ASSESSOR = 'risk_assessor',
  INNOVATOR = 'innovator',
  SYNTHESIZER = 'synthesizer',
  QUESTIONER = 'questioner'
}

export interface PersonaTemplate {
  role: PersonaRole;
  name: string;
  description: string;
  characteristics: PersonaCharacteristics;
  promptStyle: PersonaPromptStyle;
  color: string;
  examples: string[];
  tags: string[];
}

export interface PersonaContext {
  sessionId: string;
  currentIdeas: string[];
  recentMessages: string[];
  sessionGoals: string[];
  timeElapsed: number;
  participantCount: number;
  sessionPhase: 'ideation' | 'evaluation' | 'refinement' | 'conclusion';
}

export interface PersonaResponse {
  content: string;
  type: 'idea' | 'question' | 'challenge' | 'encouragement' | 'synthesis';
  confidence: number;
  reasoning: string;
  suggestedFollowUps: string[];
  relatedIdeas: string[];
}

export interface PersonaInteraction {
  id: string;
  personaId: string;
  sessionId: string;
  type: 'prompt' | 'response' | 'question' | 'challenge';
  content: string;
  context: PersonaContext;
  response?: PersonaResponse;
  timestamp: string;
  rating?: number;
  feedback?: string;
}

export interface PersonaAnalytics {
  personaId: string;
  timeframe: 'day' | 'week' | 'month' | 'all';
  metrics: {
    usage_frequency: number;
    idea_generation_rate: number;
    collaboration_score: number;
    effectiveness_rating: number;
    session_impact: number;
    user_satisfaction: number;
  };
  trends: {
    usage_over_time: Array<{ date: string; count: number }>;
    effectiveness_over_time: Array<{ date: string; score: number }>;
    idea_quality_trend: Array<{ date: string; quality: number }>;
  };
  comparisons: {
    vs_other_personas: Record<string, number>;
    vs_baseline: number;
  };
}

export interface PersonaRecommendation {
  personaId: string;
  reason: string;
  confidence: number;
  context: string;
  expectedBenefit: string;
}

export interface PersonaManagerState {
  personas: Record<string, BrainstormingPersona>;
  activePersonaId: string | null;
  templates: PersonaTemplate[];
  interactions: PersonaInteraction[];
  analytics: Record<string, PersonaAnalytics>;
  settings: {
    autoSuggestPersonas: boolean;
    rotatePersonas: boolean;
    trackAnalytics: boolean;
    showPersonaInsights: boolean;
  };
}

// Persona prompt templates
export const PERSONA_PROMPT_TEMPLATES = {
  [PersonaRole.CREATIVE_THINKER]: {
    ideation: [
      "What if we approached this from a completely different angle?",
      "Let's think outside the box - what's the wildest idea we could explore?",
      "How might we combine unexpected elements to create something new?",
      "What would this look like if we had unlimited resources?"
    ],
    encouragement: [
      "That's a fascinating direction! Let's explore it further.",
      "I love the creativity in that idea - how can we build on it?",
      "What an innovative approach! What inspired that thinking?"
    ]
  },
  [PersonaRole.DEVILS_ADVOCATE]: {
    challenges: [
      "What could go wrong with this approach?",
      "Have we considered the potential downsides?",
      "What assumptions are we making that might not be true?",
      "How would our critics respond to this idea?"
    ],
    questions: [
      "Is this really solving the core problem?",
      "What evidence do we have that this will work?",
      "Are we being realistic about the constraints?"
    ]
  },
  [PersonaRole.PRACTICAL_ANALYST]: {
    analysis: [
      "Let's break this down into actionable steps.",
      "What resources would we need to implement this?",
      "How does this align with our current capabilities?",
      "What's the timeline and budget for this approach?"
    ],
    evaluation: [
      "Which of these ideas is most feasible?",
      "What are the key metrics we should track?",
      "How can we test this hypothesis quickly?"
    ]
  }
};

// Default persona characteristics profiles
export const DEFAULT_PERSONA_PROFILES: Record<PersonaRole, PersonaCharacteristics> = {
  [PersonaRole.CREATIVE_THINKER]: {
    creativity: 95,
    analytical: 40,
    critical: 30,
    optimistic: 85,
    practical: 35,
    innovative: 90,
    collaborative: 75,
    detail_oriented: 45
  },
  [PersonaRole.DEVILS_ADVOCATE]: {
    creativity: 60,
    analytical: 85,
    critical: 95,
    optimistic: 25,
    practical: 80,
    innovative: 50,
    collaborative: 60,
    detail_oriented: 90
  },
  [PersonaRole.PRACTICAL_ANALYST]: {
    creativity: 45,
    analytical: 95,
    critical: 70,
    optimistic: 60,
    practical: 95,
    innovative: 40,
    collaborative: 70,
    detail_oriented: 90
  },
  [PersonaRole.VISIONARY]: {
    creativity: 90,
    analytical: 70,
    critical: 40,
    optimistic: 95,
    practical: 30,
    innovative: 95,
    collaborative: 80,
    detail_oriented: 40
  },
  [PersonaRole.FACILITATOR]: {
    creativity: 70,
    analytical: 75,
    critical: 60,
    optimistic: 80,
    practical: 75,
    innovative: 65,
    collaborative: 95,
    detail_oriented: 70
  },
  [PersonaRole.RESEARCHER]: {
    creativity: 60,
    analytical: 95,
    critical: 80,
    optimistic: 65,
    practical: 85,
    innovative: 70,
    collaborative: 75,
    detail_oriented: 95
  },
  [PersonaRole.IMPLEMENTER]: {
    creativity: 50,
    analytical: 80,
    critical: 70,
    optimistic: 70,
    practical: 95,
    innovative: 55,
    collaborative: 85,
    detail_oriented: 90
  },
  [PersonaRole.CUSTOMER_ADVOCATE]: {
    creativity: 70,
    analytical: 75,
    critical: 80,
    optimistic: 75,
    practical: 80,
    innovative: 65,
    collaborative: 90,
    detail_oriented: 75
  },
  [PersonaRole.RISK_ASSESSOR]: {
    creativity: 40,
    analytical: 90,
    critical: 95,
    optimistic: 35,
    practical: 90,
    innovative: 45,
    collaborative: 70,
    detail_oriented: 95
  },
  [PersonaRole.INNOVATOR]: {
    creativity: 95,
    analytical: 75,
    critical: 50,
    optimistic: 85,
    practical: 45,
    innovative: 95,
    collaborative: 75,
    detail_oriented: 60
  },
  [PersonaRole.SYNTHESIZER]: {
    creativity: 75,
    analytical: 85,
    critical: 65,
    optimistic: 75,
    practical: 70,
    innovative: 70,
    collaborative: 90,
    detail_oriented: 80
  },
  [PersonaRole.QUESTIONER]: {
    creativity: 80,
    analytical: 85,
    critical: 85,
    optimistic: 60,
    practical: 65,
    innovative: 75,
    collaborative: 80,
    detail_oriented: 75
  }
};
