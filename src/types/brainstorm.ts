/**
 * Enhanced Brainstorming System Data Models
 * 
 * This file contains all the TypeScript interfaces and types for the enhanced
 * brainstorming system, extending the existing chat-based brainstorming with
 * visualization, organization, and project management capabilities.
 */

// Re-export existing API types for compatibility
export type { BrainstormMessage } from '@/lib/brainstorm-api';

// Enums for better type safety
export enum IdeaStatus {
  TO_EXPLORE = 'to_explore',
  IN_PROGRESS = 'in_progress', 
  VALIDATED = 'validated',
  ARCHIVED = 'archived'
}

export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum TemplateType {
  SWOT = 'swot',
  FIVE_WHYS = 'five-whys',
  DESIGN_THINKING = 'design-thinking',
  CANVAS = 'canvas',
  CUSTOM = 'custom'
}

export enum VisualizationType {
  MIND_MAP = 'mind_map',
  KANBAN = 'kanban',
  MATRIX = 'matrix',
  TIMELINE = 'timeline',
  RELATIONSHIP = 'relationship'
}

export enum ViewType {
  CHAT = 'chat',
  MIND_MAP = 'mindmap',
  KANBAN = 'kanban',
  MATRIX = 'matrix',
  TEMPLATES = 'templates'
}

// Core data models
export interface Idea {
  id: string;
  content: string;
  messageId: string;
  status: IdeaStatus;
  tags: string[];
  cluster?: string;
  priority?: Priority;
  connections: string[]; // IDs of related ideas
  position?: {
    x: number;
    y: number;
  };
  impact?: number; // 1-10 scale for prioritization matrix
  effort?: number; // 1-10 scale for prioritization matrix
  attachments?: string[]; // Array of attachment IDs
  metadata?: {
    description?: string;
    subtasks?: string[];
    taskGenerated?: boolean;
    matrixQuadrant?: string;
    [key: string]: any;
  };
  createdAt: string;
  updatedAt: string;
}

export interface IdeaRelationship {
  id: string;
  sourceId: string;
  targetId: string;
  type: 'related' | 'depends-on' | 'conflicts-with' | 'enhances' | 'strong' | 'weak';
  label?: string;
  createdAt: string;
}

export interface IdeaCluster {
  id: string;
  name: string;
  theme: string;
  ideaIds: string[];
  color: string;
  createdAt: string;
  updatedAt: string;
}

export interface PersistentMemory {
  id: string;
  content: string;
  context: string;
  tags: string[];
  sessionId: string;
  importance: number; // 1-10 scale
  createdAt: string;
  lastUsed?: string;
}

export interface SessionMetadata {
  totalIdeas: number;
  totalClusters: number;
  lastActivity: string;
  template?: TemplateType;
  personas: string[]; // Track which personas were used
  exportHistory: ExportRecord[];
}

export interface ExportRecord {
  id: string;
  format: string;
  timestamp: string;
  itemCount: number;
}

// Enhanced session model extending existing chat messages
export interface BrainstormSession {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  messages: ChatMessage[]; // Using existing ChatMessage interface
  ideas: Idea[];
  tags: string[];
  template?: TemplateType;
  metadata: SessionMetadata;
}

// Chat message interface (extending existing)
export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  status?: 'pending' | 'success' | 'error';
  suggestions?: string[];
  choices?: BrainstormingChoice[];
  extractedIdeas?: string[]; // IDs of ideas extracted from this message
  persona?: string; // Which AI persona generated this message
}

export interface BrainstormingChoice {
  id: string;
  title: string;
  description: string;
  prompt: string;
  icon: React.ComponentType<any>;
  category: 'idea' | 'prd' | 'analysis' | 'strategy';
}

// Template system
export interface BrainstormTemplate {
  id: string;
  name: string;
  description: string;
  type: TemplateType;
  structure: TemplateStructure;
  prompts: TemplatePrompt[];
  visualization: VisualizationType;
  isCustom: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TemplateStructure {
  sections: TemplateSection[];
  layout: 'grid' | 'linear' | 'radial';
}

export interface TemplateSection {
  id: string;
  title: string;
  description: string;
  required: boolean;
  inputType: 'text' | 'list' | 'rating' | 'choice';
  options?: string[]; // For choice type
  placeholder?: string;
}

export interface TemplatePrompt {
  id: string;
  sectionId: string;
  text: string;
  order: number;
}

// AI Persona system
export interface AIPersona {
  id: string;
  name: string;
  description: string;
  systemPrompt: string;
  icon: string;
  color: string;
  isActive: boolean;
}

// Visualization components
export interface MindMapNode {
  id: string;
  label: string;
  ideaId: string;
  level: number;
  color: string;
  size: number;
  position: {
    x: number;
    y: number;
  };
}

export interface MindMapEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  strength: number; // Connection strength 0-1
}

export interface KanbanColumn {
  id: string;
  title: string;
  status: IdeaStatus;
  color: string;
  order: number;
  limit?: number; // WIP limit
}

export interface MatrixQuadrant {
  id: string;
  label: string;
  color: string;
  recommendation: string;
  bounds: {
    minImpact: number;
    maxImpact: number;
    minEffort: number;
    maxEffort: number;
  };
}

// Project management
export interface GeneratedTask {
  id: string;
  title: string;
  description: string;
  ideaId: string;
  priority: Priority;
  estimatedEffort: number; // hours
  dependencies: string[];
  tags: string[];
  status: 'todo' | 'in_progress' | 'done';
  assignee?: string;
  dueDate?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProjectTimeline {
  id: string;
  name: string;
  tasks: GeneratedTask[];
  milestones: Milestone[];
  startDate: string;
  endDate: string;
  resources: ResourceRequirement[];
}

export interface Milestone {
  id: string;
  title: string;
  description: string;
  date: string;
  dependencies: string[];
  completed: boolean;
}

export interface ResourceRequirement {
  id: string;
  type: 'person' | 'tool' | 'budget';
  name: string;
  quantity: number;
  unit: string;
  cost?: number;
  availability?: string;
}

// Export and integration
export interface ExportFormat {
  id: string;
  name: string;
  extension: string;
  mimeType: string;
  supports: ('ideas' | 'tasks' | 'timeline' | 'mindmap' | 'session')[];
}

export interface IntegrationConfig {
  id: string;
  name: string;
  type: 'jira' | 'asana' | 'trello' | 'github' | 'csv';
  apiKey?: string;
  baseUrl?: string;
  projectId?: string;
  enabled: boolean;
  lastSync?: string;
}

// Search and filtering
export interface SearchQuery {
  text?: string;
  tags?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  status?: IdeaStatus[];
  priority?: Priority[];
  sessionIds?: string[];
}

export interface SearchResult {
  id: string;
  type: 'idea' | 'message' | 'memory' | 'session';
  title: string;
  content: string;
  context: string;
  relevance: number;
  highlights: string[];
  sessionId: string;
  timestamp: string;
}

// Error handling
export interface BrainstormError {
  id: string;
  type: 'visualization' | 'clustering' | 'export' | 'voice' | 'research' | 'memory' | 'template';
  message: string;
  details?: any;
  timestamp: string;
  resolved: boolean;
}

// Settings and preferences
export interface BrainstormSettings {
  defaultView: ViewType;
  autoCluster: boolean;
  clusterThreshold: number;
  autoExtractIdeas: boolean;
  voiceEnabled: boolean;
  voiceLanguage: string;
  exportFormat: string;
  maxMemories: number;
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    clustering: boolean;
    exports: boolean;
    memories: boolean;
  };
}