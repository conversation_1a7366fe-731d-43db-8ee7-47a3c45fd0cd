/**
 * Unified Session Types
 * 
 * Common types and interfaces for the Master Session Coordinator
 * that unify all session types across the application.
 */

import type { SessionData } from './session';
import type { BrainstormSession } from './brainstorm';

// Re-export core types from the coordinator
export type {
  SessionType,
  BaseSessionInfo,
  ClaudeSessionInfo,
  BrainstormSessionInfo,
  OrchestraSessionInfo,
  UnifiedSessionInfo,
  SessionLifecycleEvent,
  SessionEventCallback,
  SessionRelationshipType,
  SessionRelationship,
  CrossSessionData
} from '../lib/master-session-coordinator';

// Additional unified types for the application

/**
 * Session creation options for different session types
 */
export interface CreateSessionOptions {
  type: 'claude' | 'brainstorm' | 'orchestra';
  title?: string;
  projectPath?: string;
  parentSessionId?: string;
  initialData?: {
    prompt?: string;
    template?: string;
    ideas?: string[];
    agentIds?: string[];
  };
}

/**
 * Session transition options for moving between session types
 */
export interface SessionTransitionOptions {
  sourceSessionId: string;
  targetSessionType: 'claude' | 'brainstorm' | 'orchestra';
  transitionType: 'spawn' | 'fork' | 'merge';
  dataToTransfer?: {
    ideas?: string[];
    code?: string;
    context?: string;
    tasks?: any[];
  };
  preserveSource?: boolean;
}

/**
 * Session context for components that need session information
 */
export interface SessionContextValue {
  // Current session state
  activeSession: UnifiedSessionInfo | null;
  allSessions: UnifiedSessionInfo[];
  sessionHistory: string[];
  
  // Session actions
  createSession: (options: CreateSessionOptions) => Promise<UnifiedSessionInfo>;
  activateSession: (sessionId: string) => Promise<void>;
  pauseSession: (sessionId: string) => Promise<void>;
  resumeSession: (sessionId: string) => Promise<void>;
  terminateSession: (sessionId: string) => Promise<void>;
  
  // Session relationships
  createRelationship: (parentId: string, childId: string, type: SessionRelationshipType) => void;
  getRelationships: (sessionId: string) => SessionRelationship[];
  
  // Cross-session data sharing
  shareData: (sourceId: string, targetId: string, dataType: string, data: any) => void;
  
  // Session transitions
  transitionSession: (options: SessionTransitionOptions) => Promise<UnifiedSessionInfo>;
  
  // Event subscription
  subscribe: (callback: SessionEventCallback) => () => void;
}

/**
 * Session metadata for UI components
 */
export interface SessionUIMetadata {
  id: string;
  type: 'claude' | 'brainstorm' | 'orchestra';
  title: string;
  subtitle?: string;
  icon: string;
  status: 'active' | 'idle' | 'paused' | 'terminated';
  lastActivity: string;
  hasUnsavedChanges?: boolean;
  parentSessionId?: string;
  childCount: number;
}

/**
 * Session navigation state for tab management
 */
export interface SessionNavigation {
  currentSessionId: string | null;
  sessionStack: string[]; // For back/forward navigation
  pinnedSessions: string[];
  recentSessions: string[];
}

/**
 * Unified session settings that apply across all session types
 */
export interface UnifiedSessionSettings {
  // Auto-save settings
  autoSave: boolean;
  autoSaveInterval: number; // minutes
  
  // Session management
  maxConcurrentSessions: number;
  sessionTimeout: number; // minutes of inactivity
  preserveSessionsOnClose: boolean;
  
  // Cross-session features
  enableCrossSessionSharing: boolean;
  autoCreateRelationships: boolean;
  
  // UI preferences
  showSessionRelationships: boolean;
  groupSessionsByProject: boolean;
  showSessionPreviews: boolean;
  
  // Notifications
  notifyOnSessionCreated: boolean;
  notifyOnSessionShared: boolean;
  notifyOnSessionErrors: boolean;
}

/**
 * Session export/import formats
 */
export interface SessionExportData {
  version: string;
  exportedAt: string;
  sessions: UnifiedSessionInfo[];
  relationships: SessionRelationship[];
  settings: UnifiedSessionSettings;
}

/**
 * Session template for creating pre-configured sessions
 */
export interface SessionTemplate {
  id: string;
  name: string;
  description: string;
  sessionType: 'claude' | 'brainstorm' | 'orchestra';
  defaultSettings: Partial<CreateSessionOptions>;
  includeRelationships?: {
    type: 'claude' | 'brainstorm' | 'orchestra';
    relationship: SessionRelationshipType;
  }[];
  tags: string[];
  isCustom: boolean;
  createdAt: string;
}

/**
 * Session search and filtering
 */
export interface SessionSearchQuery {
  text?: string;
  type?: ('claude' | 'brainstorm' | 'orchestra')[];
  status?: ('active' | 'idle' | 'paused' | 'terminated')[];
  dateRange?: {
    start: string;
    end: string;
  };
  projectPath?: string;
  hasRelationships?: boolean;
  tags?: string[];
}

export interface SessionSearchResult {
  session: UnifiedSessionInfo;
  relevance: number;
  matchingFields: string[];
  highlights: string[];
}

/**
 * Session backup and recovery
 */
export interface SessionBackup {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  sessionIds: string[];
  data: SessionExportData;
  size: number; // bytes
  compressed: boolean;
}

/**
 * Error types for session management
 */
export interface SessionError {
  id: string;
  sessionId?: string;
  type: 'creation' | 'activation' | 'termination' | 'data_sharing' | 'persistence' | 'relationship';
  message: string;
  details?: any;
  timestamp: string;
  resolved: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}