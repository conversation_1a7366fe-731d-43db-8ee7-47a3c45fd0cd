import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { vi } from 'vitest';

// Mock providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: AllTheProviders, ...options });

// Mock data generators
export const mockIdea = (overrides = {}) => ({
  id: 'idea-1',
  content: 'Test idea content',
  sessionId: 'session-1',
  status: 'active' as const,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  tags: [],
  priority: 'medium' as const,
  linkedIdeas: [],
  ...overrides,
});

export const mockSession = (overrides = {}) => ({
  id: 'session-1',
  title: 'Test Session',
  description: 'Test session description',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  participants: ['user-1'],
  status: 'active' as const,
  tags: [],
  ...overrides,
});

export const mockCluster = (overrides = {}) => ({
  id: 'cluster-1',
  name: 'Test Cluster',
  theme: 'Test theme',
  ideas: ['idea-1', 'idea-2'],
  color: '#3B82F6',
  sessionId: 'session-1',
  createdAt: new Date().toISOString(),
  ...overrides,
});

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };
export { vi };