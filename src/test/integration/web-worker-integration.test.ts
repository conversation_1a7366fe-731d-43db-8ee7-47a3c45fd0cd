/**
 * Integration Tests for Web Workers
 * 
 * Tests the integration of web workers with the main application
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { workerManager } from '@/lib/worker-manager';
import { enhancedIdeaExtractor } from '@/services/EnhancedIdeaExtractor';
import { clusteringService } from '@/lib/clustering-service';
import type { Idea, Message } from '@/types/brainstorm';

// Mock Worker global
class MockWorker {
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: ErrorEvent) => void) | null = null;
  
  constructor(public url: string, public options?: WorkerOptions) {}
  
  postMessage(data: any) {
    // Simulate async worker response
    setTimeout(() => {
      if (this.onmessage) {
        this.onmessage(new MessageEvent('message', {
          data: this.generateMockResponse(data)
        }));
      }
    }, 10);
  }
  
  terminate() {}
  
  addEventListener(type: string, listener: any) {
    if (type === 'message') this.onmessage = listener;
    if (type === 'error') this.onerror = listener;
  }
  
  private generateMockResponse(request: any) {
    const { taskId, type } = request;
    
    if (type === 'cluster') {
      return {
        taskId,
        type: 'success',
        clusters: [
          {
            id: 'cluster_1',
            name: 'Feature Requests',
            ideaIds: ['idea_1', 'idea_2'],
            quality: 0.85
          },
          {
            id: 'cluster_2',
            name: 'Bug Fixes',
            ideaIds: ['idea_3', 'idea_4'],
            quality: 0.78
          }
        ],
        stats: {
          totalIdeas: 4,
          clusterCount: 2,
          avgClusterSize: 2,
          avgQuality: 0.815
        }
      };
    }
    
    if (type === 'extract') {
      return {
        taskId,
        type: 'success',
        ideas: [
          {
            id: 'idea_extracted_1',
            sessionId: request.sessionId,
            content: 'Implement user authentication',
            status: 'active',
            priority: 'high',
            tags: ['security', 'feature'],
            metadata: {
              confidence: 0.9,
              source: 'auto-extraction'
            }
          }
        ],
        stats: {
          messagesProcessed: 1,
          ideasExtracted: 1,
          averageConfidence: 0.9
        }
      };
    }
    
    return { taskId, type: 'error', error: 'Unknown request type' };
  }
}

// @ts-ignore - Mock Worker in global scope
global.Worker = MockWorker;

describe('Web Worker Integration', () => {
  beforeEach(() => {
    // Ensure worker manager is initialized
    workerManager.terminate();
  });

  afterEach(() => {
    // Clean up workers
    workerManager.terminate();
  });

  describe('Worker Manager', () => {
    it('should initialize workers on first use', async () => {
      const status = workerManager.getStatus();
      expect(status.initialized).toBe(false);
      
      // Trigger initialization
      const ideas = generateMockIdeas(5);
      await workerManager.clusterIdeas(ideas);
      
      const newStatus = workerManager.getStatus();
      expect(newStatus.initialized).toBe(true);
      expect(newStatus.workers.clustering).toBe(true);
      expect(newStatus.workers.extraction).toBe(true);
    });

    it('should handle multiple concurrent tasks', async () => {
      const ideas = generateMockIdeas(10);
      const messages = generateMockMessages(5);
      
      // Launch multiple tasks concurrently
      const [clusterResult, extractResult] = await Promise.all([
        workerManager.clusterIdeas(ideas),
        workerManager.extractIdeas(messages, 'test_session')
      ]);
      
      expect(clusterResult.clusters).toHaveLength(2);
      expect(extractResult.ideas).toHaveLength(1);
    });

    it('should handle worker errors gracefully', async () => {
      // Force an error by terminating workers
      await workerManager.initialize();
      workerManager.terminate();
      
      // Try to use terminated worker
      const ideas = generateMockIdeas(5);
      await expect(workerManager.clusterIdeas(ideas)).rejects.toThrow();
    });

    it('should timeout long-running tasks', async () => {
      // Mock a worker that never responds
      const slowWorker = new MockWorker('slow.js');
      slowWorker.postMessage = () => {}; // Never respond
      
      // @ts-ignore
      global.Worker = function() { return slowWorker; };
      
      const ideas = generateMockIdeas(100);
      await expect(
        workerManager.clusterIdeas(ideas)
      ).rejects.toThrow('timed out');
    }, 35000); // Longer timeout for this test
  });

  describe('Enhanced Idea Extractor', () => {
    it('should use web worker by default', async () => {
      const messages = generateMockMessages(10);
      
      const ideas = await enhancedIdeaExtractor.extractIdeas(
        messages,
        'test_session'
      );
      
      expect(ideas).toHaveLength(1);
      expect(ideas[0].metadata?.source).toBe('auto-extraction');
    });

    it('should fall back to main thread when worker fails', async () => {
      // Disable web workers
      enhancedIdeaExtractor.setWebWorkerEnabled(false);
      
      const messages = generateMockMessages(5);
      const ideas = await enhancedIdeaExtractor.extractIdeas(
        messages,
        'test_session'
      );
      
      // Should still extract ideas on main thread
      expect(ideas.length).toBeGreaterThan(0);
    });

    it('should report progress during batch extraction', async () => {
      const messages = generateMockMessages(50);
      const progressUpdates: any[] = [];
      
      await enhancedIdeaExtractor.batchExtractIdeas(
        messages,
        'test_session',
        {},
        (progress) => {
          progressUpdates.push(progress);
        }
      );
      
      expect(progressUpdates.length).toBeGreaterThan(0);
      expect(progressUpdates[progressUpdates.length - 1].processed).toBe(50);
    });

    it('should analyze text for idea potential', async () => {
      const text = `
        We should implement OAuth authentication for better security.
        Consider adding real-time notifications to improve user engagement.
        The current database queries are slow and need optimization.
      `;
      
      const analysis = await enhancedIdeaExtractor.analyzeText(text);
      
      expect(analysis.ideaCount).toBeGreaterThan(0);
      expect(analysis.actionableRatio).toBeGreaterThan(0.5);
      expect(analysis.keywords).toContain('authentication');
    });
  });

  describe('Clustering Service', () => {
    it('should cluster ideas using web worker', async () => {
      const ideas = generateMockIdeas(20);
      
      const clusters = await clusteringService.suggestClusters(ideas);
      
      expect(clusters).toHaveLength(2);
      expect(clusters[0].name).toBe('Feature Requests');
      expect(clusters[0].confidence).toBeGreaterThan(0.7);
    });

    it('should optimize cluster count', async () => {
      const ideas = generateMockIdeas(50);
      
      const result = await clusteringService.optimizeClusters(ideas);
      
      expect(result.optimalK).toBeGreaterThan(0);
      expect(result.clusters.length).toBe(result.optimalK);
      expect(result.analysis.length).toBeGreaterThan(0);
    });

    it('should handle algorithm selection', async () => {
      const ideas = generateMockIdeas(30);
      
      // Test different algorithms
      const kmeansResult = await clusteringService.suggestClusters(ideas, {
        algorithm: 'kmeans'
      });
      
      const hierarchicalResult = await clusteringService.suggestClusters(ideas, {
        algorithm: 'hierarchical'
      });
      
      expect(kmeansResult).toBeDefined();
      expect(hierarchicalResult).toBeDefined();
    });

    it('should maintain performance with large datasets', async () => {
      const largeIdeasSet = generateMockIdeas(1000);
      
      const startTime = performance.now();
      const clusters = await clusteringService.suggestClusters(largeIdeasSet, {
        maxClusters: 20
      });
      const endTime = performance.now();
      
      expect(clusters.length).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete in < 5s
    });
  });

  describe('Worker Status and Management', () => {
    it('should track pending tasks', async () => {
      const ideas = generateMockIdeas(10);
      
      // Start a task
      const promise = workerManager.clusterIdeas(ideas);
      
      // Check status while task is pending
      const status = workerManager.getStatus();
      expect(status.pendingTasks).toBeGreaterThan(0);
      
      // Wait for completion
      await promise;
      
      // Check status after completion
      const finalStatus = workerManager.getStatus();
      expect(finalStatus.pendingTasks).toBe(0);
    });

    it('should handle worker termination gracefully', async () => {
      const ideas = generateMockIdeas(10);
      
      // Start a task
      const promise = workerManager.clusterIdeas(ideas);
      
      // Terminate workers while task is running
      workerManager.terminate();
      
      // Task should reject
      await expect(promise).rejects.toThrow();
    });
  });
});

// Helper functions
function generateMockIdeas(count: number): Idea[] {
  const ideas: Idea[] = [];
  const categories = ['feature', 'bug', 'optimization', 'refactor'];
  const priorities = ['low', 'medium', 'high', 'critical'];
  
  for (let i = 0; i < count; i++) {
    ideas.push({
      id: `idea_${i}`,
      sessionId: 'test_session',
      content: `Test idea ${i}: ${categories[i % categories.length]}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'active',
      priority: priorities[i % priorities.length] as any,
      tags: [categories[i % categories.length], 'test']
    });
  }
  
  return ideas;
}

function generateMockMessages(count: number): Message[] {
  const messages: Message[] = [];
  const topics = [
    'We should implement OAuth authentication',
    'Consider adding real-time notifications',
    'The database queries need optimization',
    'Let\'s create a mobile app version',
    'How about improving the search functionality'
  ];
  
  for (let i = 0; i < count; i++) {
    messages.push({
      id: `msg_${i}`,
      role: i % 2 === 0 ? 'user' : 'assistant',
      content: topics[i % topics.length],
      timestamp: new Date().toISOString()
    });
  }
  
  return messages;
}