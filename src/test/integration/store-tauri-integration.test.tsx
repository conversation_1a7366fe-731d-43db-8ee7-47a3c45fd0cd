/**
 * Integration Tests for Store-Tauri Data Flow
 * 
 * Tests the integration between Zustand store, React components, and Tauri backend
 */

import React from 'react';
import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { render, screen, fireEvent } from '@testing-library/react';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { invoke } from '@tauri-apps/api/core';
import { EnhancedBrainstormingChat as BrainstormingChat } from '@/components/brainstorming/EnhancedBrainstormingChat';
import { IdeaManager } from '@/components/brainstorming';
import type { BrainstormSession, Idea, Message } from '@/types/brainstorm';

// Mock Tauri API
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn(),
}));

// Mock responses
const mockSessionResponse = {
  id: 'session_123',
  title: 'Test Session',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  messages: [],
  settings: {},
};

const mockIdeaResponse = {
  id: 'idea_123',
  sessionId: 'session_123',
  content: 'Test idea content',
  status: 'active',
  priority: 'medium',
  tags: ['test'],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

describe('Store-Tauri Integration', () => {
  beforeEach(() => {
    // Reset store
    const { result } = renderHook(() => useBrainstormStore());
    act(() => {
      result.current.clearAll();
    });
    
    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Session Management', () => {
    it('should create session in store and sync with Tauri', async () => {
      const mockInvoke = vi.mocked(invoke);
      mockInvoke.mockResolvedValueOnce(mockSessionResponse);

      const { result } = renderHook(() => useBrainstormStore());

      // Create session
      let sessionId: string;
      await act(async () => {
        sessionId = await result.current.createSession('Test Session');
      });

      // Verify Tauri was called
      expect(mockInvoke).toHaveBeenCalledWith('create_brainstorm_session', {
        title: 'Test Session',
      });

      // Verify store state
      expect(result.current.sessions[sessionId!]).toBeDefined();
      expect(result.current.sessions[sessionId!].title).toBe('Test Session');
    });

    it('should load sessions from Tauri on initialization', async () => {
      const mockInvoke = vi.mocked(invoke);
      const mockSessions = [mockSessionResponse];
      mockInvoke.mockResolvedValueOnce(mockSessions);

      const { result } = renderHook(() => useBrainstormStore());

      // Load sessions
      await act(async () => {
        await result.current.loadSessions();
      });

      expect(mockInvoke).toHaveBeenCalledWith('get_brainstorm_sessions');
      expect(Object.keys(result.current.sessions)).toHaveLength(1);
    });

    it('should handle Tauri errors gracefully', async () => {
      const mockInvoke = vi.mocked(invoke);
      mockInvoke.mockRejectedValueOnce(new Error('Tauri error'));

      const { result } = renderHook(() => useBrainstormStore());

      // Try to create session
      await act(async () => {
        try {
          await result.current.createSession('Test Session');
        } catch (error) {
          expect(error).toBeDefined();
        }
      });

      // Store should remain empty
      expect(Object.keys(result.current.sessions)).toHaveLength(0);
    });
  });

  describe('Message Flow', () => {
    it('should sync messages between store and Tauri', async () => {
      const mockInvoke = vi.mocked(invoke);
      mockInvoke.mockResolvedValueOnce(mockSessionResponse);

      const { result } = renderHook(() => useBrainstormStore());

      // Create session
      let sessionId: string;
      await act(async () => {
        sessionId = await result.current.createSession('Test Session');
      });

      // Add message
      const messageContent = 'Test message content';
      mockInvoke.mockResolvedValueOnce({
        id: 'msg_123',
        role: 'user',
        content: messageContent,
        timestamp: new Date().toISOString(),
      });

      await act(async () => {
        await result.current.addMessage(sessionId, {
          role: 'user',
          content: messageContent,
        });
      });

      expect(mockInvoke).toHaveBeenCalledWith('add_brainstorm_message', {
        sessionId,
        message: expect.objectContaining({
          role: 'user',
          content: messageContent,
        }),
      });

      // Verify message in store
      expect(result.current.sessions[sessionId].messages).toHaveLength(1);
      expect(result.current.sessions[sessionId].messages[0].content).toBe(messageContent);
    });
  });

  describe('Idea Management', () => {
    it('should create ideas and sync with Tauri', async () => {
      const mockInvoke = vi.mocked(invoke);
      mockInvoke.mockResolvedValueOnce(mockSessionResponse);

      const { result } = renderHook(() => useBrainstormStore());

      // Create session
      let sessionId: string;
      await act(async () => {
        sessionId = await result.current.createSession('Test Session');
      });

      // Add idea
      mockInvoke.mockResolvedValueOnce(mockIdeaResponse);

      await act(async () => {
        await result.current.addIdea(sessionId, 'Test idea content');
      });

      expect(mockInvoke).toHaveBeenCalledWith('create_idea', {
        sessionId,
        content: 'Test idea content',
      });

      // Verify idea in store
      const ideas = result.current.getIdeasBySession(sessionId);
      expect(ideas).toHaveLength(1);
      expect(ideas[0].content).toBe('Test idea content');
    });

    it('should update idea status and sync', async () => {
      const mockInvoke = vi.mocked(invoke);
      mockInvoke.mockResolvedValueOnce(mockSessionResponse);
      mockInvoke.mockResolvedValueOnce(mockIdeaResponse);

      const { result } = renderHook(() => useBrainstormStore());

      // Create session and idea
      let sessionId: string;
      await act(async () => {
        sessionId = await result.current.createSession('Test Session');
        await result.current.addIdea(sessionId, 'Test idea');
      });

      const ideaId = mockIdeaResponse.id;

      // Update idea
      mockInvoke.mockResolvedValueOnce({
        ...mockIdeaResponse,
        status: 'validated',
      });

      await act(async () => {
        await result.current.updateIdea(ideaId, { status: 'validated' });
      });

      expect(mockInvoke).toHaveBeenCalledWith('update_idea', {
        ideaId,
        updates: { status: 'validated' },
      });

      // Verify update in store
      const ideas = result.current.getIdeasBySession(sessionId);
      expect(ideas[0].status).toBe('validated');
    });
  });

  describe('Component Integration', () => {
    it('should render BrainstormingChat with store data', async () => {
      const mockInvoke = vi.mocked(invoke);
      mockInvoke.mockResolvedValueOnce(mockSessionResponse);

      // Create session in store
      const { result } = renderHook(() => useBrainstormStore());
      let sessionId: string;
      await act(async () => {
        sessionId = await result.current.createSession('Test Session');
      });

      // Render component
      const { container } = render(
        <BrainstormingChat 
          sessionId={sessionId}
          onClose={() => {}}
        />
      );

      // Verify session title is displayed
      await waitFor(() => {
        expect(screen.getByText('Test Session')).toBeInTheDocument();
      });
    });

    it('should handle idea extraction through component', async () => {
      const mockInvoke = vi.mocked(invoke);
      mockInvoke.mockResolvedValueOnce(mockSessionResponse);

      const { result } = renderHook(() => useBrainstormStore());
      let sessionId: string;
      await act(async () => {
        sessionId = await result.current.createSession('Test Session');
      });

      // Add message with potential ideas
      await act(async () => {
        await result.current.addMessage(sessionId, {
          role: 'assistant',
          content: 'We should implement user authentication and add data caching.',
        });
      });

      // Mock idea extraction
      mockInvoke.mockResolvedValueOnce([
        {
          id: 'idea_1',
          content: 'implement user authentication',
          sessionId,
          status: 'active',
          priority: 'high',
          tags: ['feature'],
        },
        {
          id: 'idea_2',
          content: 'add data caching',
          sessionId,
          status: 'active',
          priority: 'medium',
          tags: ['optimization'],
        },
      ]);

      // Render IdeaManager
      render(<IdeaManager sessionId={sessionId} />);

      // Trigger extraction
      const extractButton = screen.getByRole('button', { name: /extract ideas/i });
      await act(async () => {
        fireEvent.click(extractButton);
      });

      // Verify Tauri was called
      expect(mockInvoke).toHaveBeenCalledWith('extract_ideas_from_messages', {
        sessionId,
        messages: expect.any(Array),
      });

      // Verify ideas appear in UI
      await waitFor(() => {
        expect(screen.getByText(/implement user authentication/i)).toBeInTheDocument();
        expect(screen.getByText(/add data caching/i)).toBeInTheDocument();
      });
    });
  });

  describe('Persistence and Recovery', () => {
    it('should persist store state to Tauri', async () => {
      const mockInvoke = vi.mocked(invoke);
      mockInvoke.mockResolvedValueOnce(mockSessionResponse);

      const { result } = renderHook(() => useBrainstormStore());

      // Create session and add data
      await act(async () => {
        const sessionId = await result.current.createSession('Test Session');
        await result.current.addIdea(sessionId, 'Test idea');
        await result.current.saveMemory({
          type: 'insight',
          content: 'Important insight',
          sessionId,
          tags: ['test'],
          importance: 0.8,
        });
      });

      // Persist state
      mockInvoke.mockResolvedValueOnce(true);
      await act(async () => {
        await result.current.persistState();
      });

      expect(mockInvoke).toHaveBeenCalledWith('persist_brainstorm_state', {
        state: expect.objectContaining({
          sessions: expect.any(Object),
          ideas: expect.any(Object),
          memories: expect.any(Object),
        }),
      });
    });

    it('should recover state from Tauri', async () => {
      const mockInvoke = vi.mocked(invoke);
      
      // Mock persisted state
      const mockState = {
        sessions: {
          'session_123': mockSessionResponse,
        },
        ideas: {
          'idea_123': mockIdeaResponse,
        },
        memories: {},
        clusters: {},
      };

      mockInvoke.mockResolvedValueOnce(mockState);

      const { result } = renderHook(() => useBrainstormStore());

      // Recover state
      await act(async () => {
        await result.current.recoverState();
      });

      expect(mockInvoke).toHaveBeenCalledWith('recover_brainstorm_state');

      // Verify state recovered
      expect(Object.keys(result.current.sessions)).toHaveLength(1);
      expect(Object.keys(result.current.ideas)).toHaveLength(1);
    });
  });

  describe('Real-time Updates', () => {
    it('should handle real-time updates from Tauri events', async () => {
      const mockInvoke = vi.mocked(invoke);
      mockInvoke.mockResolvedValueOnce(mockSessionResponse);

      const { result } = renderHook(() => useBrainstormStore());

      // Create session
      let sessionId: string;
      await act(async () => {
        sessionId = await result.current.createSession('Test Session');
      });

      // Simulate Tauri event for new idea
      const newIdea = {
        id: 'idea_new',
        sessionId,
        content: 'Real-time idea',
        status: 'active',
        priority: 'high',
        tags: ['realtime'],
      };

      // Mock event listener
      await act(async () => {
        await result.current.handleTauriEvent('idea:created', newIdea);
      });

      // Verify idea added to store
      const ideas = result.current.getIdeasBySession(sessionId);
      expect(ideas).toHaveLength(1);
      expect(ideas[0].content).toBe('Real-time idea');
    });
  });
});