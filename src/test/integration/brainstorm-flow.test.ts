import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { EnhancedIdeaExtractor } from '@/services/EnhancedIdeaExtractor';
import { webResearchService } from '@/lib/web-research';
import { brainstormMemory } from '@/lib/brainstorm-memory';
import { clusteringService } from '@/services/clustering-service';

// Mock Tauri API
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn(),
}));

describe('Brainstorming Flow Integration', () => {
  beforeEach(() => {
    // Reset store state
    const { result } = renderHook(() => useBrainstormStore());
    act(() => {
      result.current.clearAll();
    });
  });

  describe('Session Creation and Idea Extraction', () => {
    it('should create session and extract ideas from messages', async () => {
      const { result } = renderHook(() => useBrainstormStore());
      const extractor = EnhancedIdeaExtractor.getInstance();

      // Create session
      let sessionId: string;
      act(() => {
        sessionId = result.current.createSession('Product Brainstorming');
      });

      expect(sessionId).toBeDefined();
      expect(result.current.sessions[sessionId]).toBeDefined();
      expect(result.current.sessions[sessionId].title).toBe('Product Brainstorming');

      // Add messages
      act(() => {
        result.current.addMessage(sessionId, {
          role: 'user',
          content: 'We should implement user authentication with OAuth2 support',
        });
        result.current.addMessage(sessionId, {
          role: 'assistant',
          content: 'Great idea! We could also add two-factor authentication for enhanced security',
        });
      });

      // Extract ideas
      const messages = result.current.sessions[sessionId].messages;
      const ideas = extractor.extractIdeas(messages, sessionId);

      // Add extracted ideas to store
      act(() => {
        ideas.forEach(idea => {
          result.current.addIdeaFull(idea);
        });
      });

      // Verify ideas were extracted
      const sessionIdeas = result.current.getIdeasBySession(sessionId);
      expect(sessionIdeas).toHaveLength(2);
      expect(sessionIdeas[0].content).toContain('user authentication');
      expect(sessionIdeas[1].content).toContain('two-factor authentication');
    });
  });

  describe('Idea Clustering and Memory', () => {
    it('should cluster ideas and save to memory', async () => {
      const { result } = renderHook(() => useBrainstormStore());

      // Create session with ideas
      let sessionId: string;
      act(() => {
        sessionId = result.current.createSession('Tech Stack Planning');
        
        // Add multiple related ideas
        result.current.addIdea(sessionId, 'Use React for frontend development');
        result.current.addIdea(sessionId, 'Implement Redux for state management');
        result.current.addIdea(sessionId, 'Use TypeScript for type safety');
        result.current.addIdea(sessionId, 'Deploy on AWS with Docker containers');
        result.current.addIdea(sessionId, 'Use PostgreSQL for database');
      });

      const ideas = result.current.getIdeasBySession(sessionId);

      // Perform clustering
      const clusters = await clusteringService.suggestClusters(ideas);
      
      // Save clusters
      act(() => {
        clusters.forEach(cluster => {
          result.current.createCluster(sessionId, cluster.name, cluster.ideaIds);
        });
      });

      // Verify clusters
      const sessionClusters = Object.values(result.current.clusters)
        .filter(c => c.sessionId === sessionId);
      expect(sessionClusters.length).toBeGreaterThan(0);

      // Save to memory
      const memory = {
        type: 'insight' as const,
        content: 'Tech stack decisions: React, Redux, TypeScript, AWS, PostgreSQL',
        sessionId,
        tags: ['tech-stack', 'architecture'],
        importance: 0.9,
      };

      act(() => {
        const memoryId = result.current.saveMemory(memory);
        expect(memoryId).toBeDefined();
      });

      // Verify memory saved
      const memories = result.current.getMemoriesBySession(sessionId);
      expect(memories).toHaveLength(1);
      expect(memories[0].content).toContain('Tech stack decisions');
    });
  });

  describe('Web Research Integration', () => {
    it('should request and approve web research', async () => {
      const { result } = renderHook(() => useBrainstormStore());

      // Create session
      let sessionId: string;
      act(() => {
        sessionId = result.current.createSession('AI Implementation Research');
      });

      // Request research
      const queryId = await webResearchService.requestResearch(
        'best practices for LLM integration',
        'software architecture',
        sessionId,
        false // Not auto-approved
      );

      expect(queryId).toBeDefined();

      // Check pending approvals
      const pending = webResearchService.getPendingApprovals();
      expect(pending).toHaveLength(1);
      expect(pending[0].query).toBe('best practices for LLM integration');

      // Approve research
      await webResearchService.approveResearch(queryId);

      // Get results (will be mock results in test)
      const results = webResearchService.getCachedResults(queryId);
      expect(results).toBeDefined();
      expect(results?.length).toBeGreaterThan(0);

      // Generate summary
      const summary = await webResearchService.generateSummary(queryId);
      expect(summary.keyInsights.length).toBeGreaterThan(0);
    });
  });

  describe('Task Generation and Export', () => {
    it('should generate tasks from ideas and prepare for export', async () => {
      const { result } = renderHook(() => useBrainstormStore());

      // Create session with ideas
      let sessionId: string;
      act(() => {
        sessionId = result.current.createSession('Feature Planning');
        
        result.current.addIdea(sessionId, 'Implement user authentication', {
          priority: 'high',
          status: 'active',
        });
        result.current.addIdea(sessionId, 'Add dark mode support', {
          priority: 'medium',
          status: 'active',
        });
      });

      const ideas = result.current.getIdeasBySession(sessionId);

      // Generate tasks (mock for now)
      const tasks = ideas.map(idea => ({
        id: `task_${idea.id}`,
        ideaId: idea.id,
        title: idea.content,
        description: `Implement: ${idea.content}`,
        priority: idea.priority,
        status: 'pending' as const,
        estimatedEffort: idea.priority === 'high' ? 16 : 8,
        tags: idea.tags,
        assignee: null,
        dependencies: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }));

      expect(tasks).toHaveLength(2);
      expect(tasks[0].estimatedEffort).toBe(16);

      // Prepare export data
      const exportData = {
        session: result.current.sessions[sessionId],
        ideas,
        tasks,
        clusters: [],
        memories: [],
      };

      expect(exportData.ideas).toHaveLength(2);
      expect(exportData.tasks).toHaveLength(2);
    });
  });

  describe('Persona Switching', () => {
    it('should switch AI personas and maintain context', async () => {
      const { result } = renderHook(() => useBrainstormStore());

      // Get initial persona
      const initialPersona = result.current.getActivePersona();
      expect(initialPersona?.id).toBe('default');

      // Switch to critic persona
      act(() => {
        result.current.setActivePersona('critic');
      });

      const criticPersona = result.current.getActivePersona();
      expect(criticPersona?.id).toBe('critic');
      expect(criticPersona?.systemPrompt).toContain('critical thinking');

      // Create custom persona
      act(() => {
        result.current.createPersona({
          name: 'Product Manager',
          description: 'Focuses on user needs and business value',
          systemPrompt: 'You are a product manager focused on user needs, business value, and practical implementation.',
          icon: 'briefcase',
          color: '#059669',
        });
      });

      const personas = result.current.personas;
      const customPersona = Object.values(personas).find(p => p.name === 'Product Manager');
      expect(customPersona).toBeDefined();
    });
  });

  describe('Full Workflow Integration', () => {
    it('should complete full brainstorming workflow', async () => {
      const { result } = renderHook(() => useBrainstormStore());
      const extractor = EnhancedIdeaExtractor.getInstance();

      // 1. Create session
      let sessionId: string;
      act(() => {
        sessionId = result.current.createSession('Complete Workflow Test');
      });

      // 2. Add messages and extract ideas
      act(() => {
        result.current.addMessage(sessionId, {
          role: 'user',
          content: 'We need a mobile app for task management with offline support',
        });
      });

      const messages = result.current.sessions[sessionId].messages;
      const ideas = extractor.extractIdeas(messages, sessionId);

      act(() => {
        ideas.forEach(idea => result.current.addIdeaFull(idea));
      });

      // 3. Update idea status
      const sessionIdeas = result.current.getIdeasBySession(sessionId);
      act(() => {
        result.current.updateIdea(sessionIdeas[0].id, {
          status: 'in-progress',
          priority: 'high',
        });
      });

      // 4. Create cluster
      act(() => {
        result.current.createCluster(
          sessionId,
          'Mobile Features',
          [sessionIdeas[0].id]
        );
      });

      // 5. Save memory
      act(() => {
        result.current.saveMemory({
          type: 'insight',
          content: 'Mobile app with offline support is high priority',
          sessionId,
          tags: ['mobile', 'offline'],
          importance: 0.9,
        });
      });

      // Verify complete state
      const finalSession = result.current.sessions[sessionId];
      const finalIdeas = result.current.getIdeasBySession(sessionId);
      const finalClusters = Object.values(result.current.clusters)
        .filter(c => c.sessionId === sessionId);
      const finalMemories = result.current.getMemoriesBySession(sessionId);

      expect(finalSession.messages).toHaveLength(1);
      expect(finalIdeas).toHaveLength(1);
      expect(finalIdeas[0].status).toBe('in-progress');
      expect(finalClusters).toHaveLength(1);
      expect(finalMemories).toHaveLength(1);
    });
  });
});