/**
 * Idea Extraction Web Worker
 * 
 * Performs text analysis and idea extraction off the main thread
 */

import type { Message, Idea } from '@/types/brainstorm';

interface ExtractionMessage {
  type: 'extract' | 'batch' | 'analyze';
  messages?: Message[];
  text?: string;
  sessionId: string;
  options?: ExtractionOptions;
}

interface ExtractionOptions {
  minConfidence?: number;
  maxIdeasPerMessage?: number;
  includeMetadata?: boolean;
  language?: string;
}

interface ExtractedIdea {
  content: string;
  confidence: number;
  category?: string;
  keywords: string[];
  sentiment: 'positive' | 'negative' | 'neutral';
  actionable: boolean;
}

// Common stop words to filter out
const STOP_WORDS = new Set([
  'the', 'is', 'at', 'which', 'on', 'and', 'a', 'an', 'as', 'are',
  'been', 'be', 'have', 'has', 'had', 'do', 'does', 'did', 'done',
  'will', 'would', 'could', 'should', 'shall', 'may', 'might', 'must',
  'can', 'to', 'of', 'in', 'for', 'with', 'by', 'from', 'up', 'about',
  'into', 'through', 'during', 'before', 'after', 'above', 'below',
  'between', 'under', 'again', 'further', 'then', 'once'
]);

// Action verbs that indicate actionable ideas
const ACTION_VERBS = new Set([
  'implement', 'create', 'build', 'develop', 'design', 'improve',
  'optimize', 'enhance', 'add', 'remove', 'update', 'fix', 'solve',
  'integrate', 'automate', 'streamline', 'refactor', 'deploy',
  'test', 'validate', 'launch', 'migrate', 'configure', 'setup'
]);

// Extract keywords from text
function extractKeywords(text: string): string[] {
  const words = text.toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 2 && !STOP_WORDS.has(word));
  
  // Count word frequency
  const frequency = new Map<string, number>();
  for (const word of words) {
    frequency.set(word, (frequency.get(word) || 0) + 1);
  }
  
  // Return top keywords by frequency
  return Array.from(frequency.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([word]) => word);
}

// Analyze sentiment of text
function analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
  const positive = /good|great|excellent|amazing|fantastic|wonderful|perfect|love|best|awesome/i;
  const negative = /bad|terrible|awful|horrible|worst|hate|poor|fail|problem|issue/i;
  
  const hasPositive = positive.test(text);
  const hasNegative = negative.test(text);
  
  if (hasPositive && !hasNegative) return 'positive';
  if (hasNegative && !hasPositive) return 'negative';
  return 'neutral';
}

// Check if text contains actionable content
function isActionable(text: string): boolean {
  const words = text.toLowerCase().split(/\s+/);
  return words.some(word => ACTION_VERBS.has(word));
}

// Extract ideas from a single message
function extractIdeasFromMessage(
  message: Message,
  options: ExtractionOptions
): ExtractedIdea[] {
  const ideas: ExtractedIdea[] = [];
  const minConfidence = options.minConfidence || 0.7;
  const maxIdeas = options.maxIdeasPerMessage || 5;
  
  // Split message into sentences
  const sentences = message.content
    .split(/[.!?]+/)
    .map(s => s.trim())
    .filter(s => s.length > 10);
  
  for (const sentence of sentences) {
    // Skip if sentence is too short or too long
    if (sentence.length < 20 || sentence.length > 500) continue;
    
    // Calculate confidence based on various factors
    let confidence = 0.5;
    
    // Boost confidence for actionable sentences
    if (isActionable(sentence)) {
      confidence += 0.2;
    }
    
    // Boost confidence for sentences with clear structure
    if (/^(we should|I think|what if|how about|let's)/i.test(sentence)) {
      confidence += 0.15;
    }
    
    // Boost confidence for sentences with technical terms
    if (/API|database|UI|UX|backend|frontend|algorithm|feature/i.test(sentence)) {
      confidence += 0.1;
    }
    
    // Extract if confidence threshold is met
    if (confidence >= minConfidence) {
      const keywords = extractKeywords(sentence);
      const sentiment = analyzeSentiment(sentence);
      const actionable = isActionable(sentence);
      
      // Categorize based on keywords
      let category = 'general';
      if (keywords.some(k => /tech|code|api|database/.test(k))) {
        category = 'technical';
      } else if (keywords.some(k => /user|customer|experience/.test(k))) {
        category = 'user-experience';
      } else if (keywords.some(k => /business|revenue|market/.test(k))) {
        category = 'business';
      }
      
      ideas.push({
        content: sentence,
        confidence,
        category,
        keywords,
        sentiment,
        actionable
      });
      
      if (ideas.length >= maxIdeas) break;
    }
  }
  
  return ideas;
}

// Batch process multiple messages
function batchExtractIdeas(
  messages: Message[],
  sessionId: string,
  options: ExtractionOptions
): Idea[] {
  const allIdeas: Idea[] = [];
  let ideaIndex = 0;
  
  for (const message of messages) {
    const extractedIdeas = extractIdeasFromMessage(message, options);
    
    for (const extracted of extractedIdeas) {
      const idea: Idea = {
        id: `idea_${sessionId}_${Date.now()}_${ideaIndex++}`,
        sessionId,
        content: extracted.content,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'active',
        priority: extracted.actionable ? 'high' : 'medium',
        tags: extracted.keywords.slice(0, 3),
        metadata: options.includeMetadata ? {
          source: 'auto-extraction',
          confidence: extracted.confidence,
          category: extracted.category,
          sentiment: extracted.sentiment,
          actionable: extracted.actionable,
          messageId: message.id,
          extractedAt: new Date().toISOString()
        } : undefined
      };
      
      allIdeas.push(idea);
    }
  }
  
  return allIdeas;
}

// Analyze text for idea potential
function analyzeText(text: string): {
  ideaCount: number;
  categories: Record<string, number>;
  overallSentiment: 'positive' | 'negative' | 'neutral';
  actionableRatio: number;
  keywords: string[];
} {
  const sentences = text
    .split(/[.!?]+/)
    .map(s => s.trim())
    .filter(s => s.length > 20);
  
  const categories: Record<string, number> = {};
  let positiveCount = 0;
  let negativeCount = 0;
  let actionableCount = 0;
  const allKeywords: string[] = [];
  
  for (const sentence of sentences) {
    // Analyze categories
    if (/tech|code|api|database/i.test(sentence)) {
      categories.technical = (categories.technical || 0) + 1;
    }
    if (/user|customer|experience/i.test(sentence)) {
      categories['user-experience'] = (categories['user-experience'] || 0) + 1;
    }
    if (/business|revenue|market/i.test(sentence)) {
      categories.business = (categories.business || 0) + 1;
    }
    
    // Analyze sentiment
    const sentiment = analyzeSentiment(sentence);
    if (sentiment === 'positive') positiveCount++;
    else if (sentiment === 'negative') negativeCount++;
    
    // Check actionability
    if (isActionable(sentence)) actionableCount++;
    
    // Collect keywords
    allKeywords.push(...extractKeywords(sentence));
  }
  
  // Deduplicate and rank keywords
  const keywordFreq = new Map<string, number>();
  for (const keyword of allKeywords) {
    keywordFreq.set(keyword, (keywordFreq.get(keyword) || 0) + 1);
  }
  
  const topKeywords = Array.from(keywordFreq.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([word]) => word);
  
  // Determine overall sentiment
  let overallSentiment: 'positive' | 'negative' | 'neutral' = 'neutral';
  if (positiveCount > negativeCount * 1.5) overallSentiment = 'positive';
  else if (negativeCount > positiveCount * 1.5) overallSentiment = 'negative';
  
  return {
    ideaCount: sentences.length,
    categories,
    overallSentiment,
    actionableRatio: sentences.length > 0 ? actionableCount / sentences.length : 0,
    keywords: topKeywords
  };
}

// Main message handler
self.addEventListener('message', (event: MessageEvent<ExtractionMessage>) => {
  const { type, messages, text, sessionId, options = {} } = event.data;
  
  try {
    switch (type) {
      case 'extract':
        if (!messages || messages.length === 0) {
          throw new Error('No messages provided for extraction');
        }
        
        const ideas = batchExtractIdeas(messages, sessionId, options);
        
        self.postMessage({
          type: 'success',
          ideas,
          stats: {
            messagesProcessed: messages.length,
            ideasExtracted: ideas.length,
            averageConfidence: ideas.reduce((sum, idea) => 
              sum + (idea.metadata?.confidence || 0), 0
            ) / ideas.length
          }
        });
        break;
        
      case 'batch':
        if (!messages || messages.length === 0) {
          throw new Error('No messages provided for batch processing');
        }
        
        // Process in chunks to provide progress updates
        const chunkSize = 10;
        const allIdeas: Idea[] = [];
        
        for (let i = 0; i < messages.length; i += chunkSize) {
          const chunk = messages.slice(i, i + chunkSize);
          const chunkIdeas = batchExtractIdeas(chunk, sessionId, options);
          allIdeas.push(...chunkIdeas);
          
          // Send progress update
          self.postMessage({
            type: 'progress',
            processed: Math.min(i + chunkSize, messages.length),
            total: messages.length,
            currentIdeas: allIdeas.length
          });
        }
        
        self.postMessage({
          type: 'success',
          ideas: allIdeas,
          stats: {
            messagesProcessed: messages.length,
            ideasExtracted: allIdeas.length
          }
        });
        break;
        
      case 'analyze':
        if (!text) {
          throw new Error('No text provided for analysis');
        }
        
        const analysis = analyzeText(text);
        
        self.postMessage({
          type: 'success',
          analysis
        });
        break;
        
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  } catch (error) {
    self.postMessage({
      type: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Export for TypeScript
export {};