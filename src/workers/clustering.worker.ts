/**
 * Clustering Web Worker
 * 
 * Performs computationally expensive clustering calculations off the main thread
 */

import type { Idea } from '@/types/brainstorm';

interface ClusteringMessage {
  type: 'cluster' | 'recluster' | 'optimize';
  ideas: Idea[];
  options?: ClusteringOptions;
}

interface ClusteringOptions {
  algorithm?: 'kmeans' | 'hierarchical' | 'density';
  maxClusters?: number;
  minClusterSize?: number;
  similarityThreshold?: number;
}

interface ClusterResult {
  id: string;
  name: string;
  ideaIds: string[];
  centroid?: number[];
  quality: number;
}

// Vector representation of an idea
function ideaToVector(idea: Idea): number[] {
  const vector: number[] = [];
  
  // Priority weight (critical: 4, high: 3, medium: 2, low: 1)
  const priorityWeights = { critical: 4, high: 3, medium: 2, low: 1 };
  vector.push(priorityWeights[idea.priority || 'medium']);
  
  // Status weight (validated: 3, in-progress: 2, active: 1, archived: 0)
  const statusWeights = { validated: 3, 'in-progress': 2, active: 1, archived: 0 };
  vector.push(statusWeights[idea.status] || 1);
  
  // Word count
  vector.push(idea.content.split(' ').length);
  
  // Tag count
  vector.push(idea.tags.length);
  
  // Time-based features (normalized to 0-1)
  const createdTime = new Date(idea.createdAt).getTime();
  const now = Date.now();
  const ageInDays = (now - createdTime) / (1000 * 60 * 60 * 24);
  vector.push(1 / (1 + ageInDays)); // Newer ideas have higher values
  
  return vector;
}

// Calculate cosine similarity between two vectors
function cosineSimilarity(a: number[], b: number[]): number {
  let dotProduct = 0;
  let normA = 0;
  let normB = 0;
  
  for (let i = 0; i < a.length; i++) {
    dotProduct += a[i] * b[i];
    normA += a[i] * a[i];
    normB += b[i] * b[i];
  }
  
  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

// K-means clustering algorithm
function kMeansClustering(
  ideas: Idea[],
  options: ClusteringOptions
): ClusterResult[] {
  const maxClusters = options.maxClusters || 5;
  const vectors = ideas.map(ideaToVector);
  const k = Math.min(maxClusters, Math.ceil(Math.sqrt(ideas.length / 2)));
  
  if (ideas.length < k) {
    // Too few ideas for meaningful clustering
    return [{
      id: 'all',
      name: 'All Ideas',
      ideaIds: ideas.map(i => i.id),
      quality: 1
    }];
  }
  
  // Initialize centroids randomly
  const centroids: number[][] = [];
  const usedIndices = new Set<number>();
  
  while (centroids.length < k) {
    const idx = Math.floor(Math.random() * vectors.length);
    if (!usedIndices.has(idx)) {
      centroids.push([...vectors[idx]]);
      usedIndices.add(idx);
    }
  }
  
  // Iterate until convergence or max iterations
  const maxIterations = 100;
  let iteration = 0;
  let changed = true;
  const assignments: number[] = new Array(ideas.length).fill(-1);
  
  while (changed && iteration < maxIterations) {
    changed = false;
    
    // Assign each idea to nearest centroid
    for (let i = 0; i < vectors.length; i++) {
      let minDist = Infinity;
      let bestCluster = -1;
      
      for (let j = 0; j < centroids.length; j++) {
        const similarity = cosineSimilarity(vectors[i], centroids[j]);
        const dist = 1 - similarity; // Convert similarity to distance
        
        if (dist < minDist) {
          minDist = dist;
          bestCluster = j;
        }
      }
      
      if (assignments[i] !== bestCluster) {
        assignments[i] = bestCluster;
        changed = true;
      }
    }
    
    // Update centroids
    for (let j = 0; j < centroids.length; j++) {
      const clusterVectors = vectors.filter((_, i) => assignments[i] === j);
      
      if (clusterVectors.length > 0) {
        // Calculate mean of cluster vectors
        const newCentroid = new Array(vectors[0].length).fill(0);
        
        for (const vec of clusterVectors) {
          for (let d = 0; d < vec.length; d++) {
            newCentroid[d] += vec[d];
          }
        }
        
        for (let d = 0; d < newCentroid.length; d++) {
          newCentroid[d] /= clusterVectors.length;
        }
        
        centroids[j] = newCentroid;
      }
    }
    
    iteration++;
  }
  
  // Create cluster results
  const clusters: ClusterResult[] = [];
  
  for (let j = 0; j < centroids.length; j++) {
    const clusterIdeas = ideas.filter((_, i) => assignments[i] === j);
    
    if (clusterIdeas.length >= (options.minClusterSize || 1)) {
      // Calculate cluster quality (cohesion)
      let totalSimilarity = 0;
      let comparisons = 0;
      
      for (let i = 0; i < clusterIdeas.length; i++) {
        for (let k = i + 1; k < clusterIdeas.length; k++) {
          const vec1 = vectors[ideas.indexOf(clusterIdeas[i])];
          const vec2 = vectors[ideas.indexOf(clusterIdeas[k])];
          totalSimilarity += cosineSimilarity(vec1, vec2);
          comparisons++;
        }
      }
      
      const quality = comparisons > 0 ? totalSimilarity / comparisons : 1;
      
      clusters.push({
        id: `cluster_${j}`,
        name: generateClusterName(clusterIdeas),
        ideaIds: clusterIdeas.map(i => i.id),
        centroid: centroids[j],
        quality
      });
    }
  }
  
  return clusters;
}

// Generate a meaningful name for a cluster based on its ideas
function generateClusterName(ideas: Idea[]): string {
  // Find most common tags
  const tagFrequency = new Map<string, number>();
  
  for (const idea of ideas) {
    for (const tag of idea.tags) {
      tagFrequency.set(tag, (tagFrequency.get(tag) || 0) + 1);
    }
  }
  
  const sortedTags = Array.from(tagFrequency.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 2)
    .map(([tag]) => tag);
  
  if (sortedTags.length > 0) {
    return sortedTags.join(' & ');
  }
  
  // Fall back to priority-based naming
  const priorities = ideas.map(i => i.priority || 'medium');
  const highPriorityCount = priorities.filter(p => p === 'high' || p === 'critical').length;
  
  if (highPriorityCount > ideas.length / 2) {
    return 'High Priority';
  }
  
  // Default naming
  return `Group ${ideas.length}`;
}

// Hierarchical clustering algorithm
function hierarchicalClustering(
  ideas: Idea[],
  options: ClusteringOptions
): ClusterResult[] {
  const threshold = options.similarityThreshold || 0.7;
  const vectors = ideas.map(ideaToVector);
  
  // Start with each idea as its own cluster
  let clusters: ClusterResult[] = ideas.map((idea, i) => ({
    id: `cluster_${i}`,
    name: idea.content.substring(0, 30) + '...',
    ideaIds: [idea.id],
    centroid: vectors[i],
    quality: 1
  }));
  
  // Merge clusters until threshold is reached
  let merged = true;
  
  while (merged && clusters.length > 1) {
    merged = false;
    let maxSimilarity = -Infinity;
    let mergeI = -1;
    let mergeJ = -1;
    
    // Find most similar pair of clusters
    for (let i = 0; i < clusters.length; i++) {
      for (let j = i + 1; j < clusters.length; j++) {
        const similarity = cosineSimilarity(
          clusters[i].centroid!,
          clusters[j].centroid!
        );
        
        if (similarity > maxSimilarity) {
          maxSimilarity = similarity;
          mergeI = i;
          mergeJ = j;
        }
      }
    }
    
    // Merge if similarity exceeds threshold
    if (maxSimilarity >= threshold && mergeI !== -1 && mergeJ !== -1) {
      const newCluster: ClusterResult = {
        id: `cluster_merged_${Date.now()}`,
        name: generateClusterName(
          ideas.filter(idea => 
            [...clusters[mergeI].ideaIds, ...clusters[mergeJ].ideaIds].includes(idea.id)
          )
        ),
        ideaIds: [...clusters[mergeI].ideaIds, ...clusters[mergeJ].ideaIds],
        quality: maxSimilarity
      };
      
      // Calculate new centroid
      const newCentroid = new Array(vectors[0].length).fill(0);
      const clusterIdeas = ideas.filter(idea => newCluster.ideaIds.includes(idea.id));
      
      for (const idea of clusterIdeas) {
        const vec = vectors[ideas.indexOf(idea)];
        for (let d = 0; d < vec.length; d++) {
          newCentroid[d] += vec[d];
        }
      }
      
      for (let d = 0; d < newCentroid.length; d++) {
        newCentroid[d] /= clusterIdeas.length;
      }
      
      newCluster.centroid = newCentroid;
      
      // Remove old clusters and add new one
      clusters = clusters.filter((_, i) => i !== mergeI && i !== mergeJ);
      clusters.push(newCluster);
      merged = true;
    }
  }
  
  // Filter out small clusters
  return clusters.filter(c => c.ideaIds.length >= (options.minClusterSize || 1));
}

// Main message handler
self.addEventListener('message', (event: MessageEvent<ClusteringMessage>) => {
  const { type, ideas, options = {} } = event.data;
  
  try {
    let result: ClusterResult[];
    
    switch (type) {
      case 'cluster':
        const algorithm = options.algorithm || 'kmeans';
        
        if (algorithm === 'kmeans') {
          result = kMeansClustering(ideas, options);
        } else if (algorithm === 'hierarchical') {
          result = hierarchicalClustering(ideas, options);
        } else {
          throw new Error(`Unknown clustering algorithm: ${algorithm}`);
        }
        
        self.postMessage({
          type: 'success',
          clusters: result,
          stats: {
            totalIdeas: ideas.length,
            clusterCount: result.length,
            avgClusterSize: result.reduce((sum, c) => sum + c.ideaIds.length, 0) / result.length,
            avgQuality: result.reduce((sum, c) => sum + c.quality, 0) / result.length
          }
        });
        break;
        
      case 'recluster':
        // Re-cluster with different parameters
        const newOptions = { ...options, maxClusters: (options.maxClusters || 5) + 1 };
        result = kMeansClustering(ideas, newOptions);
        
        self.postMessage({
          type: 'success',
          clusters: result
        });
        break;
        
      case 'optimize':
        // Find optimal number of clusters using elbow method
        const results: { k: number; quality: number }[] = [];
        
        for (let k = 2; k <= Math.min(10, ideas.length); k++) {
          const clusters = kMeansClustering(ideas, { ...options, maxClusters: k });
          const avgQuality = clusters.reduce((sum, c) => sum + c.quality, 0) / clusters.length;
          results.push({ k, quality: avgQuality });
        }
        
        // Find elbow point
        let optimalK = 3;
        let maxDerivative = -Infinity;
        
        for (let i = 1; i < results.length - 1; i++) {
          const derivative = (results[i - 1].quality - results[i + 1].quality) / 2;
          if (derivative > maxDerivative) {
            maxDerivative = derivative;
            optimalK = results[i].k;
          }
        }
        
        // Cluster with optimal k
        result = kMeansClustering(ideas, { ...options, maxClusters: optimalK });
        
        self.postMessage({
          type: 'success',
          clusters: result,
          optimal: { k: optimalK, results }
        });
        break;
        
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  } catch (error) {
    self.postMessage({
      type: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Export for TypeScript
export {};