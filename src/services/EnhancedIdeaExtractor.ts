/**
 * Enhanced IdeaExtractor Service with Web Worker Support
 * 
 * Provides high-performance idea extraction using web workers
 */

import { Message, Idea } from '@/types/brainstorm';
import { workerManager } from '@/lib/worker-manager';

export interface ExtractionOptions {
  minConfidence?: number;
  maxIdeasPerMessage?: number;
  includeMetadata?: boolean;
  useWebWorker?: boolean;
}

export interface ExtractionProgress {
  processed: number;
  total: number;
  currentIdeas: number;
}

export class EnhancedIdeaExtractor {
  private static instance: EnhancedIdeaExtractor;
  private useWebWorker = true;
  
  // Patterns for main thread extraction (fallback)
  private ideaPatterns = [
    /(?:^|\n)[-*•]?\s*(?:idea|concept|suggestion):\s*(.+?)(?=\n|$)/i,
    /(?:^|\n)[-*•]\s*(.+?)(?=\n|$)/,
    /(?:^|\n)\d+\.\s*(.+?)(?=\n|$)/,
    /(?:we|you)\s+(?:should|could|might)\s+(.+?)(?=\.|\n|$)/i,
    /(?:what if|how about|let's)\s+(.+?)(?=\?|\.|\n|$)/i,
    /(?:consider|implement|create|build|develop)\s+(.+?)(?=\.|\n|$)/i,
  ];
  
  private constructor() {}
  
  static getInstance(): EnhancedIdeaExtractor {
    if (!EnhancedIdeaExtractor.instance) {
      EnhancedIdeaExtractor.instance = new EnhancedIdeaExtractor();
    }
    return EnhancedIdeaExtractor.instance;
  }
  
  /**
   * Enable or disable web worker usage
   */
  setWebWorkerEnabled(enabled: boolean): void {
    this.useWebWorker = enabled;
  }
  
  /**
   * Extract ideas from messages using web worker
   */
  async extractIdeas(
    messages: Message[],
    sessionId: string,
    options: ExtractionOptions = {}
  ): Promise<Idea[]> {
    const {
      minConfidence = 0.7,
      maxIdeasPerMessage = 5,
      includeMetadata = true,
      useWebWorker = this.useWebWorker
    } = options;
    
    // Use web worker if available and enabled
    if (useWebWorker && typeof Worker !== 'undefined') {
      try {
        const result = await workerManager.extractIdeas(messages, sessionId, {
          minConfidence,
          maxIdeasPerMessage,
          includeMetadata
        });
        
        return result.ideas;
      } catch (error) {
        console.warn('Web worker extraction failed, falling back to main thread:', error);
        // Fall through to main thread implementation
      }
    }
    
    // Fallback to main thread extraction
    return this.extractOnMainThread(messages, sessionId, {
      minConfidence,
      maxIdeasPerMessage,
      includeMetadata
    });
  }
  
  /**
   * Batch extract ideas with progress callback
   */
  async batchExtractIdeas(
    messages: Message[],
    sessionId: string,
    options: ExtractionOptions = {},
    onProgress?: (progress: ExtractionProgress) => void
  ): Promise<Idea[]> {
    const {
      minConfidence = 0.7,
      maxIdeasPerMessage = 5,
      includeMetadata = true,
      useWebWorker = this.useWebWorker
    } = options;
    
    // Use web worker if available and enabled
    if (useWebWorker && typeof Worker !== 'undefined') {
      try {
        const result = await workerManager.batchExtractIdeas(
          messages,
          sessionId,
          {
            minConfidence,
            maxIdeasPerMessage,
            includeMetadata
          },
          onProgress
        );
        
        return result.ideas;
      } catch (error) {
        console.warn('Web worker batch extraction failed, falling back to main thread:', error);
        // Fall through to main thread implementation
      }
    }
    
    // Fallback to main thread batch extraction
    const ideas: Idea[] = [];
    const chunkSize = 10;
    
    for (let i = 0; i < messages.length; i += chunkSize) {
      const chunk = messages.slice(i, i + chunkSize);
      const chunkIdeas = await this.extractOnMainThread(chunk, sessionId, {
        minConfidence,
        maxIdeasPerMessage,
        includeMetadata
      });
      
      ideas.push(...chunkIdeas);
      
      // Report progress
      if (onProgress) {
        onProgress({
          processed: Math.min(i + chunkSize, messages.length),
          total: messages.length,
          currentIdeas: ideas.length
        });
      }
      
      // Yield to UI thread
      await new Promise(resolve => setTimeout(resolve, 0));
    }
    
    return ideas;
  }
  
  /**
   * Analyze text for idea potential
   */
  async analyzeText(text: string): Promise<{
    ideaCount: number;
    categories: Record<string, number>;
    overallSentiment: 'positive' | 'negative' | 'neutral';
    actionableRatio: number;
    keywords: string[];
  }> {
    if (this.useWebWorker && typeof Worker !== 'undefined') {
      try {
        return await workerManager.analyzeText(text);
      } catch (error) {
        console.warn('Web worker analysis failed:', error);
      }
    }
    
    // Fallback analysis
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20);
    const keywords = this.extractKeywords(text);
    
    return {
      ideaCount: sentences.length,
      categories: this.categorizeText(text),
      overallSentiment: this.analyzeSentiment(text),
      actionableRatio: this.calculateActionableRatio(sentences),
      keywords: keywords.slice(0, 10)
    };
  }
  
  /**
   * Extract ideas from a single message
   */
  extractFromMessage(message: Message, sessionId: string): Idea[] {
    const ideas: Idea[] = [];
    const content = message.content;
    
    // Apply each pattern to find potential ideas
    this.ideaPatterns.forEach((pattern, patternIndex) => {
      const matches = content.matchAll(new RegExp(pattern, 'g'));
      
      for (const match of matches) {
        const ideaContent = match[1]?.trim();
        
        if (!ideaContent || ideaContent.length < 20) continue;
        
        // Calculate basic confidence
        let confidence = 0.5;
        if (this.isActionable(ideaContent)) confidence += 0.3;
        if (ideaContent.length > 50) confidence += 0.1;
        if (patternIndex < 3) confidence += 0.1; // Higher confidence for explicit patterns
        
        ideas.push({
          id: `idea_${sessionId}_${Date.now()}_${ideas.length}`,
          sessionId,
          content: ideaContent,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          status: 'active',
          priority: confidence > 0.7 ? 'high' : 'medium',
          tags: this.extractTags(ideaContent),
          metadata: {
            source: 'auto-extraction',
            confidence,
            messageId: message.id,
            extractedAt: new Date().toISOString()
          }
        });
      }
    });
    
    return ideas;
  }
  
  /**
   * Main thread extraction implementation (fallback)
   */
  private async extractOnMainThread(
    messages: Message[],
    sessionId: string,
    options: {
      minConfidence: number;
      maxIdeasPerMessage: number;
      includeMetadata: boolean;
    }
  ): Promise<Idea[]> {
    const allIdeas: Idea[] = [];
    
    for (const message of messages) {
      const ideas = this.extractFromMessage(message, sessionId);
      
      // Filter by confidence and limit per message
      const filteredIdeas = ideas
        .filter(idea => (idea.metadata?.confidence || 0) >= options.minConfidence)
        .slice(0, options.maxIdeasPerMessage);
      
      // Remove metadata if not requested
      if (!options.includeMetadata) {
        filteredIdeas.forEach(idea => delete idea.metadata);
      }
      
      allIdeas.push(...filteredIdeas);
    }
    
    return allIdeas;
  }
  
  /**
   * Extract keywords from text
   */
  private extractKeywords(text: string): string[] {
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3);
    
    // Count frequency
    const frequency = new Map<string, number>();
    for (const word of words) {
      frequency.set(word, (frequency.get(word) || 0) + 1);
    }
    
    // Sort by frequency
    return Array.from(frequency.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([word]) => word);
  }
  
  /**
   * Extract tags from content
   */
  private extractTags(content: string): string[] {
    const tags: string[] = [];
    
    // Extract hashtags
    const hashtags = content.match(/#\w+/g) || [];
    tags.push(...hashtags.map(tag => tag.substring(1).toLowerCase()));
    
    // Extract technical terms
    const techTerms = content.match(/\b(API|UI|UX|database|backend|frontend|algorithm|feature|implementation)\b/gi) || [];
    tags.push(...techTerms.map(term => term.toLowerCase()));
    
    // Deduplicate
    return [...new Set(tags)].slice(0, 5);
  }
  
  /**
   * Check if content is actionable
   */
  private isActionable(content: string): boolean {
    const actionWords = ['implement', 'create', 'build', 'develop', 'add', 'fix', 'improve', 'optimize'];
    return actionWords.some(word => content.toLowerCase().includes(word));
  }
  
  /**
   * Analyze sentiment
   */
  private analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    const positive = (text.match(/good|great|excellent|amazing|fantastic|wonderful|perfect|love|best/gi) || []).length;
    const negative = (text.match(/bad|terrible|awful|horrible|worst|hate|poor|fail|problem/gi) || []).length;
    
    if (positive > negative * 1.5) return 'positive';
    if (negative > positive * 1.5) return 'negative';
    return 'neutral';
  }
  
  /**
   * Categorize text content
   */
  private categorizeText(text: string): Record<string, number> {
    const categories: Record<string, number> = {};
    
    if (/tech|code|api|database/i.test(text)) {
      categories.technical = (categories.technical || 0) + 1;
    }
    if (/user|customer|experience/i.test(text)) {
      categories['user-experience'] = (categories['user-experience'] || 0) + 1;
    }
    if (/business|revenue|market/i.test(text)) {
      categories.business = (categories.business || 0) + 1;
    }
    
    return categories;
  }
  
  /**
   * Calculate actionable ratio
   */
  private calculateActionableRatio(sentences: string[]): number {
    if (sentences.length === 0) return 0;
    
    const actionableCount = sentences.filter(s => this.isActionable(s)).length;
    return actionableCount / sentences.length;
  }
  
  /**
   * Get extraction status
   */
  getStatus(): {
    webWorkerEnabled: boolean;
    workerStatus: ReturnType<typeof workerManager.getStatus>;
  } {
    return {
      webWorkerEnabled: this.useWebWorker,
      workerStatus: workerManager.getStatus()
    };
  }
}

// Export singleton instance
export const enhancedIdeaExtractor = EnhancedIdeaExtractor.getInstance();

// Also export class for testing
export default EnhancedIdeaExtractor;