/**
 * IdeaExtractor Service
 * 
 * This service is responsible for parsing chat messages and identifying
 * actionable ideas that can be extracted and managed separately.
 */

import { ChatMessage, Idea, IdeaStatus } from '@/types/brainstorm';
import { useBrainstormStore } from '@/stores/brainstormStore';

export class IdeaExtractor {
  private static instance: IdeaExtractor;
  
  // Patterns to identify potential ideas in text
  private ideaPatterns = [
    // Explicit idea markers
    /(?:^|\n)[-*•]?\s*(?:idea|concept|suggestion):\s*(.+?)(?=\n|$)/i,
    
    // Bullet points that might be ideas
    /(?:^|\n)[-*•]\s*(.+?)(?=\n|$)/,
    
    // Numbered points that might be ideas
    /(?:^|\n)\d+\.\s*(.+?)(?=\n|$)/,
    
    // "We could" or "You could" statements
    /(?:^|\n|\. )(?:we|you)\s+could\s+(.+?)(?=\.|\n|$)/i,
    
    // "Consider" statements
    /(?:^|\n|\. )consider\s+(.+?)(?=\.|\n|$)/i,
    
    // "Another approach" statements
    /(?:^|\n|\. )another\s+approach\s+(?:would be |is |)?(.+?)(?=\.|\n|$)/i,
    
    // "One way" statements
    /(?:^|\n|\. )one\s+way\s+(?:to|would be)\s+(.+?)(?=\.|\n|$)/i,
  ];
  
  private constructor() {}
  
  static getInstance(): IdeaExtractor {
    if (!IdeaExtractor.instance) {
      IdeaExtractor.instance = new IdeaExtractor();
    }
    return IdeaExtractor.instance;
  }
  
  /**
   * Extract potential ideas from a chat message
   */
  extractIdeasFromMessage(message: ChatMessage): Partial<Idea>[] {
    // Only extract ideas from assistant messages
    if (message.type !== 'assistant') {
      return [];
    }
    
    const extractedIdeas: Partial<Idea>[] = [];
    const content = message.content;
    
    // Apply each pattern to find potential ideas
    this.ideaPatterns.forEach(pattern => {
      let match;
      const regex = new RegExp(pattern);
      
      // Find all matches for this pattern
      while ((match = regex.exec(content)) !== null) {
        const ideaContent = match[1].trim();
        
        // Skip if too short or already extracted
        if (
          ideaContent.length < 10 || 
          extractedIdeas.some(idea => idea.content === ideaContent)
        ) {
          continue;
        }
        
        // Create a new idea
        extractedIdeas.push({
          content: ideaContent,
          messageId: message.id,
          status: IdeaStatus.TO_EXPLORE,
          tags: this.extractPotentialTags(ideaContent),
          connections: [],
        });
      }
    });
    
    return extractedIdeas;
  }
  
  /**
   * Extract potential tags from idea content
   */
  private extractPotentialTags(content: string): string[] {
    const tags: string[] = [];
    
    // Look for hashtags
    const hashtagRegex = /#(\w+)/g;
    let match;
    while ((match = hashtagRegex.exec(content)) !== null) {
      tags.push(match[1].toLowerCase());
    }
    
    // Extract key technical terms that might be good tags
    const techTerms = [
      'api', 'ui', 'ux', 'frontend', 'backend', 'database', 'security',
      'performance', 'testing', 'deployment', 'design', 'mobile', 'desktop',
      'web', 'cloud', 'integration', 'authentication', 'authorization',
      'analytics', 'monitoring', 'scaling', 'architecture'
    ];
    
    techTerms.forEach(term => {
      if (content.toLowerCase().includes(term)) {
        tags.push(term);
      }
    });
    
    return [...new Set(tags)]; // Remove duplicates
  }
  
  /**
   * Process a message and store extracted ideas
   */
  processMessage(message: ChatMessage): string[] {
    const ideas = this.extractIdeasFromMessage(message);
    const ideaIds: string[] = [];
    
    if (ideas.length > 0) {
      const store = useBrainstormStore.getState();
      
      // Add each idea to the store
      ideas.forEach(idea => {
        const ideaId = store.addIdeaFull(idea as Omit<Idea, 'id' | 'createdAt' | 'updatedAt'>);
        ideaIds.push(ideaId);
      });
      
      // Update the message with extracted idea IDs
      if (ideaIds.length > 0) {
        const sessionId = Object.values(store.sessions).find(session =>
          session.messages.some(m => m.id === message.id)
        )?.id;
        
        if (sessionId) {
          store.updateMessage(sessionId, message.id, {
            extractedIdeas: ideaIds
          });
        }
      }
    }
    
    return ideaIds;
  }
  
  /**
   * Find potential connections between ideas based on content similarity
   */
  findPotentialConnections(ideaId: string, threshold: number = 0.3): string[] {
    const store = useBrainstormStore.getState();
    const idea = store.ideas[ideaId];
    
    if (!idea) return [];
    
    const connections: string[] = [];
    const ideaWords = new Set(
      idea.content.toLowerCase()
        .replace(/[^\w\s]/g, '')
        .split(/\s+/)
        .filter(word => word.length > 3) // Only consider words longer than 3 chars
    );
    
    // Compare with all other ideas
    Object.entries(store.ideas).forEach(([otherId, otherIdea]) => {
      // Skip self-comparison
      if (otherId === ideaId) return;
      
      const otherWords = new Set(
        otherIdea.content.toLowerCase()
          .replace(/[^\w\s]/g, '')
          .split(/\s+/)
          .filter(word => word.length > 3)
      );
      
      // Calculate Jaccard similarity
      const intersection = new Set([...ideaWords].filter(word => otherWords.has(word)));
      const union = new Set([...ideaWords, ...otherWords]);
      
      const similarity = intersection.size / union.size;
      
      if (similarity >= threshold) {
        connections.push(otherId);
      }
    });
    
    return connections;
  }
  
  /**
   * Update connections for an idea
   */
  updateConnections(ideaId: string): void {
    const store = useBrainstormStore.getState();
    const connections = this.findPotentialConnections(ideaId);
    
    if (connections.length > 0) {
      store.updateIdea(ideaId, { connections });
    }
  }
}

// Export singleton instance
export const ideaExtractor = IdeaExtractor.getInstance();