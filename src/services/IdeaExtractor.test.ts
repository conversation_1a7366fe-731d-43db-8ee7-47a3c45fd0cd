import { describe, it, expect, vi, beforeEach } from 'vitest';
import { IdeaExtractor } from './IdeaExtractor';
import { ChatMessage } from '@/types/brainstorm';

describe('IdeaExtractor', () => {
  let extractor: IdeaExtractor;

  beforeEach(() => {
    extractor = new IdeaExtractor();
  });

  describe('extractIdeas', () => {
    it('should extract ideas from chat messages', () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'user',
          content: 'We should create a mobile app for tracking fitness goals',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'assistant',
          content: 'Great idea! We could also add social features for motivation',
          timestamp: new Date().toISOString(),
        },
      ];

      const ideas = extractor.extractIdeas(messages, 'session-1');

      expect(ideas).toHaveLength(2);
      expect(ideas[0].content).toContain('mobile app for tracking fitness goals');
      expect(ideas[1].content).toContain('social features for motivation');
      expect(ideas[0].sessionId).toBe('session-1');
    });

    it('should not extract ideas from system messages', () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'system',
          content: 'System initialization complete',
          timestamp: new Date().toISOString(),
        },
      ];

      const ideas = extractor.extractIdeas(messages, 'session-1');
      expect(ideas).toHaveLength(0);
    });

    it('should assign appropriate priorities based on keywords', () => {
      const messages: ChatMessage[] = [
        {
          id: '1',
          role: 'user',
          content: 'Critical: We must implement security measures immediately',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          role: 'user',
          content: 'Nice to have: Add dark mode support',
          timestamp: new Date().toISOString(),
        },
      ];

      const ideas = extractor.extractIdeas(messages, 'session-1');

      expect(ideas[0].priority).toBe('high');
      expect(ideas[1].priority).toBe('low');
    });
  });

  describe('analyzeContent', () => {
    it('should identify actionable content', () => {
      const actionableContent = 'We should implement user authentication';
      const nonActionableContent = 'The weather is nice today';

      expect(extractor['isActionable'](actionableContent)).toBe(true);
      expect(extractor['isActionable'](nonActionableContent)).toBe(false);
    });

    it('should extract tags from content', () => {
      const content = 'Build a #mobile #app for #fitness tracking';
      const tags = extractor['extractTags'](content);

      expect(tags).toEqual(['mobile', 'app', 'fitness']);
    });
  });

  describe('linkIdeas', () => {
    it('should link related ideas based on content similarity', () => {
      const ideas = [
        {
          id: 'idea-1',
          content: 'Create user authentication system',
          sessionId: 'session-1',
          status: 'active' as const,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          tags: ['auth', 'security'],
          priority: 'high' as const,
          linkedIdeas: [],
        },
        {
          id: 'idea-2',
          content: 'Implement OAuth for authentication',
          sessionId: 'session-1',
          status: 'active' as const,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          tags: ['auth', 'oauth'],
          priority: 'medium' as const,
          linkedIdeas: [],
        },
      ];

      const linkedIdeas = extractor.linkIdeas(ideas);

      expect(linkedIdeas[0].linkedIdeas).toContain('idea-2');
      expect(linkedIdeas[1].linkedIdeas).toContain('idea-1');
    });
  });
});