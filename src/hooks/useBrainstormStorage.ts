/**
 * Hook to integrate brainstorm storage with the store
 * 
 * This hook ensures that brainstorming data is persisted to the file system
 * and loaded on app startup.
 */

import { useEffect, useCallback } from 'react';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { brainstormStorage } from '@/lib/brainstorm-storage';
import { useToast } from '@/hooks/useToast';

export const useBrainstormStorage = () => {
  const { toast } = useToast();
  const store = useBrainstormStore();

  // Initialize storage on mount
  useEffect(() => {
    const initStorage = async () => {
      try {
        // Initialize storage directories
        await brainstormStorage.initialize();

        // Load all data from storage
        const [sessions, ideas, clusters, memories, templates, settings, personas, integrations] = await Promise.all([
          brainstormStorage.loadAllSessions(),
          brainstormStorage.loadAllIdeas(),
          brainstormStorage.loadAllClusters(),
          brainstormStorage.loadAllMemories(),
          brainstormStorage.loadAllTemplates(),
          brainstormStorage.loadSettings(),
          brainstormStorage.loadPersonas(),
          brainstormStorage.loadIntegrations()
        ]);

        // Update store with loaded data
        useBrainstormStore.setState({
          sessions: sessions || {},
          ideas: ideas || {},
          clusters: clusters || {},
          memories: memories || {},
          templates: templates || {},
          settings: settings || store.settings,
          personas: personas || store.personas,
          integrations: integrations || {}
        });

        console.log('Brainstorm storage initialized successfully');
      } catch (error) {
        console.error('Failed to initialize brainstorm storage:', error);
        toast({
          message: 'Failed to load brainstorming data',
          type: 'error'
        });
      }
    };

    initStorage();
  }, []);

  // Auto-save sessions when they change
  useEffect(() => {
    const unsubscribe = useBrainstormStore.subscribe(
      (state) => state.sessions,
      async (sessions) => {
        try {
          // Save each session
          for (const session of Object.values(sessions)) {
            await brainstormStorage.saveSession(session);
          }
        } catch (error) {
          console.error('Failed to save sessions:', error);
        }
      }
    );

    return unsubscribe;
  }, []);

  // Auto-save ideas when they change
  useEffect(() => {
    const unsubscribe = useBrainstormStore.subscribe(
      (state) => state.ideas,
      async (ideas) => {
        try {
          // Save each idea
          for (const idea of Object.values(ideas)) {
            await brainstormStorage.saveIdea(idea);
          }
        } catch (error) {
          console.error('Failed to save ideas:', error);
        }
      }
    );

    return unsubscribe;
  }, []);

  // Auto-save clusters when they change
  useEffect(() => {
    const unsubscribe = useBrainstormStore.subscribe(
      (state) => state.clusters,
      async (clusters) => {
        try {
          // Save each cluster
          for (const cluster of Object.values(clusters)) {
            await brainstormStorage.saveCluster(cluster);
          }
        } catch (error) {
          console.error('Failed to save clusters:', error);
        }
      }
    );

    return unsubscribe;
  }, []);

  // Auto-save settings when they change
  useEffect(() => {
    const unsubscribe = useBrainstormStore.subscribe(
      (state) => state.settings,
      async (settings) => {
        try {
          await brainstormStorage.saveSettings(settings);
        } catch (error) {
          console.error('Failed to save settings:', error);
        }
      }
    );

    return unsubscribe;
  }, []);

  // Manual save function
  const saveAll = useCallback(async () => {
    try {
      const state = useBrainstormStore.getState();
      
      // Save all data
      await Promise.all([
        ...Object.values(state.sessions).map(session => brainstormStorage.saveSession(session)),
        ...Object.values(state.ideas).map(idea => brainstormStorage.saveIdea(idea)),
        ...Object.values(state.clusters).map(cluster => brainstormStorage.saveCluster(cluster)),
        ...Object.values(state.memories).map(memory => brainstormStorage.saveMemory(memory)),
        ...Object.values(state.templates).map(template => brainstormStorage.saveTemplate(template)),
        brainstormStorage.saveSettings(state.settings),
        brainstormStorage.savePersonas(state.personas),
        brainstormStorage.saveIntegrations(state.integrations)
      ]);

      toast({
        message: 'Brainstorming data saved successfully',
        type: 'success'
      });
    } catch (error) {
      console.error('Failed to save brainstorming data:', error);
      toast({
        message: 'Failed to save brainstorming data',
        type: 'error'
      });
    }
  }, [toast]);

  // Create backup
  const createBackup = useCallback(async () => {
    try {
      const backupPath = await brainstormStorage.createBackup();
      toast({
        message: `Backup created successfully: ${backupPath}`,
        type: 'success'
      });
      return backupPath;
    } catch (error) {
      console.error('Failed to create backup:', error);
      toast({
        message: 'Failed to create backup',
        type: 'error'
      });
      throw error;
    }
  }, [toast]);

  // Restore from backup
  const restoreBackup = useCallback(async (backupPath: string) => {
    try {
      await brainstormStorage.restoreFromBackup(backupPath);
      
      // Reload data into store
      const [sessions, ideas, clusters, memories, templates, settings, personas, integrations] = await Promise.all([
        brainstormStorage.loadAllSessions(),
        brainstormStorage.loadAllIdeas(),
        brainstormStorage.loadAllClusters(),
        brainstormStorage.loadAllMemories(),
        brainstormStorage.loadAllTemplates(),
        brainstormStorage.loadSettings(),
        brainstormStorage.loadPersonas(),
        brainstormStorage.loadIntegrations()
      ]);

      // Update store with restored data
      useBrainstormStore.setState({
        sessions: sessions || {},
        ideas: ideas || {},
        clusters: clusters || {},
        memories: memories || {},
        templates: templates || {},
        settings: settings || store.settings,
        personas: personas || store.personas,
        integrations: integrations || {}
      });

      toast({
        message: 'Backup restored successfully',
        type: 'success'
      });
    } catch (error) {
      console.error('Failed to restore backup:', error);
      toast({
        message: 'Failed to restore backup',
        type: 'error'
      });
      throw error;
    }
  }, [store.settings, store.personas, toast]);

  // Cleanup old data
  const cleanup = useCallback(async (olderThanDays: number = 30) => {
    try {
      await brainstormStorage.cleanup(olderThanDays);
      toast({
        message: `Cleaned up data older than ${olderThanDays} days`,
        type: 'success'
      });
    } catch (error) {
      console.error('Failed to cleanup data:', error);
      toast({
        message: 'Failed to cleanup old data',
        type: 'error'
      });
    }
  }, [toast]);

  return {
    saveAll,
    createBackup,
    restoreBackup,
    cleanup
  };
};