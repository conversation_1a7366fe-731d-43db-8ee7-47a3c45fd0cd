/**
 * React hook for using the Claude Session Manager
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import { sessionManager, type CreateSessionOptions, type ResumeSessionOptions } from '../lib/session-manager';
import type { 
  SessionState, 
  SessionData, 
  ModelType, 
  SessionSettings,
  UnsubscribeFn 
} from '../types/session';

/**
 * Hook return type
 */
export interface UseSessionManagerReturn {
  // State
  sessionState: SessionState;
  currentSession: SessionData | null;
  isLoading: boolean;
  isStreaming: boolean;
  error: string | null;
  
  // Actions
  createSession: (options: CreateSessionOptions) => Promise<SessionData>;
  resumeSession: (options: ResumeSessionOptions) => Promise<SessionData>;
  terminateSession: () => Promise<void>;
  sendPrompt: (prompt: string, model?: ModelType) => Promise<void>;
  queuePrompt: (prompt: string, model?: ModelType, priority?: number) => void;
  cancelExecution: () => Promise<void>;
  updateSettings: (settings: Partial<SessionSettings>) => void;
  clearError: () => void;
}

/**
 * React hook for managing Claude sessions
 */
export function useSessionManager(): UseSessionManagerReturn {
  const [sessionState, setSessionState] = useState<SessionState>(sessionManager.getSessionState());
  const unsubscribeRef = useRef<UnsubscribeFn | null>(null);

  // Subscribe to session state updates
  useEffect(() => {
    unsubscribeRef.current = sessionManager.subscribeToUpdates((newState) => {
      setSessionState(newState);
    });

    // Initial state sync
    setSessionState(sessionManager.getSessionState());

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, []);

  // Session management actions
  const createSession = useCallback(async (options: CreateSessionOptions): Promise<SessionData> => {
    return await sessionManager.createSession(options);
  }, []);

  const resumeSession = useCallback(async (options: ResumeSessionOptions): Promise<SessionData> => {
    return await sessionManager.resumeSession(options);
  }, []);

  const terminateSession = useCallback(async (): Promise<void> => {
    await sessionManager.terminateSession();
  }, []);

  const sendPrompt = useCallback(async (prompt: string, model: ModelType = 'sonnet'): Promise<void> => {
    await sessionManager.sendPrompt(prompt, model);
  }, []);

  const queuePrompt = useCallback((prompt: string, model: ModelType = 'sonnet', priority: number = 0): void => {
    sessionManager.queuePrompt(prompt, model, priority);
  }, []);

  const cancelExecution = useCallback(async (): Promise<void> => {
    await sessionManager.cancelExecution();
  }, []);

  const updateSettings = useCallback((settings: Partial<SessionSettings>): void => {
    sessionManager.updateSessionSettings(settings);
  }, []);

  const clearError = useCallback((): void => {
    // Update state to clear error
    setSessionState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    // State
    sessionState,
    currentSession: sessionState.currentSession,
    isLoading: sessionState.isLoading,
    isStreaming: sessionState.isStreaming,
    error: sessionState.error,
    
    // Actions
    createSession,
    resumeSession,
    terminateSession,
    sendPrompt,
    queuePrompt,
    cancelExecution,
    updateSettings,
    clearError
  };
}

/**
 * Hook for session events
 */
export function useSessionEvents() {
  const [events, setEvents] = useState<Array<{ id: string; event: any; timestamp: string }>>([]);
  const unsubscribeRef = useRef<UnsubscribeFn | null>(null);

  useEffect(() => {
    const eventTypes = [
      'session_created',
      'session_resumed', 
      'session_terminated',
      'message_received',
      'message_streaming',
      'checkpoint_created',
      'error_occurred'
    ];

    const unsubscribeFunctions: UnsubscribeFn[] = [];

    eventTypes.forEach(eventType => {
      const unsubscribe = sessionManager.addEventListener(eventType, (event) => {
        const eventRecord = {
          id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          event,
          timestamp: new Date().toISOString()
        };
        
        setEvents(prev => [...prev.slice(-99), eventRecord]); // Keep last 100 events
      });
      
      unsubscribeFunctions.push(unsubscribe);
    });

    unsubscribeRef.current = () => {
      unsubscribeFunctions.forEach(fn => fn());
    };

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, []);

  const clearEvents = useCallback(() => {
    setEvents([]);
  }, []);

  return {
    events,
    clearEvents
  };
}