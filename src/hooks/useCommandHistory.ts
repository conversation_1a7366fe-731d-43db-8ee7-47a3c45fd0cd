import { useState, useCallback, useEffect } from "react";

interface CommandHistoryOptions {
  maxHistory?: number;
  storageKey?: string;
  deduplicate?: boolean;
  caseSensitive?: boolean;
}

export const useCommandHistory = (options: CommandHistoryOptions = {}) => {
  const {
    maxHistory = 100,
    storageKey = "claude-command-history",
    deduplicate = true,
    caseSensitive = false
  } = options;

  const [history, setHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [tempCommand, setTempCommand] = useState("");

  // Load history from localStorage on mount
  useEffect(() => {
    try {
      const savedHistory = localStorage.getItem(storageKey);
      if (savedHistory) {
        const parsed = JSON.parse(savedHistory);
        if (Array.isArray(parsed)) {
          setHistory(parsed);
        }
      }
    } catch (error) {
      console.error("Failed to load command history:", error);
    }
  }, [storageKey]);

  // Save history to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem(storageKey, JSON.stringify(history));
    } catch (error) {
      console.error("Failed to save command history:", error);
    }
  }, [history, storageKey]);

  // Add command to history
  const addToHistory = useCallback((command: string) => {
    if (!command.trim()) return;

    setHistory(prev => {
      let newHistory = [...prev];
      
      // Remove duplicates if enabled
      if (deduplicate) {
        const compareCommand = caseSensitive ? command : command.toLowerCase();
        newHistory = newHistory.filter(cmd => {
          const compareWith = caseSensitive ? cmd : cmd.toLowerCase();
          return compareWith !== compareCommand;
        });
      }
      
      // Add new command at the beginning
      newHistory.unshift(command);
      
      // Limit history size
      if (newHistory.length > maxHistory) {
        newHistory = newHistory.slice(0, maxHistory);
      }
      
      return newHistory;
    });
    
    // Reset history navigation
    setHistoryIndex(-1);
    setTempCommand("");
  }, [deduplicate, caseSensitive, maxHistory]);

  // Navigate to previous command
  const navigateToPrevious = useCallback((currentCommand: string) => {
    if (history.length === 0) return currentCommand;

    let newIndex = historyIndex;
    
    // Save current command if starting navigation
    if (historyIndex === -1) {
      setTempCommand(currentCommand);
      newIndex = 0;
    } else if (historyIndex < history.length - 1) {
      newIndex = historyIndex + 1;
    }
    
    setHistoryIndex(newIndex);
    return history[newIndex] || currentCommand;
  }, [history, historyIndex]);

  // Navigate to next command
  const navigateToNext = useCallback((currentCommand: string) => {
    if (historyIndex <= 0) {
      setHistoryIndex(-1);
      return tempCommand;
    }
    
    const newIndex = historyIndex - 1;
    setHistoryIndex(newIndex);
    
    if (newIndex === -1) {
      return tempCommand;
    }
    
    return history[newIndex] || currentCommand;
  }, [history, historyIndex, tempCommand]);

  // Search history
  const searchHistory = useCallback((query: string): string[] => {
    if (!query) return history;
    
    const searchQuery = caseSensitive ? query : query.toLowerCase();
    
    return history.filter(cmd => {
      const compareCmd = caseSensitive ? cmd : cmd.toLowerCase();
      return compareCmd.includes(searchQuery);
    });
  }, [history, caseSensitive]);

  // Clear history
  const clearHistory = useCallback(() => {
    setHistory([]);
    setHistoryIndex(-1);
    setTempCommand("");
    try {
      localStorage.removeItem(storageKey);
    } catch (error) {
      console.error("Failed to clear command history:", error);
    }
  }, [storageKey]);

  // Get recent commands
  const getRecentCommands = useCallback((count: number = 10): string[] => {
    return history.slice(0, count);
  }, [history]);

  // Remove specific command from history
  const removeFromHistory = useCallback((index: number) => {
    setHistory(prev => prev.filter((_, i) => i !== index));
  }, []);

  return {
    history,
    historyIndex,
    addToHistory,
    navigateToPrevious,
    navigateToNext,
    searchHistory,
    clearHistory,
    getRecentCommands,
    removeFromHistory,
    historyLength: history.length
  };
};

export default useCommandHistory;