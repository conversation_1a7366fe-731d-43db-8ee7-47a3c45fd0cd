/**
 * Undo/Redo Hook for Brainstorming Operations
 * 
 * Provides undo/redo functionality for idea operations with proper state management
 * and keyboard shortcuts support.
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { Idea } from '@/types/brainstorm';

interface UndoRedoAction {
  id: string;
  type: 'create' | 'update' | 'delete' | 'bulk_update' | 'bulk_delete';
  timestamp: number;
  sessionId: string;
  data: {
    // For create/delete: full idea data
    idea?: Idea;
    ideas?: Idea[];
    // For update: before/after states
    before?: Partial<Idea>;
    after?: Partial<Idea>;
    ideaId?: string;
    ideaIds?: string[];
  };
}

interface UseUndoRedoOptions {
  maxHistorySize?: number;
  enableKeyboardShortcuts?: boolean;
}

export const useUndoRedo = (options: UseUndoRedoOptions = {}) => {
  const { maxHistorySize = 50, enableKeyboardShortcuts = true } = options;
  
  const store = useBrainstormStore();
  const [undoStack, setUndoStack] = useState<UndoRedoAction[]>([]);
  const [redoStack, setRedoStack] = useState<UndoRedoAction[]>([]);
  const isPerformingUndoRedo = useRef(false);

  // Add action to undo stack
  const addAction = useCallback((action: Omit<UndoRedoAction, 'id' | 'timestamp'>) => {
    if (isPerformingUndoRedo.current) return;

    const newAction: UndoRedoAction = {
      ...action,
      id: `${Date.now()}_${Math.random()}`,
      timestamp: Date.now()
    };

    setUndoStack(prev => {
      const newStack = [...prev, newAction];
      // Keep only the most recent actions
      return newStack.slice(-maxHistorySize);
    });

    // Clear redo stack when new action is added
    setRedoStack([]);
  }, [maxHistorySize]);

  // Undo last action
  const undo = useCallback(() => {
    if (undoStack.length === 0) return false;

    const actionToUndo = undoStack[undoStack.length - 1];
    isPerformingUndoRedo.current = true;

    try {
      switch (actionToUndo.type) {
        case 'create':
          // Undo create by deleting the idea
          if (actionToUndo.data.idea) {
            store.deleteIdea(actionToUndo.data.idea.id);
          }
          break;

        case 'delete':
          // Undo delete by recreating the idea
          if (actionToUndo.data.idea) {
            const { id, createdAt, updatedAt, ...ideaData } = actionToUndo.data.idea;
            store.addIdeaFull(ideaData);
          }
          break;

        case 'update':
          // Undo update by reverting to previous state
          if (actionToUndo.data.ideaId && actionToUndo.data.before) {
            store.updateIdea(actionToUndo.data.ideaId, actionToUndo.data.before);
          }
          break;

        case 'bulk_delete':
          // Undo bulk delete by recreating all ideas
          if (actionToUndo.data.ideas) {
            actionToUndo.data.ideas.forEach(idea => {
              const { id, createdAt, updatedAt, ...ideaData } = idea;
              store.addIdeaFull(ideaData);
            });
          }
          break;

        case 'bulk_update':
          // Undo bulk update by reverting each idea
          if (actionToUndo.data.ideaIds && actionToUndo.data.before) {
            actionToUndo.data.ideaIds.forEach(ideaId => {
              store.updateIdea(ideaId, actionToUndo.data.before!);
            });
          }
          break;
      }

      // Move action from undo to redo stack
      setUndoStack(prev => prev.slice(0, -1));
      setRedoStack(prev => [...prev, actionToUndo]);

      return true;
    } catch (error) {
      console.error('Failed to undo action:', error);
      return false;
    } finally {
      isPerformingUndoRedo.current = false;
    }
  }, [undoStack, store]);

  // Redo last undone action
  const redo = useCallback(() => {
    if (redoStack.length === 0) return false;

    const actionToRedo = redoStack[redoStack.length - 1];
    isPerformingUndoRedo.current = true;

    try {
      switch (actionToRedo.type) {
        case 'create':
          // Redo create by adding the idea back
          if (actionToRedo.data.idea) {
            const { id, createdAt, updatedAt, ...ideaData } = actionToRedo.data.idea;
            store.addIdeaFull(ideaData);
          }
          break;

        case 'delete':
          // Redo delete by removing the idea again
          if (actionToRedo.data.idea) {
            store.deleteIdea(actionToRedo.data.idea.id);
          }
          break;

        case 'update':
          // Redo update by applying the new state
          if (actionToRedo.data.ideaId && actionToRedo.data.after) {
            store.updateIdea(actionToRedo.data.ideaId, actionToRedo.data.after);
          }
          break;

        case 'bulk_delete':
          // Redo bulk delete by removing all ideas again
          if (actionToRedo.data.ideas) {
            actionToRedo.data.ideas.forEach(idea => {
              store.deleteIdea(idea.id);
            });
          }
          break;

        case 'bulk_update':
          // Redo bulk update by applying changes again
          if (actionToRedo.data.ideaIds && actionToRedo.data.after) {
            actionToRedo.data.ideaIds.forEach(ideaId => {
              store.updateIdea(ideaId, actionToRedo.data.after!);
            });
          }
          break;
      }

      // Move action from redo to undo stack
      setRedoStack(prev => prev.slice(0, -1));
      setUndoStack(prev => [...prev, actionToRedo]);

      return true;
    } catch (error) {
      console.error('Failed to redo action:', error);
      return false;
    } finally {
      isPerformingUndoRedo.current = false;
    }
  }, [redoStack, store]);

  // Clear all history
  const clearHistory = useCallback(() => {
    setUndoStack([]);
    setRedoStack([]);
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    if (!enableKeyboardShortcuts) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+Z / Cmd+Z for undo
      if ((event.ctrlKey || event.metaKey) && event.key === 'z' && !event.shiftKey) {
        event.preventDefault();
        undo();
      }
      // Ctrl+Shift+Z / Cmd+Shift+Z for redo
      else if ((event.ctrlKey || event.metaKey) && event.key === 'z' && event.shiftKey) {
        event.preventDefault();
        redo();
      }
      // Ctrl+Y / Cmd+Y for redo (alternative)
      else if ((event.ctrlKey || event.metaKey) && event.key === 'y') {
        event.preventDefault();
        redo();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [undo, redo, enableKeyboardShortcuts]);

  // Helper functions for common operations
  const recordCreate = useCallback((sessionId: string, idea: Idea) => {
    addAction({
      type: 'create',
      sessionId,
      data: { idea }
    });
  }, [addAction]);

  const recordUpdate = useCallback((sessionId: string, ideaId: string, before: Partial<Idea>, after: Partial<Idea>) => {
    addAction({
      type: 'update',
      sessionId,
      data: { ideaId, before, after }
    });
  }, [addAction]);

  const recordDelete = useCallback((sessionId: string, idea: Idea) => {
    addAction({
      type: 'delete',
      sessionId,
      data: { idea }
    });
  }, [addAction]);

  const recordBulkDelete = useCallback((sessionId: string, ideas: Idea[]) => {
    addAction({
      type: 'bulk_delete',
      sessionId,
      data: { ideas }
    });
  }, [addAction]);

  const recordBulkUpdate = useCallback((sessionId: string, ideaIds: string[], before: Partial<Idea>, after: Partial<Idea>) => {
    addAction({
      type: 'bulk_update',
      sessionId,
      data: { ideaIds, before, after }
    });
  }, [addAction]);

  return {
    // State
    canUndo: undoStack.length > 0,
    canRedo: redoStack.length > 0,
    undoStackSize: undoStack.length,
    redoStackSize: redoStack.length,
    
    // Actions
    undo,
    redo,
    clearHistory,
    
    // Recording helpers
    recordCreate,
    recordUpdate,
    recordDelete,
    recordBulkDelete,
    recordBulkUpdate,
    
    // Recent actions (for UI display)
    recentActions: undoStack.slice(-5).reverse(),
    
    // Utility
    isPerformingUndoRedo: isPerformingUndoRedo.current
  };
};

export type UndoRedoHook = ReturnType<typeof useUndoRedo>;