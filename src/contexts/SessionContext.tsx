/**
 * React Context for Claude Session Management
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { sessionManager } from '../lib/session-manager';
import type { SessionState, SessionData } from '../types/session';

/**
 * Session context type
 */
interface SessionContextType {
  sessionState: SessionState;
  currentSession: SessionData | null;
  isLoading: boolean;
  isStreaming: boolean;
  error: string | null;
}

/**
 * Session context
 */
const SessionContext = createContext<SessionContextType | null>(null);

/**
 * Session context provider props
 */
interface SessionProviderProps {
  children: React.ReactNode;
}

/**
 * Session context provider component
 */
export function SessionProvider({ children }: SessionProviderProps) {
  const [sessionState, setSessionState] = useState<SessionState>(sessionManager.getSessionState());

  useEffect(() => {
    // Subscribe to session state updates
    const unsubscribe = sessionManager.subscribeToUpdates((newState) => {
      setSessionState(newState);
    });

    // Initial state sync
    setSessionState(sessionManager.getSessionState());

    return unsubscribe;
  }, []);

  const contextValue: SessionContextType = {
    sessionState,
    currentSession: sessionState.currentSession,
    isLoading: sessionState.isLoading,
    isStreaming: sessionState.isStreaming,
    error: sessionState.error
  };

  return (
    <SessionContext.Provider value={contextValue}>
      {children}
    </SessionContext.Provider>
  );
}

/**
 * Hook to use session context
 */
export function useSessionContext(): SessionContextType {
  const context = useContext(SessionContext);
  
  if (!context) {
    throw new Error('useSessionContext must be used within a SessionProvider');
  }
  
  return context;
}