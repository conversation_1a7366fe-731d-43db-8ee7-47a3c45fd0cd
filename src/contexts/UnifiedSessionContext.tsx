/**
 * Unified Session Context Provider
 * 
 * Provides unified session management across all session types (<PERSON>, <PERSON><PERSON>, Orchestra)
 * using the Master Session Coordinator. This replaces fragmented session management
 * with a centralized approach.
 */

import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import { masterSessionCoordinator } from '../lib/master-session-coordinator';
import type {
  UnifiedSessionInfo,
  SessionType,
  CreateSessionOptions,
  SessionTransitionOptions,
  SessionContextValue,
  SessionUIMetadata,
  SessionNavigation,
  UnifiedSessionSettings,
  SessionLifecycleEvent,
  SessionEventCallback,
  SessionRelationship
} from '../types/unified-session';

/**
 * Default unified session settings
 */
const DEFAULT_UNIFIED_SETTINGS: UnifiedSessionSettings = {
  autoSave: true,
  autoSaveInterval: 5,
  maxConcurrentSessions: 10,
  sessionTimeout: 60,
  preserveSessionsOnClose: true,
  enableCrossSessionSharing: true,
  autoCreateRelationships: true,
  showSessionRelationships: true,
  groupSessionsByProject: false,
  showSessionPreviews: true,
  notifyOnSessionCreated: true,
  notifyOnSessionShared: false,
  notifyOnSessionErrors: true
};

/**
 * Unified Session Context
 */
const UnifiedSessionContext = createContext<SessionContextValue | null>(null);

/**
 * Session Provider Props
 */
interface UnifiedSessionProviderProps {
  children: React.ReactNode;
  initialSettings?: Partial<UnifiedSessionSettings>;
}

/**
 * Unified Session Provider Component
 */
export function UnifiedSessionProvider({ children, initialSettings = {} }: UnifiedSessionProviderProps) {
  // Core session state
  const [activeSession, setActiveSession] = useState<UnifiedSessionInfo | null>(null);
  const [allSessions, setAllSessions] = useState<UnifiedSessionInfo[]>([]);
  const [sessionHistory, setSessionHistory] = useState<string[]>([]);
  const [settings, setSettings] = useState<UnifiedSessionSettings>({
    ...DEFAULT_UNIFIED_SETTINGS,
    ...initialSettings
  });

  // Navigation state
  const [navigation, setNavigation] = useState<SessionNavigation>({
    currentSessionId: null,
    sessionStack: [],
    pinnedSessions: [],
    recentSessions: []
  });

  // Event subscriptions
  const eventSubscriptionRef = useRef<(() => void) | null>(null);

  /**
   * Initialize provider state from coordinator
   */
  useEffect(() => {
    // Initialize state from coordinator
    const currentActive = masterSessionCoordinator.getActiveSession();
    const allSessionsData = masterSessionCoordinator.getAllSessions();
    const historyData = masterSessionCoordinator.getSessionHistory();

    setActiveSession(currentActive);
    setAllSessions(allSessionsData);
    setSessionHistory(historyData);
    setNavigation(prev => ({
      ...prev,
      currentSessionId: currentActive?.id || null,
      recentSessions: historyData.slice(0, 10)
    }));

    // Subscribe to coordinator events
    eventSubscriptionRef.current = masterSessionCoordinator.subscribe(handleSessionEvent);

    return () => {
      if (eventSubscriptionRef.current) {
        eventSubscriptionRef.current();
      }
    };
  }, []);

  /**
   * Handle session lifecycle events from coordinator
   */
  const handleSessionEvent = useCallback((event: SessionLifecycleEvent) => {
    switch (event.type) {
      case 'session_created':
        setAllSessions(prev => [...prev, event.sessionInfo]);
        if (settings.notifyOnSessionCreated) {
          // Emit notification
          window.dispatchEvent(new CustomEvent('session-notification', {
            detail: {
              type: 'success',
              message: `${event.sessionInfo.type} session created: ${event.sessionInfo.title}`
            }
          }));
        }
        break;

      case 'session_activated':
        const activatedSession = masterSessionCoordinator.getSession(event.sessionId);
        if (activatedSession) {
          setActiveSession(activatedSession);
          setNavigation(prev => ({
            ...prev,
            currentSessionId: event.sessionId,
            sessionStack: [event.sessionId, ...prev.sessionStack.filter(id => id !== event.sessionId)].slice(0, 20)
          }));
        }
        break;

      case 'session_terminated':
        setAllSessions(prev => prev.filter(session => session.id !== event.sessionId));
        if (activeSession?.id === event.sessionId) {
          setActiveSession(null);
          setNavigation(prev => ({
            ...prev,
            currentSessionId: null
          }));
        }
        break;

      case 'session_relationship_created':
        // Update sessions list to reflect new relationships
        setAllSessions(masterSessionCoordinator.getAllSessions());
        break;

      case 'cross_session_data_shared':
        if (settings.notifyOnSessionShared) {
          window.dispatchEvent(new CustomEvent('session-notification', {
            detail: {
              type: 'info',
              message: `Data shared between sessions`
            }
          }));
        }
        break;
    }

    // Update history
    setSessionHistory(masterSessionCoordinator.getSessionHistory());
  }, [activeSession, settings]);

  /**
   * Create a new session
   */
  const createSession = useCallback(async (options: CreateSessionOptions): Promise<UnifiedSessionInfo> => {
    try {
      const sessionInfo = await masterSessionCoordinator.createSession(options.type, {
        title: options.title,
        projectPath: options.projectPath,
        parentSessionId: options.parentSessionId,
        initialData: options.initialData
      });

      // Auto-activate if no active session or if requested
      if (!activeSession) {
        await masterSessionCoordinator.activateSession(sessionInfo.id);
      }

      return sessionInfo;
    } catch (error) {
      if (settings.notifyOnSessionErrors) {
        window.dispatchEvent(new CustomEvent('session-notification', {
          detail: {
            type: 'error',
            message: `Failed to create ${options.type} session: ${error instanceof Error ? error.message : 'Unknown error'}`
          }
        }));
      }
      throw error;
    }
  }, [activeSession, settings]);

  /**
   * Activate a session
   */
  const activateSession = useCallback(async (sessionId: string): Promise<void> => {
    try {
      await masterSessionCoordinator.activateSession(sessionId);
    } catch (error) {
      if (settings.notifyOnSessionErrors) {
        window.dispatchEvent(new CustomEvent('session-notification', {
          detail: {
            type: 'error',
            message: `Failed to activate session: ${error instanceof Error ? error.message : 'Unknown error'}`
          }
        }));
      }
      throw error;
    }
  }, [settings]);

  /**
   * Pause a session
   */
  const pauseSession = useCallback(async (sessionId: string): Promise<void> => {
    try {
      await masterSessionCoordinator.pauseSession(sessionId);
    } catch (error) {
      if (settings.notifyOnSessionErrors) {
        window.dispatchEvent(new CustomEvent('session-notification', {
          detail: {
            type: 'error',
            message: `Failed to pause session: ${error instanceof Error ? error.message : 'Unknown error'}`
          }
        }));
      }
      throw error;
    }
  }, [settings]);

  /**
   * Resume a session
   */
  const resumeSession = useCallback(async (sessionId: string): Promise<void> => {
    try {
      await masterSessionCoordinator.resumeSession(sessionId);
    } catch (error) {
      if (settings.notifyOnSessionErrors) {
        window.dispatchEvent(new CustomEvent('session-notification', {
          detail: {
            type: 'error',
            message: `Failed to resume session: ${error instanceof Error ? error.message : 'Unknown error'}`
          }
        }));
      }
      throw error;
    }
  }, [settings]);

  /**
   * Terminate a session
   */
  const terminateSession = useCallback(async (sessionId: string): Promise<void> => {
    try {
      await masterSessionCoordinator.terminateSession(sessionId);
    } catch (error) {
      if (settings.notifyOnSessionErrors) {
        window.dispatchEvent(new CustomEvent('session-notification', {
          detail: {
            type: 'error',
            message: `Failed to terminate session: ${error instanceof Error ? error.message : 'Unknown error'}`
          }
        }));
      }
      throw error;
    }
  }, [settings]);

  /**
   * Create relationship between sessions
   */
  const createRelationship = useCallback((parentId: string, childId: string, type: any) => {
    masterSessionCoordinator.createSessionRelationship(parentId, childId, type);
  }, []);

  /**
   * Get relationships for a session
   */
  const getRelationships = useCallback((sessionId: string): SessionRelationship[] => {
    return masterSessionCoordinator.getSessionRelationships(sessionId);
  }, []);

  /**
   * Share data between sessions
   */
  const shareData = useCallback((sourceId: string, targetId: string, dataType: string, data: any) => {
    masterSessionCoordinator.shareCrossSessionData(sourceId, targetId, dataType as any, data);
  }, []);

  /**
   * Transition from one session type to another
   */
  const transitionSession = useCallback(async (options: SessionTransitionOptions): Promise<UnifiedSessionInfo> => {
    const sourceSession = masterSessionCoordinator.getSession(options.sourceSessionId);
    if (!sourceSession) {
      throw new Error('Source session not found');
    }

    // Create new session
    const newSession = await createSession({
      type: options.targetSessionType,
      title: `${options.transitionType === 'spawn' ? 'Spawned from' : 'Transitioned from'} ${sourceSession.title}`,
      projectPath: sourceSession.projectPath,
      parentSessionId: options.preserveSource ? options.sourceSessionId : undefined,
      initialData: options.dataToTransfer
    });

    // Create relationship
    if (options.preserveSource) {
      createRelationship(options.sourceSessionId, newSession.id, options.transitionType as any);
    }

    // Share data if specified
    if (options.dataToTransfer) {
      Object.entries(options.dataToTransfer).forEach(([dataType, data]) => {
        if (data) {
          shareData(options.sourceSessionId, newSession.id, dataType, data);
        }
      });
    }

    // Terminate source session if not preserving
    if (!options.preserveSource) {
      await terminateSession(options.sourceSessionId);
    }

    return newSession;
  }, [createSession, createRelationship, shareData, terminateSession]);

  /**
   * Subscribe to session events
   */
  const subscribe = useCallback((callback: SessionEventCallback) => {
    return masterSessionCoordinator.subscribe(callback);
  }, []);

  /**
   * Get session UI metadata for display
   */
  const getSessionUIMetadata = useCallback((session: UnifiedSessionInfo): SessionUIMetadata => {
    const getIcon = (type: SessionType): string => {
      switch (type) {
        case 'claude': return 'terminal';
        case 'brainstorm': return 'brain';
        case 'orchestra': return 'users';
        default: return 'circle';
      }
    };

    return {
      id: session.id,
      type: session.type,
      title: session.title,
      subtitle: session.projectPath ? `${session.projectPath.split('/').pop()}` : undefined,
      icon: getIcon(session.type),
      status: session.status,
      lastActivity: session.updatedAt,
      parentSessionId: session.parentSessionId,
      childCount: session.childSessionIds.length
    };
  }, []);

  /**
   * Context value
   */
  const contextValue: SessionContextValue = {
    // State
    activeSession,
    allSessions,
    sessionHistory,

    // Actions
    createSession,
    activateSession,
    pauseSession,
    resumeSession,
    terminateSession,

    // Relationships
    createRelationship,
    getRelationships,

    // Data sharing
    shareData,

    // Transitions
    transitionSession,

    // Events
    subscribe
  };

  return (
    <UnifiedSessionContext.Provider value={contextValue}>
      {children}
    </UnifiedSessionContext.Provider>
  );
}

/**
 * Hook to use unified session context
 */
export function useUnifiedSession(): SessionContextValue {
  const context = useContext(UnifiedSessionContext);

  if (!context) {
    throw new Error('useUnifiedSession must be used within a UnifiedSessionProvider');
  }

  return context;
}

/**
 * Hook to get session UI metadata
 */
export function useSessionUIMetadata(sessionId?: string): SessionUIMetadata | null {
  const { allSessions, activeSession } = useUnifiedSession();
  
  const session = sessionId 
    ? allSessions.find(s => s.id === sessionId)
    : activeSession;

  if (!session) return null;

  const getIcon = (type: SessionType): string => {
    switch (type) {
      case 'claude': return 'terminal';
      case 'brainstorm': return 'brain';
      case 'orchestra': return 'users';
      default: return 'circle';
    }
  };

  return {
    id: session.id,
    type: session.type,
    title: session.title,
    subtitle: session.projectPath ? `${session.projectPath.split('/').pop()}` : undefined,
    icon: getIcon(session.type),
    status: session.status,
    lastActivity: session.updatedAt,
    parentSessionId: session.parentSessionId,
    childCount: session.childSessionIds.length
  };
}

/**
 * Hook for session navigation
 */
export function useSessionNavigation() {
  const { allSessions, activeSession, activateSession } = useUnifiedSession();
  const [navigation, setNavigation] = useState<SessionNavigation>({
    currentSessionId: activeSession?.id || null,
    sessionStack: [],
    pinnedSessions: JSON.parse(localStorage.getItem('pinned-sessions') || '[]'),
    recentSessions: []
  });

  useEffect(() => {
    setNavigation(prev => ({
      ...prev,
      currentSessionId: activeSession?.id || null
    }));
  }, [activeSession]);

  const pinSession = useCallback((sessionId: string) => {
    setNavigation(prev => {
      const newPinned = [...prev.pinnedSessions, sessionId];
      localStorage.setItem('pinned-sessions', JSON.stringify(newPinned));
      return { ...prev, pinnedSessions: newPinned };
    });
  }, []);

  const unpinSession = useCallback((sessionId: string) => {
    setNavigation(prev => {
      const newPinned = prev.pinnedSessions.filter(id => id !== sessionId);
      localStorage.setItem('pinned-sessions', JSON.stringify(newPinned));
      return { ...prev, pinnedSessions: newPinned };
    });
  }, []);

  const navigateBack = useCallback(async () => {
    if (navigation.sessionStack.length > 1) {
      const previousSessionId = navigation.sessionStack[1];
      await activateSession(previousSessionId);
    }
  }, [navigation.sessionStack, activateSession]);

  return {
    navigation,
    pinSession,
    unpinSession,
    navigateBack,
    canNavigateBack: navigation.sessionStack.length > 1
  };
}