// Input validation functions for various data types
#![allow(dead_code)]

use serde_json::Value as JsonValue;
use regex::Regex;
use once_cell::sync::Lazy;

// Email validation regex
static EMAIL_REGEX: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$").unwrap()
});

// URL validation regex
static URL_REGEX: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"^https?://[a-zA-Z0-9.-]+(?:\.[a-zA-Z]{2,})+(?:/[^\s]*)?$").unwrap()
});

// Alphanumeric with underscores and hyphens
static IDENTIFIER_REGEX: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"^[a-zA-Z0-9_-]+$").unwrap()
});

/// Validates an email address
pub fn validate_email(email: &str) -> Result<(), String> {
    if !EMAIL_REGEX.is_match(email) {
        return Err("Invalid email format".to_string());
    }
    Ok(())
}

/// Validates a URL
pub fn validate_url(url: &str) -> Result<(), String> {
    if !URL_REGEX.is_match(url) {
        return Err("Invalid URL format".to_string());
    }
    Ok(())
}

/// Validates an identifier (alphanumeric with underscores and hyphens)
pub fn validate_identifier(id: &str) -> Result<(), String> {
    if !IDENTIFIER_REGEX.is_match(id) {
        return Err("Invalid identifier format".to_string());
    }
    Ok(())
}

/// Validates string length
pub fn validate_string_length(s: &str, min: usize, max: usize) -> Result<(), String> {
    let len = s.len();
    if len < min {
        return Err(format!("String is too short (minimum {} characters)", min));
    }
    if len > max {
        return Err(format!("String is too long (maximum {} characters)", max));
    }
    Ok(())
}

/// Validates that a string doesn't contain dangerous characters
pub fn validate_safe_string(s: &str) -> Result<(), String> {
    // Check for null bytes
    if s.contains('\0') {
        return Err("String contains null bytes".to_string());
    }
    
    // Check for control characters (except newline and tab)
    for ch in s.chars() {
        if ch.is_control() && ch != '\n' && ch != '\t' && ch != '\r' {
            return Err("String contains control characters".to_string());
        }
    }
    
    Ok(())
}

/// Validates JSON values for SQL operations
pub fn validate_json_for_sql(value: &JsonValue) -> Result<(), String> {
    match value {
        JsonValue::String(s) => validate_safe_string(s),
        JsonValue::Object(map) => {
            for (key, val) in map {
                validate_identifier(key)?;
                validate_json_for_sql(val)?;
            }
            Ok(())
        }
        JsonValue::Array(arr) => {
            for val in arr {
                validate_json_for_sql(val)?;
            }
            Ok(())
        }
        JsonValue::Number(_) | JsonValue::Bool(_) | JsonValue::Null => Ok(()),
    }
}

/// Validates a port number
pub fn validate_port(port: u16) -> Result<(), String> {
    if port == 0 {
        return Err("Port cannot be 0".to_string());
    }
    if port < 1024 {
        return Err("Port must be >= 1024 for non-privileged use".to_string());
    }
    Ok(())
}

/// Validates a model name
pub fn validate_model_name(model: &str) -> Result<(), String> {
    let allowed_models = vec![
        "claude-3-opus",
        "claude-3-sonnet", 
        "claude-3-haiku",
        "claude-2.1",
        "claude-2.0",
        "claude-instant-1.2"
    ];
    
    if !allowed_models.contains(&model) {
        return Err(format!("Invalid model name: {}", model));
    }
    Ok(())
}

/// Validates environment variable names
pub fn validate_env_var_name(name: &str) -> Result<(), String> {
    let env_var_regex = Regex::new(r"^[A-Z_][A-Z0-9_]*$").unwrap();
    
    if !env_var_regex.is_match(name) {
        return Err("Invalid environment variable name format".to_string());
    }
    Ok(())
}

/// Validates a session ID (UUID format)
pub fn validate_session_id(id: &str) -> Result<(), String> {
    let uuid_regex = Regex::new(
        r"^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"
    ).unwrap();
    
    if !uuid_regex.is_match(id) {
        return Err("Invalid session ID format".to_string());
    }
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validate_email() {
        assert!(validate_email("<EMAIL>").is_ok());
        assert!(validate_email("<EMAIL>").is_ok());
        assert!(validate_email("invalid.email").is_err());
        assert!(validate_email("@example.com").is_err());
    }

    #[test]
    fn test_validate_url() {
        assert!(validate_url("https://example.com").is_ok());
        assert!(validate_url("http://sub.example.com/path").is_ok());
        assert!(validate_url("ftp://example.com").is_err());
        assert!(validate_url("not-a-url").is_err());
    }

    #[test]
    fn test_validate_identifier() {
        assert!(validate_identifier("test_id").is_ok());
        assert!(validate_identifier("test-id-123").is_ok());
        assert!(validate_identifier("test.id").is_err());
        assert!(validate_identifier("test id").is_err());
    }

    #[test]
    fn test_validate_safe_string() {
        assert!(validate_safe_string("Normal string").is_ok());
        assert!(validate_safe_string("String with\nnewline").is_ok());
        assert!(validate_safe_string("String with\0null").is_err());
        assert!(validate_safe_string("String with \x01 control").is_err());
    }
}