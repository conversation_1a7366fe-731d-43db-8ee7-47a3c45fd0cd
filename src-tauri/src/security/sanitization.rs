// Input sanitization functions
#![allow(dead_code)]

use regex::Regex;
use once_cell::sync::Lazy;
use html_escape;

// Regex patterns for sanitization
static SCRIPT_TAG_REGEX: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"(?i)<script[^>]*>.*?</script>").unwrap()
});

static HTML_EVENT_REGEX: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"(?i)\s*on\w+\s*=").unwrap()
});

static JAVASCRIPT_PROTOCOL_REGEX: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"(?i)javascript:").unwrap()
});

/// Sanitizes a string for safe HTML output
pub fn sanitize_html(input: &str) -> String {
    html_escape::encode_safe(input).to_string()
}

/// Sanitizes user input to remove potentially dangerous content
pub fn sanitize_user_input(input: &str) -> String {
    let mut result = input.to_string();
    
    // Remove script tags
    result = SCRIPT_TAG_REGEX.replace_all(&result, "").to_string();
    
    // Remove HTML event handlers
    result = HTML_EVENT_REGEX.replace_all(&result, "").to_string();
    
    // Remove javascript: protocol
    result = JAVASCRIPT_PROTOCOL_REGEX.replace_all(&result, "").to_string();
    
    // Trim whitespace
    result = result.trim().to_string();
    
    result
}

/// Sanitizes a filename to prevent directory traversal
pub fn sanitize_filename(filename: &str) -> String {
    // Remove any path separators
    let mut sanitized = filename
        .replace('/', "_")
        .replace('\\', "_")
        .replace(':', "_");
    
    // Remove leading dots to prevent hidden files
    while sanitized.starts_with('.') {
        sanitized = sanitized[1..].to_string();
    }
    
    // Remove any control characters
    sanitized = sanitized
        .chars()
        .filter(|c| !c.is_control())
        .collect();
    
    // Limit length
    if sanitized.len() > 255 {
        sanitized = sanitized[..255].to_string();
    }
    
    // If empty after sanitization, use a default
    if sanitized.is_empty() {
        sanitized = "unnamed".to_string();
    }
    
    sanitized
}

/// Sanitizes SQL identifiers (table names, column names)
pub fn sanitize_sql_identifier(identifier: &str) -> String {
    // Only allow alphanumeric characters and underscores
    identifier
        .chars()
        .filter(|c| c.is_alphanumeric() || *c == '_')
        .collect::<String>()
        .to_lowercase()
}

/// Sanitizes environment variable values
pub fn sanitize_env_value(value: &str) -> String {
    // Remove null bytes and control characters
    value
        .chars()
        .filter(|c| !c.is_control() || *c == '\n' || *c == '\t')
        .collect()
}

/// Sanitizes command arguments by removing shell metacharacters
pub fn sanitize_command_arg(arg: &str) -> String {
    // Remove common shell metacharacters
    arg.chars()
        .filter(|c| !matches!(c, ';' | '&' | '|' | '`' | '$' | '(' | ')' | '{' | '}' | '<' | '>' | '*' | '?' | '[' | ']' | '!' | '#'))
        .collect()
}

/// Sanitizes JSON string values
pub fn sanitize_json_string(s: &str) -> String {
    s.chars()
        .filter(|c| !c.is_control() || matches!(c, '\n' | '\r' | '\t'))
        .collect()
}

/// Truncates a string to a maximum length
pub fn truncate_string(s: &str, max_len: usize) -> String {
    if s.len() <= max_len {
        s.to_string()
    } else {
        let mut truncated = s[..max_len].to_string();
        // Ensure we don't cut in the middle of a UTF-8 character
        while !truncated.is_empty() && !truncated.is_char_boundary(truncated.len()) {
            truncated.pop();
        }
        truncated
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sanitize_html() {
        assert_eq!(sanitize_html("<script>alert('xss')</script>"), "&lt;script&gt;alert(&#x27;xss&#x27;)&lt;/script&gt;");
        assert_eq!(sanitize_html("Normal & text"), "Normal &amp; text");
    }

    #[test]
    fn test_sanitize_user_input() {
        assert_eq!(sanitize_user_input("<script>alert('xss')</script>"), "");
        assert_eq!(sanitize_user_input("<div onclick='alert()'>Test</div>"), "<div>Test</div>");
        assert_eq!(sanitize_user_input("<a href='javascript:alert()'>Link</a>"), "<a href='alert()'>Link</a>");
    }

    #[test]
    fn test_sanitize_filename() {
        assert_eq!(sanitize_filename("../../../etc/passwd"), "___etc_passwd");
        assert_eq!(sanitize_filename(".hidden"), "hidden");
        assert_eq!(sanitize_filename("file:name.txt"), "file_name.txt");
        assert_eq!(sanitize_filename(""), "unnamed");
    }

    #[test]
    fn test_sanitize_sql_identifier() {
        assert_eq!(sanitize_sql_identifier("user_table"), "user_table");
        assert_eq!(sanitize_sql_identifier("users; DROP TABLE"), "userdroptable");
        assert_eq!(sanitize_sql_identifier("User-Table-123"), "usertable123");
    }

    #[test]
    fn test_sanitize_command_arg() {
        assert_eq!(sanitize_command_arg("normal-arg"), "normal-arg");
        assert_eq!(sanitize_command_arg("arg; rm -rf /"), "arg rm -rf /");
        assert_eq!(sanitize_command_arg("$(command)"), "command");
    }
}