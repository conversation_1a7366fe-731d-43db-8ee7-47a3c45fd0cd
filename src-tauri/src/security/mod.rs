// Security module for input validation and sanitization
use std::path::{Path, PathBuf};
use std::collections::HashSet;
use regex::Regex;
use once_cell::sync::Lazy;

pub mod validation;
pub mod sanitization;

// Command validation constants
static ALLOWED_COMMANDS: Lazy<HashSet<&'static str>> = Lazy::new(|| {
    let mut set = HashSet::new();
    set.insert("list");
    set.insert("get");
    set.insert("add");
    set.insert("remove");
    set.insert("add-json");
    set.insert("serve");
    set.insert("reset-project-choices");
    set
});

static ALLOWED_MCP_SUBCOMMANDS: Lazy<HashSet<&'static str>> = Lazy::new(|| {
    let mut set = HashSet::new();
    set.insert("--version");
    set.insert("-p");
    set.insert("--model");
    set.insert("--output-format");
    set.insert("--verbose");
    set.insert("--dangerously-skip-permissions");
    set.insert("-c");
    set.insert("--resume");
    set
});

// Path validation regex patterns
static PATH_TRAVERSAL_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"(\.\.[\\/])|([/\\]\.\.)|(^\.\.)|(\.\.$)").unwrap()
});

#[allow(dead_code)]
static ABSOLUTE_PATH_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"^(/|[A-Za-z]:[\\/])").unwrap()
});

/// Validates a command to ensure it's in the allowed list
pub fn validate_command(command: &str) -> Result<(), String> {
    if !ALLOWED_COMMANDS.contains(command) {
        return Err(format!("Command '{}' is not allowed", command));
    }
    Ok(())
}

/// Validates MCP subcommands and arguments
pub fn validate_mcp_args(args: &[String]) -> Result<(), String> {
    for arg in args {
        if arg.starts_with('-') {
            if !ALLOWED_MCP_SUBCOMMANDS.contains(arg.as_str()) {
                return Err(format!("Argument '{}' is not allowed", arg));
            }
        }
    }
    Ok(())
}

/// Validates and canonicalizes a file path to prevent path traversal
pub fn validate_path(path: &str, base_dir: Option<&Path>) -> Result<PathBuf, String> {
    // Check for path traversal attempts
    if PATH_TRAVERSAL_PATTERN.is_match(path) {
        return Err("Path traversal detected".to_string());
    }
    
    let path_buf = PathBuf::from(path);
    
    // If a base directory is provided, ensure the path is within it
    if let Some(base) = base_dir {
        let canonical_base = base.canonicalize()
            .map_err(|e| format!("Failed to canonicalize base path: {}", e))?;
        
        let full_path = if path_buf.is_absolute() {
            path_buf
        } else {
            base.join(&path_buf)
        };
        
        let canonical_path = full_path.canonicalize()
            .map_err(|e| format!("Failed to canonicalize path: {}", e))?;
        
        // Ensure the canonical path starts with the base directory
        if !canonical_path.starts_with(&canonical_base) {
            return Err("Path is outside allowed directory".to_string());
        }
        
        Ok(canonical_path)
    } else {
        // If no base directory, at least canonicalize the path
        path_buf.canonicalize()
            .map_err(|e| format!("Failed to canonicalize path: {}", e))
    }
}

/// Validates a table name to prevent SQL injection
pub fn validate_table_name(name: &str) -> Result<(), String> {
    // Allow only alphanumeric characters and underscores
    let table_name_regex = Regex::new(r"^[a-zA-Z_][a-zA-Z0-9_]*$").unwrap();
    
    if !table_name_regex.is_match(name) {
        return Err("Invalid table name format".to_string());
    }
    
    // Check against reserved SQL keywords
    let reserved_keywords = vec![
        "SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "CREATE", 
        "ALTER", "TABLE", "DATABASE", "INDEX", "VIEW", "TRIGGER",
        "PROCEDURE", "FUNCTION", "UNION", "JOIN", "WHERE", "FROM"
    ];
    
    if reserved_keywords.contains(&name.to_uppercase().as_str()) {
        return Err("Table name cannot be a reserved SQL keyword".to_string());
    }
    
    Ok(())
}

/// Validates column names to prevent SQL injection
#[allow(dead_code)]
pub fn validate_column_names(names: &[String]) -> Result<(), String> {
    let column_name_regex = Regex::new(r"^[a-zA-Z_][a-zA-Z0-9_]*$").unwrap();
    
    for name in names {
        if !column_name_regex.is_match(name) {
            return Err(format!("Invalid column name format: {}", name));
        }
    }
    
    Ok(())
}

/// Escapes shell arguments to prevent command injection
pub fn escape_shell_arg(arg: &str) -> String {
    if cfg!(target_os = "windows") {
        // Windows command escaping
        if arg.is_empty() {
            return String::from("\"\"");
        }
        
        let needs_quotes = arg.contains(' ') || arg.contains('\t') || 
                          arg.contains('"') || arg.contains('\\');
        
        if needs_quotes {
            let mut escaped = String::with_capacity(arg.len() + 2);
            escaped.push('"');
            
            let mut backslash_count = 0;
            for c in arg.chars() {
                match c {
                    '\\' => backslash_count += 1,
                    '"' => {
                        // Escape all backslashes and the quote
                        escaped.push_str(&"\\".repeat(backslash_count * 2 + 1));
                        escaped.push('"');
                        backslash_count = 0;
                    }
                    _ => {
                        escaped.push_str(&"\\".repeat(backslash_count));
                        escaped.push(c);
                        backslash_count = 0;
                    }
                }
            }
            
            // Escape trailing backslashes
            escaped.push_str(&"\\".repeat(backslash_count * 2));
            escaped.push('"');
            escaped
        } else {
            arg.to_string()
        }
    } else {
        // Unix shell escaping using single quotes
        if arg.is_empty() {
            return String::from("''");
        }
        
        // Replace single quotes with '\''
        format!("'{}'", arg.replace('\'', "'\\''"))
    }
}

#[cfg(test)]
mod tests;