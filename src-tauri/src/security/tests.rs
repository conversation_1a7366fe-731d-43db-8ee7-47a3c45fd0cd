// Security module tests
#[cfg(test)]
mod security_tests {
    use super::super::*;
    use std::path::Path;
    use tempfile::TempDir;
    
    #[test]
    fn test_command_injection_prevention() {
        // Test command validation
        assert!(validate_command("list").is_ok());
        assert!(validate_command("get").is_ok());
        assert!(validate_command("add").is_ok());
        
        // Dangerous commands should be rejected
        assert!(validate_command("rm").is_err());
        assert!(validate_command("cat").is_err());
        assert!(validate_command("curl").is_err());
        assert!(validate_command("wget").is_err());
        assert!(validate_command("sh").is_err());
        assert!(validate_command("bash").is_err());
    }
    
    #[test]
    fn test_path_traversal_prevention() {
        let temp_dir = TempDir::new().unwrap();
        let base_path = temp_dir.path();
        
        // Valid paths
        assert!(validate_path("test.txt", Some(base_path)).is_ok());
        assert!(validate_path("subdir/test.txt", Some(base_path)).is_ok());
        
        // Path traversal attempts
        assert!(validate_path("../etc/passwd", Some(base_path)).is_err());
        assert!(validate_path("../../etc/passwd", Some(base_path)).is_err());
        assert!(validate_path("subdir/../../etc/passwd", Some(base_path)).is_err());
        assert!(validate_path("/etc/passwd", Some(base_path)).is_err());
        assert!(validate_path("..\\windows\\system32", Some(base_path)).is_err());
    }
    
    #[test]
    fn test_sql_injection_prevention() {
        // Valid table names
        assert!(validate_table_name("users").is_ok());
        assert!(validate_table_name("user_profiles").is_ok());
        assert!(validate_table_name("_private_table").is_ok());
        assert!(validate_table_name("table123").is_ok());
        
        // SQL injection attempts
        assert!(validate_table_name("users; DROP TABLE users").is_err());
        assert!(validate_table_name("users' OR '1'='1").is_err());
        assert!(validate_table_name("users-- comment").is_err());
        assert!(validate_table_name("users/*comment*/").is_err());
        
        // Reserved keywords
        assert!(validate_table_name("SELECT").is_err());
        assert!(validate_table_name("DROP").is_err());
        assert!(validate_table_name("DELETE").is_err());
    }
    
    #[test]
    fn test_shell_argument_escaping() {
        // Test Unix escaping
        #[cfg(not(target_os = "windows"))]
        {
            assert_eq!(escape_shell_arg("simple"), "'simple'");
            assert_eq!(escape_shell_arg("with spaces"), "'with spaces'");
            assert_eq!(escape_shell_arg("with'quote"), "'with'\\''quote'");
            assert_eq!(escape_shell_arg("rm -rf /"), "'rm -rf /'");
            assert_eq!(escape_shell_arg("$(command)"), "'$(command)'");
            assert_eq!(escape_shell_arg("`command`"), "'`command`'");
            assert_eq!(escape_shell_arg(""), "''");
        }
        
        // Test Windows escaping
        #[cfg(target_os = "windows")]
        {
            assert_eq!(escape_shell_arg("simple"), "simple");
            assert_eq!(escape_shell_arg("with spaces"), "\"with spaces\"");
            assert_eq!(escape_shell_arg("with\"quote"), "\"with\\\"quote\"");
            assert_eq!(escape_shell_arg("path\\to\\file"), "\"path\\to\\file\"");
            assert_eq!(escape_shell_arg(""), "\"\"");
        }
    }
    
    #[test]
    fn test_html_sanitization() {
        use sanitization::*;
        
        assert_eq!(
            sanitize_html("<script>alert('xss')</script>"),
            "&lt;script&gt;alert(&#x27;xss&#x27;)&lt;/script&gt;"
        );
        
        assert_eq!(
            sanitize_html("Normal & text"),
            "Normal &amp; text"
        );
        
        assert_eq!(
            sanitize_html("<img src=x onerror=alert('xss')>"),
            "&lt;img src=x onerror=alert(&#x27;xss&#x27;)&gt;"
        );
    }
    
    #[test]
    fn test_user_input_sanitization() {
        use sanitization::*;
        
        // Script tag removal
        assert_eq!(
            sanitize_user_input("<script>alert('xss')</script>Hello"),
            "Hello"
        );
        
        // Event handler removal
        assert_eq!(
            sanitize_user_input("<div onclick='alert()'>Test</div>"),
            "<div>Test</div>"
        );
        
        // JavaScript protocol removal
        assert_eq!(
            sanitize_user_input("<a href='javascript:alert()'>Link</a>"),
            "<a href='alert()'>Link</a>"
        );
        
        // Mixed case handling
        assert_eq!(
            sanitize_user_input("<ScRiPt>alert()</ScRiPt>"),
            ""
        );
    }
    
    #[test]
    fn test_filename_sanitization() {
        use sanitization::*;
        
        assert_eq!(sanitize_filename("normal.txt"), "normal.txt");
        assert_eq!(sanitize_filename("../../../etc/passwd"), "___etc_passwd");
        assert_eq!(sanitize_filename(".hidden"), "hidden");
        assert_eq!(sanitize_filename("file:name.txt"), "file_name.txt");
        assert_eq!(sanitize_filename("file\\name.txt"), "file_name.txt");
        assert_eq!(sanitize_filename(""), "unnamed");
        assert_eq!(sanitize_filename("..."), "unnamed");
    }
    
    #[test]
    fn test_env_var_validation() {
        use validation::*;
        
        assert!(validate_env_var_name("PATH").is_ok());
        assert!(validate_env_var_name("NODE_ENV").is_ok());
        assert!(validate_env_var_name("MY_VAR_123").is_ok());
        
        assert!(validate_env_var_name("my-var").is_err());
        assert!(validate_env_var_name("123VAR").is_err());
        assert!(validate_env_var_name("var name").is_err());
    }
    
    #[test]
    fn test_session_id_validation() {
        use validation::*;
        
        // Valid UUIDs
        assert!(validate_session_id("550e8400-e29b-41d4-a716-************").is_ok());
        assert!(validate_session_id("6ba7b810-9dad-11d1-80b4-00c04fd430c8").is_ok());
        
        // Invalid formats
        assert!(validate_session_id("not-a-uuid").is_err());
        assert!(validate_session_id("550e8400-e29b-41d4-a716").is_err());
        assert!(validate_session_id("").is_err());
    }
}