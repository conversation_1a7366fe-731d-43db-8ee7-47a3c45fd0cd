use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Window};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ToolbarConfig {
    pub visible: bool,
    pub position: String, // "top" | "bottom"
    pub buttons: ToolbarButtons,
    pub size: String, // "small" | "medium" | "large"
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ToolbarButtons {
    pub claude: bool,
    pub settings: bool,
    pub usage: bool,
    pub mcp: bool,
    pub agents: bool,
    pub marketplace: bool,
    pub brainstorming: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowConfig {
    pub default_width: u32,
    pub default_height: u32,
    pub always_on_top: bool,
    pub minimize_to_tray: bool,
    pub start_maximized: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct EditorConfig {
    pub line_numbers: bool,
    pub word_wrap: bool,
    pub minimap: bool,
    pub font_size: u32,
    pub tab_size: u32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AppConfig {
    pub toolbar: ToolbarConfig,
    pub window: WindowConfig,
    pub editor: EditorConfig,
    pub version: String,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            toolbar: ToolbarConfig {
                visible: true,
                position: "top".to_string(),
                buttons: ToolbarButtons {
                    claude: true,
                    settings: true,
                    usage: true,
                    mcp: true,
                    agents: true,
                    marketplace: true,
                    brainstorming: true,
                },
                size: "medium".to_string(),
            },
            window: WindowConfig {
                default_width: 1200,
                default_height: 800,
                always_on_top: false,
                minimize_to_tray: false,
                start_maximized: false,
            },
            editor: EditorConfig {
                line_numbers: true,
                word_wrap: true,
                minimap: true,
                font_size: 14,
                tab_size: 2,
            },
            version: "1.0.0".to_string(),
        }
    }
}

fn get_config_path(app_handle: &AppHandle) -> Result<PathBuf, Box<dyn std::error::Error>> {
    let app_data_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data directory: {}", e))?;
    
    // Ensure the directory exists
    fs::create_dir_all(&app_data_dir)?;
    
    Ok(app_data_dir.join("app_config.json"))
}

#[tauri::command]
pub async fn save_app_config(
    app_handle: AppHandle,
    config: AppConfig,
) -> Result<(), String> {
    let config_path = get_config_path(&app_handle)
        .map_err(|e| format!("Failed to get config path: {}", e))?;
    
    let config_json = serde_json::to_string_pretty(&config)
        .map_err(|e| format!("Failed to serialize config: {}", e))?;
    
    fs::write(&config_path, config_json)
        .map_err(|e| format!("Failed to write config file: {}", e))?;
    
    Ok(())
}

#[tauri::command]
pub async fn load_app_config(app_handle: AppHandle) -> Result<AppConfig, String> {
    let config_path = get_config_path(&app_handle)
        .map_err(|e| format!("Failed to get config path: {}", e))?;
    
    if !config_path.exists() {
        // Return default config if file doesn't exist
        return Ok(AppConfig::default());
    }
    
    let config_content = fs::read_to_string(&config_path)
        .map_err(|e| format!("Failed to read config file: {}", e))?;
    
    let config: AppConfig = serde_json::from_str(&config_content)
        .map_err(|e| format!("Failed to parse config: {}", e))?;
    
    Ok(config)
}

#[tauri::command]
pub async fn apply_window_config(
    window: Window,
    config: WindowConfig,
) -> Result<(), String> {
    // Apply window size
    window
        .set_size(tauri::LogicalSize::new(
            config.default_width,
            config.default_height,
        ))
        .map_err(|e| format!("Failed to set window size: {}", e))?;
    
    // Apply always on top
    window
        .set_always_on_top(config.always_on_top)
        .map_err(|e| format!("Failed to set always on top: {}", e))?;
    
    // Apply maximized state
    if config.start_maximized {
        window
            .maximize()
            .map_err(|e| format!("Failed to maximize window: {}", e))?;
    }
    
    Ok(())
}

#[tauri::command]
pub async fn get_window_info(window: Window) -> Result<serde_json::Value, String> {
    let size = window
        .inner_size()
        .map_err(|e| format!("Failed to get window size: {}", e))?;
    
    let position = window
        .inner_position()
        .map_err(|e| format!("Failed to get window position: {}", e))?;
    
    let is_maximized = window
        .is_maximized()
        .map_err(|e| format!("Failed to get maximized state: {}", e))?;
    
    let is_always_on_top = window
        .is_always_on_top()
        .map_err(|e| format!("Failed to get always on top state: {}", e))?;
    
    Ok(serde_json::json!({
        "width": size.width,
        "height": size.height,
        "x": position.x,
        "y": position.y,
        "isMaximized": is_maximized,
        "isAlwaysOnTop": is_always_on_top
    }))
}

#[tauri::command]
pub async fn reset_app_config(app_handle: AppHandle) -> Result<AppConfig, String> {
    let config_path = get_config_path(&app_handle)
        .map_err(|e| format!("Failed to get config path: {}", e))?;
    
    // Remove existing config file
    if config_path.exists() {
        fs::remove_file(&config_path)
            .map_err(|e| format!("Failed to remove config file: {}", e))?;
    }
    
    // Return default config
    Ok(AppConfig::default())
}

#[tauri::command]
pub async fn export_app_config(app_handle: AppHandle) -> Result<String, String> {
    let config = load_app_config(app_handle).await?;
    
    serde_json::to_string_pretty(&config)
        .map_err(|e| format!("Failed to serialize config for export: {}", e))
}

#[tauri::command]
pub async fn import_app_config(
    app_handle: AppHandle,
    config_json: String,
) -> Result<AppConfig, String> {
    let config: AppConfig = serde_json::from_str(&config_json)
        .map_err(|e| format!("Failed to parse imported config: {}", e))?;
    
    save_app_config(app_handle, config.clone()).await?;
    
    Ok(config)
}
