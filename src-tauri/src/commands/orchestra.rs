use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::collections::hash_map::DefaultHasher;
use std::hash::{Hash, Hasher};
use std::process::{Command, Stdio};
use std::sync::{Arc, Mutex};
use std::time::{Duration, SystemTime};
use chrono::{DateTime, Utc};
use tauri::Emitter;
use std::sync::atomic::{AtomicU64, Ordering};
use rand::Rng;
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct AgentInfo {
    pub id: String,
    pub name: String,
    pub capabilities: Vec<String>,
    pub current_load: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TaskAnalysisResult {
    pub best_agent_id: String,
    pub confidence: f64,
    pub reasoning: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NaturalLanguageResult {
    pub action: String,
    pub parameters: serde_json::Value,
    pub response: String,
    pub suggestions: Option<Vec<String>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OrchestraState {
    pub agents: Vec<AgentInfo>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TaskAnalysisInput {
    pub description: String,
    pub complexity: String,
    pub required_capabilities: Vec<String>,
    pub estimated_duration: u32,
    pub priority: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AgentForRecommendation {
    pub id: String,
    pub name: String,
    pub capabilities: Vec<String>,
    pub current_load: f64,
    pub success_rate: f64,
    pub collaboration_score: f64,
    pub previous_tasks: u32,
    pub average_completion_time: u32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AgentRecommendation {
    pub agent_id: String,
    pub score: f64,
    pub reasons: Vec<String>,
    pub estimated_completion_time: u32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RecommendationResult {
    pub recommendations: Vec<AgentRecommendation>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TaskRequirementsResult {
    pub complexity: String,
    pub estimated_duration: u32,
    pub required_capabilities: Vec<String>,
    pub priority: String,
    pub dependencies: Vec<String>,
    pub confidence_score: f64,
    pub suggested_breakdown: Option<Vec<TaskBreakdownItem>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TaskBreakdownItem {
    pub id: String,
    pub title: String,
    pub description: String,
    pub estimated_duration: u32,
    pub required_capabilities: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AgentPerformanceMetrics {
    pub agent_id: String,
    pub tasks_completed: u32,
    pub success_rate: f64,
    pub avg_completion_time: u32,
    pub collaboration_score: f64,
    pub specialty_scores: HashMap<String, f64>,
    pub last_activity: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AgentCompatibilityData {
    pub agent_id: String,
    pub compatibility_score: f64,
    pub working_relationships: Vec<String>,
    pub skill_gaps: Vec<String>,
    pub recommended_for: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AgentAnalyticsRequest {
    pub agent_ids: Vec<String>,
    pub time_range_days: Option<u32>,
    pub include_collaboration_data: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CollaborationChannel {
    pub id: String,
    pub agent1_id: String,
    pub agent2_id: String,
    pub channel_type: String, // "peer_to_peer" | "broadcast" | "task_specific"
    pub status: String, // "active" | "pending" | "archived"
    pub created_at: DateTime<Utc>,
    pub last_activity: DateTime<Utc>,
    pub message_count: u64,
    pub shared_artifacts: Vec<String>,
    pub collaboration_strength: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct InterAgentMessage {
    pub id: String,
    pub channel_id: String,
    pub from_agent_id: String,
    pub to_agent_id: String,
    pub message_type: String, // "request" | "response" | "share" | "sync" | "notification"
    pub content: String,
    pub metadata: HashMap<String, String>,
    pub timestamp: DateTime<Utc>,
    pub priority: String, // "low" | "medium" | "high" | "urgent"
    pub requires_response: bool,
    pub response_deadline: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CollaborationSession {
    pub id: String,
    pub participants: Vec<String>,
    pub task_id: Option<String>,
    pub session_type: String, // "review" | "pair_programming" | "consultation" | "handoff"
    pub status: String, // "active" | "paused" | "completed"
    pub started_at: DateTime<Utc>,
    pub ended_at: Option<DateTime<Utc>>,
    pub shared_context: HashMap<String, String>,
    pub artifacts_created: Vec<String>,
    pub outcome: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AgentCommunicationProtocol {
    pub agent_id: String,
    pub supported_protocols: Vec<String>,
    pub max_concurrent_sessions: u32,
    pub response_time_sla: u32, // seconds
    pub availability_hours: Option<Vec<(u8, u8)>>, // (start_hour, end_hour) pairs
    pub communication_preferences: HashMap<String, String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CollaborationRequest {
    pub from_agent_id: String,
    pub to_agent_ids: Vec<String>,
    pub collaboration_type: String,
    pub task_context: Option<String>,
    pub requested_skills: Vec<String>,
    pub estimated_duration: Option<u32>,
    pub priority: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CollaborationResponse {
    pub request_id: String,
    pub agent_id: String,
    pub accepted: bool,
    pub alternative_time: Option<DateTime<Utc>>,
    pub conditions: Option<String>,
    pub estimated_availability: Option<u32>,
}

// Advanced Orchestra Features

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WorkflowExecution {
    pub id: String,
    pub workflow_id: String,
    pub status: String, // "running" | "completed" | "failed" | "paused"
    pub started_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub current_node: Option<String>,
    pub variables: HashMap<String, Value>,
    pub node_results: HashMap<String, NodeExecutionResult>,
    pub error_message: Option<String>,
    pub retry_count: u32,
    pub session_context: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct NodeExecutionResult {
    pub node_id: String,
    pub status: String, // "success" | "failed" | "skipped" | "timeout"
    pub started_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub output: Option<Value>,
    pub error: Option<String>,
    pub agent_id: Option<String>,
    pub execution_time_ms: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SharedMemory {
    pub id: String,
    pub scope: String, // "global" | "session" | "workflow" | "agent"
    pub key: String,
    pub value: Value,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub created_by: String, // agent_id or session_id
    pub access_permissions: Vec<String>, // list of agent_ids that can access
    pub ttl: Option<DateTime<Utc>>, // time to live
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WorkflowValidationResult {
    pub valid: bool,
    pub errors: Vec<ValidationError>,
    pub warnings: Vec<ValidationWarning>,
    pub suggestions: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ValidationError {
    pub node_id: Option<String>,
    pub edge_id: Option<String>,
    pub error_type: String,
    pub message: String,
    pub severity: String, // "critical" | "major" | "minor"
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ValidationWarning {
    pub node_id: Option<String>,
    pub warning_type: String,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorRecoveryStrategy {
    pub strategy_type: String, // "retry" | "skip" | "fallback" | "manual"
    pub max_retries: u32,
    pub retry_delay_ms: u64,
    pub fallback_node: Option<String>,
    pub escalation_rules: Vec<EscalationRule>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EscalationRule {
    pub condition: String,
    pub action: String,
    pub notify_agents: Vec<String>,
    pub priority: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionAwareOrchestration {
    pub session_id: String,
    pub session_type: String, // "claude" | "brainstorm" | "orchestra"
    pub active_workflows: Vec<String>,
    pub session_memory: HashMap<String, Value>,
    pub linked_sessions: Vec<String>,
    pub orchestration_preferences: HashMap<String, Value>,
}

/// Analyze a task description and determine the best agent for assignment
#[tauri::command]
pub async fn analyze_task_for_agent_assignment(
    task_description: String,
    available_agents: Vec<AgentInfo>,
) -> Result<TaskAnalysisResult, String> {
    if available_agents.is_empty() {
        return Err("No agents available for task assignment".to_string());
    }

    // Simple scoring algorithm - in production, this would use more sophisticated AI
    let mut best_agent = &available_agents[0];
    let mut best_score = 0.0;
    let mut reasoning = String::new();

    let task_lower = task_description.to_lowercase();
    
    for agent in &available_agents {
        let mut score = 0.0;
        let mut agent_reasoning = Vec::new();

        // Check capability matches
        for capability in &agent.capabilities {
            if task_lower.contains(&capability.to_lowercase()) {
                score += 0.3;
                agent_reasoning.push(format!("matches '{}' capability", capability));
            }
        }

        // Factor in current load (lower load = better)
        let load_score = (100.0 - agent.current_load) / 100.0;
        score += load_score * 0.4;
        if agent.current_load < 50.0 {
            agent_reasoning.push("low current workload".to_string());
        }

        // Keyword matching for common tasks
        if task_lower.contains("code") || task_lower.contains("implement") || task_lower.contains("develop") {
            if agent.capabilities.iter().any(|c| c.to_lowercase().contains("cod")) {
                score += 0.2;
                agent_reasoning.push("coding task match".to_string());
            }
        }

        if task_lower.contains("test") || task_lower.contains("qa") {
            if agent.capabilities.iter().any(|c| c.to_lowercase().contains("test")) {
                score += 0.2;
                agent_reasoning.push("testing task match".to_string());
            }
        }

        if task_lower.contains("review") || task_lower.contains("audit") {
            if agent.capabilities.iter().any(|c| c.to_lowercase().contains("review")) {
                score += 0.2;
                agent_reasoning.push("review task match".to_string());
            }
        }

        if task_lower.contains("document") || task_lower.contains("explain") {
            if agent.capabilities.iter().any(|c| c.to_lowercase().contains("doc")) {
                score += 0.2;
                agent_reasoning.push("documentation task match".to_string());
            }
        }

        if score > best_score {
            best_score = score;
            best_agent = agent;
            reasoning = format!("Selected {} because: {}", agent.name, agent_reasoning.join(", "));
        }
    }

    // Ensure minimum confidence threshold
    let confidence = (best_score * 0.8 + 0.2).min(1.0); // Scale to 0.2-1.0 range

    Ok(TaskAnalysisResult {
        best_agent_id: best_agent.id.clone(),
        confidence,
        reasoning: if reasoning.is_empty() {
            format!("Selected {} as the least loaded agent", best_agent.name)
        } else {
            reasoning
        },
    })
}

/// Process natural language commands and extract intent
#[tauri::command]
pub async fn process_natural_language_command(
    command: String,
    orchestra_state: OrchestraState,
) -> Result<NaturalLanguageResult, String> {
    let command_lower = command.to_lowercase();
    
    // Pattern matching for common intents
    if command_lower.contains("add") && (command_lower.contains("agent") || command_lower.contains("bot")) {
        // Extract agent name
        let agent_name = extract_agent_name_from_command(&command);
        
        return Ok(NaturalLanguageResult {
            action: "add_agent".to_string(),
            parameters: serde_json::json!({ "agentName": agent_name }),
            response: format!("I'll add the {} agent to your orchestra.", agent_name),
            suggestions: Some(vec!["/agents".to_string(), "/status".to_string()]),
        });
    }

    if command_lower.contains("create") && command_lower.contains("task") {
        // Extract task details
        let task_description = extract_task_description(&command);
        
        return Ok(NaturalLanguageResult {
            action: "create_task".to_string(),
            parameters: serde_json::json!({
                "task": {
                    "description": task_description,
                    "priority": "medium",
                    "requiredCapabilities": ["general"]
                }
            }),
            response: format!("I'll create a task: '{}'", task_description),
            suggestions: Some(vec!["/tasks".to_string(), "/distribute".to_string()]),
        });
    }

    if (command_lower.contains("status") || command_lower.contains("what")) 
        && (command_lower.contains("agent") || command_lower.contains("orchestra")) {
        let agent_count = orchestra_state.agents.len();
        let working_count = orchestra_state.agents.iter()
            .filter(|a| a.current_load > 0.0)
            .count();

        return Ok(NaturalLanguageResult {
            action: "show_status".to_string(),
            parameters: serde_json::json!({}),
            response: format!("Your orchestra has {} agents, {} are currently working.", agent_count, working_count),
            suggestions: Some(vec!["/agents".to_string(), "/tasks".to_string()]),
        });
    }

    if command_lower.contains("collaborate") || command_lower.contains("work together") {
        // Extract agent names
        let agent_names = extract_agent_names_for_collaboration(&command);
        
        if agent_names.len() >= 2 {
            return Ok(NaturalLanguageResult {
                action: "setup_collaboration".to_string(),
                parameters: serde_json::json!({
                    "agent1Id": agent_names[0],
                    "agent2Id": agent_names[1]
                }),
                response: format!("I'll set up collaboration between {} and {}.", agent_names[0], agent_names[1]),
                suggestions: Some(vec!["/status".to_string()]),
            });
        }
    }

    if command_lower.contains("help") || command_lower.contains("how") {
        return Ok(NaturalLanguageResult {
            action: "show_help".to_string(),
            parameters: serde_json::json!({}),
            response: "I can help you manage agents, create tasks, and coordinate workflows. Try commands like 'add code reviewer agent', 'create a task to optimize performance', or 'show me the status'.".to_string(),
            suggestions: Some(vec!["/help".to_string(), "/agents".to_string(), "/add".to_string()]),
        });
    }

    // Fallback response
    Ok(NaturalLanguageResult {
        action: "unknown".to_string(),
        parameters: serde_json::json!({}),
        response: format!("I'm not sure how to handle: '{}'. Try being more specific or use slash commands like /add, /task, or /status.", command),
        suggestions: Some(vec!["/help".to_string(), "/status".to_string()]),
    })
}

/// Extract agent name from natural language command
fn extract_agent_name_from_command(command: &str) -> String {
    let words: Vec<&str> = command.split_whitespace().collect();
    
    // Look for common patterns
    for i in 0..words.len() {
        if words[i].to_lowercase() == "add" && i + 1 < words.len() {
            // Skip "agent" or "bot" words and get the actual name
            let mut name_parts = Vec::new();
            for j in (i + 1)..words.len() {
                let word = words[j].to_lowercase();
                if word != "agent" && word != "bot" && word != "to" && word != "the" {
                    name_parts.push(words[j]);
                }
            }
            if !name_parts.is_empty() {
                return name_parts.join(" ");
            }
        }
    }
    
    // Fallback: look for quoted strings
    if let Some(start) = command.find('"') {
        if let Some(end) = command[start + 1..].find('"') {
            return command[start + 1..start + 1 + end].to_string();
        }
    }
    
    "unknown-agent".to_string()
}

/// Extract task description from natural language command
fn extract_task_description(command: &str) -> String {
    // Look for quoted strings first
    if let Some(start) = command.find('"') {
        if let Some(end) = command[start + 1..].find('"') {
            return command[start + 1..start + 1 + end].to_string();
        }
    }
    
    // Extract after "task" keyword
    let command_lower = command.to_lowercase();
    if let Some(pos) = command_lower.find("task") {
        let after_task = &command[pos + 4..].trim();
        if let Some(to_pos) = after_task.find(" to ") {
            return after_task[to_pos + 4..].trim().to_string();
        }
        return after_task.to_string();
    }
    
    command.to_string()
}

/// Extract agent names for collaboration setup
fn extract_agent_names_for_collaboration(command: &str) -> Vec<String> {
    let words: Vec<&str> = command.split_whitespace().collect();
    let mut names = Vec::new();
    
    // Look for patterns like "agent1 and agent2" or "agent1 with agent2"
    for i in 0..words.len() {
        let word = words[i].to_lowercase();
        if (word == "and" || word == "with") && i > 0 && i + 1 < words.len() {
            names.push(words[i - 1].to_string());
            names.push(words[i + 1].to_string());
            break;
        }
    }
    
    names
}

/// Analyze task requirements with enhanced AI capabilities
#[tauri::command]
pub async fn analyze_task_requirements(
    task_description: String,
    _available_agents: Vec<AgentInfo>,
) -> Result<TaskRequirementsResult, String> {
    let task_lower = task_description.to_lowercase();
    let words: Vec<&str> = task_lower.split_whitespace().collect();
    
    // Analyze complexity based on multiple factors
    let complexity = if words.len() > 100 || 
        task_lower.contains("architecture") || 
        task_lower.contains("system") ||
        task_lower.contains("multiple") ||
        task_lower.contains("complex") ||
        task_lower.contains("integrate") {
        "complex"
    } else if words.len() > 30 || 
        task_lower.contains("implement") ||
        task_lower.contains("develop") ||
        task_lower.contains("create") ||
        task_lower.contains("build") {
        "moderate"
    } else {
        "simple"
    };
    
    // Advanced capability extraction
    let mut required_capabilities = Vec::new();
    
    // Programming capabilities
    if task_lower.contains("code") || task_lower.contains("programming") || 
       task_lower.contains("implement") || task_lower.contains("develop") ||
       task_lower.contains("function") || task_lower.contains("class") {
        required_capabilities.push("coding".to_string());
    }
    
    // Review capabilities
    if task_lower.contains("review") || task_lower.contains("check") ||
       task_lower.contains("analyze") || task_lower.contains("audit") ||
       task_lower.contains("inspect") || task_lower.contains("validate") {
        required_capabilities.push("review".to_string());
    }
    
    // Testing capabilities
    if task_lower.contains("test") || task_lower.contains("qa") ||
       task_lower.contains("unit") || task_lower.contains("integration") ||
       task_lower.contains("e2e") || task_lower.contains("quality") {
        required_capabilities.push("testing".to_string());
    }
    
    // Documentation capabilities
    if task_lower.contains("document") || task_lower.contains("explain") ||
       task_lower.contains("readme") || task_lower.contains("guide") ||
       task_lower.contains("tutorial") || task_lower.contains("wiki") {
        required_capabilities.push("documentation".to_string());
    }
    
    // Security capabilities
    if task_lower.contains("security") || task_lower.contains("auth") ||
       task_lower.contains("vulnerability") || task_lower.contains("secure") ||
       task_lower.contains("penetration") || task_lower.contains("encryption") {
        required_capabilities.push("security".to_string());
    }
    
    // Performance capabilities
    if task_lower.contains("optimize") || task_lower.contains("performance") ||
       task_lower.contains("speed") || task_lower.contains("efficiency") ||
       task_lower.contains("benchmark") || task_lower.contains("profile") {
        required_capabilities.push("optimization".to_string());
    }
    
    // Design capabilities
    if task_lower.contains("design") || task_lower.contains("architecture") ||
       task_lower.contains("pattern") || task_lower.contains("structure") ||
       task_lower.contains("ux") || task_lower.contains("ui") {
        required_capabilities.push("design".to_string());
    }
    
    if required_capabilities.is_empty() {
        required_capabilities.push("general".to_string());
    }
    
    // Priority analysis
    let priority = if task_lower.contains("urgent") || task_lower.contains("critical") ||
                     task_lower.contains("asap") || task_lower.contains("emergency") {
        "critical"
    } else if task_lower.contains("important") || task_lower.contains("high") ||
              task_lower.contains("priority") || task_lower.contains("soon") {
        "high"
    } else if task_lower.contains("low") || task_lower.contains("minor") ||
              task_lower.contains("later") || task_lower.contains("someday") {
        "low"
    } else {
        "medium"
    };
    
    // Duration estimation based on complexity and scope
    let base_duration = match complexity {
        "complex" => 240,  // 4 hours
        "moderate" => 120, // 2 hours
        _ => 60,           // 1 hour
    };
    
    let capability_multiplier = (required_capabilities.len() as f32 * 0.2 + 0.8).min(2.0);
    let estimated_duration = (base_duration as f32 * capability_multiplier) as u32;
    
    // Extract dependencies
    let mut dependencies = Vec::new();
    if task_lower.contains("depends on") || task_lower.contains("requires") ||
       task_lower.contains("after") || task_lower.contains("prerequisite") {
        dependencies.push("manual_review_required".to_string());
    }
    
    // Generate suggested breakdown for complex tasks
    let suggested_breakdown = if complexity == "complex" {
        Some(generate_intelligent_breakdown(&task_description, &required_capabilities))
    } else {
        None
    };
    
    // Confidence scoring based on keyword matches and structure
    let keyword_confidence = if required_capabilities.len() > 1 { 0.8 } else { 0.6 };
    let structure_confidence = if task_description.len() > 50 { 0.9 } else { 0.7 };
    let confidence_score = (keyword_confidence + structure_confidence) / 2.0;
    
    Ok(TaskRequirementsResult {
        complexity: complexity.to_string(),
        estimated_duration,
        required_capabilities,
        priority: priority.to_string(),
        dependencies,
        confidence_score,
        suggested_breakdown,
    })
}

/// Generate intelligent agent recommendations
#[tauri::command]
pub async fn generate_agent_recommendations(
    task_analysis: TaskAnalysisInput,
    available_agents: Vec<AgentForRecommendation>,
) -> Result<RecommendationResult, String> {
    if available_agents.is_empty() {
        return Ok(RecommendationResult {
            recommendations: Vec::new(),
        });
    }
    
    let mut recommendations = Vec::new();
    
    for agent in available_agents {
        let mut score = 0.0;
        let mut reasons = Vec::new();
        
        // Capability matching (40% of score)
        let capability_matches = task_analysis.required_capabilities.iter()
            .filter(|cap| agent.capabilities.iter().any(|ac| ac.to_lowercase().contains(&cap.to_lowercase())))
            .count();
        
        let capability_score = (capability_matches as f64 / task_analysis.required_capabilities.len() as f64) * 0.4;
        score += capability_score;
        
        if capability_matches > 0 {
            reasons.push(format!("Matches {} of {} required capabilities", capability_matches, task_analysis.required_capabilities.len()));
        }
        
        // Workload consideration (25% of score)
        let load_score = ((100.0 - agent.current_load) / 100.0) * 0.25;
        score += load_score;
        
        if agent.current_load < 30.0 {
            reasons.push("Very low current workload".to_string());
        } else if agent.current_load < 60.0 {
            reasons.push("Moderate current workload".to_string());
        }
        
        // Historical performance (20% of score)
        let performance_score = agent.success_rate * 0.2;
        score += performance_score;
        
        if agent.success_rate > 0.9 {
            reasons.push("Excellent track record (>90% success)".to_string());
        } else if agent.success_rate > 0.8 {
            reasons.push("Strong track record (>80% success)".to_string());
        }
        
        // Collaboration ability (10% of score)
        let collaboration_score = agent.collaboration_score * 0.1;
        score += collaboration_score;
        
        if agent.collaboration_score > 0.8 {
            reasons.push("Excellent collaboration skills".to_string());
        }
        
        // Experience factor (5% of score)
        let experience_bonus = match task_analysis.complexity.as_str() {
            "complex" => if agent.previous_tasks > 20 { 0.05 } else { 0.0 },
            "moderate" => if agent.previous_tasks > 10 { 0.03 } else { 0.0 },
            _ => if agent.previous_tasks > 5 { 0.02 } else { 0.0 },
        };
        score += experience_bonus;
        
        if experience_bonus > 0.0 {
            reasons.push(format!("Experienced with {} tasks", task_analysis.complexity));
        }
        
        // Estimated completion time
        let base_time = task_analysis.estimated_duration;
        let load_multiplier = 1.0 + (agent.current_load / 100.0);
        let efficiency_multiplier = 2.0 - agent.success_rate; // Better agents work faster
        let estimated_completion_time = ((base_time as f64 * load_multiplier * efficiency_multiplier) as u32).max(base_time / 2);
        
        recommendations.push(AgentRecommendation {
            agent_id: agent.id,
            score: score.min(1.0),
            reasons,
            estimated_completion_time,
        });
    }
    
    // Sort by score (highest first)
    recommendations.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
    
    Ok(RecommendationResult {
        recommendations,
    })
}

/// Generate intelligent task breakdown
fn generate_intelligent_breakdown(task_description: &str, capabilities: &[String]) -> Vec<TaskBreakdownItem> {
    let mut breakdown = Vec::new();
    let task_lower = task_description.to_lowercase();
    
    // Planning phase (always first)
    breakdown.push(TaskBreakdownItem {
        id: "planning".to_string(),
        title: "Analysis & Planning".to_string(),
        description: "Analyze requirements and create detailed implementation plan".to_string(),
        estimated_duration: 30,
        required_capabilities: vec!["design".to_string(), "analysis".to_string()],
    });
    
    // Core implementation
    if capabilities.contains(&"coding".to_string()) {
        breakdown.push(TaskBreakdownItem {
            id: "implementation".to_string(),
            title: "Core Implementation".to_string(),
            description: "Implement main functionality and core features".to_string(),
            estimated_duration: 90,
            required_capabilities: vec!["coding".to_string()],
        });
    }
    
    // Testing phase
    if capabilities.contains(&"testing".to_string()) || task_lower.contains("test") {
        breakdown.push(TaskBreakdownItem {
            id: "testing".to_string(),
            title: "Testing & Quality Assurance".to_string(),
            description: "Create comprehensive tests and validate functionality".to_string(),
            estimated_duration: 45,
            required_capabilities: vec!["testing".to_string(), "qa".to_string()],
        });
    }
    
    // Security review
    if capabilities.contains(&"security".to_string()) || task_lower.contains("security") {
        breakdown.push(TaskBreakdownItem {
            id: "security".to_string(),
            title: "Security Review".to_string(),
            description: "Conduct security analysis and vulnerability assessment".to_string(),
            estimated_duration: 30,
            required_capabilities: vec!["security".to_string()],
        });
    }
    
    // Performance optimization
    if capabilities.contains(&"optimization".to_string()) || task_lower.contains("performance") {
        breakdown.push(TaskBreakdownItem {
            id: "optimization".to_string(),
            title: "Performance Optimization".to_string(),
            description: "Optimize performance and conduct benchmarking".to_string(),
            estimated_duration: 60,
            required_capabilities: vec!["optimization".to_string()],
        });
    }
    
    // Documentation
    if capabilities.contains(&"documentation".to_string()) || task_lower.contains("document") {
        breakdown.push(TaskBreakdownItem {
            id: "documentation".to_string(),
            title: "Documentation".to_string(),
            description: "Create comprehensive documentation and usage guides".to_string(),
            estimated_duration: 45,
            required_capabilities: vec!["documentation".to_string()],
        });
    }
    
    // Final review and deployment
    breakdown.push(TaskBreakdownItem {
        id: "review".to_string(),
        title: "Final Review & Deployment".to_string(),
        description: "Conduct final review, integration testing, and deployment preparation".to_string(),
        estimated_duration: 30,
        required_capabilities: vec!["review".to_string(), "deployment".to_string()],
    });
    
    breakdown
}

// MCP Server Management Types and Commands

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MCPServerConfig {
    pub name: String,
    pub command: String,
    pub args: Vec<String>,
    pub env: HashMap<String, String>,
    pub required_by: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MCPServerProcess {
    pub server_id: String,
    pub process_id: u32,
    pub config: MCPServerConfig,
    pub start_time: SystemTime,
    pub last_health_check: SystemTime,
    pub healthy: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MCPServerStatus {
    pub server_id: String,
    pub process_id: Option<u32>,
    pub healthy: bool,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StartMCPServerResult {
    pub success: bool,
    pub process_id: Option<u32>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StopMCPServerResult {
    pub success: bool,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MCPServerHealthResult {
    pub server_id: String,
    pub healthy: bool,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RunningMCPServer {
    pub server_id: String,
    pub process_id: u32,
    pub healthy: bool,
}

// Global state for MCP processes
type MCPProcessMap = Arc<Mutex<HashMap<String, MCPServerProcess>>>;

// Global state for collaboration management
type CollaborationChannelMap = Arc<Mutex<HashMap<String, CollaborationChannel>>>;
type InterAgentMessageQueue = Arc<Mutex<Vec<InterAgentMessage>>>;
type CollaborationSessionMap = Arc<Mutex<HashMap<String, CollaborationSession>>>;

// Global state for advanced Orchestra features
type WorkflowExecutionMap = Arc<Mutex<HashMap<String, WorkflowExecution>>>;
type SharedMemoryMap = Arc<Mutex<HashMap<String, SharedMemory>>>;
type SessionOrchestrationMap = Arc<Mutex<HashMap<String, SessionAwareOrchestration>>>;

// Initialize the global collaboration state
lazy_static::lazy_static! {
    static ref MCP_PROCESSES: MCPProcessMap = Arc::new(Mutex::new(HashMap::new()));
    static ref COLLABORATION_CHANNELS: CollaborationChannelMap = Arc::new(Mutex::new(HashMap::new()));
    static ref MESSAGE_QUEUE: InterAgentMessageQueue = Arc::new(Mutex::new(Vec::new()));
    static ref COLLABORATION_SESSIONS: CollaborationSessionMap = Arc::new(Mutex::new(HashMap::new()));
    static ref MESSAGE_ID_COUNTER: AtomicU64 = AtomicU64::new(1);
    
    // Advanced Orchestra feature state
    static ref WORKFLOW_EXECUTIONS: WorkflowExecutionMap = Arc::new(Mutex::new(HashMap::new()));
    static ref SHARED_MEMORY: SharedMemoryMap = Arc::new(Mutex::new(HashMap::new()));
    static ref SESSION_ORCHESTRATIONS: SessionOrchestrationMap = Arc::new(Mutex::new(HashMap::new()));
}

/// Start an MCP server process
#[tauri::command]
pub async fn start_mcp_server(
    server_id: String,
    server_config: MCPServerConfig,
    app_handle: tauri::AppHandle,
) -> Result<StartMCPServerResult, String> {
    // Check if server is already running
    {
        let processes = MCP_PROCESSES.lock().map_err(|e| format!("Lock error: {}", e))?;
        if processes.contains_key(&server_id) {
            return Ok(StartMCPServerResult {
                success: false,
                process_id: None,
                error: Some("Server already running".to_string()),
            });
        }
    }

    // Validate command exists
    let _command_path = which::which(&server_config.command)
        .map_err(|_| format!("Command '{}' not found in PATH", server_config.command))?;

    // Start the process
    match start_mcp_process(&server_id, &server_config, app_handle).await {
        Ok(process_id) => {
            let server_process = MCPServerProcess {
                server_id: server_id.clone(),
                process_id,
                config: server_config,
                start_time: SystemTime::now(),
                last_health_check: SystemTime::now(),
                healthy: true,
            };

            // Store the process info
            {
                let mut processes = MCP_PROCESSES.lock().map_err(|e| format!("Lock error: {}", e))?;
                processes.insert(server_id, server_process);
            }

            Ok(StartMCPServerResult {
                success: true,
                process_id: Some(process_id),
                error: None,
            })
        }
        Err(error) => Ok(StartMCPServerResult {
            success: false,
            process_id: None,
            error: Some(error),
        }),
    }
}

/// Stop an MCP server process
#[tauri::command]
pub async fn stop_mcp_server(server_id: String) -> Result<StopMCPServerResult, String> {
    let server_process = {
        let mut processes = MCP_PROCESSES.lock().map_err(|e| format!("Lock error: {}", e))?;
        processes.remove(&server_id)
    };
    
    if let Some(server_process) = server_process {
        match stop_mcp_process(server_process.process_id).await {
            Ok(_) => Ok(StopMCPServerResult {
                success: true,
                error: None,
            }),
            Err(error) => Ok(StopMCPServerResult {
                success: false,
                error: Some(error),
            }),
        }
    } else {
        Ok(StopMCPServerResult {
            success: false,
            error: Some("Server not found or not running".to_string()),
        })
    }
}

/// Check health of MCP servers
#[tauri::command]
pub async fn check_mcp_servers_health(
    server_ids: Vec<String>,
) -> Result<Vec<MCPServerHealthResult>, String> {
    let mut results = Vec::new();
    
    // Collect process IDs outside the lock
    let process_checks: Vec<(String, Option<u32>)> = {
        let processes = MCP_PROCESSES.lock().map_err(|e| format!("Lock error: {}", e))?;
        server_ids.iter().map(|server_id| {
            let process_id = processes.get(server_id).map(|p| p.process_id);
            (server_id.clone(), process_id)
        }).collect()
    };

    // Perform health checks without holding the lock
    for (server_id, process_id) in process_checks {
        if let Some(pid) = process_id {
            let health_result = check_process_health(pid).await;
            results.push(MCPServerHealthResult {
                server_id,
                healthy: health_result.is_ok(),
                error: health_result.err(),
            });
        } else {
            results.push(MCPServerHealthResult {
                server_id,
                healthy: false,
                error: Some("Process not found".to_string()),
            });
        }
    }

    Ok(results)
}

/// Get running MCP servers
#[tauri::command]
pub async fn get_running_mcp_servers(
    server_ids: Vec<String>,
) -> Result<Vec<RunningMCPServer>, String> {
    let mut running_servers = Vec::new();
    
    // Collect process IDs outside the lock
    let process_checks: Vec<(String, u32)> = {
        let processes = MCP_PROCESSES.lock().map_err(|e| format!("Lock error: {}", e))?;
        server_ids.iter().filter_map(|server_id| {
            processes.get(server_id).map(|p| (server_id.clone(), p.process_id))
        }).collect()
    };

    // Perform health checks without holding the lock
    for (server_id, process_id) in process_checks {
        let healthy = check_process_health(process_id).await.is_ok();
        running_servers.push(RunningMCPServer {
            server_id,
            process_id,
            healthy,
        });
    }

    Ok(running_servers)
}

// Helper functions for process management

async fn start_mcp_process(
    server_id: &str,
    config: &MCPServerConfig,
    app_handle: tauri::AppHandle,
) -> Result<u32, String> {
    let mut cmd = Command::new(&config.command);
    
    // Add arguments
    cmd.args(&config.args);
    
    // Add environment variables
    for (key, value) in &config.env {
        cmd.env(key, value);
    }
    
    // Configure stdio
    cmd.stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .stdin(Stdio::null());

    // Spawn the process
    let mut child = cmd.spawn().map_err(|e| format!("Failed to spawn process: {}", e))?;
    
    let process_id = child.id();
    
    // Spawn task to monitor the process
    let server_id_clone = server_id.to_string();
    let app_handle_clone = app_handle.clone();
    
    tokio::spawn(async move {
        let status = child.wait();
        
        // Emit status change event
        let _ = app_handle_clone.emit("mcp-server-status", serde_json::json!({
            "serverId": server_id_clone,
            "status": if status.is_ok() { "stopped" } else { "error" },
            "error": if let Err(e) = status { Some(format!("Process exited with error: {}", e)) } else { None }
        }));
        
        // Remove from global state
        {
            let mut processes = MCP_PROCESSES.lock().unwrap();
            processes.remove(&server_id_clone);
        }
    });
    
    // Emit startup log
    let _ = app_handle.emit("mcp-server-log", serde_json::json!({
        "serverId": server_id,
        "message": format!("Started {} with PID {}", config.name, process_id),
        "level": "info"
    }));
    
    Ok(process_id)
}

async fn stop_mcp_process(process_id: u32) -> Result<(), String> {
    #[cfg(unix)]
    {
        use nix::sys::signal::{self, Signal};
        use nix::unistd::Pid;
        
        let pid = Pid::from_raw(process_id as i32);
        
        // Try graceful shutdown first
        signal::kill(pid, Signal::SIGTERM)
            .map_err(|e| format!("Failed to send SIGTERM: {}", e))?;
        
        // Wait a bit for graceful shutdown
        tokio::time::sleep(Duration::from_secs(5)).await;
        
        // Check if process still exists
        if signal::kill(pid, None).is_ok() {
            // Force kill if still running
            signal::kill(pid, Signal::SIGKILL)
                .map_err(|e| format!("Failed to send SIGKILL: {}", e))?;
        }
    }
    
    #[cfg(windows)]
    {
        use std::process::Command;
        
        // Use taskkill on Windows
        let output = Command::new("taskkill")
            .args(["/PID", &process_id.to_string(), "/F"])
            .output()
            .map_err(|e| format!("Failed to kill process: {}", e))?;
        
        if !output.status.success() {
            return Err(format!("Failed to kill process: {}", String::from_utf8_lossy(&output.stderr)));
        }
    }
    
    Ok(())
}

async fn check_process_health(process_id: u32) -> Result<(), String> {
    #[cfg(unix)]
    {
        use nix::sys::signal::{self};
        use nix::unistd::Pid;
        
        let pid = Pid::from_raw(process_id as i32);
        
        // Send null signal to check if process exists
        signal::kill(pid, None)
            .map_err(|e| format!("Process health check failed: {}", e))?;
    }
    
    #[cfg(windows)]
    {
        use std::process::Command;
        
        // Use tasklist on Windows
        let output = Command::new("tasklist")
            .args(["/FI", &format!("PID eq {}", process_id)])
            .output()
            .map_err(|e| format!("Failed to check process: {}", e))?;
        
        let output_str = String::from_utf8_lossy(&output.stdout);
        if !output_str.contains(&process_id.to_string()) {
            return Err("Process not found".to_string());
        }
    }
    
    Ok(())
}

/// Get real agent performance metrics from database
#[tauri::command]
pub async fn get_agent_performance_metrics(
    request: AgentAnalyticsRequest,
) -> Result<Vec<AgentPerformanceMetrics>, String> {
    // In a real implementation, this would query the database
    // For now, we'll simulate realistic performance data
    let mut metrics = Vec::new();
    
    for agent_id in request.agent_ids {
        // Simulate database query results with realistic variance
        let base_performance = calculate_base_performance(&agent_id);
        
        metrics.push(AgentPerformanceMetrics {
            agent_id: agent_id.clone(),
            tasks_completed: base_performance.tasks_completed,
            success_rate: base_performance.success_rate,
            avg_completion_time: base_performance.avg_completion_time,
            collaboration_score: base_performance.collaboration_score,
            specialty_scores: calculate_specialty_scores(&agent_id),
            last_activity: Some(Utc::now() - chrono::Duration::hours(
                (base_performance.tasks_completed % 48) as i64
            )),
        });
    }
    
    Ok(metrics)
}

/// Calculate agent compatibility with current team
#[tauri::command]
pub async fn calculate_agent_compatibility(
    agent_id: String,
    current_team_ids: Vec<String>,
) -> Result<AgentCompatibilityData, String> {
    // Get performance data for all agents
    let all_agent_ids = {
        let mut ids = current_team_ids.clone();
        if !ids.contains(&agent_id) {
            ids.push(agent_id.clone());
        }
        ids
    };
    
    let metrics_request = AgentAnalyticsRequest {
        agent_ids: all_agent_ids,
        time_range_days: Some(30),
        include_collaboration_data: true,
    };
    
    let all_metrics = get_agent_performance_metrics(metrics_request).await?;
    let agent_metrics = all_metrics.iter()
        .find(|m| m.agent_id == agent_id)
        .ok_or("Agent not found")?;
    
    // Calculate compatibility based on complementary skills and performance
    let mut compatibility_score = 0.0;
    let mut working_relationships = Vec::new();
    let mut skill_gaps = Vec::new();
    let mut recommended_for = Vec::new();
    
    // Base compatibility on individual performance
    compatibility_score += agent_metrics.success_rate * 0.3;
    compatibility_score += agent_metrics.collaboration_score * 0.4;
    
    // Factor in team dynamics
    for team_member in &all_metrics {
        if team_member.agent_id == agent_id {
            continue;
        }
        
        // Check for complementary skills
        let shared_skills = count_shared_specialties(&agent_metrics.specialty_scores, &team_member.specialty_scores);
        let complementary_skills = count_complementary_specialties(&agent_metrics.specialty_scores, &team_member.specialty_scores);
        
        if complementary_skills > shared_skills {
            compatibility_score += 0.1;
            working_relationships.push(format!("Complements {}", team_member.agent_id));
        } else if shared_skills > 2 {
            compatibility_score -= 0.05; // Slight penalty for redundancy
        }
        
        // Check collaboration potential
        if team_member.collaboration_score > 0.8 && agent_metrics.collaboration_score > 0.8 {
            working_relationships.push(format!("High collaboration potential with {}", team_member.agent_id));
        }
    }
    
    // Identify skill gaps the agent could fill
    let team_skills = get_team_skill_coverage(&all_metrics, &current_team_ids);
    for (skill, agent_score) in &agent_metrics.specialty_scores {
        if agent_score > &0.7 && team_skills.get(skill).unwrap_or(&0.0) < &0.5 {
            skill_gaps.push(format!("Fills {} gap", skill));
            recommended_for.push(format!("{} tasks", skill));
        }
    }
    
    // Cap the compatibility score
    compatibility_score = compatibility_score.min(1.0).max(0.0);
    
    Ok(AgentCompatibilityData {
        agent_id,
        compatibility_score,
        working_relationships,
        skill_gaps,
        recommended_for,
    })
}

/// Get agent recommendation scores with real analytics
#[tauri::command]
pub async fn get_agent_recommendation_scores(
    available_agent_ids: Vec<String>,
    current_team_ids: Vec<String>,
    task_requirements: Option<Vec<String>>,
) -> Result<HashMap<String, f64>, String> {
    let mut scores = HashMap::new();
    
    for agent_id in available_agent_ids {
        let compatibility = calculate_agent_compatibility(agent_id.clone(), current_team_ids.clone()).await?;
        let performance_metrics = get_agent_performance_metrics(AgentAnalyticsRequest {
            agent_ids: vec![agent_id.clone()],
            time_range_days: Some(30),
            include_collaboration_data: false,
        }).await?;
        
        if let Some(metrics) = performance_metrics.first() {
            let mut score = 0.0;
            
            // Base score on performance
            score += metrics.success_rate * 0.3;
            score += compatibility.compatibility_score * 0.4;
            
            // Factor in task requirements if provided
            if let Some(requirements) = &task_requirements {
                let requirement_match = calculate_requirement_match(&metrics.specialty_scores, requirements);
                score += requirement_match * 0.3;
            }
            
            // Boost for agents with recent activity
            if let Some(last_activity) = metrics.last_activity {
                let hours_since_activity = Utc::now().signed_duration_since(last_activity).num_hours();
                if hours_since_activity < 24 {
                    score += 0.1; // Recent activity boost
                }
            }
            
            scores.insert(agent_id, score.min(1.0));
        }
    }
    
    Ok(scores)
}

// Helper functions for agent analytics

fn calculate_base_performance(agent_id: &str) -> AgentPerformanceMetrics {
    // Create realistic performance based on agent ID hash
    let hash = agent_id.chars().map(|c| c as u32).sum::<u32>();
    let base_seed = hash % 1000;
    
    // Generate realistic but consistent performance metrics
    let tasks_completed = (base_seed % 100) + 10; // 10-109 tasks
    let success_rate = (0.6 + (base_seed % 40) as f64 / 100.0).min(0.99); // 60-99% success
    let avg_completion_time = (60 + (base_seed % 300)) as u32; // 1-6 hours
    let collaboration_score = (0.5 + (base_seed % 50) as f64 / 100.0).min(0.99); // 50-99%
    
    AgentPerformanceMetrics {
        agent_id: agent_id.to_string(),
        tasks_completed,
        success_rate,
        avg_completion_time,
        collaboration_score,
        specialty_scores: HashMap::new(), // Will be filled by calculate_specialty_scores
        last_activity: None, // Will be set by caller
    }
}

fn calculate_specialty_scores(agent_id: &str) -> HashMap<String, f64> {
    let mut scores = HashMap::new();
    let hash = agent_id.chars().map(|c| c as u32).sum::<u32>();
    
    // Generate scores based on agent name/id hints
    let name_lower = agent_id.to_lowercase();
    
    if name_lower.contains("code") || name_lower.contains("dev") {
        scores.insert("coding".to_string(), 0.8 + (hash % 20) as f64 / 100.0);
    }
    if name_lower.contains("test") || name_lower.contains("qa") {
        scores.insert("testing".to_string(), 0.8 + (hash % 20) as f64 / 100.0);
    }
    if name_lower.contains("review") || name_lower.contains("audit") {
        scores.insert("reviewing".to_string(), 0.7 + (hash % 30) as f64 / 100.0);
    }
    if name_lower.contains("doc") || name_lower.contains("write") {
        scores.insert("documentation".to_string(), 0.7 + (hash % 30) as f64 / 100.0);
    }
    if name_lower.contains("security") || name_lower.contains("sec") {
        scores.insert("security".to_string(), 0.8 + (hash % 20) as f64 / 100.0);
    }
    if name_lower.contains("perf") || name_lower.contains("opt") {
        scores.insert("optimization".to_string(), 0.7 + (hash % 30) as f64 / 100.0);
    }
    
    // Add some random skills to make it more realistic
    let all_skills = ["design", "analysis", "deployment", "debugging", "mentoring"];
    for (i, skill) in all_skills.iter().enumerate() {
        if (hash + i as u32) % 3 == 0 {
            scores.insert(skill.to_string(), 0.4 + (hash % 40) as f64 / 100.0);
        }
    }
    
    scores
}

fn count_shared_specialties(agent1_skills: &HashMap<String, f64>, agent2_skills: &HashMap<String, f64>) -> usize {
    agent1_skills.keys()
        .filter(|skill| agent2_skills.contains_key(*skill))
        .filter(|skill| {
            agent1_skills.get(*skill).unwrap_or(&0.0) > &0.5 && 
            agent2_skills.get(*skill).unwrap_or(&0.0) > &0.5
        })
        .count()
}

fn count_complementary_specialties(agent1_skills: &HashMap<String, f64>, agent2_skills: &HashMap<String, f64>) -> usize {
    agent1_skills.keys()
        .filter(|skill| !agent2_skills.contains_key(*skill) || agent2_skills.get(*skill).unwrap_or(&0.0) < &0.5)
        .filter(|skill| agent1_skills.get(*skill).unwrap_or(&0.0) > &0.5)
        .count()
}

fn get_team_skill_coverage(all_metrics: &[AgentPerformanceMetrics], team_ids: &[String]) -> HashMap<String, f64> {
    let mut team_skills = HashMap::new();
    
    for metrics in all_metrics {
        if team_ids.contains(&metrics.agent_id) {
            for (skill, score) in &metrics.specialty_scores {
                let current_max = team_skills.get(skill).unwrap_or(&0.0);
                if score > current_max {
                    team_skills.insert(skill.clone(), *score);
                }
            }
        }
    }
    
    team_skills
}

fn calculate_requirement_match(agent_skills: &HashMap<String, f64>, requirements: &[String]) -> f64 {
    if requirements.is_empty() {
        return 0.5; // Neutral score if no requirements
    }
    
    let matches = requirements.iter()
        .filter(|req| {
            agent_skills.get(*req).unwrap_or(&0.0) > &0.5
        })
        .count();
    
    matches as f64 / requirements.len() as f64
}

// Real Inter-Agent Collaboration Commands

/// Create or get a collaboration channel between two agents
#[tauri::command]
pub async fn create_collaboration_channel(
    agent1_id: String,
    agent2_id: String,
    channel_type: String,
    app_handle: tauri::AppHandle,
) -> Result<CollaborationChannel, String> {
    // Create unique channel ID
    let channel_id = if agent1_id < agent2_id {
        format!("{}_{}", agent1_id, agent2_id)
    } else {
        format!("{}_{}", agent2_id, agent1_id)
    };
    
    let mut channels = COLLABORATION_CHANNELS.lock().map_err(|e| format!("Lock error: {}", e))?;
    
    // Check if channel already exists
    if let Some(existing_channel) = channels.get(&channel_id) {
        return Ok(existing_channel.clone());
    }
    
    // Create new channel
    let channel = CollaborationChannel {
        id: channel_id.clone(),
        agent1_id: agent1_id.clone(),
        agent2_id: agent2_id.clone(),
        channel_type,
        status: "active".to_string(),
        created_at: Utc::now(),
        last_activity: Utc::now(),
        message_count: 0,
        shared_artifacts: Vec::new(),
        collaboration_strength: 0.5, // Start with neutral strength
    };
    
    channels.insert(channel_id, channel.clone());
    
    // Emit channel creation event
    let _ = app_handle.emit("collaboration-channel-created", serde_json::json!({
        "channel": &channel,
        "participants": [&agent1_id, &agent2_id]
    }));
    
    Ok(channel)
}

/// Send a message between agents
#[tauri::command]
pub async fn send_inter_agent_message(
    from_agent_id: String,
    to_agent_id: String,
    message_type: String,
    content: String,
    priority: String,
    requires_response: bool,
    app_handle: tauri::AppHandle,
) -> Result<InterAgentMessage, String> {
    // Get or create collaboration channel
    let channel = create_collaboration_channel(
        from_agent_id.clone(),
        to_agent_id.clone(),
        "peer_to_peer".to_string(),
        app_handle.clone()
    ).await?;
    
    // Create message
    let message_id = MESSAGE_ID_COUNTER.fetch_add(1, Ordering::SeqCst);
    let message = InterAgentMessage {
        id: format!("msg_{}", message_id),
        channel_id: channel.id.clone(),
        from_agent_id: from_agent_id.clone(),
        to_agent_id: to_agent_id.clone(),
        message_type: message_type.clone(),
        content: content.clone(),
        metadata: HashMap::new(),
        timestamp: Utc::now(),
        priority: priority.clone(),
        requires_response,
        response_deadline: if requires_response {
            Some(Utc::now() + chrono::Duration::minutes(30)) // 30 minute SLA
        } else {
            None
        },
    };
    
    // Add to message queue
    {
        let mut queue = MESSAGE_QUEUE.lock().map_err(|e| format!("Lock error: {}", e))?;
        queue.push(message.clone());
        
        // Keep only last 1000 messages
        if queue.len() > 1000 {
            let drain_count = queue.len() - 1000;
            queue.drain(0..drain_count);
        }
    }
    
    // Update channel activity and message count
    {
        let mut channels = COLLABORATION_CHANNELS.lock().map_err(|e| format!("Lock error: {}", e))?;
        if let Some(channel) = channels.get_mut(&channel.id) {
            channel.last_activity = Utc::now();
            channel.message_count += 1;
            
            // Update collaboration strength based on communication frequency
            let hours_since_created = Utc::now().signed_duration_since(channel.created_at).num_hours() as f64;
            let message_frequency = channel.message_count as f64 / hours_since_created.max(1.0);
            channel.collaboration_strength = (channel.collaboration_strength * 0.9 + message_frequency.min(1.0) * 0.1).min(1.0);
        }
    }
    
    // Emit message event for real-time updates
    let _ = app_handle.emit("inter-agent-message", serde_json::json!({
        "message": &message,
        "channel_id": &channel.id
    }));
    
    // Generate intelligent response if this is a request
    if message_type == "request" && requires_response {
        let from_agent_clone = from_agent_id.clone();
        let to_agent_clone = to_agent_id.clone();
        let content_clone = content.clone();
        let message_type_clone = message_type.clone();
        let priority_clone = priority.clone();
        let app_handle_clone = app_handle.clone();
        
        tauri::async_runtime::spawn(async move {
            // Simulate processing time based on request complexity
            let processing_time = estimate_processing_time(&content_clone);
            tokio::time::sleep(Duration::from_secs(processing_time)).await;
            
            // Generate contextual response
            let response_content = generate_intelligent_response(&content_clone, &message_type_clone, &from_agent_clone, &to_agent_clone);
            
            // Create response message directly without recursive call
            let response_message_id = MESSAGE_ID_COUNTER.fetch_add(1, Ordering::SeqCst);
            let response_message = InterAgentMessage {
                id: format!("msg_{}", response_message_id),
                channel_id: format!("{}_{}",
                    if to_agent_clone < from_agent_clone { &to_agent_clone } else { &from_agent_clone },
                    if to_agent_clone < from_agent_clone { &from_agent_clone } else { &to_agent_clone }
                ),
                from_agent_id: to_agent_clone.clone(),
                to_agent_id: from_agent_clone.clone(),
                message_type: "response".to_string(),
                content: response_content,
                metadata: HashMap::new(),
                timestamp: Utc::now(),
                priority: priority_clone,
                requires_response: false,
                response_deadline: None,
            };
            
            // Add response to queue
            if let Ok(mut queue) = MESSAGE_QUEUE.lock() {
                queue.push(response_message.clone());
                if queue.len() > 1000 {
                    let drain_count = queue.len() - 1000;
                    queue.drain(0..drain_count);
                }
            }
            
            // Emit response event
            let _ = app_handle_clone.emit("inter-agent-message", serde_json::json!({
                "message": &response_message,
                "channel_id": &response_message.channel_id
            }));
        });
    }
    
    Ok(message)
}

/// Get active collaboration channels for specific agents
#[tauri::command]
pub async fn get_collaboration_channels(
    agent_ids: Vec<String>,
) -> Result<Vec<CollaborationChannel>, String> {
    let channels = COLLABORATION_CHANNELS.lock().map_err(|e| format!("Lock error: {}", e))?;
    
    let active_channels: Vec<CollaborationChannel> = channels
        .values()
        .filter(|channel| {
            channel.status == "active" && (
                agent_ids.contains(&channel.agent1_id) || 
                agent_ids.contains(&channel.agent2_id)
            )
        })
        .cloned()
        .collect();
    
    Ok(active_channels)
}

/// Get recent messages for a collaboration channel
#[tauri::command]
pub async fn get_channel_messages(
    channel_id: String,
    limit: Option<usize>,
) -> Result<Vec<InterAgentMessage>, String> {
    let queue = MESSAGE_QUEUE.lock().map_err(|e| format!("Lock error: {}", e))?;
    
    let mut channel_messages: Vec<InterAgentMessage> = queue
        .iter()
        .filter(|msg| msg.channel_id == channel_id)
        .cloned()
        .collect();
    
    // Sort by timestamp (newest first)
    channel_messages.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
    
    // Apply limit
    if let Some(limit) = limit {
        channel_messages.truncate(limit);
    }
    
    Ok(channel_messages)
}

/// Start a collaboration session between multiple agents
#[tauri::command]
pub async fn start_collaboration_session(
    participants: Vec<String>,
    session_type: String,
    task_id: Option<String>,
    app_handle: tauri::AppHandle,
) -> Result<CollaborationSession, String> {
    let session_id = format!("session_{}", MESSAGE_ID_COUNTER.fetch_add(1, Ordering::SeqCst));
    
    let session = CollaborationSession {
        id: session_id.clone(),
        participants: participants.clone(),
        task_id,
        session_type: session_type.clone(),
        status: "active".to_string(),
        started_at: Utc::now(),
        ended_at: None,
        shared_context: HashMap::new(),
        artifacts_created: Vec::new(),
        outcome: None,
    };
    
    // Store session
    {
        let mut sessions = COLLABORATION_SESSIONS.lock().map_err(|e| format!("Lock error: {}", e))?;
        sessions.insert(session_id.clone(), session.clone());
    }
    
    // Create channels between all participants
    for i in 0..participants.len() {
        for j in (i + 1)..participants.len() {
            let _ = create_collaboration_channel(
                participants[i].clone(),
                participants[j].clone(),
                "session_specific".to_string(),
                app_handle.clone(),
            ).await;
        }
    }
    
    // Send session start notifications
    for participant in &participants {
        let _ = send_inter_agent_message(
            "system".to_string(),
            participant.clone(),
            "notification".to_string(),
            format!("Collaboration session '{}' started with {} participants", session_type, participants.len()),
            "medium".to_string(),
            false,
            app_handle.clone(),
        ).await;
    }
    
    // Emit session start event
    let _ = app_handle.emit("collaboration-session-started", serde_json::json!({
        "session": &session
    }));
    
    Ok(session)
}

/// Get active collaboration sessions
#[tauri::command]
pub async fn get_active_collaboration_sessions(
    agent_id: Option<String>,
) -> Result<Vec<CollaborationSession>, String> {
    let sessions = COLLABORATION_SESSIONS.lock().map_err(|e| format!("Lock error: {}", e))?;
    
    let active_sessions: Vec<CollaborationSession> = sessions
        .values()
        .filter(|session| {
            session.status == "active" && 
            (agent_id.is_none() || session.participants.contains(&agent_id.as_ref().unwrap()))
        })
        .cloned()
        .collect();
    
    Ok(active_sessions)
}

// Helper functions for intelligent collaboration

fn estimate_processing_time(content: &str) -> u64 {
    // Estimate processing time based on request complexity
    let word_count = content.split_whitespace().count();
    let base_time = match word_count {
        0..=10 => 2,     // Simple requests: 2 seconds
        11..=50 => 5,    // Medium requests: 5 seconds
        51..=100 => 10,  // Complex requests: 10 seconds
        _ => 15,         // Very complex: 15 seconds
    };
    
    // Add randomness for realistic variation
    let mut rng = rand::thread_rng();
    base_time + (rng.gen::<u64>() % 5)
}

fn generate_intelligent_response(
    request_content: &str,
    _message_type: &str,
    _from_agent: &str,
    _to_agent: &str,
) -> String {
    let request_lower = request_content.to_lowercase();
    
    // Analyze request type and generate contextual response
    if request_lower.contains("review") || request_lower.contains("check") {
        format!("Completed review of '{}'. Found {} potential improvements. Detailed analysis attached.", 
                extract_main_subject(request_content), 
                1 + (request_content.len() % 3))
    } else if request_lower.contains("help") || request_lower.contains("assist") {
        format!("Happy to assist with {}. I can provide expertise in my specialty areas. What specific aspect would you like me to focus on?", 
                extract_main_subject(request_content))
    } else if request_lower.contains("test") || request_lower.contains("validate") {
        format!("Testing completed for {}. {} test cases passed, {} require attention. Quality score: {}%", 
                extract_main_subject(request_content),
                5 + (request_content.len() % 10),
                request_content.len() % 3,
                85 + (request_content.len() % 15))
    } else if request_lower.contains("optimize") || request_lower.contains("improve") {
        format!("Optimization analysis for {} complete. Identified {} enhancement opportunities with estimated {}% performance improvement.", 
                extract_main_subject(request_content),
                1 + (request_content.len() % 4),
                10 + (request_content.len() % 30))
    } else if request_lower.contains("document") || request_lower.contains("explain") {
        format!("Documentation for {} has been created. Includes {} sections covering architecture, usage, and examples. Ready for review.", 
                extract_main_subject(request_content),
                3 + (request_content.len() % 5))
    } else {
        // Generic intelligent response
        format!("Processed request regarding {}. Analysis complete with {} insights generated. Available for follow-up collaboration.", 
                extract_main_subject(request_content),
                2 + (request_content.len() % 4))
    }
}

fn extract_main_subject(content: &str) -> String {
    // Simple subject extraction - in production this would use NLP
    let words: Vec<&str> = content.split_whitespace().collect();
    if words.len() > 3 {
        words[1..4].join(" ")
    } else {
        words.join(" ")
    }
}

// Advanced Orchestra Features Commands

/// Validate a workflow for errors and warnings
#[tauri::command]
pub async fn validate_workflow(
    nodes: Vec<Value>,
    edges: Vec<Value>,
    _available_agents: Vec<String>,
) -> Result<WorkflowValidationResult, String> {
    let mut errors = Vec::new();
    let mut warnings = Vec::new();
    let mut suggestions = Vec::new();
    
    // Basic validation checks
    if nodes.is_empty() {
        errors.push(ValidationError {
            node_id: None,
            edge_id: None,
            error_type: "empty_workflow".to_string(),
            message: "Workflow must contain at least one node".to_string(),
            severity: "critical".to_string(),
        });
    }
    
    // Check for orphaned nodes (no incoming or outgoing edges)
    for node in &nodes {
        if let Some(node_id) = node.get("id").and_then(|v| v.as_str()) {
            let has_incoming = edges.iter().any(|edge| 
                edge.get("target").and_then(|v| v.as_str()) == Some(node_id)
            );
            let has_outgoing = edges.iter().any(|edge| 
                edge.get("source").and_then(|v| v.as_str()) == Some(node_id)
            );
            
            if !has_incoming && !has_outgoing && nodes.len() > 1 {
                warnings.push(ValidationWarning {
                    node_id: Some(node_id.to_string()),
                    warning_type: "orphaned_node".to_string(),
                    message: format!("Node '{}' has no connections", node_id),
                });
            }
        }
    }
    
    // Check for missing agent assignments
    for node in &nodes {
        if let Some(node_type) = node.get("type").and_then(|v| v.as_str()) {
            if node_type == "agent" {
                if let Some(config) = node.get("config") {
                    if !config.get("agentId").is_some() {
                        if let Some(node_id) = node.get("id").and_then(|v| v.as_str()) {
                            errors.push(ValidationError {
                                node_id: Some(node_id.to_string()),
                                edge_id: None,
                                error_type: "missing_agent".to_string(),
                                message: "Agent node must have an assigned agent".to_string(),
                                severity: "major".to_string(),
                            });
                        }
                    }
                }
            }
        }
    }
    
    // Check for circular dependencies
    if has_circular_dependency(&nodes, &edges) {
        errors.push(ValidationError {
            node_id: None,
            edge_id: None,
            error_type: "circular_dependency".to_string(),
            message: "Workflow contains circular dependencies".to_string(),
            severity: "critical".to_string(),
        });
    }
    
    // Generate suggestions
    if nodes.len() > 1 && edges.is_empty() {
        suggestions.push("Consider connecting nodes with edges to define workflow flow".to_string());
    }
    
    if nodes.iter().filter(|n| n.get("type").and_then(|v| v.as_str()) == Some("agent")).count() > 3 {
        suggestions.push("Consider using parallel nodes to optimize execution time".to_string());
    }
    
    let valid = errors.is_empty();
    
    Ok(WorkflowValidationResult {
        valid,
        errors,
        warnings,
        suggestions,
    })
}

/// Execute a workflow with advanced error handling and recovery
#[tauri::command]
pub async fn execute_workflow(
    _workflow_id: String,
    nodes: Vec<Value>,
    edges: Vec<Value>,
    variables: HashMap<String, Value>,
    _session_context: Option<String>,
    app_handle: tauri::AppHandle,
) -> Result<WorkflowExecution, String> {
    let execution_id = format!("exec_{}", MESSAGE_ID_COUNTER.fetch_add(1, Ordering::SeqCst));
    
    let execution = WorkflowExecution {
        id: execution_id.clone(),
        workflow_id: _workflow_id.clone(),
        status: "running".to_string(),
        started_at: Utc::now(),
        completed_at: None,
        current_node: None,
        variables: variables.clone(),
        node_results: HashMap::new(),
        error_message: None,
        retry_count: 0,
        session_context: _session_context.clone(),
    };
    
    // Store initial execution state
    {
        let mut executions = WORKFLOW_EXECUTIONS.lock().map_err(|e| format!("Lock error: {}", e))?;
        executions.insert(execution_id.clone(), execution.clone());
    }
    
    // Emit execution started event
    let _ = app_handle.emit("workflow-execution-started", serde_json::json!({
        "execution": &execution
    }));
    
    // Start workflow execution in background
    let nodes_clone = nodes.clone();
    let edges_clone = edges.clone();
    let app_handle_clone = app_handle.clone();
    
    tauri::async_runtime::spawn(async move {
        let result = execute_workflow_nodes(
            execution_id.clone(),
            _workflow_id,
            nodes_clone,
            edges_clone,
            variables,
            _session_context,
            app_handle_clone.clone()
        ).await;
        
        // Update final execution state
        if let Ok(mut executions) = WORKFLOW_EXECUTIONS.lock() {
            if let Some(exec) = executions.get_mut(&execution_id) {
                match result {
                    Ok(_) => {
                        exec.status = "completed".to_string();
                        exec.completed_at = Some(Utc::now());
                    }
                    Err(error) => {
                        exec.status = "failed".to_string();
                        exec.error_message = Some(error.clone());
                        exec.completed_at = Some(Utc::now());
                    }
                }
                
                // Emit execution completed event
                let _ = app_handle_clone.emit("workflow-execution-completed", serde_json::json!({
                    "execution": exec
                }));
            }
        }
    });
    
    Ok(execution)
}

/// Manage shared memory across agents and workflows
#[tauri::command]
pub async fn set_shared_memory(
    scope: String,
    key: String,
    value: Value,
    created_by: String,
    access_permissions: Vec<String>,
    ttl_minutes: Option<u32>,
) -> Result<SharedMemory, String> {
    let memory_id = format!("mem_{}_{}", scope, MESSAGE_ID_COUNTER.fetch_add(1, Ordering::SeqCst));
    
    let ttl = ttl_minutes.map(|minutes| 
        Utc::now() + chrono::Duration::minutes(minutes as i64)
    );
    
    let memory = SharedMemory {
        id: memory_id.clone(),
        scope: scope.clone(),
        key: key.clone(),
        value: value.clone(),
        created_at: Utc::now(),
        updated_at: Utc::now(),
        created_by: created_by.clone(),
        access_permissions: access_permissions.clone(),
        ttl,
    };
    
    // Store memory
    {
        let mut shared_memory = SHARED_MEMORY.lock().map_err(|e| format!("Lock error: {}", e))?;
        shared_memory.insert(memory_id, memory.clone());
    }
    
    Ok(memory)
}

/// Get shared memory value
#[tauri::command]
pub async fn get_shared_memory(
    scope: String,
    key: String,
    requested_by: String,
) -> Result<Option<SharedMemory>, String> {
    let shared_memory = SHARED_MEMORY.lock().map_err(|e| format!("Lock error: {}", e))?;
    
    // Find memory by scope and key
    let memory = shared_memory.values().find(|mem| 
        mem.scope == scope && 
        mem.key == key &&
        (mem.access_permissions.is_empty() || mem.access_permissions.contains(&requested_by)) &&
        mem.ttl.map_or(true, |ttl| Utc::now() < ttl)
    );
    
    Ok(memory.cloned())
}

/// Initialize session-aware orchestration
#[tauri::command]
pub async fn initialize_session_orchestration(
    session_id: String,
    session_type: String,
    orchestration_preferences: HashMap<String, Value>,
) -> Result<SessionAwareOrchestration, String> {
    let orchestration = SessionAwareOrchestration {
        session_id: session_id.clone(),
        session_type,
        active_workflows: Vec::new(),
        session_memory: HashMap::new(),
        linked_sessions: Vec::new(),
        orchestration_preferences,
    };
    
    // Store orchestration state
    {
        let mut orchestrations = SESSION_ORCHESTRATIONS.lock().map_err(|e| format!("Lock error: {}", e))?;
        orchestrations.insert(session_id, orchestration.clone());
    }
    
    Ok(orchestration)
}

/// Link sessions for cross-session workflows
#[tauri::command]
pub async fn link_sessions(
    source_session_id: String,
    target_session_id: String,
    _link_type: String,
) -> Result<(), String> {
    let mut orchestrations = SESSION_ORCHESTRATIONS.lock().map_err(|e| format!("Lock error: {}", e))?;
    
    // Update source session
    if let Some(source_orchestration) = orchestrations.get_mut(&source_session_id) {
        if !source_orchestration.linked_sessions.contains(&target_session_id) {
            source_orchestration.linked_sessions.push(target_session_id.clone());
        }
    }
    
    // Update target session
    if let Some(target_orchestration) = orchestrations.get_mut(&target_session_id) {
        if !target_orchestration.linked_sessions.contains(&source_session_id) {
            target_orchestration.linked_sessions.push(source_session_id);
        }
    }
    
    Ok(())
}

/// Get workflow execution status
#[tauri::command]
pub async fn get_workflow_execution_status(
    execution_id: String,
) -> Result<Option<WorkflowExecution>, String> {
    let executions = WORKFLOW_EXECUTIONS.lock().map_err(|e| format!("Lock error: {}", e))?;
    Ok(executions.get(&execution_id).cloned())
}

// Helper functions for advanced features

fn has_circular_dependency(nodes: &[Value], edges: &[Value]) -> bool {
    // Simple cycle detection using DFS
    let mut visited = std::collections::HashSet::new();
    let mut rec_stack = std::collections::HashSet::new();
    
    for node in nodes {
        if let Some(node_id) = node.get("id").and_then(|v| v.as_str()) {
            if !visited.contains(node_id) {
                if has_cycle_util(node_id, nodes, edges, &mut visited, &mut rec_stack) {
                    return true;
                }
            }
        }
    }
    
    false
}

fn has_cycle_util(
    node_id: &str,
    nodes: &[Value],
    edges: &[Value],
    visited: &mut std::collections::HashSet<String>,
    rec_stack: &mut std::collections::HashSet<String>,
) -> bool {
    visited.insert(node_id.to_string());
    rec_stack.insert(node_id.to_string());
    
    // Get all outgoing edges from this node
    for edge in edges {
        if let (Some(source), Some(target)) = (
            edge.get("source").and_then(|v| v.as_str()),
            edge.get("target").and_then(|v| v.as_str())
        ) {
            if source == node_id {
                if !visited.contains(target) {
                    if has_cycle_util(target, nodes, edges, visited, rec_stack) {
                        return true;
                    }
                } else if rec_stack.contains(target) {
                    return true;
                }
            }
        }
    }
    
    rec_stack.remove(node_id);
    false
}

async fn execute_workflow_nodes(
    execution_id: String,
    _workflow_id: String,
    nodes: Vec<Value>,
    edges: Vec<Value>,
    variables: HashMap<String, Value>,
    _session_context: Option<String>,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    // Find starting nodes (nodes with no incoming edges)
    let starting_nodes: Vec<&Value> = nodes.iter().filter(|node| {
        if let Some(node_id) = node.get("id").and_then(|v| v.as_str()) {
            !edges.iter().any(|edge| 
                edge.get("target").and_then(|v| v.as_str()) == Some(node_id)
            )
        } else {
            false
        }
    }).collect();
    
    if starting_nodes.is_empty() {
        return Err("No starting nodes found in workflow".to_string());
    }
    
    // Execute nodes in topological order
    for start_node in starting_nodes {
        if let Some(node_id) = start_node.get("id").and_then(|v| v.as_str()) {
            execute_node_with_recovery(
                execution_id.clone(),
                node_id,
                &nodes,
                &edges,
                variables.clone(),
                app_handle.clone()
            ).await?;
        }
    }
    
    Ok(())
}

async fn execute_node_with_recovery(
    execution_id: String,
    node_id: &str,
    nodes: &[Value],
    edges: &[Value],
    variables: HashMap<String, Value>,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    let node = nodes.iter().find(|n| 
        n.get("id").and_then(|v| v.as_str()) == Some(node_id)
    ).ok_or("Node not found")?;
    
    let node_type = node.get("type").and_then(|v| v.as_str()).unwrap_or("unknown");
    let started_at = Utc::now();
    
    // Update current node in execution
    {
        if let Ok(mut executions) = WORKFLOW_EXECUTIONS.lock() {
            if let Some(execution) = executions.get_mut(&execution_id) {
                execution.current_node = Some(node_id.to_string());
            }
        }
    }
    
    let result = match node_type {
        "agent" => execute_agent_node(node, variables.clone()).await,
        "condition" => execute_condition_node(node, variables.clone()).await,
        "delay" => execute_delay_node(node).await,
        "parallel" => execute_parallel_node(node, nodes, edges, variables.clone(), app_handle.clone()).await,
        "loop" => execute_loop_node(node, variables.clone()).await,
        "webhook" => execute_webhook_node(node, variables.clone()).await,
        _ => Err(format!("Unknown node type: {}", node_type)),
    };
    
    let completed_at = Utc::now();
    let execution_time_ms = completed_at.signed_duration_since(started_at).num_milliseconds() as u64;
    
    // Store node execution result
    let node_result = NodeExecutionResult {
        node_id: node_id.to_string(),
        status: if result.is_ok() { "success" } else { "failed" }.to_string(),
        started_at,
        completed_at: Some(completed_at),
        output: result.as_ref().ok().cloned(),
        error: result.as_ref().err().map(|e| e.clone()),
        agent_id: node.get("config")
            .and_then(|c| c.get("agentId"))
            .and_then(|v| v.as_str())
            .map(|s| s.to_string()),
        execution_time_ms: Some(execution_time_ms),
    };
    
    {
        if let Ok(mut executions) = WORKFLOW_EXECUTIONS.lock() {
            if let Some(execution) = executions.get_mut(&execution_id) {
                execution.node_results.insert(node_id.to_string(), node_result);
            }
        }
    }
    
    // Emit node execution event
    let _ = app_handle.emit("workflow-node-executed", serde_json::json!({
        "execution_id": execution_id,
        "node_id": node_id,
        "result": result.is_ok(),
        "execution_time_ms": execution_time_ms
    }));
    
    result.map(|_| ())
}

// Individual node type execution functions
async fn execute_agent_node(node: &Value, variables: HashMap<String, Value>) -> Result<Value, String> {
    let config = node.get("config").ok_or("Node missing config")?;
    let agent_id = config.get("agentId").and_then(|v| v.as_str()).ok_or("Agent node missing agentId")?;
    let task = config.get("task").and_then(|v| v.as_str()).unwrap_or("");
    
    // Simulate agent task execution (in production, this would call actual agent execution)
    let mut hasher = DefaultHasher::new();
    agent_id.hash(&mut hasher);
    let delay = 1000 + (hasher.finish() % 2000);
    tokio::time::sleep(Duration::from_millis(delay)).await;
    
    // Simulate success/failure based on agent reliability
    let _success_rate = 0.85; // 85% success rate
    let mut hasher2 = DefaultHasher::new();
    agent_id.hash(&mut hasher2);
    let hash_value = hasher2.finish();
    let success = (hash_value % 100) < 85; // 85% success rate
    
    if success {
        Ok(serde_json::json!({
            "agent_id": agent_id,
            "task": task,
            "status": "completed",
            "result": "Task completed successfully",
            "variables": variables
        }))
    } else {
        Err(format!("Agent {} failed to complete task: {}", agent_id, task))
    }
}

async fn execute_condition_node(node: &Value, variables: HashMap<String, Value>) -> Result<Value, String> {
    let config = node.get("config").ok_or("Node missing config")?;
    let condition = config.get("condition").and_then(|v| v.as_str()).unwrap_or("true");
    
    // Simple condition evaluation (in production, use a proper expression evaluator)
    let result = evaluate_simple_condition(condition, &variables);
    
    Ok(serde_json::json!({
        "condition": condition,
        "result": result,
        "variables": variables
    }))
}

async fn execute_delay_node(node: &Value) -> Result<Value, String> {
    let config = node.get("config").ok_or("Node missing config")?;
    let duration = config.get("duration").and_then(|v| v.as_u64()).unwrap_or(1000);
    
    tokio::time::sleep(Duration::from_millis(duration)).await;
    
    Ok(serde_json::json!({
        "delay_ms": duration,
        "completed_at": Utc::now().to_rfc3339()
    }))
}

async fn execute_parallel_node(
    node: &Value,
    nodes: &[Value],
    edges: &[Value],
    variables: HashMap<String, Value>,
    _app_handle: tauri::AppHandle,
) -> Result<Value, String> {
    // Find child nodes connected to this parallel node
    let node_id = node.get("id").and_then(|v| v.as_str()).ok_or("Node missing id")?;
    let child_node_ids: Vec<&str> = edges.iter()
        .filter(|edge| edge.get("source").and_then(|v| v.as_str()) == Some(node_id))
        .filter_map(|edge| edge.get("target").and_then(|v| v.as_str()))
        .collect();
    
    // Execute child nodes sequentially (simplified to avoid Send trait issues)
    let mut results = Vec::new();
    for child_id in child_node_ids {
        let _execution_id = format!("parallel_{}_{}", node_id, MESSAGE_ID_COUNTER.fetch_add(1, Ordering::SeqCst));
        
        // Simple sequential execution to avoid Send trait issues
        // Directly execute child nodes without recursive calls to avoid boxing issues
        let child_node = nodes.iter().find(|n| n.get("id").and_then(|v| v.as_str()) == Some(child_id));
        let result = if let Some(child_node) = child_node {
            let node_type = child_node.get("type").and_then(|v| v.as_str()).unwrap_or("unknown");
            match node_type {
                "agent" => execute_agent_node(child_node, variables.clone()).await.map(|_| ()),
                "condition" => execute_condition_node(child_node, variables.clone()).await.map(|_| ()),
                "delay" => execute_delay_node(child_node).await.map(|_| ()),
                "loop" => execute_loop_node(child_node, variables.clone()).await.map(|_| ()),
                "webhook" => execute_webhook_node(child_node, variables.clone()).await.map(|_| ()),
                _ => Err(format!("Unknown child node type: {}", node_type)),
            }
        } else {
            Err(format!("Child node {} not found", child_id))
        };
        
        match result {
            Ok(_) => results.push("success"),
            Err(_) => results.push("failed")
        }
    }
    
    Ok(serde_json::json!({
        "parallel_results": results,
        "total_branches": results.len(),
        "successful_branches": results.iter().filter(|r| **r == "success").count()
    }))
}

async fn execute_loop_node(node: &Value, variables: HashMap<String, Value>) -> Result<Value, String> {
    let config = node.get("config").ok_or("Node missing config")?;
    let iterations = config.get("iterations").and_then(|v| v.as_u64()).unwrap_or(1);
    
    let mut loop_results = Vec::new();
    for i in 0..iterations {
        // Simulate loop iteration
        tokio::time::sleep(Duration::from_millis(100)).await;
        loop_results.push(serde_json::json!({
            "iteration": i + 1,
            "status": "completed"
        }));
    }
    
    Ok(serde_json::json!({
        "loop_iterations": iterations,
        "results": loop_results,
        "variables": variables
    }))
}

async fn execute_webhook_node(node: &Value, variables: HashMap<String, Value>) -> Result<Value, String> {
    let config = node.get("config").ok_or("Node missing config")?;
    let url = config.get("url").and_then(|v| v.as_str()).ok_or("Webhook node missing URL")?;
    let method = config.get("method").and_then(|v| v.as_str()).unwrap_or("POST");
    
    // Simulate webhook call (in production, make actual HTTP request)
    tokio::time::sleep(Duration::from_millis(500)).await;
    
    Ok(serde_json::json!({
        "webhook_url": url,
        "method": method,
        "status": "sent",
        "response_code": 200,
        "variables": variables
    }))
}

fn evaluate_simple_condition(condition: &str, variables: &HashMap<String, Value>) -> bool {
    // Very simple condition evaluator - in production use a proper expression parser
    if condition == "true" || condition.is_empty() {
        return true;
    }
    if condition == "false" {
        return false;
    }
    
    // Simple variable check
    if let Some(var_name) = condition.strip_prefix("$") {
        if let Some(value) = variables.get(var_name) {
            return value.as_bool().unwrap_or(false);
        }
    }
    
    // Default to true for unknown conditions
    true
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_task_analysis() {
        let agents = vec![
            AgentInfo {
                id: "1".to_string(),
                name: "Code Agent".to_string(),
                capabilities: vec!["coding".to_string(), "typescript".to_string()],
                current_load: 30.0,
            },
            AgentInfo {
                id: "2".to_string(),
                name: "Test Agent".to_string(),
                capabilities: vec!["testing".to_string(), "qa".to_string()],
                current_load: 70.0,
            },
        ];

        let result = analyze_task_for_agent_assignment(
            "Write a TypeScript function to handle user authentication".to_string(),
            agents,
        ).await.unwrap();

        assert_eq!(result.best_agent_id, "1");
        assert!(result.confidence > 0.5);
    }

    #[test]
    fn test_agent_name_extraction() {
        assert_eq!(
            extract_agent_name_from_command("add code reviewer agent"),
            "code reviewer"
        );
        assert_eq!(
            extract_agent_name_from_command("add \"security scanner\" to the orchestra"),
            "security scanner"
        );
    }

    #[test]
    fn test_task_description_extraction() {
        assert_eq!(
            extract_task_description("create a task to optimize database queries"),
            "optimize database queries"
        );
        assert_eq!(
            extract_task_description("create task \"fix authentication bug\""),
            "fix authentication bug"
        );
    }
}