use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use chrono::DateTime;
use tauri::Manager;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserInteraction {
    pub id: String,
    pub session_id: String,
    #[serde(rename = "type")]
    pub interaction_type: String,
    pub timestamp: String,
    pub user_id: Option<String>,
    pub context: InteractionContext,
    pub outcome: Option<String>,
    pub feedback: Option<UserFeedback>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct InteractionContext {
    pub idea_id: Option<String>,
    pub cluster_id: Option<String>,
    pub previous_value: Option<serde_json::Value>,
    pub new_value: Option<serde_json::Value>,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserFeedback {
    pub rating: i32,
    pub comment: Option<String>,
    pub category: String,
    pub timestamp: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LearningPattern {
    pub id: String,
    #[serde(rename = "type")]
    pub pattern_type: String,
    pub pattern: serde_json::Value,
    pub confidence: f64,
    pub frequency: i32,
    pub last_seen: String,
    pub user_id: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SessionOutcome {
    pub session_id: String,
    pub outcome: SessionOutcomeData,
    pub timestamp: String,
    pub user_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SessionOutcomeData {
    pub ideas_generated: i32,
    pub ideas_implemented: i32,
    pub clusters_created: i32,
    pub export_count: i32,
    pub session_duration: i32,
    pub user_satisfaction: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdaptationRule {
    pub id: String,
    pub condition: String,
    pub action: String,
    pub priority: i32,
    pub enabled: bool,
    pub success_rate: f64,
    pub times_applied: i32,
}

// Save user interaction
#[tauri::command]
pub async fn save_user_interaction(
    interaction: UserInteraction,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let interactions_dir = app_dir.join("learning").join("interactions");
    fs::create_dir_all(&interactions_dir)
        .map_err(|e| format!("Failed to create interactions directory: {}", e))?;
    
    let interaction_file = interactions_dir.join(format!("{}.json", interaction.id));
    
    let interaction_json = serde_json::to_string_pretty(&interaction)
        .map_err(|e| format!("Failed to serialize interaction: {}", e))?;
    
    fs::write(&interaction_file, interaction_json)
        .map_err(|e| format!("Failed to write interaction file: {}", e))?;
    
    Ok(())
}

// Get user interactions
#[tauri::command]
pub async fn get_user_interactions(
    user_id: String,
    app_handle: tauri::AppHandle,
) -> Result<Vec<UserInteraction>, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let interactions_dir = app_dir.join("learning").join("interactions");
    let mut interactions = Vec::new();
    
    if !interactions_dir.exists() {
        return Ok(interactions);
    }
    
    let entries = fs::read_dir(&interactions_dir)
        .map_err(|e| format!("Failed to read interactions directory: {}", e))?;
    
    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let path = entry.path();
        
        if path.extension().and_then(|s| s.to_str()) == Some("json") {
            let content = fs::read_to_string(&path)
                .map_err(|e| format!("Failed to read interaction file: {}", e))?;
            
            if let Ok(interaction) = serde_json::from_str::<UserInteraction>(&content) {
                if interaction.user_id.as_ref() == Some(&user_id) || user_id == "default" {
                    interactions.push(interaction);
                }
            }
        }
    }
    
    // Sort by timestamp (newest first)
    interactions.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
    
    Ok(interactions)
}

// Save user feedback
#[tauri::command]
pub async fn save_user_feedback(
    interaction_id: String,
    feedback: UserFeedback,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let interactions_dir = app_dir.join("learning").join("interactions");
    let interaction_file = interactions_dir.join(format!("{}.json", interaction_id));
    
    if !interaction_file.exists() {
        return Err("Interaction not found".to_string());
    }
    
    // Read existing interaction
    let content = fs::read_to_string(&interaction_file)
        .map_err(|e| format!("Failed to read interaction file: {}", e))?;
    
    let mut interaction: UserInteraction = serde_json::from_str(&content)
        .map_err(|e| format!("Failed to parse interaction: {}", e))?;
    
    // Update with feedback
    interaction.feedback = Some(feedback.clone());
    interaction.outcome = Some(if feedback.rating >= 4 {
        "positive".to_string()
    } else if feedback.rating <= 2 {
        "negative".to_string()
    } else {
        "neutral".to_string()
    });
    
    // Write back
    let interaction_json = serde_json::to_string_pretty(&interaction)
        .map_err(|e| format!("Failed to serialize interaction: {}", e))?;
    
    fs::write(&interaction_file, interaction_json)
        .map_err(|e| format!("Failed to write interaction file: {}", e))?;
    
    Ok(())
}

// Save learning pattern
#[tauri::command]
pub async fn save_learning_pattern(
    pattern: LearningPattern,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let patterns_dir = app_dir.join("learning").join("patterns");
    fs::create_dir_all(&patterns_dir)
        .map_err(|e| format!("Failed to create patterns directory: {}", e))?;
    
    let pattern_file = patterns_dir.join(format!("{}.json", pattern.id));
    
    let pattern_json = serde_json::to_string_pretty(&pattern)
        .map_err(|e| format!("Failed to serialize pattern: {}", e))?;
    
    fs::write(&pattern_file, pattern_json)
        .map_err(|e| format!("Failed to write pattern file: {}", e))?;
    
    Ok(())
}

// Get learning patterns
#[tauri::command]
pub async fn get_learning_patterns(
    user_id: String,
    app_handle: tauri::AppHandle,
) -> Result<Vec<LearningPattern>, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let patterns_dir = app_dir.join("learning").join("patterns");
    let mut patterns = Vec::new();
    
    if !patterns_dir.exists() {
        return Ok(patterns);
    }
    
    let entries = fs::read_dir(&patterns_dir)
        .map_err(|e| format!("Failed to read patterns directory: {}", e))?;
    
    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let path = entry.path();
        
        if path.extension().and_then(|s| s.to_str()) == Some("json") {
            let content = fs::read_to_string(&path)
                .map_err(|e| format!("Failed to read pattern file: {}", e))?;
            
            if let Ok(pattern) = serde_json::from_str::<LearningPattern>(&content) {
                if pattern.user_id.as_ref() == Some(&user_id) || user_id == "default" {
                    patterns.push(pattern);
                }
            }
        }
    }
    
    // Sort by confidence and frequency
    patterns.sort_by(|a, b| {
        let score_a = a.confidence * (a.frequency as f64).log10().max(1.0);
        let score_b = b.confidence * (b.frequency as f64).log10().max(1.0);
        score_b.partial_cmp(&score_a).unwrap_or(std::cmp::Ordering::Equal)
    });
    
    Ok(patterns)
}

// Save session outcome
#[tauri::command]
pub async fn save_session_outcome(
    session_data: SessionOutcome,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let outcomes_dir = app_dir.join("learning").join("outcomes");
    fs::create_dir_all(&outcomes_dir)
        .map_err(|e| format!("Failed to create outcomes directory: {}", e))?;
    
    let outcome_file = outcomes_dir.join(format!("{}_{}.json", 
        session_data.session_id, 
        session_data.timestamp.replace(':', "-")
    ));
    
    let outcome_json = serde_json::to_string_pretty(&session_data)
        .map_err(|e| format!("Failed to serialize outcome: {}", e))?;
    
    fs::write(&outcome_file, outcome_json)
        .map_err(|e| format!("Failed to write outcome file: {}", e))?;
    
    Ok(())
}

// Get recent interactions for a session
#[tauri::command]
pub async fn get_recent_interactions(
    session_id: String,
    limit: usize,
    app_handle: tauri::AppHandle,
) -> Result<Vec<UserInteraction>, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let interactions_dir = app_dir.join("learning").join("interactions");
    let mut interactions = Vec::new();
    
    if !interactions_dir.exists() {
        return Ok(interactions);
    }
    
    let entries = fs::read_dir(&interactions_dir)
        .map_err(|e| format!("Failed to read interactions directory: {}", e))?;
    
    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let path = entry.path();
        
        if path.extension().and_then(|s| s.to_str()) == Some("json") {
            let content = fs::read_to_string(&path)
                .map_err(|e| format!("Failed to read interaction file: {}", e))?;
            
            if let Ok(interaction) = serde_json::from_str::<UserInteraction>(&content) {
                if interaction.session_id == session_id {
                    interactions.push(interaction);
                }
            }
        }
    }
    
    // Sort by timestamp (newest first) and limit
    interactions.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));
    interactions.truncate(limit);
    
    Ok(interactions)
}

// Get adaptation rules
#[tauri::command]
pub async fn get_adaptation_rules(
    app_handle: tauri::AppHandle,
) -> Result<Vec<AdaptationRule>, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let rules_file = app_dir.join("learning").join("adaptation_rules.json");
    
    if !rules_file.exists() {
        // Return default rules
        return Ok(get_default_adaptation_rules());
    }
    
    let content = fs::read_to_string(&rules_file)
        .map_err(|e| format!("Failed to read rules file: {}", e))?;
    
    let rules: Vec<AdaptationRule> = serde_json::from_str(&content)
        .map_err(|e| format!("Failed to parse rules: {}", e))?;
    
    Ok(rules)
}

// Analyze user behavior patterns
#[tauri::command]
pub async fn analyze_user_behavior(
    user_id: String,
    app_handle: tauri::AppHandle,
) -> Result<serde_json::Value, String> {
    let interactions = get_user_interactions(user_id.clone(), app_handle.clone()).await?;
    let patterns = get_learning_patterns(user_id, app_handle).await?;
    
    let mut analysis = serde_json::Map::new();
    
    // Activity analysis
    let total_interactions = interactions.len();
    let interaction_types: HashMap<String, usize> = interactions
        .iter()
        .fold(HashMap::new(), |mut acc, interaction| {
            *acc.entry(interaction.interaction_type.clone()).or_insert(0) += 1;
            acc
        });
    
    analysis.insert("total_interactions".to_string(), serde_json::Value::Number(total_interactions.into()));
    analysis.insert("interaction_types".to_string(), serde_json::to_value(interaction_types).unwrap());
    
    // Feedback analysis
    let feedback_interactions: Vec<&UserInteraction> = interactions
        .iter()
        .filter(|i| i.feedback.is_some())
        .collect();
    
    if !feedback_interactions.is_empty() {
        let avg_rating = feedback_interactions
            .iter()
            .map(|i| i.feedback.as_ref().unwrap().rating as f64)
            .sum::<f64>() / feedback_interactions.len() as f64;
        
        analysis.insert("average_feedback_rating".to_string(), serde_json::Value::Number(
            serde_json::Number::from_f64(avg_rating).unwrap_or(serde_json::Number::from(0))
        ));
    }
    
    // Pattern analysis
    let high_confidence_patterns = patterns
        .iter()
        .filter(|p| p.confidence > 0.7)
        .count();
    
    analysis.insert("high_confidence_patterns".to_string(), serde_json::Value::Number(high_confidence_patterns.into()));
    
    // Time-based analysis
    if let Some(first_interaction) = interactions.last() {
        if let Some(last_interaction) = interactions.first() {
            if let (Ok(first_time), Ok(last_time)) = (
                DateTime::parse_from_rfc3339(&first_interaction.timestamp),
                DateTime::parse_from_rfc3339(&last_interaction.timestamp)
            ) {
                let duration_days = (last_time - first_time).num_days();
                analysis.insert("active_days".to_string(), serde_json::Value::Number(duration_days.into()));
                
                if duration_days > 0 {
                    let interactions_per_day = total_interactions as f64 / duration_days as f64;
                    analysis.insert("avg_interactions_per_day".to_string(), serde_json::Value::Number(
                        serde_json::Number::from_f64(interactions_per_day).unwrap_or(serde_json::Number::from(0))
                    ));
                }
            }
        }
    }
    
    Ok(serde_json::Value::Object(analysis))
}

fn get_default_adaptation_rules() -> Vec<AdaptationRule> {
    vec![
        AdaptationRule {
            id: "auto_cluster_threshold".to_string(),
            condition: "ideas.length >= 8 && clustered_ratio < 0.5".to_string(),
            action: "suggest_clustering".to_string(),
            priority: 1,
            enabled: true,
            success_rate: 0.0,
            times_applied: 0,
        },
        AdaptationRule {
            id: "priority_reminder".to_string(),
            condition: "unprioritized_ideas > 5".to_string(),
            action: "suggest_prioritization".to_string(),
            priority: 2,
            enabled: true,
            success_rate: 0.0,
            times_applied: 0,
        },
        AdaptationRule {
            id: "export_suggestion".to_string(),
            condition: "session_duration > 30 && export_count == 0".to_string(),
            action: "suggest_export".to_string(),
            priority: 3,
            enabled: true,
            success_rate: 0.0,
            times_applied: 0,
        },
    ]
}