use serde::{Deserialize, Serialize};
use std::fs;
use std::process::Command;
use base64::{Engine as _, engine::general_purpose};
use tempfile::NamedTempFile;
use tauri::Manager;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct VoiceConfig {
    pub provider: String, // "whisper", "azure", "google", "aws"
    pub language: String,
    pub quality: String, // "low", "medium", "high"
    pub real_time_processing: bool,
    pub voice_commands: bool,
    pub speaker_diarization: bool,
    pub punctuation: bool,
    pub profanity_filter: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TranscriptionResult {
    pub id: String,
    pub text: String,
    pub confidence: f64,
    pub segments: Vec<TranscriptionSegment>,
    pub language: String,
    pub duration: f64,
    pub speaker_labels: Option<Vec<SpeakerLabel>>,
    pub processed_at: String,
    pub provider: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TranscriptionSegment {
    pub start: f64,
    pub end: f64,
    pub text: String,
    pub confidence: f64,
    pub speaker: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SpeakerLabel {
    pub speaker: String,
    pub start: f64,
    pub end: f64,
    pub confidence: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VoiceNote {
    pub id: String,
    pub transcription: TranscriptionResult,
    pub extracted_ideas: Vec<String>,
    pub sentiment: String,
    pub summary: String,
    pub action_items: Vec<String>,
    pub created_at: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VoiceAnalysis {
    pub extracted_ideas: Vec<String>,
    pub sentiment: String,
    pub summary: String,
    pub action_items: Vec<String>,
}

// Transcribe audio using selected provider
#[tauri::command]
pub async fn transcribe_audio(
    audio_data: String, // Base64 encoded audio
    config: VoiceConfig,
    app_handle: tauri::AppHandle,
) -> Result<TranscriptionResult, String> {
    match config.provider.as_str() {
        "whisper" => transcribe_with_whisper(audio_data, config, app_handle).await,
        "azure" => transcribe_with_azure(audio_data, config).await,
        "google" => transcribe_with_google(audio_data, config).await,
        "aws" => transcribe_with_aws(audio_data, config).await,
        _ => Err(format!("Unknown provider: {}", config.provider)),
    }
}

// Transcribe audio chunk for real-time processing
#[tauri::command]
pub async fn transcribe_audio_chunk(
    audio_data: Vec<u8>,
    config: VoiceConfig,
) -> Result<serde_json::Value, String> {
    // For real-time processing, we'd typically use streaming APIs
    // This is a simplified implementation
    let _base64_data = general_purpose::STANDARD.encode(&audio_data);
    
    // Process smaller chunks with faster models
    let mut chunk_config = config;
    chunk_config.quality = "low".to_string(); // Use faster, lower quality for real-time
    
    match chunk_config.provider.as_str() {
        "whisper" => {
            // Use smaller Whisper model for real-time
            Ok(serde_json::json!({
                "text": "",
                "confidence": 0.0,
                "partial": true
            }))
        }
        _ => Ok(serde_json::json!({
            "text": "",
            "confidence": 0.0,
            "partial": true
        })),
    }
}

// Transcribe using local Whisper
async fn transcribe_with_whisper(
    audio_data: String,
    config: VoiceConfig,
    app_handle: tauri::AppHandle,
) -> Result<TranscriptionResult, String> {
    // Decode base64 audio data
    let audio_bytes = general_purpose::STANDARD.decode(&audio_data)
        .map_err(|e| format!("Failed to decode audio data: {}", e))?;
    
    // Create temporary file for audio
    let temp_file = NamedTempFile::new()
        .map_err(|e| format!("Failed to create temp file: {}", e))?;
    
    fs::write(temp_file.path(), &audio_bytes)
        .map_err(|e| format!("Failed to write audio to temp file: {}", e))?;
    
    // Get app data directory for Whisper models
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let models_dir = app_dir.join("whisper_models");
    fs::create_dir_all(&models_dir)
        .map_err(|e| format!("Failed to create models directory: {}", e))?;
    
    // Select model based on quality
    let model_name = match config.quality.as_str() {
        "low" => "tiny",
        "medium" => "base",
        "high" => "large-v2",
        _ => "base",
    };
    
    // Check if whisper is available
    let whisper_command = if cfg!(target_os = "windows") {
        "whisper.exe"
    } else {
        "whisper"
    };
    
    // Build whisper command
    let mut cmd = Command::new(whisper_command);
    cmd.arg(temp_file.path())
        .arg("--model")
        .arg(model_name)
        .arg("--language")
        .arg(&config.language)
        .arg("--output_format")
        .arg("json")
        .arg("--output_dir")
        .arg(&models_dir);
    
    if config.speaker_diarization {
        cmd.arg("--diarize");
    }
    
    // Execute whisper
    let output = cmd.output()
        .map_err(|e| format!("Failed to execute whisper: {}. Make sure Whisper is installed.", e))?;
    
    if !output.status.success() {
        let error = String::from_utf8_lossy(&output.stderr);
        return Err(format!("Whisper failed: {}", error));
    }
    
    // Parse whisper output
    let temp_filename = temp_file.path().file_stem()
        .and_then(|s| s.to_str())
        .ok_or("Invalid temp filename")?;
    
    let json_file = models_dir.join(format!("{}.json", temp_filename));
    
    if !json_file.exists() {
        return Err("Whisper output file not found".to_string());
    }
    
    let json_content = fs::read_to_string(&json_file)
        .map_err(|e| format!("Failed to read whisper output: {}", e))?;
    
    let whisper_result: serde_json::Value = serde_json::from_str(&json_content)
        .map_err(|e| format!("Failed to parse whisper output: {}", e))?;
    
    // Convert whisper output to our format
    let text = whisper_result["text"].as_str().unwrap_or("").to_string();
    let segments = if let Some(segments_array) = whisper_result["segments"].as_array() {
        segments_array
            .iter()
            .map(|seg| TranscriptionSegment {
                start: seg["start"].as_f64().unwrap_or(0.0),
                end: seg["end"].as_f64().unwrap_or(0.0),
                text: seg["text"].as_str().unwrap_or("").to_string(),
                confidence: seg["confidence"].as_f64().unwrap_or(0.0),
                speaker: seg["speaker"].as_str().map(|s| s.to_string()),
            })
            .collect()
    } else {
        vec![]
    };
    
    // Clean up temp files
    let _ = fs::remove_file(&json_file);
    
    Ok(TranscriptionResult {
        id: uuid::Uuid::new_v4().to_string(),
        text,
        confidence: segments.iter().map(|s| s.confidence).sum::<f64>() / segments.len() as f64,
        segments,
        language: config.language,
        duration: whisper_result["duration"].as_f64().unwrap_or(0.0),
        speaker_labels: None, // Would be populated if diarization is enabled
        processed_at: chrono::Utc::now().to_rfc3339(),
        provider: "whisper".to_string(),
    })
}

// Transcribe using Azure Speech Services
async fn transcribe_with_azure(
    _audio_data: String,
    _config: VoiceConfig,
) -> Result<TranscriptionResult, String> {
    // Azure Speech Services implementation
    // This would require Azure SDK integration
    Err("Azure Speech Services not implemented yet".to_string())
}

// Transcribe using Google Speech-to-Text
async fn transcribe_with_google(
    _audio_data: String,
    _config: VoiceConfig,
) -> Result<TranscriptionResult, String> {
    // Google Speech-to-Text implementation
    // This would require Google Cloud SDK integration
    Err("Google Speech-to-Text not implemented yet".to_string())
}

// Transcribe using AWS Transcribe
async fn transcribe_with_aws(
    _audio_data: String,
    _config: VoiceConfig,
) -> Result<TranscriptionResult, String> {
    // AWS Transcribe implementation
    // This would require AWS SDK integration
    Err("AWS Transcribe not implemented yet".to_string())
}

// Analyze voice content using AI
#[tauri::command]
pub async fn analyze_voice_content(
    text: String,
) -> Result<VoiceAnalysis, String> {
    // This would typically use an AI service to analyze the transcribed text
    // For now, we'll use simple keyword extraction and sentiment analysis
    
    let extracted_ideas = extract_ideas_from_text(&text);
    let sentiment = analyze_sentiment(&text);
    let summary = generate_summary(&text);
    let action_items = extract_action_items(&text);
    
    Ok(VoiceAnalysis {
        extracted_ideas,
        sentiment,
        summary,
        action_items,
    })
}

// Save voice note
#[tauri::command]
pub async fn save_voice_note(
    voice_note: VoiceNote,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let voice_notes_dir = app_dir.join("voice_notes");
    fs::create_dir_all(&voice_notes_dir)
        .map_err(|e| format!("Failed to create voice notes directory: {}", e))?;
    
    let note_file = voice_notes_dir.join(format!("{}.json", voice_note.id));
    
    let note_json = serde_json::to_string_pretty(&voice_note)
        .map_err(|e| format!("Failed to serialize voice note: {}", e))?;
    
    fs::write(&note_file, note_json)
        .map_err(|e| format!("Failed to write voice note: {}", e))?;
    
    Ok(())
}

// Get voice notes
#[tauri::command]
pub async fn get_voice_notes(
    session_id: Option<String>,
    app_handle: tauri::AppHandle,
) -> Result<Vec<VoiceNote>, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let voice_notes_dir = app_dir.join("voice_notes");
    let mut notes = Vec::new();
    
    if !voice_notes_dir.exists() {
        return Ok(notes);
    }
    
    let entries = fs::read_dir(&voice_notes_dir)
        .map_err(|e| format!("Failed to read voice notes directory: {}", e))?;
    
    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let path = entry.path();
        
        if path.extension().and_then(|s| s.to_str()) == Some("json") {
            let content = fs::read_to_string(&path)
                .map_err(|e| format!("Failed to read voice note file: {}", e))?;
            
            if let Ok(note) = serde_json::from_str::<VoiceNote>(&content) {
                // Filter by session_id if provided
                if session_id.is_none() || 
                   note.transcription.id.contains(session_id.as_ref().unwrap()) {
                    notes.push(note);
                }
            }
        }
    }
    
    // Sort by creation time (newest first)
    notes.sort_by(|a, b| b.created_at.cmp(&a.created_at));
    
    Ok(notes)
}

// Helper functions for content analysis
fn extract_ideas_from_text(text: &str) -> Vec<String> {
    // Simple keyword-based idea extraction
    let idea_keywords = ["idea", "concept", "thought", "suggestion", "proposal", "plan"];
    let sentences: Vec<&str> = text.split(&['.', '!', '?'][..]).collect();
    
    let mut ideas = Vec::new();
    for sentence in sentences {
        let lower_sentence = sentence.to_lowercase();
        if idea_keywords.iter().any(|&keyword| lower_sentence.contains(keyword)) {
            let clean_sentence = sentence.trim().to_string();
            if !clean_sentence.is_empty() && clean_sentence.len() > 10 {
                ideas.push(clean_sentence);
            }
        }
    }
    
    ideas
}

fn analyze_sentiment(text: &str) -> String {
    // Simple sentiment analysis based on keywords
    let positive_words = ["good", "great", "excellent", "amazing", "love", "like", "happy", "excited"];
    let negative_words = ["bad", "terrible", "awful", "hate", "dislike", "sad", "angry", "frustrated"];
    
    let lower_text = text.to_lowercase();
    let positive_count = positive_words.iter().filter(|&&word| lower_text.contains(word)).count();
    let negative_count = negative_words.iter().filter(|&&word| lower_text.contains(word)).count();
    
    if positive_count > negative_count {
        "positive".to_string()
    } else if negative_count > positive_count {
        "negative".to_string()
    } else {
        "neutral".to_string()
    }
}

fn generate_summary(text: &str) -> String {
    // Simple summary generation - take first sentence and key points
    let sentences: Vec<&str> = text.split(&['.', '!', '?'][..]).collect();
    
    if sentences.is_empty() {
        return text.chars().take(100).collect::<String>() + "...";
    }
    
    // Take first sentence as summary
    let first_sentence = sentences[0].trim();
    if first_sentence.len() > 200 {
        first_sentence.chars().take(200).collect::<String>() + "..."
    } else {
        first_sentence.to_string()
    }
}

fn extract_action_items(text: &str) -> Vec<String> {
    // Extract action items based on keywords
    let action_keywords = ["need to", "should", "must", "have to", "will", "action", "todo", "task"];
    let sentences: Vec<&str> = text.split(&['.', '!', '?'][..]).collect();
    
    let mut action_items = Vec::new();
    for sentence in sentences {
        let lower_sentence = sentence.to_lowercase();
        if action_keywords.iter().any(|&keyword| lower_sentence.contains(keyword)) {
            let clean_sentence = sentence.trim().to_string();
            if !clean_sentence.is_empty() && clean_sentence.len() > 5 {
                action_items.push(clean_sentence);
            }
        }
    }
    
    action_items
}