use crate::process::{ProcessRegistryState, ProcessType};
use serde::{Deserialize, Serialize};
use std::process::Stdio;
use tauri::{A<PERSON><PERSON><PERSON><PERSON>, Emitter, Manager, State};
use tokio::io::{AsyncBufReadExt, BufReader};
use tokio::process::Command;

/// Finds the full path to the claude binary
fn find_claude_binary(app_handle: &AppHandle) -> Result<String, String> {
    crate::claude_binary::find_claude_binary(app_handle)
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BrainstormMessage {
    pub role: String,
    pub content: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BrainstormRequest {
    #[serde(rename = "sessionId")]
    pub session_id: String,
    pub messages: Vec<BrainstormMessage>,
    pub model: String,
    pub temperature: Option<f32>,
    #[serde(rename = "maxTokens")]
    pub max_tokens: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BrainstormStreamMessage {
    pub session_id: String,
    pub content: String,
    pub accumulated: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BrainstormCompleteMessage {
    pub session_id: String,
    pub content: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BrainstormErrorMessage {
    pub session_id: String,
    pub error: String,
}

#[tauri::command]
pub async fn brainstorm_chat(
    request: BrainstormRequest,
    window: tauri::Window,
    process_registry: State<'_, ProcessRegistryState>,
) -> Result<(), String> {
    let app_handle = window.app_handle();
    let binary_path = find_claude_binary(&app_handle)
        .map_err(|e| format!("Failed to get Claude binary path: {}", e))?;

    // Create a simple prompt that combines all messages
    let mut prompt = String::new();
    
    // Add system message for brainstorming context
    prompt.push_str("You are an expert brainstorming assistant and product strategist. Please help generate creative ideas, provide practical implementation suggestions, identify potential challenges and solutions, and suggest actionable next steps. Be thorough, creative, and actionable in your responses.\n\n");
    
    // Add conversation history
    for msg in &request.messages {
        match msg.role.as_str() {
            "user" => prompt.push_str(&format!("User: {}\n\n", msg.content)),
            "assistant" => prompt.push_str(&format!("Assistant: {}\n\n", msg.content)),
            _ => {}
        }
    }
    
    // Add instruction for the assistant to respond
    prompt.push_str("Assistant: ");

    // Prepare the command with minimal flags for simple chat
    let mut cmd = Command::new(&binary_path);
    cmd.arg("--print") // Use print mode for non-interactive output
        .arg("--model")
        .arg(&request.model);

    // Note: Claude CLI doesn't support max_tokens or temperature flags
    // These would need to be part of the system prompt if we want to influence them
    
    // Add the prompt as the last positional argument
    cmd.arg(&prompt);

    // Set up process for streaming output
    cmd.stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .stdin(Stdio::null());

    // Spawn the process
    let mut child = cmd
        .spawn()
        .map_err(|e| format!("Failed to spawn Claude process: {}", e))?;

    let pid = child.id().ok_or("Failed to get process ID")?;
    
    // Register the process as a brainstorm session (not a Claude Code session)
    let run_id = process_registry
        .0
        .register_brainstorm_session(
            request.session_id.clone(),
            pid,
            ".".to_string(), // project path
            "Brainstorming chat".to_string(),
            request.model.clone(),
        )
        .map_err(|e| format!("Failed to register process: {}", e))?;

    // Handle stdout
    let stdout = child
        .stdout
        .take()
        .ok_or("Failed to get stdout from Claude process")?;
    
    let stderr = child
        .stderr
        .take()
        .ok_or("Failed to get stderr from Claude process")?;

    let window_clone = window.clone();
    let session_id = request.session_id.clone();
    let registry_clone = process_registry.0.clone();
    
    // Spawn task to handle stdout
    let stdout_task = tokio::spawn(async move {
        let reader = BufReader::new(stdout);
        let mut lines = reader.lines();
        let mut accumulated = String::new();

        while let Ok(Some(line)) = lines.next_line().await {
            // Skip empty lines and system messages
            if line.trim().is_empty() {
                continue;
            }

            accumulated.push_str(&line);
            accumulated.push('\n');

            // Emit streaming content
            let _ = window_clone.emit(
                "chat-stream",
                BrainstormStreamMessage {
                    session_id: session_id.clone(),
                    content: line.clone(),
                    accumulated: accumulated.clone(),
                },
            );
        }

        // Emit completion
        let _ = window_clone.emit(
            "chat-complete",
            BrainstormCompleteMessage {
                session_id: session_id.clone(),
                content: accumulated.trim().to_string(),
            },
        );
    });

    // Spawn task to handle stderr
    let window_clone = window.clone();
    let session_id = request.session_id.clone();
    let stderr_task = tokio::spawn(async move {
        let reader = BufReader::new(stderr);
        let mut lines = reader.lines();
        let mut error_output = String::new();

        while let Ok(Some(line)) = lines.next_line().await {
            error_output.push_str(&line);
            error_output.push('\n');
        }

        if !error_output.trim().is_empty() {
            let _ = window_clone.emit(
                "chat-error",
                BrainstormErrorMessage {
                    session_id,
                    error: error_output.trim().to_string(),
                },
            );
        }
    });

    // Wait for the process to complete
    tokio::spawn(async move {
        let _ = stdout_task.await;
        let _ = stderr_task.await;
        let _ = child.wait().await;
        
        // Unregister the process
        let _ = registry_clone.unregister_process(run_id);
    });

    Ok(())
}

#[tauri::command]
pub async fn cancel_brainstorm(
    session_id: String,
    process_registry: State<'_, ProcessRegistryState>,
) -> Result<(), String> {
    // Find and kill the process associated with this session
    let processes = process_registry.0.get_running_processes()
        .map_err(|e| format!("Failed to get processes: {}", e))?;
    
    for info in processes {
        if let ProcessType::BrainstormSession { session_id: sid } = &info.process_type {
            if sid == &session_id {
                process_registry
                    .0
                    .kill_process(info.run_id)
                    .await
                    .map_err(|e| format!("Failed to cancel brainstorm session: {}", e))?;
                return Ok(());
            }
        }
    }
    
    Err("Brainstorm session not found".to_string())
}