use std::fs;
use std::path::Path;
use serde::{Deserialize, Serialize};
use tauri::Manager;
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize)]
pub struct MediaAttachment {
    pub id: String,
    pub idea_id: String,
    pub file_name: String,
    pub file_type: String,
    pub file_size: u64,
    pub mime_type: String,
    pub created_at: String,
    pub local_path: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UploadMediaRequest {
    pub idea_id: String,
    pub file_name: String,
    pub mime_type: String,
    pub data: Vec<u8>,
}

#[tauri::command]
pub async fn upload_media_attachment(
    request: UploadMediaRequest,
    app_handle: tauri::AppHandle,
) -> Result<MediaAttachment, String> {
    // Get app data directory
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    // Create media directory if it doesn't exist
    let media_dir = app_dir.join("brainstorm").join("media");
    fs::create_dir_all(&media_dir)
        .map_err(|e| format!("Failed to create media directory: {}", e))?;
    
    // Generate unique ID for the attachment
    let attachment_id = Uuid::new_v4().to_string();
    
    // Determine file extension from mime type
    let extension = match request.mime_type.as_str() {
        "image/jpeg" => "jpg",
        "image/png" => "png",
        "image/gif" => "gif",
        "image/webp" => "webp",
        "audio/mpeg" => "mp3",
        "audio/wav" => "wav",
        "audio/webm" => "webm",
        "application/pdf" => "pdf",
        _ => "bin",
    };
    
    // Create file path
    let file_name = format!("{}_{}.{}", request.idea_id, attachment_id, extension);
    let file_path = media_dir.join(&file_name);
    
    // Write file to disk
    fs::write(&file_path, &request.data)
        .map_err(|e| format!("Failed to write file: {}", e))?;
    
    // Create attachment record
    let attachment = MediaAttachment {
        id: attachment_id,
        idea_id: request.idea_id,
        file_name: request.file_name,
        file_type: extension.to_string(),
        file_size: request.data.len() as u64,
        mime_type: request.mime_type,
        created_at: chrono::Utc::now().to_rfc3339(),
        local_path: file_path.to_string_lossy().to_string(),
    };
    
    Ok(attachment)
}

#[tauri::command]
pub async fn get_media_attachment(
    attachment_id: String,
    app_handle: tauri::AppHandle,
) -> Result<Vec<u8>, String> {
    // Get app data directory
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let media_dir = app_dir.join("brainstorm").join("media");
    
    // Find the file with the attachment ID
    let entries = fs::read_dir(&media_dir)
        .map_err(|e| format!("Failed to read media directory: {}", e))?;
    
    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let file_name = entry.file_name().to_string_lossy().to_string();
        
        if file_name.contains(&attachment_id) {
            let file_data = fs::read(entry.path())
                .map_err(|e| format!("Failed to read file: {}", e))?;
            return Ok(file_data);
        }
    }
    
    Err("Attachment not found".to_string())
}

#[tauri::command]
pub async fn delete_media_attachment(
    attachment_id: String,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    // Get app data directory
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let media_dir = app_dir.join("brainstorm").join("media");
    
    // Find and delete the file with the attachment ID
    let entries = fs::read_dir(&media_dir)
        .map_err(|e| format!("Failed to read media directory: {}", e))?;
    
    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let file_name = entry.file_name().to_string_lossy().to_string();
        
        if file_name.contains(&attachment_id) {
            fs::remove_file(entry.path())
                .map_err(|e| format!("Failed to delete file: {}", e))?;
            return Ok(());
        }
    }
    
    Err("Attachment not found".to_string())
}

#[tauri::command]
pub async fn get_media_attachments_for_idea(
    idea_id: String,
    app_handle: tauri::AppHandle,
) -> Result<Vec<MediaAttachment>, String> {
    // Get app data directory
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let media_dir = app_dir.join("brainstorm").join("media");
    
    let mut attachments = Vec::new();
    
    // Check if directory exists
    if !media_dir.exists() {
        return Ok(attachments);
    }
    
    // Find all files for this idea
    let entries = fs::read_dir(&media_dir)
        .map_err(|e| format!("Failed to read media directory: {}", e))?;
    
    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let file_name = entry.file_name().to_string_lossy().to_string();
        
        if file_name.starts_with(&format!("{}_", idea_id)) {
            // Parse file metadata
            let metadata = entry.metadata()
                .map_err(|e| format!("Failed to read file metadata: {}", e))?;
            
            // Extract attachment ID from filename
            let parts: Vec<&str> = file_name.split('_').collect();
            if parts.len() >= 2 {
                let attachment_id_with_ext = parts[1];
                let attachment_id = attachment_id_with_ext.split('.').next().unwrap_or("");
                
                // Determine mime type from extension
                let extension = Path::new(&file_name)
                    .extension()
                    .and_then(|ext| ext.to_str())
                    .unwrap_or("");
                
                let mime_type = match extension {
                    "jpg" | "jpeg" => "image/jpeg",
                    "png" => "image/png",
                    "gif" => "image/gif",
                    "webp" => "image/webp",
                    "mp3" => "audio/mpeg",
                    "wav" => "audio/wav",
                    "webm" => "audio/webm",
                    "pdf" => "application/pdf",
                    _ => "application/octet-stream",
                }.to_string();
                
                attachments.push(MediaAttachment {
                    id: attachment_id.to_string(),
                    idea_id: idea_id.clone(),
                    file_name: file_name.clone(),
                    file_type: extension.to_string(),
                    file_size: metadata.len(),
                    mime_type,
                    created_at: metadata.created()
                        .ok()
                        .and_then(|time| time.duration_since(std::time::UNIX_EPOCH).ok())
                        .map(|duration| chrono::DateTime::<chrono::Utc>::from_timestamp(duration.as_secs() as i64, 0)
                            .map(|dt| dt.to_rfc3339())
                            .unwrap_or_default())
                        .unwrap_or_default(),
                    local_path: entry.path().to_string_lossy().to_string(),
                });
            }
        }
    }
    
    Ok(attachments)
}