use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use chrono::Utc;
use uuid::Uuid;
use tauri::Manager;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TransferableData {
    #[serde(rename = "type")]
    pub data_type: String,
    pub data: serde_json::Value,
    pub source_session_id: String,
    pub target_session_id: Option<String>,
    pub metadata: Option<TransferMetadata>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TransferMetadata {
    pub transferred_at: String,
    pub transferred_by: Option<String>,
    pub notes: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SessionLink {
    pub id: String,
    pub source_session_id: String,
    pub target_session_id: String,
    pub link_type: String,
    pub created_at: String,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkflowTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub steps: Vec<WorkflowStep>,
    pub created_at: String,
    pub updated_at: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkflowStep {
    pub id: String,
    pub name: String,
    pub description: String,
    pub session_type: String,
    pub input_data_type: Option<Vec<String>>,
    pub output_data_type: Option<Vec<String>>,
    pub auto_transfer: Option<bool>,
}

// Transfer data between sessions
#[tauri::command]
pub async fn transfer_session_data(
    transfer_data: TransferableData,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let transfers_dir = app_dir.join("session_transfers");
    fs::create_dir_all(&transfers_dir)
        .map_err(|e| format!("Failed to create transfers directory: {}", e))?;
    
    // Save transfer record
    let transfer_id = Uuid::new_v4().to_string();
    let transfer_file = transfers_dir.join(format!("{}.json", transfer_id));
    
    let transfer_json = serde_json::to_string_pretty(&transfer_data)
        .map_err(|e| format!("Failed to serialize transfer data: {}", e))?;
    
    fs::write(&transfer_file, transfer_json)
        .map_err(|e| format!("Failed to write transfer file: {}", e))?;
    
    // If target session is specified, add the data to that session
    if let Some(target_session_id) = &transfer_data.target_session_id {
        apply_transfer_to_session(target_session_id, &transfer_data, &app_handle)?;
    }
    
    Ok(())
}

// Create a link between two sessions
#[tauri::command]
pub async fn create_session_link(
    link: SessionLink,
    app_handle: tauri::AppHandle,
) -> Result<SessionLink, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let links_dir = app_dir.join("session_links");
    fs::create_dir_all(&links_dir)
        .map_err(|e| format!("Failed to create links directory: {}", e))?;
    
    let link_file = links_dir.join(format!("{}.json", link.id));
    
    let link_json = serde_json::to_string_pretty(&link)
        .map_err(|e| format!("Failed to serialize link: {}", e))?;
    
    fs::write(&link_file, link_json)
        .map_err(|e| format!("Failed to write link file: {}", e))?;
    
    Ok(link)
}

// Get all links for a session
#[tauri::command]
pub async fn get_session_links(
    session_id: String,
    app_handle: tauri::AppHandle,
) -> Result<Vec<SessionLink>, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let links_dir = app_dir.join("session_links");
    let mut links = Vec::new();
    
    if !links_dir.exists() {
        return Ok(links);
    }
    
    let entries = fs::read_dir(&links_dir)
        .map_err(|e| format!("Failed to read links directory: {}", e))?;
    
    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let path = entry.path();
        
        if path.extension().and_then(|s| s.to_str()) == Some("json") {
            let content = fs::read_to_string(&path)
                .map_err(|e| format!("Failed to read link file: {}", e))?;
            
            if let Ok(link) = serde_json::from_str::<SessionLink>(&content) {
                if link.source_session_id == session_id || link.target_session_id == session_id {
                    links.push(link);
                }
            }
        }
    }
    
    Ok(links)
}

// Save workflow template
#[tauri::command]
pub async fn save_workflow_template(
    workflow: WorkflowTemplate,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let workflows_dir = app_dir.join("workflow_templates");
    fs::create_dir_all(&workflows_dir)
        .map_err(|e| format!("Failed to create workflows directory: {}", e))?;
    
    let workflow_file = workflows_dir.join(format!("{}.json", workflow.id));
    
    let workflow_json = serde_json::to_string_pretty(&workflow)
        .map_err(|e| format!("Failed to serialize workflow: {}", e))?;
    
    fs::write(&workflow_file, workflow_json)
        .map_err(|e| format!("Failed to write workflow file: {}", e))?;
    
    Ok(())
}

// Get transfer history for a session
#[tauri::command]
pub async fn get_transfer_history(
    session_id: String,
    app_handle: tauri::AppHandle,
) -> Result<Vec<TransferableData>, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let transfers_dir = app_dir.join("session_transfers");
    let mut transfers = Vec::new();
    
    if !transfers_dir.exists() {
        return Ok(transfers);
    }
    
    let entries = fs::read_dir(&transfers_dir)
        .map_err(|e| format!("Failed to read transfers directory: {}", e))?;
    
    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let path = entry.path();
        
        if path.extension().and_then(|s| s.to_str()) == Some("json") {
            let content = fs::read_to_string(&path)
                .map_err(|e| format!("Failed to read transfer file: {}", e))?;
            
            if let Ok(transfer) = serde_json::from_str::<TransferableData>(&content) {
                if transfer.source_session_id == session_id || 
                   transfer.target_session_id.as_ref() == Some(&session_id) {
                    transfers.push(transfer);
                }
            }
        }
    }
    
    Ok(transfers)
}

// Fork a session
#[tauri::command]
pub async fn fork_session(
    source_session_id: String,
    fork_name: String,
    include_data: HashMap<String, bool>,
    app_handle: tauri::AppHandle,
) -> Result<String, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    // Read source session
    let brainstorm_dir = app_dir.join("brainstorm");
    let source_session_file = brainstorm_dir
        .join("sessions")
        .join(format!("{}.json", source_session_id));
    
    if !source_session_file.exists() {
        return Err("Source session not found".to_string());
    }
    
    let source_content = fs::read_to_string(&source_session_file)
        .map_err(|e| format!("Failed to read source session: {}", e))?;
    
    let mut source_data: serde_json::Value = serde_json::from_str(&source_content)
        .map_err(|e| format!("Failed to parse source session: {}", e))?;
    
    // Create new session ID
    let new_session_id = format!("session_{}", Uuid::new_v4());
    
    // Update session data
    if let Some(obj) = source_data.as_object_mut() {
        obj.insert("id".to_string(), serde_json::Value::String(new_session_id.clone()));
        obj.insert("title".to_string(), serde_json::Value::String(fork_name));
        obj.insert("created_at".to_string(), serde_json::Value::String(Utc::now().to_rfc3339()));
        obj.insert("updated_at".to_string(), serde_json::Value::String(Utc::now().to_rfc3339()));
        
        // Remove data based on include_data flags
        if !include_data.get("messages").unwrap_or(&false) {
            obj.insert("messages".to_string(), serde_json::Value::Array(vec![]));
        }
        
        if !include_data.get("metadata").unwrap_or(&true) {
            obj.insert("metadata".to_string(), serde_json::Value::Object(serde_json::Map::new()));
        }
    }
    
    // Save forked session
    let fork_session_file = brainstorm_dir
        .join("sessions")
        .join(format!("{}.json", new_session_id));
    
    let fork_json = serde_json::to_string_pretty(&source_data)
        .map_err(|e| format!("Failed to serialize forked session: {}", e))?;
    
    fs::write(&fork_session_file, fork_json)
        .map_err(|e| format!("Failed to write forked session: {}", e))?;
    
    // Copy ideas if requested
    if *include_data.get("ideas").unwrap_or(&true) {
        let source_ideas_file = brainstorm_dir
            .join("ideas")
            .join(format!("{}.json", source_session_id));
        
        if source_ideas_file.exists() {
            let fork_ideas_file = brainstorm_dir
                .join("ideas")
                .join(format!("{}.json", new_session_id));
            
            fs::copy(&source_ideas_file, &fork_ideas_file)
                .map_err(|e| format!("Failed to copy ideas: {}", e))?;
        }
    }
    
    // Copy clusters if requested
    if *include_data.get("clusters").unwrap_or(&true) {
        let source_clusters_file = brainstorm_dir
            .join("clusters")
            .join(format!("{}.json", source_session_id));
        
        if source_clusters_file.exists() {
            let fork_clusters_file = brainstorm_dir
                .join("clusters")
                .join(format!("{}.json", new_session_id));
            
            fs::copy(&source_clusters_file, &fork_clusters_file)
                .map_err(|e| format!("Failed to copy clusters: {}", e))?;
        }
    }
    
    Ok(new_session_id)
}

// Helper function to apply transfer to a session
fn apply_transfer_to_session(
    session_id: &str,
    transfer_data: &TransferableData,
    app_handle: &tauri::AppHandle,
) -> Result<(), String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let brainstorm_dir = app_dir.join("brainstorm");
    
    match transfer_data.data_type.as_str() {
        "idea" => {
            // Add idea to target session's ideas
            let ideas_file = brainstorm_dir
                .join("ideas")
                .join(format!("{}.json", session_id));
            
            let mut ideas = if ideas_file.exists() {
                let content = fs::read_to_string(&ideas_file)
                    .map_err(|e| format!("Failed to read ideas file: {}", e))?;
                serde_json::from_str::<Vec<serde_json::Value>>(&content)
                    .unwrap_or_default()
            } else {
                Vec::new()
            };
            
            // Generate new ID for the idea
            if let Some(mut idea) = transfer_data.data.clone().as_object().cloned() {
                idea.insert("id".to_string(), serde_json::Value::String(Uuid::new_v4().to_string()));
                idea.insert("transferred_from".to_string(), serde_json::Value::String(transfer_data.source_session_id.clone()));
                ideas.push(serde_json::Value::Object(idea));
            }
            
            let ideas_json = serde_json::to_string_pretty(&ideas)
                .map_err(|e| format!("Failed to serialize ideas: {}", e))?;
            
            fs::create_dir_all(ideas_file.parent().unwrap())
                .map_err(|e| format!("Failed to create ideas directory: {}", e))?;
            
            fs::write(&ideas_file, ideas_json)
                .map_err(|e| format!("Failed to write ideas file: {}", e))?;
        }
        "cluster" => {
            // Add cluster and its ideas to target session
            // This would be similar to the idea case but handling both cluster and ideas
        }
        "selection" => {
            // Add multiple items to target session
        }
        _ => {
            return Err(format!("Unknown transfer data type: {}", transfer_data.data_type));
        }
    }
    
    Ok(())
}