#[allow(unused_imports)]
use super::registry::ProcessRegistry as OldRegistry;
use super::registry_optimized::ProcessRegistry as NewRegistry;
use std::sync::Arc;

/// Migration utilities for transitioning from old to new registry
#[allow(dead_code)]
pub struct RegistryMigration;

#[allow(dead_code)]
impl RegistryMigration {
    /// Create a new optimized registry with same interface as old one
    pub fn create_optimized_registry() -> Arc<NewRegistry> {
        Arc::new(NewRegistry::new())
    }

    /// Adapter to make new registry compatible with old interface
    pub fn adapt_new_to_old(new_registry: Arc<NewRegistry>) -> RegistryAdapter {
        RegistryAdapter { inner: new_registry }
    }
}

/// Adapter that provides old registry interface using new implementation
#[allow(dead_code)]
pub struct RegistryAdapter {
    inner: Arc<NewRegistry>,
}

#[allow(dead_code)]
impl RegistryAdapter {
    /// All the methods from old registry mapped to new implementation
    pub fn register_process(
        &self,
        run_id: i64,
        agent_id: i64,
        agent_name: String,
        pid: u32,
        project_path: String,
        task: String,
        model: String,
        child: tokio::process::Child,
    ) -> Result<(), String> {
        self.inner.register_process(
            run_id,
            agent_id,
            agent_name,
            pid,
            project_path,
            task,
            model,
            child,
        )
    }

    pub fn register_claude_session(
        &self,
        session_id: String,
        pid: u32,
        project_path: String,
        task: String,
        model: String,
    ) -> Result<i64, String> {
        self.inner.register_claude_session(
            session_id,
            pid,
            project_path,
            task,
            model,
        )
    }

    pub fn get_running_claude_sessions(&self) -> Result<Vec<super::registry::ProcessInfo>, String> {
        // Convert new ProcessInfo to old format
        Ok(self.inner.get_running_claude_sessions()
            .into_iter()
            .map(|info| super::registry::ProcessInfo {
                run_id: info.run_id,
                process_type: match info.process_type {
                    super::registry_optimized::ProcessType::AgentRun { agent_id, agent_name } => 
                        super::registry::ProcessType::AgentRun { agent_id, agent_name },
                    super::registry_optimized::ProcessType::ClaudeSession { session_id } =>
                        super::registry::ProcessType::ClaudeSession { session_id },
                    super::registry_optimized::ProcessType::BrainstormSession { session_id } =>
                        super::registry::ProcessType::BrainstormSession { session_id },
                },
                pid: info.pid,
                started_at: info.started_at,
                project_path: info.project_path,
                task: info.task,
                model: info.model,
            })
            .collect())
    }

    pub fn generate_id(&self) -> Result<i64, String> {
        Ok(self.inner.generate_id())
    }

    pub fn unregister_process(&self, run_id: i64) -> Result<(), String> {
        self.inner.unregister_process(run_id)
    }

    pub async fn kill_process(&self, run_id: i64) -> Result<bool, String> {
        self.inner.kill_process(run_id).await
    }

    pub fn append_live_output(&self, run_id: i64, output: &str) -> Result<(), String> {
        // Use tokio::spawn to make it non-blocking
        let inner = self.inner.clone();
        let output = output.to_string();
        tokio::spawn(async move {
            let _ = inner.append_live_output(run_id, &output).await;
        });
        Ok(())
    }

    pub fn get_live_output(&self, run_id: i64) -> Result<String, String> {
        // Use blocking read for compatibility
        tokio::task::block_in_place(|| {
            tokio::runtime::Handle::current().block_on(async {
                self.inner.get_live_output(run_id, true).await
            })
        })
    }
}