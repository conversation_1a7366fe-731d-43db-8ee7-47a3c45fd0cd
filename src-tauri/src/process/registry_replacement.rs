/// Drop-in replacement for the original ProcessRegistry with identical API
/// but using the optimized implementation internally
use chrono::{DateTime, Utc};
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::atomic::{AtomicI64, Ordering};
use std::sync::{Arc, Mutex};
use tokio::process::Child;

/// Type of process being tracked
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum ProcessType {
    AgentRun {
        agent_id: i64,
        agent_name: String,
    },
    ClaudeSession {
        session_id: String,
    },
    BrainstormSession {
        session_id: String,
    },
}

/// Information about a running agent process
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ProcessInfo {
    pub run_id: i64,
    pub process_type: ProcessType,
    pub pid: u32,
    pub started_at: DateTime<Utc>,
    pub project_path: String,
    pub task: String,
    pub model: String,
}

/// Information about a running process with handle
#[allow(dead_code)]
pub struct ProcessHandle {
    pub info: ProcessInfo,
    pub child: Arc<Mutex<Option<Child>>>,
    pub live_output: Arc<Mutex<String>>,
}

/// Registry for tracking active agent processes - optimized implementation
pub struct ProcessRegistry {
    // Use DashMap for better concurrent performance
    processes: Arc<DashMap<i64, ProcessHandle>>,
    // Atomic counter instead of mutex
    next_id: AtomicI64,
    // Session indexes for O(1) lookups
    claude_sessions: Arc<DashMap<String, i64>>,
}

impl ProcessRegistry {
    pub fn new() -> Self {
        Self {
            processes: Arc::new(DashMap::with_capacity_and_shard_amount(128, 16)),
            next_id: AtomicI64::new(1000000),
            claude_sessions: Arc::new(DashMap::new()),
        }
    }

    /// Generate a unique ID for non-agent processes
    pub fn generate_id(&self) -> Result<i64, String> {
        Ok(self.next_id.fetch_add(1, Ordering::Relaxed))
    }

    /// Register a new running agent process
    pub fn register_process(
        &self,
        run_id: i64,
        agent_id: i64,
        agent_name: String,
        pid: u32,
        project_path: String,
        task: String,
        model: String,
        child: Child,
    ) -> Result<(), String> {
        let process_info = ProcessInfo {
            run_id,
            process_type: ProcessType::AgentRun { agent_id, agent_name },
            pid,
            started_at: Utc::now(),
            project_path,
            task,
            model,
        };

        self.register_process_internal(run_id, process_info, child)
    }

    /// Register a new Claude session (without child process - handled separately)
    pub fn register_claude_session(
        &self,
        session_id: String,
        pid: u32,
        project_path: String,
        task: String,
        model: String,
    ) -> Result<i64, String> {
        let run_id = self.generate_id()?;
        
        let process_info = ProcessInfo {
            run_id,
            process_type: ProcessType::ClaudeSession { session_id: session_id.clone() },
            pid,
            started_at: Utc::now(),
            project_path,
            task,
            model,
        };

        let process_handle = ProcessHandle {
            info: process_info,
            child: Arc::new(Mutex::new(None)),
            live_output: Arc::new(Mutex::new(String::new())),
        };

        self.processes.insert(run_id, process_handle);
        self.claude_sessions.insert(session_id, run_id);
        
        Ok(run_id)
    }

    /// Register a new Brainstorm session (without child process - handled separately)
    pub fn register_brainstorm_session(
        &self,
        session_id: String,
        pid: u32,
        project_path: String,
        task: String,
        model: String,
    ) -> Result<i64, String> {
        let run_id = self.generate_id()?;
        
        let process_info = ProcessInfo {
            run_id,
            process_type: ProcessType::BrainstormSession { session_id },
            pid,
            started_at: Utc::now(),
            project_path,
            task,
            model,
        };

        let process_handle = ProcessHandle {
            info: process_info,
            child: Arc::new(Mutex::new(None)),
            live_output: Arc::new(Mutex::new(String::new())),
        };

        self.processes.insert(run_id, process_handle);
        Ok(run_id)
    }

    /// Internal method to register any process
    fn register_process_internal(
        &self,
        run_id: i64,
        process_info: ProcessInfo,
        child: Child,
    ) -> Result<(), String> {
        let process_handle = ProcessHandle {
            info: process_info,
            child: Arc::new(Mutex::new(Some(child))),
            live_output: Arc::new(Mutex::new(String::new())),
        };

        self.processes.insert(run_id, process_handle);
        Ok(())
    }

    /// Get all running Claude sessions - optimized with index
    pub fn get_running_claude_sessions(&self) -> Result<Vec<ProcessInfo>, String> {
        Ok(self.claude_sessions
            .iter()
            .filter_map(|entry| {
                self.processes
                    .get(entry.value())
                    .map(|handle| handle.info.clone())
            })
            .collect())
    }

    /// Get a specific Claude session by session ID - O(1) lookup
    pub fn get_claude_session_by_id(&self, session_id: &str) -> Result<Option<ProcessInfo>, String> {
        Ok(self.claude_sessions
            .get(session_id)
            .and_then(|run_id| self.processes.get(&*run_id))
            .map(|handle| handle.info.clone()))
    }

    /// Unregister a process (called when it completes)
    #[allow(dead_code)]
    pub fn unregister_process(&self, run_id: i64) -> Result<(), String> {
        if let Some((_, handle)) = self.processes.remove(&run_id) {
            // Clean up session index if it's a Claude session
            if let ProcessType::ClaudeSession { ref session_id } = handle.info.process_type {
                self.claude_sessions.remove(session_id);
            }
        }
        Ok(())
    }

    /// Get all running processes
    #[allow(dead_code)]
    pub fn get_running_processes(&self) -> Result<Vec<ProcessInfo>, String> {
        Ok(self.processes
            .iter()
            .map(|entry| entry.value().info.clone())
            .collect())
    }

    /// Get all running agent processes
    pub fn get_running_agent_processes(&self) -> Result<Vec<ProcessInfo>, String> {
        Ok(self.processes
            .iter()
            .filter_map(|entry| {
                match &entry.value().info.process_type {
                    ProcessType::AgentRun { .. } => Some(entry.value().info.clone()),
                    _ => None,
                }
            })
            .collect())
    }

    /// Get a specific running process
    #[allow(dead_code)]
    pub fn get_process(&self, run_id: i64) -> Result<Option<ProcessInfo>, String> {
        Ok(self.processes.get(&run_id).map(|handle| handle.info.clone()))
    }

    /// Kill a running process with proper cleanup
    pub async fn kill_process(&self, run_id: i64) -> Result<bool, String> {
        use log::{error, info, warn};

        let handle = match self.processes.get(&run_id) {
            Some(h) => h.clone(),
            None => {
                warn!("Process {} not found in registry", run_id);
                return Ok(false);
            }
        };

        let pid = handle.info.pid;
        let child_arc = handle.child.clone();
        drop(handle); // Release the DashMap read lock

        info!("Attempting graceful shutdown of process {} (PID: {})", run_id, pid);

        // Send kill signal to the process
        let kill_sent = {
            let mut child_guard = child_arc.lock().map_err(|e| e.to_string())?;
            if let Some(child) = child_guard.as_mut() {
                match child.start_kill() {
                    Ok(_) => {
                        info!("Successfully sent kill signal to process {}", run_id);
                        true
                    }
                    Err(e) => {
                        error!("Failed to send kill signal to process {}: {}", run_id, e);
                        false
                    }
                }
            } else {
                warn!("No child handle available for process {} (PID: {})", run_id, pid);
                false
            }
        };

        // If direct kill didn't work, try system command as fallback
        if !kill_sent {
            info!("Attempting fallback kill for process {} (PID: {})", run_id, pid);
            self.kill_process_by_pid(run_id, pid)?;
        }

        // Wait for the process to exit (with timeout)
        let wait_result = tokio::time::timeout(tokio::time::Duration::from_secs(5), async {
            loop {
                let status = {
                    let mut child_guard = child_arc.lock().map_err(|e| e.to_string())?;
                    if let Some(child) = child_guard.as_mut() {
                        match child.try_wait() {
                            Ok(Some(status)) => {
                                info!("Process {} exited with status: {:?}", run_id, status);
                                *child_guard = None;
                                Some(Ok::<(), String>(()))
                            }
                            Ok(None) => None,
                            Err(e) => Some(Err(e.to_string()))
                        }
                    } else {
                        Some(Ok(()))
                    }
                };

                match status {
                    Some(result) => return result,
                    None => tokio::time::sleep(tokio::time::Duration::from_millis(100)).await,
                }
            }
        })
        .await;

        if let Err(_) = wait_result {
            warn!("Process {} didn't exit within 5 seconds after kill", run_id);
            let _ = self.kill_process_by_pid(run_id, pid);
        }

        // Remove from registry after killing
        self.unregister_process(run_id)?;
        Ok(true)
    }

    /// Kill a process by PID using system commands (fallback method)
    pub fn kill_process_by_pid(&self, run_id: i64, pid: u32) -> Result<bool, String> {
        use log::{error, info, warn};

        info!("Attempting to kill process {} by PID {}", run_id, pid);

        let kill_result = if cfg!(target_os = "windows") {
            std::process::Command::new("taskkill")
                .args(["/F", "/PID", &pid.to_string()])
                .output()
        } else {
            std::process::Command::new("kill")
                .args(["-TERM", &pid.to_string()])
                .output()
        };

        match kill_result {
            Ok(output) => {
                if output.status.success() {
                    info!("Successfully killed process with PID {}", pid);
                    self.unregister_process(run_id)?;
                    Ok(true)
                } else {
                    let error_msg = String::from_utf8_lossy(&output.stderr);
                    warn!("Failed to kill PID {}: {}", pid, error_msg);
                    Ok(false)
                }
            }
            Err(e) => {
                error!("Failed to execute kill command for PID {}: {}", pid, e);
                Err(format!("Failed to execute kill command: {}", e))
            }
        }
    }

    /// Check if a process is still running by trying to get its status
    #[allow(dead_code)]
    pub async fn is_process_running(&self, run_id: i64) -> Result<bool, String> {
        if let Some(handle) = self.processes.get(&run_id) {
            let child_arc = handle.child.clone();
            drop(handle); // Release DashMap lock

            let mut child_guard = child_arc.lock().map_err(|e| e.to_string())?;
            if let Some(ref mut child) = child_guard.as_mut() {
                match child.try_wait() {
                    Ok(Some(_)) => {
                        *child_guard = None;
                        Ok(false)
                    }
                    Ok(None) => Ok(true),
                    Err(_) => {
                        *child_guard = None;
                        Ok(false)
                    }
                }
            } else {
                Ok(false)
            }
        } else {
            Ok(false)
        }
    }

    /// Append to live output for a process
    pub fn append_live_output(&self, run_id: i64, output: &str) -> Result<(), String> {
        if let Some(handle) = self.processes.get(&run_id) {
            if let Ok(mut live_output) = handle.live_output.lock() {
                live_output.push_str(output);
                live_output.push('\n');
            }
        }
        Ok(())
    }

    /// Get live output for a process
    pub fn get_live_output(&self, run_id: i64) -> Result<String, String> {
        if let Some(handle) = self.processes.get(&run_id) {
            if let Ok(live_output) = handle.live_output.lock() {
                return Ok(live_output.clone());
            }
        }
        Ok(String::new())
    }

    /// Cleanup finished processes
    #[allow(dead_code)]
    pub async fn cleanup_finished_processes(&self) -> Result<Vec<i64>, String> {
        let mut finished_runs = Vec::new();

        // Collect all run_ids first to avoid holding locks
        let run_ids: Vec<i64> = self.processes.iter().map(|entry| *entry.key()).collect();

        for run_id in run_ids {
            if !self.is_process_running(run_id).await? {
                finished_runs.push(run_id);
                self.unregister_process(run_id)?;
            }
        }

        Ok(finished_runs)
    }
}

impl Default for ProcessRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// Global process registry state
pub struct ProcessRegistryState(pub Arc<ProcessRegistry>);

impl Default for ProcessRegistryState {
    fn default() -> Self {
        Self(Arc::new(ProcessRegistry::new()))
    }
}