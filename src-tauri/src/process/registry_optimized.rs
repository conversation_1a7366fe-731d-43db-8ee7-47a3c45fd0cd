#![allow(dead_code)]

use chrono::{DateTime, Utc};
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use std::sync::atomic::{AtomicI64, AtomicU64, Ordering};
use std::sync::Arc;
use tokio::process::Child;
use tokio::sync::RwLock;

/// Type of process being tracked
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum ProcessType {
    AgentRun {
        agent_id: i64,
        agent_name: String,
    },
    ClaudeSession {
        session_id: String,
    },
    BrainstormSession {
        session_id: String,
    },
}

/// Information about a running agent process
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ProcessInfo {
    pub run_id: i64,
    pub process_type: ProcessType,
    pub pid: u32,
    pub started_at: DateTime<Utc>,
    pub project_path: String,
    pub task: String,
    pub model: String,
}

/// Optimized process handle with lock-free output buffer
pub struct ProcessHandle {
    pub info: ProcessInfo,
    pub child: Option<Arc<RwLock<Option<Child>>>>,
    pub live_output: Arc<RwLock<Vec<String>>>, // Changed to vector for better performance
    pub output_version: Arc<AtomicU64>, // Version counter for lock-free reads
}

/// Performance metrics for monitoring
#[derive(Debug, Default)]
pub struct RegistryMetrics {
    pub total_operations: AtomicU64,
    pub lock_contentions: AtomicU64,
    pub operation_durations: Arc<RwLock<Vec<u128>>>,
}

/// High-performance registry using DashMap for lock-free concurrent access
pub struct ProcessRegistry {
    // DashMap provides sharded locks - much better concurrency than single mutex
    processes: Arc<DashMap<i64, Arc<ProcessHandle>>>,
    
    // Atomic counter for ID generation - no locks needed
    next_id: Arc<AtomicI64>,
    
    // Type-specific indexes for fast lookups
    claude_sessions: Arc<DashMap<String, i64>>, // session_id -> run_id
    agent_runs: Arc<DashMap<i64, Vec<i64>>>, // agent_id -> run_ids
    
    // Performance metrics
    metrics: Arc<RegistryMetrics>,
}

impl ProcessRegistry {
    pub fn new() -> Self {
        Self {
            processes: Arc::new(DashMap::with_capacity_and_shard_amount(128, 16)),
            next_id: Arc::new(AtomicI64::new(1000000)),
            claude_sessions: Arc::new(DashMap::new()),
            agent_runs: Arc::new(DashMap::new()),
            metrics: Arc::new(RegistryMetrics::default()),
        }
    }

    /// Generate a unique ID using atomic operations - no locks
    #[inline]
    pub fn generate_id(&self) -> i64 {
        self.next_id.fetch_add(1, Ordering::Relaxed)
    }

    /// Register a new running agent process - optimized version
    pub fn register_process(
        &self,
        run_id: i64,
        agent_id: i64,
        agent_name: String,
        pid: u32,
        project_path: String,
        task: String,
        model: String,
        child: Child,
    ) -> Result<(), String> {
        let start = std::time::Instant::now();
        self.metrics.total_operations.fetch_add(1, Ordering::Relaxed);

        let process_info = ProcessInfo {
            run_id,
            process_type: ProcessType::AgentRun { agent_id, agent_name },
            pid,
            started_at: Utc::now(),
            project_path,
            task,
            model,
        };

        let handle = Arc::new(ProcessHandle {
            info: process_info,
            child: Some(Arc::new(RwLock::new(Some(child)))),
            live_output: Arc::new(RwLock::new(Vec::with_capacity(1000))),
            output_version: Arc::new(AtomicU64::new(0)),
        });

        // Insert into main registry
        self.processes.insert(run_id, handle);
        
        // Update agent index
        self.agent_runs
            .entry(agent_id)
            .or_insert_with(Vec::new)
            .push(run_id);

        self.record_operation_duration(start.elapsed().as_micros());
        Ok(())
    }

    /// Register Claude session with optimized indexing
    pub fn register_claude_session(
        &self,
        session_id: String,
        pid: u32,
        project_path: String,
        task: String,
        model: String,
    ) -> Result<i64, String> {
        let start = std::time::Instant::now();
        self.metrics.total_operations.fetch_add(1, Ordering::Relaxed);
        
        let run_id = self.generate_id();
        
        let process_info = ProcessInfo {
            run_id,
            process_type: ProcessType::ClaudeSession { session_id: session_id.clone() },
            pid,
            started_at: Utc::now(),
            project_path,
            task,
            model,
        };

        let handle = Arc::new(ProcessHandle {
            info: process_info,
            child: None,
            live_output: Arc::new(RwLock::new(Vec::with_capacity(1000))),
            output_version: Arc::new(AtomicU64::new(0)),
        });

        // Insert into registries
        self.processes.insert(run_id, handle);
        self.claude_sessions.insert(session_id, run_id);

        self.record_operation_duration(start.elapsed().as_micros());
        Ok(run_id)
    }

    /// Get running Claude sessions - uses indexed lookup
    pub fn get_running_claude_sessions(&self) -> Vec<ProcessInfo> {
        let start = std::time::Instant::now();
        self.metrics.total_operations.fetch_add(1, Ordering::Relaxed);

        let results: Vec<ProcessInfo> = self.claude_sessions
            .iter()
            .filter_map(|entry| {
                self.processes
                    .get(entry.value())
                    .map(|handle| handle.info.clone())
            })
            .collect();

        self.record_operation_duration(start.elapsed().as_micros());
        results
    }

    /// Get Claude session by ID - O(1) lookup
    pub fn get_claude_session_by_id(&self, session_id: &str) -> Option<ProcessInfo> {
        self.claude_sessions
            .get(session_id)
            .and_then(|run_id| self.processes.get(&*run_id))
            .map(|handle| handle.info.clone())
    }

    /// Unregister process with index cleanup
    pub fn unregister_process(&self, run_id: i64) -> Result<(), String> {
        let start = std::time::Instant::now();
        self.metrics.total_operations.fetch_add(1, Ordering::Relaxed);

        if let Some((_, handle)) = self.processes.remove(&run_id) {
            // Clean up indexes based on process type
            match &handle.info.process_type {
                ProcessType::ClaudeSession { session_id } => {
                    self.claude_sessions.remove(session_id);
                }
                ProcessType::AgentRun { agent_id, .. } => {
                    if let Some(mut runs) = self.agent_runs.get_mut(agent_id) {
                        runs.retain(|&id| id != run_id);
                    }
                }
                ProcessType::BrainstormSession { session_id } => {
                    // Add brainstorm index cleanup if needed
                    _ = session_id;
                }
            }
        }

        self.record_operation_duration(start.elapsed().as_micros());
        Ok(())
    }

    /// Append output with lock-free versioning for readers
    pub async fn append_live_output(&self, run_id: i64, output: &str) -> Result<(), String> {
        if let Some(handle) = self.processes.get(&run_id) {
            let mut live_output = handle.live_output.write().await;
            live_output.push(output.to_string());
            
            // Update version for lock-free readers
            handle.output_version.fetch_add(1, Ordering::Release);
            
            // Limit buffer size to prevent unbounded growth
            if live_output.len() > 10000 {
                // Keep last 5000 entries
                *live_output = live_output.split_off(5000);
            }
        }
        Ok(())
    }

    /// Get live output with optional lock-free read attempt
    pub async fn get_live_output(&self, run_id: i64, try_lock_free: bool) -> Result<String, String> {
        if let Some(handle) = self.processes.get(&run_id) {
            if try_lock_free {
                // Try to get read lock without blocking
                if let Ok(output) = handle.live_output.try_read() {
                    return Ok(output.join("\n"));
                }
            }
            
            // Fall back to blocking read
            let output = handle.live_output.read().await;
            Ok(output.join("\n"))
        } else {
            Ok(String::new())
        }
    }

    /// Kill process with async operations
    pub async fn kill_process(&self, run_id: i64) -> Result<bool, String> {
        use log::{error, info, warn};

        let handle = match self.processes.get(&run_id) {
            Some(h) => h.clone(),
            None => {
                warn!("Process {} not found in registry", run_id);
                return Ok(false);
            }
        };

        info!("Attempting graceful shutdown of process {} (PID: {})", run_id, handle.info.pid);

        // Kill child process if it exists
        let kill_sent = if let Some(child_lock) = &handle.child {
            let mut child_guard = child_lock.write().await;
            if let Some(child) = child_guard.as_mut() {
                match child.start_kill() {
                    Ok(_) => {
                        info!("Successfully sent kill signal to process {}", run_id);
                        true
                    }
                    Err(e) => {
                        error!("Failed to send kill signal to process {}: {}", run_id, e);
                        false
                    }
                }
            } else {
                false
            }
        } else {
            false
        };

        // If direct kill didn't work, try system command
        if !kill_sent {
            self.kill_process_by_pid(run_id, handle.info.pid)?;
        }

        // Remove from registry
        self.unregister_process(run_id)?;
        Ok(true)
    }

    /// System-level process kill (unchanged from original)
    pub fn kill_process_by_pid(&self, run_id: i64, pid: u32) -> Result<bool, String> {
        use log::{error, info, warn};

        info!("Attempting to kill process {} by PID {}", run_id, pid);

        let kill_result = if cfg!(target_os = "windows") {
            std::process::Command::new("taskkill")
                .args(["/F", "/PID", &pid.to_string()])
                .output()
        } else {
            std::process::Command::new("kill")
                .args(["-TERM", &pid.to_string()])
                .output()
        };

        match kill_result {
            Ok(output) => {
                if output.status.success() {
                    info!("Successfully killed process with PID {}", pid);
                    Ok(true)
                } else {
                    let error_msg = String::from_utf8_lossy(&output.stderr);
                    warn!("Failed to kill PID {}: {}", pid, error_msg);
                    Ok(false)
                }
            }
            Err(e) => {
                error!("Failed to execute kill command for PID {}: {}", pid, e);
                Err(format!("Failed to execute kill command: {}", e))
            }
        }
    }

    /// Get performance metrics
    pub async fn get_metrics(&self) -> (u64, u64, f64) {
        let total_ops = self.metrics.total_operations.load(Ordering::Relaxed);
        let contentions = self.metrics.lock_contentions.load(Ordering::Relaxed);
        
        let durations = self.metrics.operation_durations.read().await;
        let avg_duration = if !durations.is_empty() {
            durations.iter().sum::<u128>() as f64 / durations.len() as f64
        } else {
            0.0
        };
        
        (total_ops, contentions, avg_duration)
    }

    #[inline]
    fn record_operation_duration(&self, duration: u128) {
        // Use try_write to avoid blocking
        if let Ok(mut durations) = self.metrics.operation_durations.try_write() {
            durations.push(duration);
            // Keep last 1000 measurements
            if durations.len() > 1000 {
                durations.drain(0..500);
            }
        }
    }
}

impl Default for ProcessRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// Global process registry state
pub struct ProcessRegistryState(pub Arc<ProcessRegistry>);

impl Default for ProcessRegistryState {
    fn default() -> Self {
        Self(Arc::new(ProcessRegistry::new()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::test;

    #[test]
    async fn test_concurrent_operations() {
        let registry = Arc::new(ProcessRegistry::new());
        let mut handles = vec![];

        // Spawn 100 concurrent operations
        for i in 0..100 {
            let reg = registry.clone();
            let handle = tokio::spawn(async move {
                let session_id = format!("session_{}", i);
                reg.register_claude_session(
                    session_id.clone(),
                    1000 + i as u32,
                    "/test/path".to_string(),
                    "test task".to_string(),
                    "gpt-4".to_string(),
                ).unwrap();
            });
            handles.push(handle);
        }

        // Wait for all operations
        for handle in handles {
            handle.await.unwrap();
        }

        // Verify all sessions were registered
        let sessions = registry.get_running_claude_sessions();
        assert_eq!(sessions.len(), 100);
    }

    #[test]
    async fn test_performance_metrics() {
        let registry = ProcessRegistry::new();
        
        // Register some sessions
        for i in 0..10 {
            registry.register_claude_session(
                format!("session_{}", i),
                1000 + i,
                "/test".to_string(),
                "task".to_string(),
                "model".to_string(),
            ).unwrap();
        }

        let (total_ops, contentions, avg_duration) = registry.get_metrics().await;
        assert_eq!(total_ops, 10);
        assert_eq!(contentions, 0); // Should have no contentions with DashMap
        assert!(avg_duration > 0.0);
    }
}