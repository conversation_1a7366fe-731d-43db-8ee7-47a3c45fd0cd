use std::sync::Arc;
use std::time::Instant;
use tokio::sync::<PERSON><PERSON>;

/// Benchmark results for process registry operations
#[derive(Debug)]
pub struct BenchmarkResults {
    pub operation: String,
    pub old_impl_ms: f64,
    pub new_impl_ms: f64,
    pub improvement_percent: f64,
    pub operations_per_second_old: f64,
    pub operations_per_second_new: f64,
}

/// Benchmark runner for comparing old vs new registry implementations
pub struct RegistryBenchmark;

impl RegistryBenchmark {
    /// Run concurrent registration benchmark
    pub async fn benchmark_concurrent_registrations(num_threads: usize, ops_per_thread: usize) -> BenchmarkResults {
        // Benchmark old implementation
        let old_registry = Arc::new(super::registry::ProcessRegistry::new());
        let old_time = Self::run_registration_benchmark(old_registry, num_threads, ops_per_thread).await;

        // Benchmark new implementation
        let new_registry = Arc::new(super::registry_optimized::ProcessRegistry::new());
        let new_time = Self::run_registration_benchmark_new(new_registry, num_threads, ops_per_thread).await;

        let total_ops = (num_threads * ops_per_thread) as f64;
        let old_ops_per_sec = total_ops / (old_time / 1000.0);
        let new_ops_per_sec = total_ops / (new_time / 1000.0);
        let improvement = ((old_time - new_time) / old_time) * 100.0;

        BenchmarkResults {
            operation: format!("Concurrent registrations ({} threads, {} ops each)", num_threads, ops_per_thread),
            old_impl_ms: old_time,
            new_impl_ms: new_time,
            improvement_percent: improvement,
            operations_per_second_old: old_ops_per_sec,
            operations_per_second_new: new_ops_per_sec,
        }
    }

    /// Run lookup benchmark
    pub async fn benchmark_lookups(num_sessions: usize, num_lookups: usize) -> BenchmarkResults {
        // Setup old registry with sessions
        let old_registry = Arc::new(super::registry::ProcessRegistry::new());
        for i in 0..num_sessions {
            old_registry.register_claude_session(
                format!("session_{}", i),
                1000 + i as u32,
                "/test".to_string(),
                "task".to_string(),
                "model".to_string(),
            ).unwrap();
        }

        // Setup new registry with sessions
        let new_registry = Arc::new(super::registry_optimized::ProcessRegistry::new());
        for i in 0..num_sessions {
            new_registry.register_claude_session(
                format!("session_{}", i),
                1000 + i as u32,
                "/test".to_string(),
                "task".to_string(),
                "model".to_string(),
            ).unwrap();
        }

        // Benchmark old lookups
        let start = Instant::now();
        for _ in 0..num_lookups {
            let _ = old_registry.get_running_claude_sessions();
        }
        let old_time = start.elapsed().as_millis() as f64;

        // Benchmark new lookups
        let start = Instant::now();
        for _ in 0..num_lookups {
            let _ = new_registry.get_running_claude_sessions();
        }
        let new_time = start.elapsed().as_millis() as f64;

        let old_ops_per_sec = num_lookups as f64 / (old_time / 1000.0);
        let new_ops_per_sec = num_lookups as f64 / (new_time / 1000.0);
        let improvement = ((old_time - new_time) / old_time) * 100.0;

        BenchmarkResults {
            operation: format!("Session lookups ({} sessions, {} lookups)", num_sessions, num_lookups),
            old_impl_ms: old_time,
            new_impl_ms: new_time,
            improvement_percent: improvement,
            operations_per_second_old: old_ops_per_sec,
            operations_per_second_new: new_ops_per_sec,
        }
    }

    /// Helper to run registration benchmark on old registry
    async fn run_registration_benchmark(
        registry: Arc<super::registry::ProcessRegistry>,
        num_threads: usize,
        ops_per_thread: usize,
    ) -> f64 {
        let barrier = Arc::new(Barrier::new(num_threads));
        let start = Instant::now();
        let mut handles = vec![];

        for thread_id in 0..num_threads {
            let reg = registry.clone();
            let barrier = barrier.clone();
            let handle = tokio::spawn(async move {
                barrier.wait().await;
                for i in 0..ops_per_thread {
                    let session_id = format!("session_{}_{}", thread_id, i);
                    let _ = reg.register_claude_session(
                        session_id,
                        1000 + (thread_id * ops_per_thread + i) as u32,
                        "/test/path".to_string(),
                        "test task".to_string(),
                        "gpt-4".to_string(),
                    );
                }
            });
            handles.push(handle);
        }

        for handle in handles {
            handle.await.unwrap();
        }

        start.elapsed().as_millis() as f64
    }

    /// Helper to run registration benchmark on new registry
    async fn run_registration_benchmark_new(
        registry: Arc<super::registry_optimized::ProcessRegistry>,
        num_threads: usize,
        ops_per_thread: usize,
    ) -> f64 {
        let barrier = Arc::new(Barrier::new(num_threads));
        let start = Instant::now();
        let mut handles = vec![];

        for thread_id in 0..num_threads {
            let reg = registry.clone();
            let barrier = barrier.clone();
            let handle = tokio::spawn(async move {
                barrier.wait().await;
                for i in 0..ops_per_thread {
                    let session_id = format!("session_{}_{}", thread_id, i);
                    let _ = reg.register_claude_session(
                        session_id,
                        1000 + (thread_id * ops_per_thread + i) as u32,
                        "/test/path".to_string(),
                        "test task".to_string(),
                        "gpt-4".to_string(),
                    );
                }
            });
            handles.push(handle);
        }

        for handle in handles {
            handle.await.unwrap();
        }

        start.elapsed().as_millis() as f64
    }

    /// Run all benchmarks and print results
    pub async fn run_all_benchmarks() {
        println!("\n=== Process Registry Performance Benchmarks ===\n");

        // Test 1: Concurrent registrations
        let bench1 = Self::benchmark_concurrent_registrations(10, 100).await;
        Self::print_result(&bench1);

        // Test 2: High contention registrations
        let bench2 = Self::benchmark_concurrent_registrations(50, 200).await;
        Self::print_result(&bench2);

        // Test 3: Session lookups
        let bench3 = Self::benchmark_lookups(1000, 10000).await;
        Self::print_result(&bench3);

        // Test 4: Large registry lookups
        let bench4 = Self::benchmark_lookups(10000, 1000).await;
        Self::print_result(&bench4);

        println!("\n=== Summary ===");
        println!("Average improvement: {:.1}%", 
            (bench1.improvement_percent + bench2.improvement_percent + 
             bench3.improvement_percent + bench4.improvement_percent) / 4.0);
    }

    fn print_result(result: &BenchmarkResults) {
        println!("\n{}", result.operation);
        println!("  Old implementation: {:.2}ms ({:.0} ops/sec)", 
            result.old_impl_ms, result.operations_per_second_old);
        println!("  New implementation: {:.2}ms ({:.0} ops/sec)", 
            result.new_impl_ms, result.operations_per_second_new);
        println!("  Improvement: {:.1}% faster", result.improvement_percent);
        println!("  Speedup: {:.1}x", result.old_impl_ms / result.new_impl_ms);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_benchmarks() {
        RegistryBenchmark::run_all_benchmarks().await;
    }
}