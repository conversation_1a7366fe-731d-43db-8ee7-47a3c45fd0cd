// Comprehensive regression tests for <PERSON>
// Ensures existing functionality remains intact after security and performance optimizations

use std::sync::Arc;
use std::path::PathBuf;
use tempfile::TempDir;
use serde_json::json;

use claudia_lib::process::registry::{ProcessRegistry, ProcessType};
use claudia_lib::commands::claude::{start_claude_session, ClaudeConfig};
use claudia_lib::commands::agents::{run_agent, list_agents, get_agent_output};
use claudia_lib::commands::storage::{get_storage_path, save_project_data, load_project_data};
use claudia_lib::commands::mcp::{list_mcp_servers, get_mcp_server_config};
use claudia_lib::commands::slash_commands::{get_available_commands, execute_slash_command};

#[cfg(test)]
mod core_functionality_tests {
    use super::*;

    /// Test basic process registry functionality
    #[tokio::test]
    async fn test_process_registry_basic_operations() {
        let registry = ProcessRegistry::new();
        
        // Test ID generation
        let id1 = registry.generate_id().unwrap();
        let id2 = registry.generate_id().unwrap();
        assert_ne!(id1, id2, "IDs should be unique");
        assert!(id2 > id1, "IDs should be sequential");
        
        // Test process registration
        let run_id = 12345;
        registry.register_process(
            run_id,
            1,
            "test-agent".to_string(),
            5678,
            "/test/path".to_string(),
            "test task".to_string(),
            "claude-3-opus".to_string(),
            tokio::process::Command::new("echo").arg("test").spawn().unwrap(),
        ).unwrap();
        
        // Test listing
        let processes = registry.list_processes();
        assert_eq!(processes.len(), 1);
        assert_eq!(processes[0].run_id, run_id);
        assert_eq!(processes[0].process_type, ProcessType::AgentRun { 
            agent_id: 1, 
            agent_name: "test-agent".to_string() 
        });
        
        // Test output management
        registry.append_output(run_id, "Line 1\n").await.unwrap();
        registry.append_output(run_id, "Line 2\n").await.unwrap();
        
        let output = registry.get_output(run_id).await.unwrap();
        assert!(output.contains("Line 1"));
        assert!(output.contains("Line 2"));
        
        // Test clearing output
        registry.clear_output(run_id).await.unwrap();
        let cleared_output = registry.get_output(run_id).await.unwrap();
        assert_eq!(cleared_output, "");
        
        // Test process removal
        registry.remove_process(run_id).unwrap();
        assert_eq!(registry.list_processes().len(), 0);
        
        // Test error handling
        assert!(registry.get_output(run_id).await.is_err());
        assert!(registry.remove_process(run_id).is_err());
    }

    /// Test Claude session management
    #[tokio::test]
    async fn test_claude_session_management() {
        let registry = ProcessRegistry::new();
        
        // Register multiple Claude sessions
        let sessions = vec![
            ("session-1", "/project1", "Task 1"),
            ("session-2", "/project2", "Task 2"),
            ("session-3", "/project1", "Task 3"),
        ];
        
        for (session_id, project_path, task) in &sessions {
            registry.register_claude_session(
                session_id.to_string(),
                1234,
                project_path.to_string(),
                task.to_string(),
                "claude-3-opus".to_string(),
                None,
            ).unwrap();
        }
        
        // Test listing all sessions
        let all_sessions = registry.list_claude_sessions();
        assert_eq!(all_sessions.len(), 3);
        
        // Test getting specific session
        let (run_id, info) = registry.get_claude_session("session-2").unwrap();
        assert_eq!(info.project_path, "/project2");
        assert_eq!(info.task, "Task 2");
        
        // Test session not found
        assert!(registry.get_claude_session("non-existent").is_err());
        
        // Test removing session
        registry.remove_process(run_id).unwrap();
        assert_eq!(registry.list_claude_sessions().len(), 2);
        assert!(registry.get_claude_session("session-2").is_err());
    }

    /// Test agent run tracking
    #[tokio::test]
    async fn test_agent_run_tracking() {
        let registry = ProcessRegistry::new();
        
        // Register multiple agent runs
        for i in 0..5 {
            registry.register_process(
                1000 + i,
                i,
                format!("agent-{}", i),
                2000 + i as u32,
                format!("/project{}", i),
                format!("Task {}", i),
                "claude-3-haiku".to_string(),
                tokio::process::Command::new("echo").spawn().unwrap(),
            ).unwrap();
        }
        
        // Test filtering by agent
        let agent_1_runs: Vec<_> = registry.list_processes()
            .into_iter()
            .filter(|p| match &p.process_type {
                ProcessType::AgentRun { agent_id, .. } => *agent_id == 1,
                _ => false,
            })
            .collect();
        
        assert_eq!(agent_1_runs.len(), 1);
        assert_eq!(agent_1_runs[0].run_id, 1001);
        
        // Test getting all agent runs
        let all_runs = registry.list_processes();
        assert_eq!(all_runs.len(), 5);
        
        // Test concurrent access
        let registry = Arc::new(registry);
        let mut handles = vec![];
        
        for i in 0..10 {
            let reg = registry.clone();
            let handle = tokio::spawn(async move {
                let processes = reg.list_processes();
                processes.len()
            });
            handles.push(handle);
        }
        
        for handle in handles {
            let count = handle.await.unwrap();
            assert_eq!(count, 5);
        }
    }

    /// Test storage functionality
    #[test]
    fn test_storage_operations() {
        let temp_dir = TempDir::new().unwrap();
        std::env::set_var("HOME", temp_dir.path());
        
        // Test storage path creation
        let storage_path = get_storage_path().unwrap();
        assert!(storage_path.exists());
        assert!(storage_path.is_dir());
        
        // Test saving project data
        let project_data = json!({
            "name": "Test Project",
            "path": "/test/project",
            "settings": {
                "theme": "dark",
                "auto_save": true
            }
        });
        
        save_project_data("test-project", &project_data).unwrap();
        
        // Test loading project data
        let loaded_data = load_project_data("test-project").unwrap();
        assert_eq!(loaded_data["name"], "Test Project");
        assert_eq!(loaded_data["settings"]["theme"], "dark");
        
        // Test updating project data
        let updated_data = json!({
            "name": "Test Project Updated",
            "path": "/test/project",
            "settings": {
                "theme": "light",
                "auto_save": false
            }
        });
        
        save_project_data("test-project", &updated_data).unwrap();
        let reloaded_data = load_project_data("test-project").unwrap();
        assert_eq!(reloaded_data["name"], "Test Project Updated");
        assert_eq!(reloaded_data["settings"]["theme"], "light");
        
        // Test non-existent project
        assert!(load_project_data("non-existent").is_err());
    }

    /// Test tab management functionality
    #[tokio::test]
    async fn test_tab_management() {
        let registry = Arc::new(ProcessRegistry::new());
        
        // Simulate multiple tabs with different content types
        let mut tab_sessions = vec![];
        
        // Claude session tab
        let session_id = "claude-tab-1";
        registry.register_claude_session(
            session_id.to_string(),
            3000,
            "/project/claude".to_string(),
            "Claude task".to_string(),
            "claude-3-opus".to_string(),
            None,
        ).unwrap();
        tab_sessions.push((session_id, "claude"));
        
        // Agent run tab
        let run_id = 2000;
        registry.register_process(
            run_id,
            1,
            "agent-tab".to_string(),
            3001,
            "/project/agent".to_string(),
            "Agent task".to_string(),
            "claude-3-haiku".to_string(),
            tokio::process::Command::new("echo").spawn().unwrap(),
        ).unwrap();
        tab_sessions.push((&run_id.to_string(), "agent"));
        
        // Test concurrent tab operations
        let mut handles = vec![];
        
        for (id, tab_type) in tab_sessions {
            let reg = registry.clone();
            let tab_id = id.to_string();
            
            let handle = tokio::spawn(async move {
                // Simulate tab operations
                match tab_type {
                    "claude" => {
                        let (run_id, _) = reg.get_claude_session(&tab_id).unwrap();
                        reg.append_output(run_id, "Claude output\n").await.unwrap();
                        reg.get_output(run_id).await.unwrap()
                    }
                    "agent" => {
                        let run_id: i64 = tab_id.parse().unwrap();
                        reg.append_output(run_id, "Agent output\n").await.unwrap();
                        reg.get_output(run_id).await.unwrap()
                    }
                    _ => String::new()
                }
            });
            
            handles.push(handle);
        }
        
        // Verify all tabs work correctly
        for handle in handles {
            let output = handle.await.unwrap();
            assert!(!output.is_empty());
        }
    }

    /// Test error boundary scenarios
    #[tokio::test]
    async fn test_error_handling_and_recovery() {
        let registry = ProcessRegistry::new();
        
        // Test handling of invalid process IDs
        assert!(registry.get_output(99999).await.is_err());
        assert!(registry.remove_process(99999).is_err());
        assert!(registry.append_output(99999, "test").await.is_err());
        
        // Test handling of invalid session IDs
        assert!(registry.get_claude_session("invalid-session").is_err());
        
        // Test recovery after errors
        let valid_id = registry.generate_id().unwrap();
        registry.register_process(
            valid_id,
            1,
            "recovery-test".to_string(),
            4000,
            "/test".to_string(),
            "test".to_string(),
            "claude-3-opus".to_string(),
            tokio::process::Command::new("echo").spawn().unwrap(),
        ).unwrap();
        
        // Should work after previous errors
        assert!(registry.get_output(valid_id).await.is_ok());
        assert_eq!(registry.list_processes().len(), 1);
    }

    /// Test MCP server functionality
    #[test]
    fn test_mcp_server_operations() {
        // Test listing MCP servers
        let servers = list_mcp_servers().unwrap_or_else(|_| vec![]);
        
        // Test getting server config (if any servers exist)
        if !servers.is_empty() {
            let server_name = &servers[0];
            let config = get_mcp_server_config(server_name);
            assert!(config.is_ok() || config.is_err()); // Just ensure it doesn't panic
        }
        
        // Test with non-existent server
        assert!(get_mcp_server_config("non-existent-server").is_err());
    }

    /// Test slash command functionality
    #[tokio::test]
    async fn test_slash_commands() {
        // Test getting available commands
        let commands = get_available_commands();
        assert!(!commands.is_empty());
        
        // Common commands that should exist
        let expected_commands = vec!["help", "clear", "reset"];
        for cmd in expected_commands {
            assert!(
                commands.iter().any(|c| c.name == cmd),
                "Command '{}' should be available", cmd
            );
        }
        
        // Test command execution
        let result = execute_slash_command("help", vec![]).await;
        assert!(result.is_ok());
        
        // Test invalid command
        let invalid_result = execute_slash_command("invalid-command-xyz", vec![]).await;
        assert!(invalid_result.is_err());
    }

    /// Test UI state persistence
    #[test]
    fn test_ui_state_persistence() {
        let temp_dir = TempDir::new().unwrap();
        std::env::set_var("HOME", temp_dir.path());
        
        // Save UI state
        let ui_state = json!({
            "layout": {
                "sidebar_width": 250,
                "main_panel_split": 0.7
            },
            "preferences": {
                "theme": "dark",
                "font_size": 14,
                "auto_save": true
            },
            "recent_projects": [
                "/project1",
                "/project2"
            ]
        });
        
        save_project_data("ui-state", &ui_state).unwrap();
        
        // Load and verify
        let loaded_state = load_project_data("ui-state").unwrap();
        assert_eq!(loaded_state["layout"]["sidebar_width"], 250);
        assert_eq!(loaded_state["preferences"]["theme"], "dark");
        assert_eq!(loaded_state["recent_projects"].as_array().unwrap().len(), 2);
    }

    /// Test cross-platform compatibility
    #[test]
    fn test_cross_platform_paths() {
        let test_paths = vec![
            "/unix/style/path",
            "C:\\Windows\\Style\\Path",
            "relative/path",
            "./current/dir/path",
            "../parent/dir/path",
        ];
        
        for path_str in test_paths {
            let path = PathBuf::from(path_str);
            
            // Ensure path operations don't panic
            let _ = path.parent();
            let _ = path.file_name();
            let _ = path.extension();
            let _ = path.to_string_lossy();
            
            // Test path joining
            let joined = path.join("subdir");
            assert!(joined.to_string_lossy().contains("subdir"));
        }
    }

    /// Test memory management with large outputs
    #[tokio::test]
    async fn test_large_output_handling() {
        let registry = ProcessRegistry::new();
        let run_id = registry.generate_id().unwrap();
        
        registry.register_process(
            run_id,
            1,
            "memory-test".to_string(),
            5000,
            "/test".to_string(),
            "memory test".to_string(),
            "claude-3-opus".to_string(),
            tokio::process::Command::new("echo").spawn().unwrap(),
        ).unwrap();
        
        // Add large amount of output
        let chunk = "x".repeat(1000); // 1KB chunks
        for i in 0..100 {
            registry.append_output(run_id, &format!("{}: {}\n", i, chunk)).await.unwrap();
        }
        
        // Verify output is handled correctly
        let output = registry.get_output(run_id).await.unwrap();
        assert!(!output.is_empty());
        
        // Test output rotation (should keep last 10K lines based on implementation)
        for i in 100..20000 {
            registry.append_output(run_id, &format!("Line {}\n", i)).await.unwrap();
        }
        
        let final_output = registry.get_output(run_id).await.unwrap();
        let lines: Vec<&str> = final_output.lines().collect();
        assert!(lines.len() <= 10000, "Output should be capped at 10K lines");
    }

    /// Test concurrent session management
    #[tokio::test]
    async fn test_concurrent_session_operations() {
        let registry = Arc::new(ProcessRegistry::new());
        let mut handles = vec![];
        
        // Spawn 20 concurrent tasks
        for i in 0..20 {
            let reg = registry.clone();
            let handle = tokio::spawn(async move {
                // Each task creates its own session
                let session_id = format!("concurrent-session-{}", i);
                reg.register_claude_session(
                    session_id.clone(),
                    6000 + i as u32,
                    format!("/project{}", i),
                    format!("Task {}", i),
                    "claude-3-opus".to_string(),
                    None,
                ).unwrap();
                
                // Perform operations
                let (run_id, _) = reg.get_claude_session(&session_id).unwrap();
                
                for j in 0..10 {
                    reg.append_output(run_id, &format!("Output {} from session {}\n", j, i))
                        .await
                        .unwrap();
                }
                
                let output = reg.get_output(run_id).await.unwrap();
                assert!(output.contains(&format!("session {}", i)));
                
                // Clean up
                reg.remove_process(run_id).unwrap();
            });
            
            handles.push(handle);
        }
        
        // Wait for all tasks
        for handle in handles {
            handle.await.unwrap();
        }
        
        // Verify clean state
        assert_eq!(registry.list_claude_sessions().len(), 0);
    }
}

/// Backward compatibility tests
#[cfg(test)]
mod backward_compatibility_tests {
    use super::*;

    /// Test that old API signatures still work
    #[tokio::test]
    async fn test_legacy_api_compatibility() {
        let registry = ProcessRegistry::new();
        
        // Test old-style process registration (if API changed)
        let run_id = 9999;
        let result = registry.register_process(
            run_id,
            1,
            "legacy-agent".to_string(),
            7000,
            "/legacy/path".to_string(),
            "legacy task".to_string(),
            "claude-3-opus".to_string(),
            tokio::process::Command::new("echo").spawn().unwrap(),
        );
        
        assert!(result.is_ok(), "Legacy API should still work");
        
        // Test old-style listing
        let processes = registry.list_processes();
        assert!(!processes.is_empty());
        
        // Clean up
        registry.remove_process(run_id).unwrap();
    }

    /// Test data format compatibility
    #[test]
    fn test_data_format_compatibility() {
        // Test that we can still read old data formats
        let old_format_json = json!({
            "version": "1.0",
            "data": {
                "key": "value"
            }
        });
        
        // Should be able to parse without errors
        assert!(old_format_json["version"].is_string());
        assert!(old_format_json["data"].is_object());
        
        // Test new format is backward compatible
        let new_format_json = json!({
            "version": "2.0",
            "data": {
                "key": "value",
                "new_field": "new_value"
            },
            "metadata": {
                "created": "2024-01-01"
            }
        });
        
        // Old code should still be able to read essential fields
        assert!(new_format_json["version"].is_string());
        assert!(new_format_json["data"]["key"].is_string());
    }
}

/// Platform-specific regression tests
#[cfg(test)]
mod platform_specific_tests {
    use super::*;

    #[test]
    #[cfg(target_os = "macos")]
    fn test_macos_specific_functionality() {
        // Test macOS-specific paths
        let home = std::env::var("HOME").unwrap_or_else(|_| "/Users/<USER>".to_string());
        let library_path = PathBuf::from(&home).join("Library/Application Support/Claudia");
        
        // Path should be valid
        assert!(!library_path.to_string_lossy().is_empty());
    }

    #[test]
    #[cfg(target_os = "windows")]
    fn test_windows_specific_functionality() {
        // Test Windows-specific paths
        let appdata = std::env::var("APPDATA").unwrap_or_else(|_| "C:\\Users\\<USER>\\AppData\\Roaming".to_string());
        let config_path = PathBuf::from(&appdata).join("Claudia");
        
        // Path should be valid
        assert!(!config_path.to_string_lossy().is_empty());
    }

    #[test]
    #[cfg(target_os = "linux")]
    fn test_linux_specific_functionality() {
        // Test Linux-specific paths
        let home = std::env::var("HOME").unwrap_or_else(|_| "/home/<USER>".to_string());
        let config_path = PathBuf::from(&home).join(".config/claudia");
        
        // Path should be valid
        assert!(!config_path.to_string_lossy().is_empty());
    }
}