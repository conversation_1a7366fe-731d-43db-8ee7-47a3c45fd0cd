// Integration tests for security and performance layers
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tokio::task::JoinSet;
use tempfile::TempDir;

use claudia_lib::security::{
    validate_command, validate_path, validate_table_name, escape_shell_arg
};
use claudia_lib::security::sanitization::*;
use claudia_lib::security::validation::*;
use claudia_lib::process::registry::ProcessRegistry;
use claudia_lib::commands::claude::{start_claude_session, ClaudeConfig};
use claudia_lib::commands::agents::{run_agent, AgentRunConfig};

/// Integration test configuration
struct IntegrationTestConfig {
    enable_security: bool,
    enable_performance_optimizations: bool,
    concurrent_operations: usize,
}

/// Test results structure
#[derive(Debug)]
struct IntegrationTestResult {
    security_violations_blocked: usize,
    performance_metrics: PerformanceMetrics,
    errors: Vec<String>,
    success: bool,
}

#[derive(Debug)]
struct PerformanceMetrics {
    avg_response_time_ms: f64,
    peak_memory_mb: f64,
    concurrent_operations_handled: usize,
    ops_per_second: f64,
}

#[cfg(test)]
mod security_performance_integration {
    use super::*;

    /// Test that security validations don't significantly impact performance
    #[tokio::test]
    async fn test_security_validation_performance_impact() {
        let registry = Arc::new(ProcessRegistry::new());
        let iterations = 10000;
        
        // Measure performance without security checks (baseline)
        let baseline_start = Instant::now();
        for i in 0..iterations {
            let run_id = registry.generate_id().unwrap();
            // Direct registration without validation
            registry.register_process(
                run_id,
                i as i64,
                format!("agent-{}", i),
                1234,
                "/test/path".to_string(),
                "test task".to_string(),
                "claude-3-opus".to_string(),
                tokio::process::Command::new("echo").spawn().unwrap(),
            ).unwrap();
        }
        let baseline_duration = baseline_start.elapsed();
        
        // Measure performance with security checks
        let security_start = Instant::now();
        for i in 0..iterations {
            // Apply security validations
            let agent_name = format!("agent-{}", i);
            let path = "/test/path";
            let task = "test task";
            let model = "claude-3-opus";
            
            // Validate inputs
            validate_identifier(&agent_name).unwrap();
            validate_path(path, None).unwrap();
            validate_safe_string(task).unwrap();
            validate_model_name(model).unwrap();
            
            let run_id = registry.generate_id().unwrap();
            registry.register_process(
                run_id,
                i as i64,
                sanitize_user_input(&agent_name),
                1234,
                path.to_string(),
                sanitize_user_input(task),
                model.to_string(),
                tokio::process::Command::new("echo").spawn().unwrap(),
            ).unwrap();
        }
        let security_duration = security_start.elapsed();
        
        // Calculate overhead
        let overhead_percent = ((security_duration.as_secs_f64() / baseline_duration.as_secs_f64()) - 1.0) * 100.0;
        
        println!("Performance Impact of Security:");
        println!("  Baseline: {:?}", baseline_duration);
        println!("  With Security: {:?}", security_duration);
        println!("  Overhead: {:.2}%", overhead_percent);
        
        // Security should add less than 20% overhead
        assert!(overhead_percent < 20.0, "Security overhead too high: {:.2}%", overhead_percent);
    }

    /// Test concurrent operations with security enabled
    #[tokio::test]
    async fn test_concurrent_secure_operations() {
        let registry = Arc::new(ProcessRegistry::new());
        let num_threads = 50;
        let ops_per_thread = 100;
        let semaphore = Arc::new(Semaphore::new(num_threads));
        let mut tasks = JoinSet::new();
        let start = Instant::now();
        
        let blocked_attempts = Arc::new(parking_lot::Mutex::new(0));
        let successful_ops = Arc::new(parking_lot::Mutex::new(0));
        
        for thread_id in 0..num_threads {
            let registry = registry.clone();
            let semaphore = semaphore.clone();
            let blocked = blocked_attempts.clone();
            let successful = successful_ops.clone();
            
            tasks.spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                
                for op_id in 0..ops_per_thread {
                    // Mix of valid and invalid operations
                    if op_id % 10 == 0 {
                        // Attempt malicious operation
                        let malicious_cmd = "rm -rf /";
                        match validate_command(malicious_cmd) {
                            Err(_) => {
                                *blocked.lock() += 1;
                            }
                            Ok(_) => panic!("Malicious command should be blocked"),
                        }
                    } else {
                        // Valid operation
                        let cmd = "list";
                        validate_command(cmd).unwrap();
                        
                        let run_id = registry.generate_id().unwrap();
                        registry.register_process(
                            run_id,
                            thread_id as i64,
                            format!("agent-{}", thread_id),
                            1234,
                            "/test/path".to_string(),
                            "test task".to_string(),
                            "claude-3-opus".to_string(),
                            tokio::process::Command::new("echo").spawn().unwrap(),
                        ).unwrap();
                        
                        *successful.lock() += 1;
                    }
                }
            });
        }
        
        while let Some(_) = tasks.join_next().await {}
        let duration = start.elapsed();
        
        let total_blocked = *blocked_attempts.lock();
        let total_successful = *successful_ops.lock();
        let total_ops = total_blocked + total_successful;
        let ops_per_second = total_ops as f64 / duration.as_secs_f64();
        
        println!("\nConcurrent Secure Operations Test:");
        println!("  Total operations: {}", total_ops);
        println!("  Successful: {}", total_successful);
        println!("  Blocked (security): {}", total_blocked);
        println!("  Duration: {:?}", duration);
        println!("  Ops/second: {:.2}", ops_per_second);
        
        // Should handle high concurrency efficiently
        assert!(ops_per_second > 10000.0, "Performance too low under security constraints");
        assert_eq!(total_blocked, num_threads * 10, "Should block exactly the malicious attempts");
    }

    /// Test path traversal prevention under load
    #[tokio::test]
    async fn test_path_security_at_scale() {
        let temp_dir = TempDir::new().unwrap();
        let base_path = temp_dir.path();
        let num_attempts = 10000;
        
        let mut blocked = 0;
        let mut allowed = 0;
        
        let start = Instant::now();
        
        for i in 0..num_attempts {
            let path = if i % 3 == 0 {
                // Malicious path
                format!("../../../etc/passwd_{}", i)
            } else {
                // Valid path
                format!("valid/path/file_{}.txt", i)
            };
            
            match validate_path(&path, Some(base_path)) {
                Ok(_) => allowed += 1,
                Err(_) => blocked += 1,
            }
        }
        
        let duration = start.elapsed();
        let validations_per_second = num_attempts as f64 / duration.as_secs_f64();
        
        println!("\nPath Security at Scale:");
        println!("  Total attempts: {}", num_attempts);
        println!("  Allowed: {}", allowed);
        println!("  Blocked: {}", blocked);
        println!("  Validations/second: {:.2}", validations_per_second);
        
        assert_eq!(blocked, num_attempts / 3, "Should block exactly 1/3 of attempts");
        assert!(validations_per_second > 100000.0, "Path validation too slow");
    }

    /// Test SQL injection prevention with database operations
    #[tokio::test]
    async fn test_sql_security_integration() {
        use sqlx::sqlite::SqlitePool;
        
        // Create in-memory database
        let pool = SqlitePool::connect("sqlite::memory:").await.unwrap();
        
        // Create test table
        sqlx::query("CREATE TABLE test_table (id INTEGER, name TEXT)")
            .execute(&pool)
            .await
            .unwrap();
        
        let test_cases = vec![
            ("valid_table", "Valid data", true),
            ("users; DROP TABLE test_table", "Malicious", false),
            ("users' OR '1'='1", "SQL Injection", false),
            ("test_table", "Another valid", true),
        ];
        
        let mut blocked = 0;
        let mut successful = 0;
        
        for (table_name, data, should_succeed) in test_cases {
            match validate_table_name(table_name) {
                Ok(_) => {
                    if should_succeed {
                        // Safe to use in query
                        let query = format!("INSERT INTO {} (id, name) VALUES (?, ?)", table_name);
                        sqlx::query(&query)
                            .bind(1)
                            .bind(data)
                            .execute(&pool)
                            .await
                            .unwrap();
                        successful += 1;
                    } else {
                        panic!("Malicious table name should have been blocked: {}", table_name);
                    }
                }
                Err(_) => {
                    if !should_succeed {
                        blocked += 1;
                    } else {
                        panic!("Valid table name was blocked: {}", table_name);
                    }
                }
            }
        }
        
        println!("\nSQL Security Integration:");
        println!("  Successful operations: {}", successful);
        println!("  Blocked attempts: {}", blocked);
        
        assert_eq!(successful, 2);
        assert_eq!(blocked, 2);
    }

    /// Test XSS prevention in output handling
    #[tokio::test]
    async fn test_xss_prevention_in_output() {
        let registry = Arc::new(ProcessRegistry::new());
        
        // Register a process
        let run_id = registry.generate_id().unwrap();
        registry.register_process(
            run_id,
            1,
            "test-agent".to_string(),
            1234,
            "/test/path".to_string(),
            "test task".to_string(),
            "claude-3-opus".to_string(),
            tokio::process::Command::new("echo").spawn().unwrap(),
        ).unwrap();
        
        // Test various XSS payloads in output
        let xss_payloads = vec![
            "<script>alert('xss')</script>",
            "<img src=x onerror=alert('xss')>",
            "<iframe src='javascript:alert()'></iframe>",
            "';alert(String.fromCharCode(88,83,83))//",
            "<svg onload=alert('xss')>",
        ];
        
        for payload in &xss_payloads {
            // Sanitize before adding to output
            let sanitized = sanitize_html(payload);
            registry.append_output(run_id, &sanitized).await.unwrap();
        }
        
        // Retrieve and verify output
        let output = registry.get_output(run_id).await.unwrap();
        
        // Ensure no script tags or event handlers remain
        assert!(!output.contains("<script"));
        assert!(!output.contains("onerror="));
        assert!(!output.contains("javascript:"));
        assert!(!output.contains("onload="));
        
        println!("\nXSS Prevention Test:");
        println!("  Tested {} payloads", xss_payloads.len());
        println!("  All payloads sanitized successfully");
    }

    /// Test complete secure command execution flow
    #[tokio::test]
    async fn test_secure_command_execution() {
        let registry = Arc::new(ProcessRegistry::new());
        
        // Simulate user input
        let user_inputs = vec![
            ("list", vec!["--format", "json"], true),
            ("rm", vec!["-rf", "/"], false),
            ("get", vec!["item; cat /etc/passwd"], true), // Args should be escaped
            ("serve", vec!["--port", "8080"], true),
        ];
        
        for (cmd, args, should_succeed) in user_inputs {
            println!("\nTesting command: {} {:?}", cmd, args);
            
            // Validate command
            match validate_command(cmd) {
                Ok(_) => {
                    if !should_succeed && cmd == "rm" {
                        panic!("Dangerous command should be blocked");
                    }
                    
                    // Validate and escape arguments
                    let safe_args: Vec<String> = args.iter()
                        .map(|arg| escape_shell_arg(arg))
                        .collect();
                    
                    // Verify no injection possible
                    for arg in &safe_args {
                        assert!(!arg.contains(';') || arg.starts_with('\''));
                        assert!(!arg.contains('|') || arg.starts_with('\''));
                        assert!(!arg.contains('&') || arg.starts_with('\''));
                    }
                    
                    println!("  Command allowed, args escaped: {:?}", safe_args);
                }
                Err(e) => {
                    if should_succeed {
                        panic!("Valid command blocked: {}", e);
                    }
                    println!("  Command blocked: {}", e);
                }
            }
        }
    }

    /// Test performance monitoring integration
    #[tokio::test]
    async fn test_performance_monitoring() {
        let registry = Arc::new(ProcessRegistry::new());
        let monitoring_duration = Duration::from_secs(5);
        let start = Instant::now();
        
        let mut operations = 0;
        let mut peak_concurrent = 0;
        let mut current_concurrent = Arc::new(parking_lot::Mutex::new(0));
        
        // Spawn multiple tasks doing operations
        let mut tasks = JoinSet::new();
        
        for i in 0..20 {
            let registry = registry.clone();
            let concurrent = current_concurrent.clone();
            
            tasks.spawn(async move {
                while start.elapsed() < monitoring_duration {
                    *concurrent.lock() += 1;
                    let current = *concurrent.lock();
                    
                    let run_id = registry.generate_id().unwrap();
                    registry.register_process(
                        run_id,
                        i,
                        format!("monitor-agent-{}", i),
                        1234,
                        "/test/path".to_string(),
                        "monitoring task".to_string(),
                        "claude-3-opus".to_string(),
                        tokio::process::Command::new("echo").spawn().unwrap(),
                    ).unwrap();
                    
                    // Simulate some work
                    tokio::time::sleep(Duration::from_millis(10)).await;
                    
                    // Update output
                    registry.append_output(run_id, "Working...\n").await.unwrap();
                    
                    *concurrent.lock() -= 1;
                    
                    current
                }
            });
        }
        
        // Monitor metrics
        let monitor_task = tokio::spawn(async move {
            let mut samples = Vec::new();
            
            while start.elapsed() < monitoring_duration {
                let current = *current_concurrent.lock();
                samples.push(current);
                peak_concurrent = peak_concurrent.max(current);
                operations += current;
                
                tokio::time::sleep(Duration::from_millis(100)).await;
            }
            
            (peak_concurrent, operations, samples)
        });
        
        // Wait for all tasks
        while let Some(_) = tasks.join_next().await {}
        let (peak, total_ops, samples) = monitor_task.await.unwrap();
        
        let avg_concurrent = samples.iter().sum::<usize>() as f64 / samples.len() as f64;
        
        println!("\nPerformance Monitoring Results:");
        println!("  Duration: {:?}", monitoring_duration);
        println!("  Peak concurrent operations: {}", peak);
        println!("  Average concurrent: {:.2}", avg_concurrent);
        println!("  Estimated total operations: {}", total_ops);
        
        // Should maintain good concurrency
        assert!(peak >= 15, "Should handle at least 15 concurrent operations");
        assert!(avg_concurrent > 10.0, "Average concurrency too low");
    }

    /// Test error recovery with security enabled
    #[tokio::test]
    async fn test_error_recovery_integration() {
        let registry = Arc::new(ProcessRegistry::new());
        let mut errors_recovered = 0;
        let mut permanent_failures = 0;
        
        for i in 0..100 {
            let simulated_error = i % 10 == 0; // 10% error rate
            
            if simulated_error {
                // Try invalid operation
                match validate_command("invalid_command") {
                    Err(_) => {
                        // Try recovery with valid command
                        match validate_command("list") {
                            Ok(_) => errors_recovered += 1,
                            Err(_) => permanent_failures += 1,
                        }
                    }
                    Ok(_) => panic!("Invalid command should fail"),
                }
            } else {
                // Normal operation
                validate_command("get").unwrap();
            }
        }
        
        println!("\nError Recovery Test:");
        println!("  Errors recovered: {}", errors_recovered);
        println!("  Permanent failures: {}", permanent_failures);
        
        assert_eq!(errors_recovered, 10, "Should recover from all errors");
        assert_eq!(permanent_failures, 0, "Should have no permanent failures");
    }

    /// Test memory safety under concurrent load
    #[tokio::test]
    async fn test_memory_safety_concurrent() {
        let registry = Arc::new(ProcessRegistry::new());
        let duration = Duration::from_secs(3);
        let start = Instant::now();
        
        // Spawn tasks that rapidly create and destroy processes
        let mut tasks = JoinSet::new();
        
        for i in 0..50 {
            let registry = registry.clone();
            
            tasks.spawn(async move {
                let mut local_ids = Vec::new();
                
                while start.elapsed() < duration {
                    // Create processes
                    for j in 0..10 {
                        let run_id = registry.generate_id().unwrap();
                        registry.register_process(
                            run_id,
                            i * 1000 + j,
                            format!("memory-test-{}-{}", i, j),
                            1234,
                            "/test/path".to_string(),
                            "memory test".to_string(),
                            "claude-3-opus".to_string(),
                            tokio::process::Command::new("echo").spawn().unwrap(),
                        ).unwrap();
                        local_ids.push(run_id);
                    }
                    
                    // Add output
                    for &id in &local_ids {
                        registry.append_output(id, "test\n").await.unwrap();
                    }
                    
                    // Remove processes
                    for id in local_ids.drain(..) {
                        let _ = registry.remove_process(id);
                    }
                }
            });
        }
        
        while let Some(_) = tasks.join_next().await {}
        
        // Check final state
        let remaining = registry.list_processes();
        
        println!("\nMemory Safety Test:");
        println!("  Test duration: {:?}", duration);
        println!("  Remaining processes: {}", remaining.len());
        
        // Should have cleaned up properly
        assert!(remaining.len() < 100, "Too many processes leaked");
    }
}

/// End-to-end integration tests
#[cfg(test)]
mod e2e_integration_tests {
    use super::*;

    /// Test complete Claude session with security
    #[tokio::test]
    async fn test_secure_claude_session() {
        let registry = Arc::new(ProcessRegistry::new());
        let temp_dir = TempDir::new().unwrap();
        
        // Validate project path
        let project_path = temp_dir.path().to_str().unwrap();
        validate_path(project_path, None).unwrap();
        
        // Sanitize task input
        let user_task = "Help me <script>alert('xss')</script> with my code";
        let safe_task = sanitize_user_input(user_task);
        
        // Validate model
        let model = "claude-3-opus";
        validate_model_name(model).unwrap();
        
        // Start Claude session
        let config = ClaudeConfig {
            project_path: project_path.to_string(),
            task: safe_task,
            model: model.to_string(),
            ..Default::default()
        };
        
        match start_claude_session(registry.clone(), config).await {
            Ok(session_id) => {
                println!("Claude session started: {}", session_id);
                
                // Verify session is registered
                let (run_id, info) = registry.get_claude_session(&session_id).unwrap();
                assert!(info.task.contains("Help me"));
                assert!(!info.task.contains("<script>"));
            }
            Err(e) => {
                // Expected in test environment without actual Claude
                println!("Claude session failed (expected in tests): {}", e);
            }
        }
    }

    /// Test agent execution with full security
    #[tokio::test]
    async fn test_secure_agent_execution() {
        let registry = Arc::new(ProcessRegistry::new());
        let temp_dir = TempDir::new().unwrap();
        
        // Create test agent configuration
        let agent_config = AgentRunConfig {
            agent_id: 1,
            agent_name: "test-agent".to_string(),
            project_path: temp_dir.path().to_str().unwrap().to_string(),
            task: "Test task".to_string(),
            model: "claude-3-haiku".to_string(),
        };
        
        // Validate all inputs
        validate_identifier(&agent_config.agent_name).unwrap();
        validate_path(&agent_config.project_path, None).unwrap();
        validate_safe_string(&agent_config.task).unwrap();
        validate_model_name(&agent_config.model).unwrap();
        
        match run_agent(registry.clone(), agent_config).await {
            Ok(run_id) => {
                println!("Agent started with run_id: {}", run_id);
                
                // Verify process is registered
                let processes = registry.list_processes();
                assert!(processes.iter().any(|p| p.run_id == run_id));
            }
            Err(e) => {
                // Expected in test environment
                println!("Agent execution failed (expected in tests): {}", e);
            }
        }
    }
}

/// Performance regression tests
#[cfg(test)]
mod performance_regression_tests {
    use super::*;
    use std::collections::HashMap;

    /// Baseline performance metrics
    fn get_baseline_metrics() -> HashMap<&'static str, f64> {
        let mut baselines = HashMap::new();
        baselines.insert("process_registration", 100.0); // microseconds
        baselines.insert("concurrent_operations", 50000.0); // ops/sec
        baselines.insert("memory_per_process", 10.0); // KB
        baselines.insert("session_lookup", 10.0); // microseconds
        baselines
    }

    #[tokio::test]
    async fn test_performance_regression() {
        let baselines = get_baseline_metrics();
        let mut results = HashMap::new();
        
        // Test process registration performance
        let registry = Arc::new(ProcessRegistry::new());
        let start = Instant::now();
        
        for i in 0..1000 {
            let run_id = registry.generate_id().unwrap();
            registry.register_process(
                run_id,
                i,
                format!("perf-agent-{}", i),
                1234,
                "/test/path".to_string(),
                "perf test".to_string(),
                "claude-3-opus".to_string(),
                tokio::process::Command::new("echo").spawn().unwrap(),
            ).unwrap();
        }
        
        let duration = start.elapsed();
        let avg_registration_us = duration.as_micros() as f64 / 1000.0;
        results.insert("process_registration", avg_registration_us);
        
        // Test concurrent operations
        let start = Instant::now();
        let mut tasks = JoinSet::new();
        
        for i in 0..10 {
            let registry = registry.clone();
            tasks.spawn(async move {
                for j in 0..1000 {
                    let _ = registry.list_processes();
                    if j % 10 == 0 {
                        let run_id = registry.generate_id().unwrap();
                        let _ = registry.register_process(
                            run_id,
                            i * 1000 + j,
                            format!("concurrent-{}-{}", i, j),
                            1234,
                            "/test/path".to_string(),
                            "test".to_string(),
                            "claude-3-opus".to_string(),
                            tokio::process::Command::new("echo").spawn().unwrap(),
                        );
                    }
                }
            });
        }
        
        while let Some(_) = tasks.join_next().await {}
        let duration = start.elapsed();
        let ops_per_sec = 10000.0 / duration.as_secs_f64();
        results.insert("concurrent_operations", ops_per_sec);
        
        // Compare with baselines
        println!("\nPerformance Regression Test Results:");
        for (metric, value) in &results {
            let baseline = baselines.get(metric).unwrap();
            let ratio = value / baseline;
            let status = if ratio <= 1.5 { "PASS" } else { "FAIL" };
            
            println!("  {}: {:.2} (baseline: {:.2}, ratio: {:.2}x) [{}]", 
                     metric, value, baseline, ratio, status);
            
            // Allow up to 50% regression
            assert!(ratio <= 1.5, 
                    "Performance regression detected for {}: {:.2}x slower than baseline", 
                    metric, ratio);
        }
    }
}