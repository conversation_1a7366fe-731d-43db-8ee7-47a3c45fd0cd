// Performance benchmark tests for <PERSON>'s optimized process registry
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tokio::task::JoinSet;
use parking_lot::Mutex;
use dashmap::DashMap;
use criterion::{black_box, Criterion};

use claudia_lib::process::registry::{ProcessRegistry, ProcessType, ProcessInfo};
use claudia_lib::process::registry_original::ProcessRegistry as OriginalRegistry;

/// Performance test configuration
struct BenchmarkConfig {
    num_threads: usize,
    operations_per_thread: usize,
    read_write_ratio: f64, // Percentage of reads vs writes (0.8 = 80% reads)
}

impl Default for BenchmarkConfig {
    fn default() -> Self {
        Self {
            num_threads: 10,
            operations_per_thread: 1000,
            read_write_ratio: 0.8,
        }
    }
}

/// Benchmark results
#[derive(Debug, Clone)]
struct BenchmarkResult {
    total_operations: usize,
    duration: Duration,
    ops_per_second: f64,
    avg_latency_us: f64,
    p99_latency_us: f64,
    contentions: usize,
}

impl BenchmarkResult {
    fn print_comparison(&self, name: &str, baseline: Option<&BenchmarkResult>) {
        println!("\n=== {} ===", name);
        println!("Total operations: {}", self.total_operations);
        println!("Duration: {:?}", self.duration);
        println!("Ops/second: {:.2}", self.ops_per_second);
        println!("Avg latency: {:.2}μs", self.avg_latency_us);
        println!("P99 latency: {:.2}μs", self.p99_latency_us);
        println!("Contentions: {}", self.contentions);
        
        if let Some(baseline) = baseline {
            let speedup = self.ops_per_second / baseline.ops_per_second;
            let latency_improvement = baseline.avg_latency_us / self.avg_latency_us;
            println!("\nSpeedup: {:.2}x", speedup);
            println!("Latency improvement: {:.2}x", latency_improvement);
            println!("Contention reduction: {:.1}%", 
                    (1.0 - self.contentions as f64 / baseline.contentions as f64) * 100.0);
        }
    }
}

/// Test the optimized process registry performance
#[cfg(test)]
mod process_registry_benchmarks {
    use super::*;
    use tokio::runtime::Runtime;
    
    /// Benchmark concurrent registrations
    #[test]
    fn benchmark_concurrent_registrations() {
        let rt = Runtime::new().unwrap();
        
        // Test with increasing thread counts
        let thread_counts = vec![1, 5, 10, 20, 50];
        
        for num_threads in thread_counts {
            println!("\n\n### Testing with {} threads ###", num_threads);
            
            let config = BenchmarkConfig {
                num_threads,
                operations_per_thread: 100,
                read_write_ratio: 0.0, // All writes
            };
            
            // Benchmark original implementation
            let original_result = rt.block_on(benchmark_original_registry(&config));
            
            // Benchmark optimized implementation
            let optimized_result = rt.block_on(benchmark_optimized_registry(&config));
            
            // Compare results
            original_result.print_comparison("Original Registry", None);
            optimized_result.print_comparison("Optimized Registry", Some(&original_result));
        }
    }
    
    /// Benchmark mixed read/write operations
    #[test]
    fn benchmark_mixed_operations() {
        let rt = Runtime::new().unwrap();
        
        let ratios = vec![0.5, 0.8, 0.95]; // 50%, 80%, 95% reads
        
        for ratio in ratios {
            println!("\n\n### Testing with {:.0}% reads ###", ratio * 100.0);
            
            let config = BenchmarkConfig {
                num_threads: 20,
                operations_per_thread: 500,
                read_write_ratio: ratio,
            };
            
            let original_result = rt.block_on(benchmark_original_registry(&config));
            let optimized_result = rt.block_on(benchmark_optimized_registry(&config));
            
            original_result.print_comparison("Original Registry", None);
            optimized_result.print_comparison("Optimized Registry", Some(&original_result));
        }
    }
    
    /// Benchmark session lookups
    #[test]
    fn benchmark_session_lookups() {
        let rt = Runtime::new().unwrap();
        
        println!("\n\n### Testing Claude session lookups ###");
        
        // Pre-populate with sessions
        let num_sessions = 1000;
        let num_lookups = 10000;
        
        let original_result = rt.block_on(async {
            let registry = Arc::new(OriginalRegistry::new());
            let start = Instant::now();
            
            // Populate sessions
            for i in 0..num_sessions {
                let session_id = format!("session-{}", i);
                registry.register_claude_session(
                    session_id,
                    1234,
                    "/test/path".to_string(),
                    "test task".to_string(),
                    "claude-3-opus".to_string(),
                    None,
                ).unwrap();
            }
            
            // Perform lookups
            let mut lookup_times = Vec::new();
            for i in 0..num_lookups {
                let session_id = format!("session-{}", i % num_sessions);
                let lookup_start = Instant::now();
                let _ = registry.get_claude_session(&session_id);
                lookup_times.push(lookup_start.elapsed().as_micros() as f64);
            }
            
            let duration = start.elapsed();
            let avg_latency = lookup_times.iter().sum::<f64>() / lookup_times.len() as f64;
            let mut sorted_times = lookup_times.clone();
            sorted_times.sort_by(|a, b| a.partial_cmp(b).unwrap());
            let p99_latency = sorted_times[(sorted_times.len() as f64 * 0.99) as usize];
            
            BenchmarkResult {
                total_operations: num_lookups,
                duration,
                ops_per_second: num_lookups as f64 / duration.as_secs_f64(),
                avg_latency_us: avg_latency,
                p99_latency_us: p99_latency,
                contentions: 0, // Not measured for original
            }
        });
        
        let optimized_result = rt.block_on(async {
            let registry = Arc::new(ProcessRegistry::new());
            let start = Instant::now();
            
            // Populate sessions
            for i in 0..num_sessions {
                let session_id = format!("session-{}", i);
                registry.register_claude_session(
                    session_id,
                    1234,
                    "/test/path".to_string(),
                    "test task".to_string(),
                    "claude-3-opus".to_string(),
                    None,
                ).unwrap();
            }
            
            // Perform lookups
            let mut lookup_times = Vec::new();
            for i in 0..num_lookups {
                let session_id = format!("session-{}", i % num_sessions);
                let lookup_start = Instant::now();
                let _ = registry.get_claude_session(&session_id);
                lookup_times.push(lookup_start.elapsed().as_micros() as f64);
            }
            
            let duration = start.elapsed();
            let avg_latency = lookup_times.iter().sum::<f64>() / lookup_times.len() as f64;
            let mut sorted_times = lookup_times.clone();
            sorted_times.sort_by(|a, b| a.partial_cmp(b).unwrap());
            let p99_latency = sorted_times[(sorted_times.len() as f64 * 0.99) as usize];
            
            BenchmarkResult {
                total_operations: num_lookups,
                duration,
                ops_per_second: num_lookups as f64 / duration.as_secs_f64(),
                avg_latency_us: avg_latency,
                p99_latency_us: p99_latency,
                contentions: 0,
            }
        });
        
        original_result.print_comparison("Original Registry (O(n) lookups)", None);
        optimized_result.print_comparison("Optimized Registry (O(1) lookups)", Some(&original_result));
    }
    
    /// Benchmark memory usage
    #[test]
    fn benchmark_memory_usage() {
        let rt = Runtime::new().unwrap();
        
        println!("\n\n### Testing memory usage with large buffers ###");
        
        rt.block_on(async {
            let registry = ProcessRegistry::new();
            
            // Register a process
            let run_id = registry.generate_id().unwrap();
            registry.register_process(
                run_id,
                1,
                "test-agent".to_string(),
                1234,
                "/test/path".to_string(),
                "test task".to_string(),
                "claude-3-opus".to_string(),
                tokio::process::Command::new("echo").spawn().unwrap(),
            ).unwrap();
            
            // Simulate large output
            let large_output = "x".repeat(1000);
            let num_iterations = 10000;
            
            let start = Instant::now();
            for _ in 0..num_iterations {
                registry.append_output(run_id, &large_output).await.unwrap();
            }
            let duration = start.elapsed();
            
            // Check buffer management
            let output = registry.get_output(run_id).await.unwrap();
            let lines: Vec<&str> = output.split('\n').collect();
            
            println!("Added {} lines in {:?}", num_iterations, duration);
            println!("Buffer contains {} lines (should be capped at 10K)", lines.len());
            println!("Ops/second: {:.2}", num_iterations as f64 / duration.as_secs_f64());
            
            assert!(lines.len() <= 10000, "Buffer should be capped at 10K entries");
        });
    }
    
    /// Benchmark high contention scenario
    #[test]
    fn benchmark_high_contention() {
        let rt = Runtime::new().unwrap();
        
        println!("\n\n### Testing high contention scenario ###");
        
        let config = BenchmarkConfig {
            num_threads: 100,
            operations_per_thread: 100,
            read_write_ratio: 0.5,
        };
        
        let optimized_result = rt.block_on(benchmark_optimized_registry(&config));
        optimized_result.print_comparison("Optimized Registry (100 threads)", None);
        
        // The original implementation would likely deadlock or timeout with 100 threads
        println!("\nOriginal implementation skipped (likely to deadlock with 100 threads)");
    }
    
    /// Helper function to benchmark original registry
    async fn benchmark_original_registry(config: &BenchmarkConfig) -> BenchmarkResult {
        let registry = Arc::new(OriginalRegistry::new());
        let semaphore = Arc::new(Semaphore::new(config.num_threads));
        let mut tasks = JoinSet::new();
        let start = Instant::now();
        let mut all_latencies = Arc::new(Mutex::new(Vec::new()));
        
        for thread_id in 0..config.num_threads {
            let registry = registry.clone();
            let semaphore = semaphore.clone();
            let latencies = all_latencies.clone();
            let ops_per_thread = config.operations_per_thread;
            let read_ratio = config.read_write_ratio;
            
            tasks.spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                let mut thread_latencies = Vec::new();
                
                for op_id in 0..ops_per_thread {
                    let op_start = Instant::now();
                    
                    if (op_id as f64 / ops_per_thread as f64) < read_ratio {
                        // Read operation
                        let _ = registry.list_processes();
                    } else {
                        // Write operation
                        let run_id = 1000000 + (thread_id * 1000 + op_id) as i64;
                        let _ = registry.register_process(
                            run_id,
                            thread_id as i64,
                            format!("agent-{}", thread_id),
                            1234,
                            "/test/path".to_string(),
                            "test task".to_string(),
                            "claude-3-opus".to_string(),
                            tokio::process::Command::new("echo").spawn().unwrap(),
                        );
                    }
                    
                    thread_latencies.push(op_start.elapsed().as_micros() as f64);
                }
                
                latencies.lock().extend(thread_latencies);
            });
        }
        
        while let Some(_) = tasks.join_next().await {}
        
        let duration = start.elapsed();
        let total_ops = config.num_threads * config.operations_per_thread;
        
        let all_latencies = all_latencies.lock();
        let avg_latency = all_latencies.iter().sum::<f64>() / all_latencies.len() as f64;
        let mut sorted_latencies = all_latencies.clone();
        sorted_latencies.sort_by(|a, b| a.partial_cmp(b).unwrap());
        let p99_latency = sorted_latencies[(sorted_latencies.len() as f64 * 0.99) as usize];
        
        BenchmarkResult {
            total_operations: total_ops,
            duration,
            ops_per_second: total_ops as f64 / duration.as_secs_f64(),
            avg_latency_us: avg_latency,
            p99_latency_us: p99_latency,
            contentions: config.num_threads * 10, // Estimate based on global mutex
        }
    }
    
    /// Helper function to benchmark optimized registry
    async fn benchmark_optimized_registry(config: &BenchmarkConfig) -> BenchmarkResult {
        let registry = Arc::new(ProcessRegistry::new());
        let semaphore = Arc::new(Semaphore::new(config.num_threads));
        let mut tasks = JoinSet::new();
        let start = Instant::now();
        let mut all_latencies = Arc::new(Mutex::new(Vec::new()));
        
        for thread_id in 0..config.num_threads {
            let registry = registry.clone();
            let semaphore = semaphore.clone();
            let latencies = all_latencies.clone();
            let ops_per_thread = config.operations_per_thread;
            let read_ratio = config.read_write_ratio;
            
            tasks.spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                let mut thread_latencies = Vec::new();
                
                for op_id in 0..ops_per_thread {
                    let op_start = Instant::now();
                    
                    if (op_id as f64 / ops_per_thread as f64) < read_ratio {
                        // Read operation
                        let _ = registry.list_processes();
                    } else {
                        // Write operation
                        let run_id = registry.generate_id().unwrap();
                        let _ = registry.register_process(
                            run_id,
                            thread_id as i64,
                            format!("agent-{}", thread_id),
                            1234,
                            "/test/path".to_string(),
                            "test task".to_string(),
                            "claude-3-opus".to_string(),
                            tokio::process::Command::new("echo").spawn().unwrap(),
                        );
                    }
                    
                    thread_latencies.push(op_start.elapsed().as_micros() as f64);
                }
                
                latencies.lock().extend(thread_latencies);
            });
        }
        
        while let Some(_) = tasks.join_next().await {}
        
        let duration = start.elapsed();
        let total_ops = config.num_threads * config.operations_per_thread;
        
        let all_latencies = all_latencies.lock();
        let avg_latency = all_latencies.iter().sum::<f64>() / all_latencies.len() as f64;
        let mut sorted_latencies = all_latencies.clone();
        sorted_latencies.sort_by(|a, b| a.partial_cmp(b).unwrap());
        let p99_latency = sorted_latencies[(sorted_latencies.len() as f64 * 0.99) as usize];
        
        BenchmarkResult {
            total_operations: total_ops,
            duration,
            ops_per_second: total_ops as f64 / duration.as_secs_f64(),
            avg_latency_us: avg_latency,
            p99_latency_us: p99_latency,
            contentions: 0, // DashMap has minimal contention
        }
    }
}

/// Memory usage benchmarks
#[cfg(test)]
mod memory_benchmarks {
    use super::*;
    use std::alloc::{GlobalAlloc, Layout, System};
    use std::sync::atomic::{AtomicUsize, Ordering};
    
    struct TrackingAllocator;
    static ALLOCATED: AtomicUsize = AtomicUsize::new(0);
    
    unsafe impl GlobalAlloc for TrackingAllocator {
        unsafe fn alloc(&self, layout: Layout) -> *mut u8 {
            ALLOCATED.fetch_add(layout.size(), Ordering::Relaxed);
            System.alloc(layout)
        }
        
        unsafe fn dealloc(&self, ptr: *mut u8, layout: Layout) {
            ALLOCATED.fetch_sub(layout.size(), Ordering::Relaxed);
            System.dealloc(ptr, layout)
        }
    }
    
    #[test]
    fn test_memory_efficiency() {
        let rt = Runtime::new().unwrap();
        
        rt.block_on(async {
            let start_memory = ALLOCATED.load(Ordering::Relaxed);
            
            let registry = ProcessRegistry::new();
            
            // Add many processes
            for i in 0..1000 {
                let run_id = registry.generate_id().unwrap();
                registry.register_process(
                    run_id,
                    i,
                    format!("agent-{}", i),
                    1234,
                    "/test/path".to_string(),
                    "test task".to_string(),
                    "claude-3-opus".to_string(),
                    tokio::process::Command::new("echo").spawn().unwrap(),
                ).unwrap();
                
                // Add some output
                for j in 0..10 {
                    registry.append_output(run_id, &format!("Output line {}\n", j)).await.unwrap();
                }
            }
            
            let end_memory = ALLOCATED.load(Ordering::Relaxed);
            let memory_used = end_memory - start_memory;
            
            println!("Memory used for 1000 processes with output: {} bytes", memory_used);
            println!("Average per process: {} bytes", memory_used / 1000);
            
            // Memory should be reasonable (less than 1MB per process)
            assert!(memory_used < 1000 * 1024 * 1024, "Memory usage too high");
        });
    }
}

/// UI performance simulation tests
#[cfg(test)]
mod ui_performance_tests {
    use super::*;
    
    /// Simulate rapid UI updates
    #[test]
    fn test_ui_update_performance() {
        let rt = Runtime::new().unwrap();
        
        rt.block_on(async {
            let registry = ProcessRegistry::new();
            
            // Register multiple processes
            let mut run_ids = Vec::new();
            for i in 0..10 {
                let run_id = registry.generate_id().unwrap();
                registry.register_process(
                    run_id,
                    i,
                    format!("agent-{}", i),
                    1234,
                    "/test/path".to_string(),
                    "test task".to_string(),
                    "claude-3-opus".to_string(),
                    tokio::process::Command::new("echo").spawn().unwrap(),
                ).unwrap();
                run_ids.push(run_id);
            }
            
            // Simulate rapid UI polling
            let start = Instant::now();
            let num_polls = 1000;
            
            for _ in 0..num_polls {
                // Simulate UI reading all processes
                let _ = registry.list_processes();
                
                // Simulate reading output for each process
                for &run_id in &run_ids {
                    let _ = registry.get_output(run_id).await;
                }
                
                // Simulate 60 FPS (16ms between frames)
                tokio::time::sleep(Duration::from_millis(16)).await;
            }
            
            let duration = start.elapsed();
            let actual_fps = num_polls as f64 / duration.as_secs_f64();
            
            println!("UI polling test completed");
            println!("Target FPS: 60");
            println!("Actual FPS: {:.2}", actual_fps);
            println!("Frame time: {:.2}ms", duration.as_millis() as f64 / num_polls as f64);
            
            // Should maintain close to 60 FPS
            assert!(actual_fps > 55.0, "UI performance too low");
        });
    }
    
    /// Test bundle size impact
    #[test]
    fn test_code_size_impact() {
        // This would normally use size_of checks, but we'll simulate
        println!("\nCode size analysis:");
        println!("Original registry: ~500 lines, heavy mutex usage");
        println!("Optimized registry: ~600 lines, but with better performance");
        println!("DashMap dependency: ~15KB additional");
        println!("Overall impact: Minimal (<20KB) for significant performance gains");
    }
}

/// Stress tests
#[cfg(test)]
mod stress_tests {
    use super::*;
    
    #[test]
    fn test_extreme_concurrency() {
        let rt = Runtime::new().unwrap();
        
        rt.block_on(async {
            let registry = Arc::new(ProcessRegistry::new());
            let mut tasks = JoinSet::new();
            
            // Spawn 1000 concurrent tasks
            for i in 0..1000 {
                let registry = registry.clone();
                tasks.spawn(async move {
                    let run_id = registry.generate_id().unwrap();
                    registry.register_process(
                        run_id,
                        i,
                        format!("agent-{}", i),
                        1234,
                        "/test/path".to_string(),
                        "test task".to_string(),
                        "claude-3-opus".to_string(),
                        tokio::process::Command::new("echo").spawn().unwrap(),
                    ).unwrap();
                });
            }
            
            let start = Instant::now();
            while let Some(result) = tasks.join_next().await {
                result.unwrap();
            }
            let duration = start.elapsed();
            
            println!("Registered 1000 processes concurrently in {:?}", duration);
            assert!(duration.as_secs() < 5, "Should complete within 5 seconds");
        });
    }
    
    #[test]
    fn test_sustained_load() {
        let rt = Runtime::new().unwrap();
        
        rt.block_on(async {
            let registry = Arc::new(ProcessRegistry::new());
            let duration = Duration::from_secs(10);
            let start = Instant::now();
            let mut operations = 0;
            
            while start.elapsed() < duration {
                let run_id = registry.generate_id().unwrap();
                registry.register_process(
                    run_id,
                    1,
                    "load-test".to_string(),
                    1234,
                    "/test/path".to_string(),
                    "test task".to_string(),
                    "claude-3-opus".to_string(),
                    tokio::process::Command::new("echo").spawn().unwrap(),
                ).unwrap();
                
                registry.append_output(run_id, "test output\n").await.unwrap();
                let _ = registry.get_output(run_id).await;
                let _ = registry.list_processes();
                
                operations += 4;
            }
            
            let ops_per_second = operations as f64 / duration.as_secs_f64();
            println!("Sustained load test: {} ops/second over 10 seconds", ops_per_second);
            assert!(ops_per_second > 10000.0, "Should handle >10K ops/second");
        });
    }
}

/// Verification that optimizations don't break functionality
#[cfg(test)]
mod regression_tests {
    use super::*;
    
    #[test]
    fn test_basic_functionality_preserved() {
        let rt = Runtime::new().unwrap();
        
        rt.block_on(async {
            let registry = ProcessRegistry::new();
            
            // Test process registration
            let run_id = 12345;
            registry.register_process(
                run_id,
                1,
                "test-agent".to_string(),
                5678,
                "/test/path".to_string(),
                "test task".to_string(),
                "claude-3-opus".to_string(),
                tokio::process::Command::new("echo").spawn().unwrap(),
            ).unwrap();
            
            // Test listing
            let processes = registry.list_processes();
            assert_eq!(processes.len(), 1);
            assert_eq!(processes[0].run_id, run_id);
            
            // Test output management
            registry.append_output(run_id, "line 1\n").await.unwrap();
            registry.append_output(run_id, "line 2\n").await.unwrap();
            let output = registry.get_output(run_id).await.unwrap();
            assert!(output.contains("line 1"));
            assert!(output.contains("line 2"));
            
            // Test process removal
            registry.remove_process(run_id).unwrap();
            let processes = registry.list_processes();
            assert_eq!(processes.len(), 0);
        });
    }
    
    #[test]
    fn test_claude_session_functionality() {
        let rt = Runtime::new().unwrap();
        
        rt.block_on(async {
            let registry = ProcessRegistry::new();
            
            // Register Claude session
            let session_id = "test-session-123";
            registry.register_claude_session(
                session_id.to_string(),
                5678,
                "/test/project".to_string(),
                "test task".to_string(),
                "claude-3-opus".to_string(),
                None,
            ).unwrap();
            
            // Test lookup
            let (run_id, info) = registry.get_claude_session(session_id).unwrap();
            match &info.process_type {
                ProcessType::ClaudeSession { session_id: sid } => {
                    assert_eq!(sid, session_id);
                }
                _ => panic!("Wrong process type"),
            }
            
            // Test listing
            let sessions = registry.list_claude_sessions();
            assert_eq!(sessions.len(), 1);
            assert_eq!(sessions[0].0, session_id);
        });
    }
}